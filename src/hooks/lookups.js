import { useRef, useState, useEffect, useCallback } from 'react';
import { Enums, Api } from '/src';

// Helpers
const DEFAULT_PROPS = {
  cached: false, // Set default to false to avoid caching
  params: null,
};

const useLocalLookups = () => {
  const getLookup = useCallback(async (name, type = 'remote', cached = false, params = {}) => {
    const action = type === 'remote' ? 'get' : 'getCustom';

    try {
      const response = await Api.lookups[action]({ name, params });
      const responseLookup = response.data;
      return responseLookup;
    } catch (error) {
      throw error;
    }
  }, []);

  return { getLookup };
};

const getType = (lookup) => {
  if (Array.isArray(lookup)) {
    return 'inline';
  }

  if (lookup && lookup[0] === '$') {
    return 'static';
  }

  if (lookup && lookup[0] === '#') {
    return 'custom';
  }

  return 'remote';
};

const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

export const useLookups = (lookup, { cached, params } = DEFAULT_PROPS) => {
  const lookupValue = useRef(lookup);
  const [loading, setLoading] = useState(false);
  const [lookups, setLookups] = useState([]);

  const debouncedLookup = useDebounce(lookup, 300); // Adjust the delay as needed
  const { getLookup } = useLocalLookups();

  const handleGetLookup = useCallback(async () => {
    const type = getType(lookupValue.current);
    if (type === 'remote' || type === 'custom') {
      try {
        setLoading(true);
        const response = await getLookup(lookupValue.current, type, cached, params);
        setLookups(response);
      } finally {
        setLoading(false);
      }
    } else if (type === 'inline') {
      setLookups(lookup);
    } else if (type === 'static') {
      setLookups(Enums[lookupValue.current.substring(1)]);
    }
  }, [lookup, cached, JSON.stringify(params), getLookup]);

  useEffect(() => {
    if (debouncedLookup) {
      handleGetLookup();
    }
  }, [debouncedLookup, handleGetLookup]);

  const refresh = () => {
    handleGetLookup(); // Refresh the lookups
  };

  return {
    lookups,
    loading,
    refresh,
  };
};
