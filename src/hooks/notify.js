import { useContext } from "react";
import { AppContext } from "/src/components/provider";

export function useNotify() {
  const app = useContext(AppContext);

  function notify(message) {
    app.notify({ message });
  }

  notify.error = (message = "Something went wrong !") => {
    app.notify({
      message,
      type: "error",
    });
  };

  notify.warning = (message) => {
    app.notify({
      message,
      type: "warning",
    });
  };

  return { notify };
}
