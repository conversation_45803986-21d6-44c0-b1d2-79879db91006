import { useState, useEffect, useRef } from 'react';

export const useSpeech = (options = {}) => {
  const [isListening, setIsListening] = useState(false);
  const [isConverting, setIsConverting] = useState(false);
  const [finalTranscript, setFinalTranscript] = useState('');
  const [interimTranscript, setInterimTranscript] = useState('');
  const recognitionRef = useRef(null);
  const isPausedRef = useRef(false);
  const interimRef = useRef('');

  useEffect(() => {
    if (!('webkitSpeechRecognition' in window)) {
      console.error('Web Speech API is not supported in this browser.');
      return;
    }

    recognitionRef.current = new window.webkitSpeechRecognition();
    const recognition = recognitionRef.current;
    recognition.interimResults = options.interimResults ?? true;
    recognition.lang = options.lang ?? 'en-US';
    recognition.continuous = options.continuous ?? false;

    if ('webkitSpeechGrammarList' in window) {
      const grammar = '#JSGF V1.0; grammar punctuation; public <punc> = . | , | ? | ! | ; | : ;';
      const speechRecognitionList = new window.webkitSpeechGrammarList();
      speechRecognitionList.addFromString(grammar, 1);
      recognition.grammars = speechRecognitionList;
    }

    recognition.onstart = () => {
      setIsListening(true);
      setIsConverting(false); // Reset converting state on start
    };

    recognition.onresult = (event) => {
      setIsConverting(true); // Set converting to true when results are being processed
      let interim = '';
      let final = '';
      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          final += transcript + ' ';
        } else {
          interim += transcript;
        }
      }
      interimRef.current = interim;
      setFinalTranscript((prevFinal) => prevFinal + final);
      setInterimTranscript(interim);

      // Reset converting state after processing results
      if (event.results[event.results.length - 1].isFinal) {
        setIsConverting(false);
      }
    };

    recognition.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
      setIsConverting(false);
    };

    recognition.onend = () => {
      if (!isPausedRef.current) {
        setIsListening(false);
      }
      setIsConverting(false);
    };

    const updateInterimTranscript = () => {
      setInterimTranscript(interimRef.current);
    };

    const intervalId = setInterval(updateInterimTranscript, 200);

    return () => {
      clearInterval(intervalId);
      recognition.stop();
      recognition.onresult = null;
      recognition.onerror = null;
      recognition.onend = null;
      recognition.onstart = null;
    };
  }, [options.interimResults, options.lang, options.continuous]);

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      try {
        recognitionRef.current.start();
        setIsListening(true);
        isPausedRef.current = false;
        setFinalTranscript('');
        setInterimTranscript('');
      } catch (error) {
        console.error('Error starting speech recognition:', error);
      }
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && (isListening || isPausedRef.current)) {
      recognitionRef.current.stop();
      setIsListening(false);
      isPausedRef.current = false;
      setIsConverting(false);
    }
  };

  const pauseListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
      setIsListening(false);
      isPausedRef.current = true;
    }
  };

  const resumeListening = () => {
    if (recognitionRef.current && isPausedRef.current) {
      try {
        recognitionRef.current.start();
        setIsListening(true);
        isPausedRef.current = false;
      } catch (error) {
        console.error('Error resuming speech recognition:', error);
      }
    }
  };

  const clearTranscript = () => {
    setFinalTranscript('');
    setInterimTranscript('');
  };

  return {
    isListening,
    isConverting,
    transcript: finalTranscript + interimTranscript,
    startListening,
    stopListening,
    pauseListening,
    resumeListening,
    clearTranscript,
  };
};
