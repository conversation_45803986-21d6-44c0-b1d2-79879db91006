import { useImmer as useState } from 'use-immer';
import { useCallback, useEffect, useMemo, useRef } from 'react';

import { useNotify, Api, useDebounce, Enums, useLookups } from '/src';
import { useLocation } from 'react-router-dom';

export const useFetchList = (endpoint, options = {}) => {
  // Helpers
  // const generateFiltersState = () => {
  //   if (options.filters) {
  //     return Object.entries(options.filters).reduce((output, [key, filter]) => {
  //       output[key] = Enums[filter.enum].map((item) => item.value);

  //       return output;
  //     }, {});
  //   }

  //   return {};
  // };

  // Hooks
  const { notify } = useNotify();
  const location = useLocation();

  // State
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState(null);
  const [count, setCount] = useState(0);
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();

  // Search
  const [search, setSearch] = useState(options.search || '');
  const debouncedValue = useDebounce(search, 500);
  // Questions exclude
  const [exclude, setExclude] = useState(options.exclude || []);
  // Filters
  const [filters, setFilters] = useState({});

  // Pagination
  const [pagination, setPagination] = useState(options.pagination || { page: 1, size: 20 });

  // Methods
  const handleGet = async () => {
    try {
      // Set Loading
      setLoading(true);

      // Payload
      const payload = {
        search,
        pagination,
        startDate,
        endDate,
      };
      if (Object.keys(filters)?.length > 0) {
        payload.filters = filters;
      }
      if (exclude.length > 0) {
        payload.exclude = exclude;
      }
      if (options.id) {
        payload.id = options.id;
      }
      if (options.difficulty) {
        payload.difficulty = options.difficulty;
      }

      const response = await Api.get(endpoint, payload);
      const { items, count } = response.data;
      setList(items);
      setCount(count);
    } catch (error) {
      notify.error(error.response.data.message);
    } finally {
      setLoading(false);
    }
  };
  const generateSearch = () => ({
    value: search,
    update: setSearch,
  });
  const generateExclude = () => ({
    value: exclude,
    update: setExclude,
  });

  const handleDates = () => ({
    startDate: {
      value: startDate,
      update: setStartDate,
    },
    endDate: {
      value: endDate,
      update: setEndDate,
    },
  });

  const generatePagination = () => ({
    ...pagination,
    update(updates) {
      setPagination({
        ...pagination,
        ...updates,
      });
    },
  });
  // const handleFilterChange = (filterName, filterValue) => () =>
  //   setFilters((draft) => {
  //     draft[filterName] = filterValue;
  //   });

  const handleFilterChange = (filterName, filterValue) => () =>
    /* NB: Immer Draft Object:
     * This is callback Proxy object created by Immer.
     * You can't log the draft directly here
     * use this method: console.log({ ...draft })
     * or use this: JSON.stringify(draft), though it doesn't work well with circular references.
     */

    setFilters((draft) => {
      const filterArray = draft[filterName] || []; // Ensure the property is an array

      if (filterArray.includes(filterValue)) {
        // If the value is already in the array, remove it
        draft[filterName] = filterArray.filter((value) => value !== filterValue);
      } else {
        // If the value is not in the array, add it
        draft[filterName] = [...filterArray, filterValue];
      }
    });

  const resetFilters = () => () => {
    setFilters((draft) => {
      Object.keys(draft).forEach((key) => {
        delete draft[key];
      });
    });
    // // Cashing filter
    // localStorage.removeItem('cashed-filters');
    // localStorage.removeItem('cashed-previous-page');
  };

  const resetSingleFilter = (propertyKeyObject) => {
    setFilters((draft) => {
      delete draft[propertyKeyObject];
    });
  };

  const generateFilters = useCallback(() => {
    const createOptions = (filter, key) => {
      if (filter.lookup) {
        let params = null;

        if (filter?.parentLookup && filters?.[filter?.parentLookup?.key]) {
          const { fieldName, key } = filter.parentLookup;
          params = { [fieldName]: filters[key] };
        }
        if (filter?.parentLookup && filter?.parentLookup?.fieldValue) {
          const { fieldName, fieldValue } = filter.parentLookup;
          params = { [fieldName]: [fieldValue] };
        }

        /* Add specific params in case of no Authentication  */
        if (filter.techPassProgrammingTest) {
          params = {
            ...params,
            techPassProgrammingTest: true,
          };
        }

        const { lookups } = useLookups(filter.lookup, { params });

        return {
          label: filter.label,
          key: filter.lookup,
          originalKey: key,
          options: lookups.map((option) => ({
            name: `${filter.label}-${option._id}`,
            label: option?.name,
            value: filters?.[key]?.includes(option._id),
            onChange: handleFilterChange(key, option._id),
            reset: resetFilters(),
            resetSingle: resetSingleFilter,
          })),
        };
      } else if (filter.enum) {
        return {
          label: filter.label,
          key: filter.enum,
          originalKey: key,
          options: Enums[filter.enum].map((option) => {
            return {
              name: `${filter.label}-${option.value}`,
              label: option?.label,
              value: filters[key]?.includes(option.value),
              onChange: handleFilterChange(key, option.value),
              reset: resetFilters(),
              resetSingle: resetSingleFilter,
            };
          }),
        };
      }
    };

    const generateNestedFilters = (nestedFilters) => {
      return Object.entries(nestedFilters).reduce((acc, [nestedKey, nestedFilter]) => {
        acc.push(createOptions(nestedFilter, nestedKey));
        return acc;
      }, []);
    };

    if (options.filters) {
      return Object.entries(options.filters).map(([key, filter]) => {
        return createOptions(filter, key);
      });
    }
    return [];
  }, [options.filters, filters]); // Ensure dependencies are correct to prevent unnecessary re-renders

  // Lifecycle
  useEffect(() => {
    if (pagination.page !== 1) {
      setPagination({ ...pagination, page: 1 });
    }
  }, [debouncedValue, filters]);
  useEffect(() => {
    handleGet();
  }, [debouncedValue, pagination, filters, startDate, endDate]);

  // Subtract one from pagination when delete the last element in page
  useEffect(() => {
    if (pagination?.page > 0 && count > 0 && count <= (pagination?.page - 1) * pagination?.size) {
      setPagination((prev) => ({ ...pagination, page: prev.page - 1 }));
    }
  }, [list]);

  // // Cashing filter
  // useEffect(() => {
  //   // Set filters array, previous page
  //   if (Object.keys(filters)?.length) {
  //     localStorage.setItem('cashed-filters', JSON.stringify(filters));
  //     localStorage.setItem('cashed-previous-page', location.pathname);
  //   }
  // }, [filters]);

  // useEffect(() => {
  //   // Check same previous page
  //   const previousPage = localStorage.getItem('cashed-previous-page');
  //   if (location.pathname === previousPage) {
  //     // Check cashed filter
  //     const cashedFilter = localStorage.getItem('cashed-filters');
  //     if (cashedFilter) {
  //       setFilters(JSON.parse(cashedFilter));
  //     }
  //   }
  //   localStorage.removeItem('cashed-filters');
  //   localStorage.removeItem('cashed-previous-page');
  // }, []);

  return {
    // Loading
    loading,
    ready: !!list,
    // List
    list: list || [],
    count,
    refresh: handleGet,
    // Search
    search: generateSearch(),
    exclude: generateExclude(),
    handleDates: handleDates(),
    pagination: generatePagination(),
    filters: generateFilters(),
    setFilters: setFilters,

    setPagination,
    setLoading,
    setSearch,
  };
};
