import { useState, useEffect, useContext } from 'react';

// Context
import { SubmissionAiContext } from '/src/pages/quiz-ai';

const useTypingEffect = (text, interval = 75) => {
  const [displayedText, setDisplayedText] = useState('');
  const [charIndex, setCharIndex] = useState(0);

  // Context
  const { aiIsTyping, setAiIsTyping } = useContext(SubmissionAiContext);

  useEffect(() => {
    if (charIndex < text.length) {
      setAiIsTyping(true);
      const timer = setInterval(() => {
        setDisplayedText((prev) => prev + text[charIndex]);
        setCharIndex((prev) => prev + 1);
      }, interval);
      return () => clearInterval(timer);
    } else {
      setAiIsTyping(false);
    }
  }, [charIndex, text, interval]);

  return displayedText;
};

export { useTypingEffect };
