import { useNavigate, useMatches } from 'react-router-dom';

export const useBreadcrumb = () => {
  // Composables
  const navigate = useNavigate();
  const matches = useMatches();

  // Routes
  const routes = matches.filter((match) => !!match.data);

  // Methods
  const handleRouteClick = (e, path) => {
    e.preventDefault();

    navigate(path);
  };

  const currentRoute = routes[routes.length - 1];

  return {
    routes,
    currentRoute,
    handleRouteClick,
  };
};
