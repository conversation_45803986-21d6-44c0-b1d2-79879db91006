import { useImmer as useState } from 'use-immer';
import { set } from 'object-path';

export const useForm = (payload) => {
  const backup = JSON.parse(JSON.stringify(payload));
  const [form, setFormValue] = useState(payload);

  const setFieldValue =
    (path, type = (a) => a) =>
    (value) => {
      setFormValue((draft) => {
        set(draft, path, type(value));
      });
    };

  const resetForm = () => {
    setFormValue(backup);
  };

  return {
    form,
    resetForm,
    setFormValue,
    setFieldValue,
  };
};
