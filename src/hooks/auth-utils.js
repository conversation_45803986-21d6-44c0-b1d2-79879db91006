import { useNotify } from '/src';
import { useNavigate } from 'react-router-dom';
import { AUTH_ROUTE_PATH } from '/src/configs/router';
import { AppContext } from '/src/components/provider';
import { useContext } from 'react';

export function useAuthUtils() {
  const { notify } = useNotify();
  const navigate = useNavigate();
  const { updateUser } = useContext(AppContext);

  const logout = () => {
    localStorage.removeItem('userData');
    updateUser({});
    navigate(`${AUTH_ROUTE_PATH}/login`);
    notify('Logout succeeded!');
  };

  return {
    logout,
  };
}
