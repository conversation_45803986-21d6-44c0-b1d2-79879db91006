// React
import { useContext, useEffect } from 'react';

// Context
import { AppContext } from '/src/components/provider';
import { VITE_API_BASE_URL } from 'src/configs/api';

export const useUserSSE = () => {
  const { userData, updateUser } = useContext(AppContext);

  useEffect(() => {
    if (!userData?.organizationId) return;

    const eventSource = new EventSource(`${VITE_API_BASE_URL}organizations/events/${userData.organizationId}`);

    eventSource.onmessage = (event) => {
      try {
        const { data: features } = JSON.parse(event.data);

        if (features) {
          // Merge new features into existing user data
          const updatedUser = {
            ...userData,
            features: features,
          };

          updateUser(updatedUser);
          localStorage.setItem('userData', JSON.stringify(updatedUser));
        }
      } catch (err) {
        console.error('Failed to parse SSE data:', err);
      }
    };

    eventSource.onerror = (error) => {
      console.error('SSE error:', error);
      eventSource.close();
    };

    return () => {
      eventSource.close();
    };
  }, [userData?.organizationId]); // rerun if org changes
};
