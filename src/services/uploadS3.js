import { Api } from '/src';
import axios from 'axios';

export const uploadToS3 = async (videoBlob, interviewId, answerNumber) => {
  if (!videoBlob || !interviewId || !answerNumber) return;

  // Generate a unique filename per answer to avoid overwrite
  const timestamp = Date.now();
  const randomSuffix = Math.floor(Math.random() * 100000);
  const fileName = `interviews/${interviewId}/answer-${answerNumber}-${timestamp}-${randomSuffix}.webm`;

  const file = new File([videoBlob], fileName, { type: 'video/webm' });

  const MAX_RETRIES = 3;
  let retryCount = 0;

  while (retryCount < MAX_RETRIES) {
    try {
      // 1. Get pre-signed URL from backend
      const { data } = await Api.get(`ai-interview/upload-url?mimeType=${file.type}&fileName=${encodeURIComponent(fileName)}`);

      // 2. Upload to S3
      await axios.put(data.url, file, {
        headers: {
          'Content-Type': file.type,
        },
      });

      return data.key;
    } catch (error) {
      retryCount++;
      console.error(`Upload attempt ${retryCount} failed:`, error);

      if (retryCount >= MAX_RETRIES) {
        console.error('Failed to upload interview recording after multiple attempts');
      }

      // Exponential backoff
      await new Promise((resolve) => setTimeout(resolve, 1000 * Math.pow(2, retryCount)));
    }
  }

  throw new Error('Failed to upload to S3 after multiple attempts');
};
