import { defineEnum } from '/src/utils';

export const Enums = Object.freeze({
  // Submissions
  SubmissionStatus: defineEnum([
    {
      value: 4,

      label: 'Scheduled',
    },
    {
      value: 1,
      label: 'Not Started',
    },
    {
      value: 2,
      label: 'In Progress',
    },
    {
      value: 3,
      label: 'Completed',
    },
  ]),

  // @FIXME:
  AverageScore: defineEnum([
    {
      value: 1,
      label: '< 80%',
    },
    {
      value: 2,
      label: '> 50%',
    },
  ]),

  // Submissions due date
  //@TODO:Hashing until handle date filters logic
  SubmissionDueDate: defineEnum([
    // {
    //   value: 1,
    //   label: 'Today',
    // },
    // {
    //   value: 2,
    //   label: 'Last Week',
    // },
    // {
    //   value: 3,
    //   label: 'Last Month',
    // },
    {
      value: 4,
      label: 'Pick a Date',
    },
  ]),

  // SubmissionScoreRange
  grade: defineEnum([
    {
      value: 1,
      label: 'Excellent',
    },
    {
      value: 2,
      label: 'Good',
    },
    {
      value: 3,
      label: 'Poor',
    },
  ]),

  // SubmissionWarning
  SubmissionWarning: defineEnum([
    {
      value: 1,
      label: 'Cheating Behaviour',
    },
    {
      value: 2,
      label: 'Missed Deadline',
    },
    {
      value: 3,
      label: 'Overdue',
    },
  ]),

  // Question Difficulty
  QuestionDifficulty: defineEnum([
    {
      value: 1,
      label: 'Easy',
    },
    {
      value: 2,
      label: 'Medium',
    },
    {
      value: 3,
      label: 'Hard',
    },
    {
      value: 4,
      label: 'Very Hard',
    },
  ]),

  // Quiz Difficulty
  QuizDifficulty: defineEnum([
    {
      value: 1,
      label: 'Intern',
    },
    {
      value: 2,
      label: 'Fresh',
    },
    {
      value: 3,
      label: 'Junior',
    },
    {
      value: 4,
      label: 'Mid-level',
    },
    {
      value: 5,
      label: 'Senior',
    },
  ]),

  InterviewModel: defineEnum([
    {
      value: 'o3-mini',
      label: 'o3-mini',
    },
    {
      value: 'o4-mini',
      label: 'o4-mini',
    },
    {
      value: 'gpt-4.1-nano',
      label: 'gpt-4.1-nano',
    },
    {
      value: 'gpt-4-turbo',
      label: 'gpt-4-turbo',
    },
    {
      value: 'gpt-4.1-mini',
      label: 'gpt-4.1-mini',
    },
    { value: 'gpt-4o-mini', label: 'gpt-4o-mini' },
  ]),

  // QuestionType
  QuestionType: defineEnum([
    {
      label: 'Singlechoice',
      key: 'Singlechoice',
      value: 1,
    },
    {
      value: 2,
      label: 'Multichoice',
      key: 'Multichoice',
    },
    {
      value: 3,
      label: 'Essay',
      key: 'Textarea',
    },
  ]),

  // InterviewType
  InterviewType: defineEnum([
    {
      label: 'Ready Questions',
      value: 1,
      key: 'Ready',
    },
    {
      label: 'Interactive Interview',
      value: 2,
      key: 'Interactive',
    },
  ]),

  // User Gender
  Gender: defineEnum([
    {
      value: 1,
      label: 'Male',
    },
    {
      value: 2,
      label: 'Female',
    },
  ]),

  YesNo: defineEnum([
    {
      value: true,
      label: 'Yes',
    },
    {
      value: false,
      label: 'No',
    },
  ]),

  // Logs
  Logs: defineEnum([
    {
      value: 1,
      label: 'Window Refresh',
      key: 'WindowRefresh',
    },
    {
      value: 3,
      label: 'Submit Answer',
      key: 'SubmitAnswer',
    },
    {
      value: 4,
      label: 'Step Move',
      key: 'StepMove',
    },
    {
      value: 5,
      label: 'Submit Submission',
      key: 'SubmitSubmission',
    },
    {
      value: 6,
      label: 'Tab Switched',
      key: 'WindowSwitched',
    },
    {
      value: 8,
      label: 'Open Context Menu (Right Click)',
      key: 'ContextMenu',
    },
    {
      value: 9,
      label: 'IP Changed',
      key: 'IpChanged',
    },
  ]),

  // Scope
  Scope: defineEnum([
    {
      value: 1,
      label: 'My Data',
    },
  ]),

  // Roles
  Role: defineEnum([
    {
      value: 1,
      label: 'Super Admin',
    },
    {
      value: 2,
      label: 'Content Creator',
    },
    {
      value: 3,
      label: 'HR',
    },
  ]),

  // Intern Phase
  InternPhase: defineEnum([
    {
      value: 1,
      label: '1',
    },
    {
      value: 2,
      label: '2',
    },
    {
      value: 3,
      label: '3',
    },
    {
      value: 4,
      label: '4',
    },
    {
      value: 5,
      label: '5',
    },
  ]),

  // Recommended
  Recommended: defineEnum([
    {
      value: 1,
      label: 'Recommended',
    },
    {
      value: 2,
      label: 'Not Recommended',
    },
  ]),

  // Type of assignment
  AssignmentType: defineEnum([
    {
      value: 1,
      label: 'Screenings',
    },
    {
      value: 2,
      label: 'Tests',
    },
    {
      value: 3,
      label: 'Interviews',
    },
  ]),

  Language: defineEnum([
    {
      value: 1,
      label: 'English',
      code: 'en-US',
    },
    {
      value: 2,
      label: 'Arabic',
      code: 'ar-XA',
    },
    {
      value: 3,
      label: 'Turkish',
      code: 'tr-TR',
    },
  ]),

  // 0 Female, 1 Male
  AiAvatarModelLanguages: defineEnum([
    {
      lang: 'English',
      langCode: 'en-US',
      voice: 'en-US-Wavenet-F',
      gender: 0,
    },
    {
      lang: 'English',
      langCode: 'en-US',
      voice: 'en-US-Wavenet-I',
      gender: 1,
    },
    {
      lang: 'Arabic',
      langCode: 'ar-XA',
      voice: 'ar-XA-Wavenet-D',
      gender: 0,
    },
    {
      lang: 'Arabic',
      langCode: 'ar-XA',
      voice: 'ar-XA-Wavenet-B',
      gender: 1,
    },
    {
      lang: 'Turkish',
      langCode: 'tr-TR',
      voice: 'tr-TR-Wavenet-E',
      gender: 1,
    },
    {
      lang: 'Turkish',
      langCode: 'tr-TR',
      voice: 'tr-TR-Wavenet-D',
      gender: 0,
    },
  ]),

  // Ai avatar model
  AiAvatarModel: defineEnum([
    {
      value: 'ramy',
      modelPath: 'male-01.glb',
      iconPath: 'male-01.png',
      iconPathOut: 'ramy-body.png',
      gender: 1,
    },
    {
      value: 'lina',
      modelPath: 'female-01.glb',
      iconPath: 'female-01.png',
      iconPathOut: 'lina-out.png',
      gender: 0,
    },
    {
      value: 'adam',
      modelPath: 'male-02.glb',
      iconPath: 'male-02.png',
      iconPathOut: 'adam-body.png',
      gender: 1,
    },
    {
      value: 'sarah',
      modelPath: 'female-02.glb',
      iconPath: 'female-02.png',
      iconPathOut: 'sarah-out.png',
      gender: 0,
    },
    {
      value: 'deniz',
      modelPath: 'male-03.glb',
      iconPath: 'male-03.png',
      iconPathOut: 'deniz-body.png',
      gender: 1,
    },
    {
      value: 'emma',
      modelPath: 'female-03.glb',
      iconPath: 'female-03.png',
      iconPathOut: 'emma-out.png',
      gender: 0,
    },
  ]),

  // subscriptionStatus
  SubscriptionStatus: defineEnum([
    {
      value: 1,
      label: 'Pending',
    },
    {
      value: 2,
      label: 'Active',
    },
    {
      value: 3,
      label: 'Cancelled',
    },
    {
      value: 4,
      label: 'Failed',
    },
    {
      value: 5,
      label: 'Expired',
    },
  ]),

  // billingCycle
  BillingCycle: defineEnum([
    {
      value: 1,
      label: 'Monthly',
    },
    {
      value: 2,
      label: 'Yearly',
    },
  ]),

  // PlanType
  PlanType: defineEnum([
    {
      value: 1,
      label: 'Basic',
    },
    {
      value: 2,
      label: 'VIP',
    },
    {
      value: 3,
      label: 'Pro',
    },
    {
      value: 4,
      label: 'Free',
    },
  ]),

  // Available Ai languages
  InterviewLanguage: defineEnum([
    { value: 'English', icon: 'circle-flags:uk' },
    { value: 'Turkish', icon: 'emojione:flag-for-turkey' },
    { value: 'Arabic', icon: 'emojione:flag-for-saudi-arabia' },
  ]),
});
