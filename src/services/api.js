import { api } from './axios';

export const Api = {
  get: (endpoint, params) => api.get(endpoint, { params }),
  post: (endpoint, data) => api.post(endpoint, data),
  put: (endpoint, data) => api.put(endpoint, data),
  delete: (endpoint, params) => api.delete(endpoint, { params }),
  call: (method, endpoint, data, params) =>
    api({
      method,
      url: endpoint,
      data,
      params,
    }),
  lookups: {
    get: ({ name, params }) => api.get(`/lookups/${name}`, { params }),
    getCustom: ({ name, params }) => api.get(name.substring(1), { params }),
  },
};
