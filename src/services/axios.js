import axios from 'axios';
import { VITE_API_BASE_URL } from 'src/configs/api';
import globalRouter from './global-router';

const api = axios.create({
  baseURL: VITE_API_BASE_URL,
});

api.interceptors.request.use(
  (config) => {
    const userData = JSON.parse(localStorage.getItem('userData'));
    const access_token = userData?.access_token;
    if (access_token) {
      config.headers.Authorization = `Bearer ${access_token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => response,
  (error) => {
    const status = error?.response?.status;
    if (status === 401) {
      localStorage.removeItem('userData');
      globalRouter.navigate('/auth/login');
    }
    // @FIXME:
    // if (status === 403) {
    //   globalRouter.navigate('/403');
    // }
    return Promise.reject(error);
  }
);

export { api };
