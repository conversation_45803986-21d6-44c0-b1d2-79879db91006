export const Regex = {
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+{}|:"<>?[\];',.\/\\-])[A-Za-z\d!@#$%^&*()_+{}|:"<>?[\];',.\/\\-]{8,}$/,
  name: /^[A-Za-z ]+$/,
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  phoneNumber: /^\d+$/,
  // categorySubcategoryTopic: /^[A-Za-z]/, // Old way to regex with english and valid inputs
  categorySubcategoryTopic: /^.*$/s, // Accept anything
};
