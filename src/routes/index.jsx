import { Navigate } from 'react-router-dom';

// Routes
import appRoutes from '../modules/app/routes';
import authRoutes from '../modules/auth/routes';
import globalRoutes from '../modules/global/routes';

// Tests
import { QuizLayout } from '/src/layouts/quiz';
import { QuizAiLayout } from '/src/layouts/quiz-ai';
import { QuizPage } from '/src/pages/quiz';
import { QuizAiPage } from '/src/pages/quiz-ai';

// Global
import { Error404Page } from '/src/pages/404';
import { Error403Page } from '/src/pages/403';
import { DemoRequest } from '../pages/demo-request';
import { DemoMainLayout } from '../pages/mainDemo-request';

import AppWrapper from './RouterProvider';
import { TestAiApi } from '../pages/test-ai-api/render';
import { PaymentPage } from '../pages/payment';
import PaymentCallback from '../modules/global/modules/pricing/components/paymentCallBack';

const routes = [
  {
    path: '/test-ai-api',
    element: <TestAiApi />,
  },
  {
    path: '/',
    element: <AppWrapper />,
    children: [
      // Demo request
      {
        path: '/request-demo',
        element: <DemoMainLayout />,
        children: [{ path: '', element: <DemoRequest /> }],
      },

      // Payment routes
      {
        path: '/payment/:planId',
        element: <PaymentPage />,
      },
      // @FIXME: Needs to be handled in a n appropriate route
      {
        path: '/payment-callback',
        element: <PaymentCallback />,
      },

      // Routes
      ...appRoutes,
      ...authRoutes,
      ...globalRoutes,

      // Test
      {
        path: '/test',
        element: <QuizLayout />,
        children: [
          {
            path: '',
            element: <Navigate to="/404" replace />,
          },
          {
            path: ':id',
            element: <QuizPage />,
          },
        ],
      },

      // Ai-Test
      {
        path: '/interview',
        element: <QuizAiLayout />,
        children: [
          {
            path: '',
            element: <Navigate to="/404" replace />,
          },
          {
            path: ':id',
            element: <QuizAiPage />,
          },
        ],
      },
    ],
  },
  {
    path: '/403',
    element: <Error403Page />,
  },
  {
    path: '*',
    element: <Error404Page />,
  },
];

// Remove the subscribe route if it exists
// {
//   path: '/subscribe',
//   element: <SubscribeGlobal />,
// },

export default routes;
