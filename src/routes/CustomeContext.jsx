// 📄 /hooks/useAuthInterceptor.js
import React from 'react';
import { api } from '../services/axios';
import { useNavigate } from 'react-router-dom';

// hook for intercepting api requests
export const useAuthInterceptor = function () {
  const navigate = useNavigate();
  const isNavigating = React.useRef(false);

  React.useEffect(() => {
    const authInterceptor = api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response && error.response.status === 401 && !isNavigating.current) {
          isNavigating.current = true;
          localStorage.removeItem('userData');

          setTimeout(() => {
            navigate('/auth/login');
          }, 2500);
          setTimeout(() => {
            isNavigating.current = false;
          }, 5000); // Prevent further navigation for 5 seconds
        }
        return Promise.reject(error);
      }
    );

    return () => {
      api.interceptors.response.eject(authInterceptor); // Remove interceptor on dismount/auth change
    };
  }, [navigate]); // Run if navigate changes
};
