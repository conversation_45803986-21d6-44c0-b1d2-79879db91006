import { GetEndpoints } from '../../lib/endpoints/get';
import { config } from '../../config';

/**
 * Sends a GET request to the specified endpoint.
 * @typedef {keyof GetEndpoints} Endpoint
 * @param {Endpoint} endpoint
 * @returns {Promise<Object>} - The response data as a JSON object.
 * @throws {Error} - Throws an error if the endpoint is invalid.
 */
export const GET = async (endpoint) => {
  const validEndpoints = Object.keys(GetEndpoints);
  if (!validEndpoints.includes(endpoint)) {
    throw new Error(`Invalid endpoint: ${endpoint}`, { validEndpoints });
  }
  const res = await fetch(`${config.apiBaseUrl}/${config.version}/${endpoint}`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${config.apiKey}`,
    },
  });
  return await res.json();
};
