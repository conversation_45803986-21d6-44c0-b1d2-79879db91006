// import { <PERSON><PERSON><PERSON> } from 'buffer';
import { config } from '../../config';
const defaultBody = {
  audio_format: 'wav',
};

export const POST = async (endpoint = 'audio/speech', input = '<speak>Hello, world!</speak>', voiceId = 'george') => {
  const res = await fetch(`${config.apiBaseUrl}/${config.version}/${endpoint}`, {
    method: 'POST',
    body: JSON.stringify({ ...defaultBody, input, voice_id: voiceId }),
    headers: {
      Authorization: `Bearer ${config.apiKey}`,
      'content-type': 'application/json',
    },
  });
  // const decodedAudioData = Buffer.from(res.audio_data, 'base64');
  // console.log({ decodedAudioData });
  // const audioSrc = generateAudioSrc(res.audio_data);

  if (!res.ok) {
    throw new Error(`${res.status} ${res.statusText}\n${await res.text()}`);
  }

  // return await { ...res.json(), audioSrc };
  return await res.json();
};
