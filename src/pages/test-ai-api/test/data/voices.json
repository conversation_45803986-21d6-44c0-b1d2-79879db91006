[{"id": "henry", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-henry-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-henry-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-henry-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-henry-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-henry-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-henry-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-henry-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-henry-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-henry-speechify.webp", "tags": ["accent:american-neutral", "age:middle-aged", "timbre:assertive-or-confident", "timbre:deep", "timbre:direct", "timbre:professional", "use-case:advertisement", "use-case:audiobook-and-narration", "use-case:social-media"]}, {"id": "bwyneth", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-bwyneth-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-bwyneth-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-bwyneth-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-bwyneth-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-bwyneth-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-bwyneth-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-bwyneth-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-bwyneth-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-bwyneth-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-bwyneth-speechify.webp", "tags": ["accent:american-neutral", "age:young-adult", "timbre:cheerful", "timbre:neutral", "timbre:warm-or-friendly", "use-case:conversational", "use-case:e-learning", "use-case:social-media"]}, {"id": "carly", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-carly-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-carly-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-carly-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-carly-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-carly-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-carly-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-carly-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-carly-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-carly-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-carly-speechify.webp", "tags": ["accent:american-neutral", "age:young-adult", "timbre:cheerful", "timbre:energetic", "timbre:high-pitch", "timbre:warm-or-friendly", "use-case:advertisement", "use-case:conversational", "use-case:movies-acting-and-gaming", "use-case:social-media"]}, {"id": "kristy", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kristy-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-kristy-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-kristy-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kristy-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kristy-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-kristy-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-kristy-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kristy-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-kristy-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-kristy-speechify.webp", "tags": ["accent:american-neutral", "age:young-adult", "real-person", "timbre:calm-or-relaxed", "timbre:neutral", "timbre:relaxed", "timbre:warm-or-friendly", "use-case:audiobook-and-narration", "use-case:conversational", "use-case:e-learning"]}, {"id": "oliver", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-oliver-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-oliver-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-oliver-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-oliver-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-oliver-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-oliver-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-oliver-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-oliver-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-oliver-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-oliver-speechify.webp", "tags": ["accent:american-neutral", "age:middle-aged", "timbre:calm-or-relaxed", "timbre:deep", "timbre:relaxed", "timbre:sad", "use-case:audiobook-and-narration", "use-case:conversational"]}, {"id": "tasha", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-tasha-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-tasha-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-tasha-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-tasha-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-tasha-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-tasha-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-tasha-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-tasha-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-tasha-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-tasha-speechify.webp", "tags": ["accent:american-african-american", "age:young-adult", "timbre:assertive-or-confident", "timbre:deep", "use-case:audiobook-and-narration", "use-case:conversational", "use-case:e-learning"]}, {"id": "joe", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-joe-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-joe-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-joe-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-joe-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-joe-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-joe-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-joe-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-joe-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-joe-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-joe-speechify.webp", "tags": ["accent:american-neutral", "age:young-adult", "timbre:calm-or-relaxed", "timbre:deep", "timbre:direct", "timbre:professional", "timbre:relaxed", "timbre:sad", "use-case:audiobook-and-narration", "use-case:conversational", "use-case:e-learning", "use-case:meditation"]}, {"id": "lisa", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lisa-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-lisa-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-lisa-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lisa-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lisa-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-lisa-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-lisa-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lisa-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-lisa-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-lisa-speechify.webp", "tags": ["accent:american-neutral", "age:young-adult", "timbre:calm-or-relaxed", "timbre:deep", "timbre:direct", "timbre:relaxed", "use-case:animation", "use-case:audiobook-and-narration", "use-case:conversational", "use-case:e-learning", "use-case:social-media"]}, {"id": "george", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-george-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-george-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-george-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-george-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-george-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-george-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-george-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-george-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-george-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-george-speechify.webp", "tags": ["accent:american-neutral", "age:middle-aged", "timbre:assertive-or-confident", "timbre:deep", "timbre:direct", "timbre:warm-or-friendly", "use-case:audiobook-and-narration", "use-case:conversational", "use-case:e-learning"]}, {"id": "emily", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-emily-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-emily-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-emily-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-emily-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-emily-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-emily-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-emily-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-emily-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-emily-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-emily-speechify.webp", "tags": ["accent:american-neutral", "age:young-adult", "timbre:calm-or-relaxed", "timbre:direct", "timbre:neutral", "timbre:relaxed", "timbre:warm-or-friendly", "use-case:advertisement", "use-case:animation", "use-case:conversational", "use-case:e-learning", "use-case:movies-acting-and-gaming", "use-case:social-media"]}, {"id": "rob", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-rob-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-rob-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-rob-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-rob-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-rob-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-rob-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-rob-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-rob-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-rob-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-rob-speechify.webp", "tags": ["accent:american-neutral", "age:middle-aged", "timbre:calm-or-relaxed", "timbre:deep", "timbre:professional", "timbre:relaxed", "timbre:sad", "use-case:audiobook-and-narration", "use-case:e-learning", "use-case:meditation"]}, {"id": "russell", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-russell-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-russell-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-russell-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-russell-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-russell-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-russell-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-russell-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-russell-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-russell-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-GB-russell-speechify.webp", "tags": ["accent:british", "age:senior", "real-person", "timbre:calm-or-relaxed", "timbre:cheerful", "timbre:deep", "timbre:professional", "timbre:relaxed", "timbre:warm-or-friendly", "use-case:advertisement", "use-case:animation", "use-case:audiobook-and-narration", "use-case:e-learning", "use-case:movies-acting-and-gaming"]}, {"id": "benjamin", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-benjamin-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-benjamin-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-benjamin-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-benjamin-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-benjamin-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-benjamin-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-benjamin-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-benjamin-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-benjamin-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-GB-benjamin-speechify.webp", "tags": ["accent:british", "age:middle-aged", "timbre:assertive-or-confident", "timbre:calm-or-relaxed", "timbre:deep", "timbre:direct", "timbre:professional", "timbre:relaxed", "use-case:audiobook-and-narration", "use-case:conversational", "use-case:e-learning", "use-case:meditation", "use-case:movies-acting-and-gaming"]}, {"id": "<PERSON><PERSON><PERSON>", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-michael-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-michael-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-michael-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-michael-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-michael-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-michael-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-michael-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-michael-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-michael-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-GB-michael-speechify.webp", "tags": ["accent:british", "age:young-adult", "timbre:bright", "timbre:cheerful", "timbre:direct", "timbre:neutral", "timbre:warm-or-friendly", "use-case:advertisement", "use-case:audiobook-and-narration", "use-case:conversational", "use-case:e-learning", "use-case:social-media"]}, {"id": "kim", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-AU", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-AU-kim-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-kim-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-kim-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-AU", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-AU-kim-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-AU", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-AU-kim-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-kim-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-kim-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-AU", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-AU-kim-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-kim-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-AU-kim-speechify.webp", "tags": ["age:middle-aged", "accent:australian", "use-case:audiobook-and-narration", "use-case:meditation", "use-case:e-learning", "use-case:social-media", "timbre:deep"]}, {"id": "ankit", "type": "shared", "display_name": "Ankit", "models": [{"name": "simba-base", "languages": [{"locale": "en-IN", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-IN-ankit-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-ankit-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-ankit-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-IN", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-IN-ankit-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-IN", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-IN-ankit-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-ankit-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-ankit-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-IN", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-IN-ankit-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-ankit-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-IN-ankit-speechify.webp", "tags": ["age:young-adult", "accent:indian", "use-case:audiobook-and-narration", "use-case:meditation", "use-case:e-learning", "use-case:social-media", "timbre:neutral"]}, {"id": "arun", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-IN", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-IN-arun-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-arun-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-arun-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-IN", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-IN-arun-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-IN", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-IN-arun-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-arun-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-arun-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-IN", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-IN-arun-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-arun-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-IN-arun-speechify.webp", "tags": ["age:middle-aged", "accent:indian", "use-case:audiobook-and-narration", "use-case:e-learning", "use-case:movies-acting-and-gaming", "use-case:advertisement", "use-case:animation", "use-case:conversational", "timbre:deep"]}, {"id": "carol", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-carol-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-carol-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-carol-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-carol-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-carol-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-carol-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-carol-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-carol-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-carol-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-GB-carol-speechify.webp", "tags": ["age:middle-aged", "accent:british", "use-case:audiobook-and-narration", "use-case:e-learning", "use-case:advertisement", "use-case:movies-acting-and-gaming", "timbre:deep"]}, {"id": "helen", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-helen-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-helen-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-helen-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-helen-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-helen-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-helen-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-helen-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-helen-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-helen-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-GB-helen-speechify.webp", "tags": ["age:middle-aged", "accent:british", "use-case:e-learning", "use-case:audiobook-and-narration", "use-case:conversational", "timbre:deep"]}, {"id": "julie", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-julie-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-julie-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-julie-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-julie-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-julie-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-julie-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-julie-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-julie-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-julie-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-julie-speechify.webp", "tags": ["age:young-adult", "accent:british", "use-case:movies-acting-and-gaming", "use-case:animation", "use-case:conversational", "use-case:social-media", "use-case:advertisement", "use-case:audiobook-and-narration", "timbre:neutral"]}, {"id": "linda", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-AU", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-AU-linda-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-linda-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-linda-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-AU", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-AU-linda-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-AU", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-AU-linda-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-linda-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-linda-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-AU", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-AU-linda-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-linda-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-AU-linda-speechify.webp", "tags": ["age:teen", "accent:australian", "use-case:movies-acting-and-gaming", "use-case:animation", "use-case:social-media", "use-case:conversational", "use-case:advertisement", "timbre:neutral"]}, {"id": "mark", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-mark-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-mark-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-mark-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-mark-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-mark-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-mark-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-mark-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-mark-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-mark-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-mark-speechify.webp", "tags": ["age:young-adult", "accent:american-neutral", "use-case:conversational", "use-case:e-learning", "use-case:advertisement", "use-case:social-media", "use-case:conversational", "use-case:audiobook-and-narration", "timbre:deep"]}, {"id": "nick", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-nick-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-nick-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-nick-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-nick-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-nick-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-nick-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-nick-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-nick-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-nick-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-nick-speechify.webp", "tags": ["age:teen", "accent:american-neutral", "use-case:audiobook-and-narration", "use-case:conversational", "use-case:social-media", "use-case:advertisement", "use-case:animation", "accent:american-neutral"]}, {"id": "elijah", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-NG", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-NG-elijah-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-elijah-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-elijah-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-NG", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-NG-elijah-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-NG", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-NG-elijah-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-elijah-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-elijah-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-NG", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-NG-elijah-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-elijah-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-NG-elijah-speechify.webp", "tags": ["age:middle-aged", "accent:nigerian", "use-case:audiobook-and-narration", "use-case:movies-acting-and-gaming", "use-case:e-learning", "use-case:advertisement", "timbre:deep"]}, {"id": "beverly", "type": "shared", "display_name": "Beverly", "models": [{"name": "simba-base", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-beverly-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-beverly-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-beverly-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-beverly-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-beverly-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-beverly-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-beverly-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-beverly-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-beverly-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-GB-beverly-speechify.webp", "tags": ["age:senior", "accent:british", "use-case:advertisement", "use-case:audiobook-and-narration", "use-case:movies-acting-and-gaming", "use-case:e-learning", "timbre:neutral"]}, {"id": "collin", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-collin-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-collin-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-collin-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-collin-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-collin-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-collin-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-collin-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-collin-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-collin-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-GB-collin-speechify.webp", "tags": ["age:middle-aged", "accent:british", "use-case:conversational", "use-case:e-learning", "use-case:audiobook-and-narration", "use-case:advertisement", "timbre:deep"]}, {"id": "erin", "type": "shared", "display_name": "Erin", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-erin-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-erin-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-erin-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-erin-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-erin-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-erin-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-erin-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-erin-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-erin-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-erin-speechify.webp", "tags": ["age:young-adult", "accent:american-neutral", "use-case:advertisement", "use-case:e-learning", "use-case:meditation", "use-case:conversational", "use-case:social-media", "use-case:audiobook-and-narration", "timbre:deep"]}, {"id": "jack", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jack-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-jack-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-jack-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jack-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jack-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-jack-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-jack-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jack-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-jack-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-jack-speechify.webp", "tags": ["age:young-adult", "accent:american-neutral", "use-case:conversational", "use-case:e-learning", "use-case:advertisement", "use-case:social-media", "timbre:deep"]}, {"id": "jesse", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jesse-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-jesse-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-jesse-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jesse-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jesse-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-jesse-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-jesse-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jesse-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-jesse-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-jesse-speechify.webp", "tags": ["age:middle-aged", "accent:american-neutral", "use-case:conversational", "use-case:social-media", "use-case:e-learning", "use-case:audiobook-and-narration", "use-case:advertisement", "timbre:deep"]}, {"id": "ken", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-ken-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-ken-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-ken-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-ken-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-ken-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-ken-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-ken-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-ken-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-ken-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-ken-speechify.webp", "tags": ["age:middle-aged", "accent:american-neutral", "use-case:conversational", "use-case:audiobook-and-narration", "use-case:e-learning", "use-case:advertisement", "timbre:deep"]}, {"id": "lindsey", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lindsey-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-lindsey-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-lindsey-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lindsey-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lindsey-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-lindsey-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-lindsey-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lindsey-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-lindsey-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-lindsey-speechify.webp", "tags": ["age:young-adult", "accent:american-neutral", "use-case:movies-acting-and-gaming", "use-case:advertisement", "use-case:conversational", "use-case:social-media", "use-case:animation", "use-case:audiobook-and-narration", "use-case:social-media", "timbre:neutral"]}, {"id": "monica", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-monica-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-monica-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-monica-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-monica-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-monica-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-monica-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-monica-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-monica-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-monica-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-monica-speechify.webp", "tags": ["age:young-adult", "accent:american-neutral", "use-case:movies-acting-and-gaming", "use-case:audiobook-and-narration", "use-case:e-learning", "use-case:social-media", "use-case:conversational", "timbre:neutral"]}, {"id": "phil", "type": "shared", "display_name": "Phil", "models": [{"name": "simba-base", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-phil-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-phil-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-phil-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-phil-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-phil-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-phil-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-phil-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-phil-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-phil-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-GB-phil-speechify.webp", "tags": ["age:middle-aged", "accent:british", "use-case:advertisement", "use-case:conversational", "use-case:movies-acting-and-gaming", "use-case:audiobook-and-narration", "use-case:e-learning", "timbre:deep"]}, {"id": "ron", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-ron-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-ron-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-ron-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-ron-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-ron-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-ron-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-ron-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-ron-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-ron-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-GB-ron-speechify.webp", "tags": ["age:senior", "accent:british", "use-case:audiobook-and-narration", "use-case:conversational", "use-case:e-learning", "use-case:advertisement", "timbre:deep"]}, {"id": "stacy", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-stacy-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-stacy-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-stacy-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-stacy-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-stacy-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-stacy-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-stacy-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-stacy-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-stacy-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-stacy-speechify.webp", "tags": ["age:young-adult", "accent:american-neutral", "use-case:audiobook-and-narration", "use-case:meditation", "use-case:e-learning", "use-case:social-media", "use-case:animation", "timbre:deep"]}, {"id": "archie", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-archie-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-archie-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-archie-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-archie-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-archie-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-archie-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-archie-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-archie-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-archie-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-GB-archie-speechify.webp", "tags": ["age:young-adult", "accent:british", "use-case:audiobook-and-narration", "use-case:conversational", "use-case:e-learning", "use-case:advertisement", "timbre:neutral"]}, {"id": "<PERSON>lyn", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-evelyn-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-evelyn-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-evelyn-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-evelyn-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-evelyn-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-evelyn-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-evelyn-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-evelyn-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-evelyn-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-evelyn-speechify.webp", "tags": ["age:young-adult", "accent:american-neutral", "use-case:conversational", "use-case:advertisement", "use-case:e-learning", "use-case:social-media", "timbre:neutral"]}, {"id": "freddie", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-freddie-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-freddie-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-freddie-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-freddie-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-freddie-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-freddie-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-freddie-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-freddie-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-freddie-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-GB-freddie-speechify.webp", "tags": ["age:young-adult", "accent:british", "use-case:audiobook-and-narration", "use-case:movies-acting-and-gaming", "use-case:meditation", "use-case:e-learning", "use-case:audiobook-and-narration", "use-case:animation", "timbre:neutral"]}, {"id": "harper", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-harper-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-harper-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-harper-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-harper-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-harper-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-harper-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-harper-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-harper-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-harper-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-GB-harper-speechify.webp", "tags": ["age:young-adult", "accent:british", "use-case:e-learning", "use-case:conversational", "use-case:social-media", "use-case:e-learning", "timbre:high-pitch"]}, {"id": "j<PERSON><PERSON>", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jacob-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-jacob-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-jacob-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jacob-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jacob-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-jacob-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-jacob-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jacob-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-jacob-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-jacob-speechify.webp", "tags": ["age:young-adult", "accent:american-neutral", "use-case:movies-acting-and-gaming", "use-case:animation", "use-case:conversational", "use-case:e-learning", "use-case:audiobook-and-narration", "use-case:advertisement", "timbre:neutral"]}, {"id": "james", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-james-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-james-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-james-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-james-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-james-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-james-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-james-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-james-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-james-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-james-speechify.webp", "tags": ["age:young-adult", "accent:american-neutral", "use-case:advertisement", "use-case:social-media", "use-case:e-learning", "use-case:audiobook-and-narration", "timbre:neutral"]}, {"id": "mason", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-mason-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-mason-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-mason-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-mason-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-mason-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-mason-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-mason-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-mason-speechify-turbo.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-mason-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-mason-speechify.webp", "tags": ["age:middle-aged", "accent:american-neutral", "use-case:conversational", "use-case:audiobook-and-narration", "use-case:advertisement", "use-case:e-learning", "timbre:neutral"]}, {"id": "victoria", "type": "shared", "display_name": "Victoria", "models": [{"name": "simba-base", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-victoria-speechify.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-victoria-speechify.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-victoria-speechify.mp3"}]}, {"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-victoria-speechify-english.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-victoria-speechify-multilingual.mp3"}, {"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-victoria-speechify-multilingual.mp3"}, {"locale": "es-ES", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-victoria-speechify-multilingual.mp3"}]}, {"name": "simba-turbo", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-victoria-speechify-turbo.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-victoria-speechify-multilingual.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-victoria-speechify.webp", "tags": ["age:middle-aged", "accent:american-neutral", "use-case:e-learning", "use-case:advertisement", "use-case:social-media", "timbre:neutral"]}, {"id": "christina", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-christina-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-christina-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "<PERSON><PERSON><PERSON>", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-douglas-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-douglas-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "lauren", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lauren-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lauren-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "patricia", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-patricia-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-patricia-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "jennifer", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jennifer-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jennifer-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "robert", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-robert-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-robert-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "peter", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-peter-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-peter-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "jeremy", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jeremy-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jeremy-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "barbara", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-barbara-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-barbara-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "<PERSON>san", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-susan-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-susan-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "charles", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-charles-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-charles-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "harold", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-harold-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-harold-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "sarah", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-sarah-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-sarah-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "karen", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-karen-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-karen-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "anthony", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-anthony-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-anthony-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "donald", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-donald-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-donald-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "paul", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-paul-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-paul-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "steven", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-steven-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-steven-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "andrew", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-andrew-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-andrew-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "kenneth", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kenneth-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kenneth-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "joshua", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-joshua-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-joshua-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "betty", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-betty-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-betty-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "margaret", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-margaret-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-margaret-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "kyle", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kyle-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kyle-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "edward", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-edward-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-edward-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "ronald", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-ronald-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-ronald-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "timothy", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-timothy-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-timothy-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "sandra", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-sandra-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-sandra-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "dorothy", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-dorothy-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-dorothy-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "<PERSON><PERSON><PERSON>", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jeffrey-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jeffrey-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "kimberly", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kimberly-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kimberly-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "donna", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-donna-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-donna-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "walter", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-walter-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-walter-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "megan", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-megan-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-megan-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "richard", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-richard-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-richard-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "amanda", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-amanda-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-amanda-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "melissa", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-melissa-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-melissa-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "<PERSON><PERSON><PERSON>", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-deborah-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-deborah-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "gary", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-gary-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-gary-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "rebecca", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-rebecca-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-rebecca-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "sharon", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-sharon-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-sharon-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "cynthia", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-cynthia-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-cynthia-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "kath<PERSON>", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kathleen-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kathleen-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "joan", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-joan-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-joan-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "shirley", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-shirley-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-shirley-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "angela", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-angela-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-angela-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "anna", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-anna-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-anna-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "brenda", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-brenda-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-brenda-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "larry", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-larry-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-larry-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "keith", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-keith-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-keith-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "scott", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-scott-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-scott-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "pamela", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-pamela-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-pamela-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "samuel", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-samuel-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-samuel-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "gregory", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-gregory-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-gregory-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "samantha", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-samantha-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-samantha-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "katherine", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-katherine-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-katherine-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "christine", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-christine-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-christine-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "frank", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-frank-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-frank-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "alexander", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-alexander-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-alexander-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "<PERSON><PERSON>", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-raymond-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-raymond-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "debra", "type": "shared", "display_name": "Debra", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-debra-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-debra-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "patrick", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-patrick-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-patrick-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "catherine", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-catherine-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-catherine-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "carolyn", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-carolyn-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-carolyn-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "janet", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-janet-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-janet-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "ruth", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-ruth-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-ruth-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "zachary", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-zachary-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-zachary-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "heather", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-heather-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-heather-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "diane", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-diane-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-diane-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "dennis", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-dennis-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-dennis-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "jerry", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jerry-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jerry-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "tyler", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-tyler-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-tyler-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "aaron", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-aaron-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-aaron-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "virginia", "type": "shared", "display_name": "Virginia", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-virginia-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-virginia-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "joyce", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-joyce-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-joyce-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "judith", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-judith-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-judith-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "kelly", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kelly-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kelly-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "nathan", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-nathan-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-nathan-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "terry", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-terry-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-terry-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "carl", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-carl-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-carl-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "gerald", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-gerald-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-gerald-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "jaqueline", "type": "shared", "display_name": "<PERSON><PERSON><PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jaqueline-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jaqueline-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "cheryl", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-cheryl-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-cheryl-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "christian", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-christian-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-christian-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "rohit", "type": "shared", "display_name": "<PERSON><PERSON><PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-IN", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-IN-rohit-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-IN-rohit-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "gloria", "type": "shared", "display_name": "Gloria", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-gloria-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-gloria-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "<PERSON>hur", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-arthur-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-arthur-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "austin", "type": "shared", "display_name": "Austin", "models": [{"name": "simba-english", "languages": [{"locale": "en-GB", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-austin-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-GB-austin-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "sean", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-sean-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-sean-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "martha", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-martha-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-martha-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "randy", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-randy-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-randy-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "ralph", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-ralph-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-ralph-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "roy", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-roy-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-roy-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "alan", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-alan-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-alan-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "logan", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-logan-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-logan-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "willie", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-willie-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-willie-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "kath<PERSON>", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kathryn-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kathryn-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "frances", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-frances-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-frances-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "madison", "type": "shared", "display_name": "Madison", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-madison-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-madison-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "bruce", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-bruce-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-bruce-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "billy", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-billy-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-billy-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "jordan", "type": "shared", "display_name": "Jordan", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jordan-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jordan-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "bryan", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-bryan-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-bryan-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "dylan", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-dylan-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-dylan-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "vincent", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-vincent-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-vincent-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "eugene", "type": "shared", "display_name": "Eugene", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-eugene-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-eugene-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "janice", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-janice-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-janice-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "jean", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jean-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jean-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "abigail", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-abigail-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-abigail-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "alice", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-alice-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-alice-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "bobby", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-bobby-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-bobby-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "julia", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-julia-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-julia-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "johnny", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-johnny-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-johnny-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "judy", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-judy-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-judy-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "bradley", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-bradley-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-bradley-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "hunter", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-hunter-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-hunter-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "dale", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-dale-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-dale-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "howard", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-howard-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-howard-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "fred", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-fred-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-fred-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "<PERSON><PERSON><PERSON>", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-danielle-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-danielle-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "marilyn", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-marilyn-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-marilyn-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "blake", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-blake-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-blake-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "doris", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-doris-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-doris-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "denise", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-denise-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-denise-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "issac", "type": "shared", "display_name": "Issac", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-issac-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-issac-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "theresa", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-theresa-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-theresa-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "natalie", "type": "shared", "display_name": "Natalie", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-natalie-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-natalie-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "aiden", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-aiden-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-aiden-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "brittany", "type": "shared", "display_name": "Brittany", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-brittany-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-brittany-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "charlotte", "type": "shared", "display_name": "Charlotte", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-charlotte-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-charlotte-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "marie", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-marie-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-marie-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "kayla", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kayla-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kayla-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "alexis", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-alexis-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-alexis-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "lori", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lori-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lori-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "landon", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-landon-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-landon-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "tiffany", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-tiffany-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-tiffany-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "marcus", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-marcus-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-marcus-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "martin", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-martin-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-martin-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "curtis", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-curtis-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-curtis-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "kathy", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kathy-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-kathy-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "todd", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-todd-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-todd-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "leonard", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-leonard-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-leonard-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "calvin", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-calvin-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-calvin-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "rose", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-rose-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-rose-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "ava", "type": "shared", "display_name": "Ava", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-ava-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-ava-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "bonnie", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-bonnie-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-bonnie-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "peggy", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-peggy-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-peggy-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "edwin", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-edwin-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-edwin-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "don", "type": "shared", "display_name": "Don", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-don-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-don-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "ruby", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-ruby-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-ruby-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "crystal", "type": "shared", "display_name": "Crystal", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-crystal-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-crystal-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "craig", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-craig-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-craig-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "norma", "type": "shared", "display_name": "Norma", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-norma-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-norma-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "paula", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-paula-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-paula-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "annie", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-annie-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-annie-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "shawn", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-shawn-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-shawn-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "lillian", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lillian-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lillian-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "robin", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-robin-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-robin-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "evan", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-evan-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-evan-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "garrette", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-garrette-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-garrette-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "francis", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-francis-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-francis-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "danny", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-danny-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-danny-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "stanley", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-stanley-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-stanley-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "lucy", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lucy-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lucy-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "j<PERSON><PERSON><PERSON>", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jeffery-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-jeffery-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "herbert", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-herbert-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-herbert-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "lee", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lee-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-lee-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "april", "type": "shared", "display_name": "April", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-april-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-april-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "anne", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-anne-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-anne-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "tammy", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-tammy-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-tammy-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "trevor", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-trevor-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-trevor-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "eleanor", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-eleanor-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-eleanor-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "regina", "type": "shared", "display_name": "Regina", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-regina-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-regina-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "carrie", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-carrie-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-carrie-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "leah", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-leah-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-leah-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "beth", "type": "shared", "display_name": "Beth", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-beth-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-beth-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "cody", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-cody-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-cody-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "shane", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-shane-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-shane-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "dana", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-dana-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-dana-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "allison", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-allison-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-allison-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "dawn", "type": "shared", "display_name": "Dawn", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-dawn-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-dawn-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "julian", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-julian-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-julian-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "wendy", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-wendy-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-wendy-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "travis", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-travis-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-travis-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "florence", "type": "shared", "display_name": "Florence", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-florence-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-florence-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "tracy", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-tracy-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-tracy-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "<PERSON><PERSON>", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-adrian-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-adrian-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "phillis", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-phillis-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-phillis-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "carole", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-carole-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-carole-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "mildred", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-mildred-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-mildred-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "cameron", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-cameron-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-cameron-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "chad", "type": "shared", "display_name": "Chad", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-chad-speechify-english.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-chad-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "connie", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-connie-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-connie-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "gladys", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-english", "languages": [{"locale": "en-US", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-gladys-speechify-english.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-gladys-speechify-english.mp3", "avatar_image": null, "tags": []}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "es-MX", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-MX-alejandro-speechify.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "es-MX", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-MX-alejandro-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-MX-alejandro-speechify.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/es-MX-alejandro-speechify.webp", "tags": null}, {"id": "carmen", "type": "shared", "display_name": "Carmen", "models": [{"name": "simba-base", "languages": [{"locale": "es-MX", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-MX-carmen-speechify.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "es-MX", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-MX-carmen-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-MX-carmen-speechify.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/es-MX-carmen-speechify.webp", "tags": null}, {"id": "carlos", "type": "shared", "display_name": "Diego", "models": [{"name": "simba-base", "languages": [{"locale": "es-MX", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-MX-carlos-speechify.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "es-MX", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-MX-carlos-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-MX-carlos-speechify.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/es-MX-carlos-speechify.webp", "tags": null}, {"id": "celia", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-base", "languages": [{"locale": "es-MX", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-MX-celia-speechify.mp3"}]}, {"name": "simba-multilingual", "languages": [{"locale": "es-MX", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-MX-celia-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-MX-celia-speechify.mp3", "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/es-MX-celia-speechify.webp", "tags": null}, {"id": "lucas", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "pt-BR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/pt-BR-lucas-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/pt-BR-lucas-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "luiza", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "pt-BR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/pt-BR-luiza-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/pt-BR-luiza-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "<PERSON><PERSON>", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "pt-PT", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/pt-PT-diogo-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/pt-PT-diogo-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "agueda", "type": "shared", "display_name": "<PERSON>gue<PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "pt-PT", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/pt-PT-agueda-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/pt-PT-agueda-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "<PERSON><PERSON><PERSON>", "type": "shared", "display_name": "<PERSON><PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-raphael-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-raphael-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "elise", "type": "shared", "display_name": "<PERSON><PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "fr-FR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-elise-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-elise-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "f<PERSON><PERSON>", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "de-DE", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/de-<PERSON>-frederick-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/de-<PERSON>-frederick-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "andra", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "de-DE", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/de-DE-andra-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/de-DE-andra-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "f<PERSON><PERSON><PERSON>", "type": "shared", "display_name": "<PERSON><PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "da-DK", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/da-DK-frederik-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/da-DK-frederik-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "freja", "type": "shared", "display_name": "<PERSON><PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "da-DK", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/da-DK-freja-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/da-DK-freja-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "daan", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "nl-NL", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/nl-NL-daan-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/nl-NL-daan-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "lotte", "type": "shared", "display_name": "Lotte", "models": [{"name": "simba-multilingual", "languages": [{"locale": "nl-NL", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/nl-NL-lotte-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/nl-NL-lotte-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "eino", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "fi-FI", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fi-FI-eino-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fi-FI-eino-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "<PERSON><PERSON>", "type": "shared", "display_name": "<PERSON><PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "fi-FI", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fi-FI-helmi-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fi-FI-helmi-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "jakob", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "nb-NO", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/nb-NO-jakob-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/nb-NO-jakob-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "frida", "type": "shared", "display_name": "Frida", "models": [{"name": "simba-multilingual", "languages": [{"locale": "nb-NO", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/nb-NO-frida-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/nb-NO-frida-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "kostas", "type": "shared", "display_name": "<PERSON><PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "el-GR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/el-GR-kostas-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/el-GR-kostas-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "eleni", "type": "shared", "display_name": "Eleni", "models": [{"name": "simba-multilingual", "languages": [{"locale": "el-GR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/el-GR-eleni-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/el-GR-eleni-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "lazzaro", "type": "shared", "display_name": "<PERSON><PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "it-IT", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/it-IT-lazzaro-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/it-IT-lazzaro-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "alessia", "type": "shared", "display_name": "<PERSON><PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "it-IT", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/it-IT-alessia-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/it-IT-alessia-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "mart", "type": "shared", "display_name": "<PERSON><PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "et-EE", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/et-EE-mart-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/et-EE-mart-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "liis", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "et-EE", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/et-EE-liis-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/et-EE-liis-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "trinh", "type": "shared", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "vi-VN", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/vi-VN-trinh-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/vi-VN-trinh-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "thoa", "type": "shared", "display_name": "<PERSON><PERSON>a", "models": [{"name": "simba-multilingual", "languages": [{"locale": "vi-VN", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/vi-VN-thoa-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/vi-VN-thoa-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "lesya", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "uk-UA", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/uk-UA-lesya-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/uk-UA-lesya-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "taras", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "uk-UA", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/uk-UA-taras-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/uk-UA-taras-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "<PERSON><PERSON><PERSON>", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "ru-RU", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/ru-RU-mikhail-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/ru-RU-mikhail-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "olga", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "ru-RU", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/ru-RU-olga-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/ru-RU-olga-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "axel", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "sv-SE", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/sv-SE-axel-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/sv-SE-axel-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "ebba", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "sv-SE", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/sv-SE-ebba-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/sv-SE-ebba-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "hemant", "type": "shared", "display_name": "<PERSON><PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "hi-IN", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/hi-IN-hemant-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/hi-IN-hemant-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "priya", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "hi-IN", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/hi-IN-priya-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/hi-IN-priya-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "aicha", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "ar-AE", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/ar-AE-aicha-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/ar-AE-aicha-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "ismail", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "ar-AE", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/ar-AE-ismail-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/ar-AE-ismail-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "moshe", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "he-IL", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/he-IL-moshe-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/he-IL-moshe-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "inbar", "type": "shared", "display_name": "Inbar", "models": [{"name": "simba-multilingual", "languages": [{"locale": "he-IL", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/he-IL-inbar-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/he-IL-inbar-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "gil", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "he-IL", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/he-IL-gil-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/he-IL-gil-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "lital", "type": "shared", "display_name": "Lital", "models": [{"name": "simba-multilingual", "languages": [{"locale": "he-IL", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/he-IL-lital-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/he-IL-lital-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "yusuf", "type": "shared", "display_name": "<PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "tr-TR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/tr-TR-yusuf-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/tr-TR-yusuf-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "elif", "type": "shared", "display_name": "<PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "tr-TR", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/tr-TR-elif-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/tr-TR-elif-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "dominika", "type": "shared", "display_name": "Dominika", "models": [{"name": "simba-multilingual", "languages": [{"locale": "pl-PL", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/pl-PL-dominika-speechify-multilingual.mp3"}]}], "gender": "female", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/pl-PL-dominika-speechify-multilingual.mp3", "avatar_image": null, "tags": []}, {"id": "michal", "type": "shared", "display_name": "<PERSON><PERSON><PERSON>", "models": [{"name": "simba-multilingual", "languages": [{"locale": "pl-PL", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/pl-PL-michal-speechify-multilingual.mp3"}]}], "gender": "male", "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/pl-PL-michal-speechify-multilingual.mp3", "avatar_image": null, "tags": []}]