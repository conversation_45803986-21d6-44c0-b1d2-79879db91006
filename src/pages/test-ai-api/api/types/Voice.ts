type VoiceModel = {
  name: string;
  languages: {
    locale: string;
    preview_audio: string;
  }[];
};

export type Voice = {
  id: string;
  type: string;
  display_name: string;
  models: VoiceModel[];
  gender: string;
  preview_audio: string;
  avatar_image: string;
  tags: string[];
};

//! example:
// {
//   "id": "henry",
//   "type": "shared",
//   "display_name": "<PERSON>",
//   "models": [
//       {
//           "name": "simba-base",
//           "languages": [
//               {
//                   "locale": "en-US",
//                   "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-henry-speechify.mp3"
//               },
//               {
//                   "locale": "fr-FR",
//                   "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-henry-speechify.mp3"
//               },
//               {
//                   "locale": "es-ES",
//                   "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-henry-speechify.mp3"
//               }
//           ]
//       },
//       {
//           "name": "simba-english",
//           "languages": [
//               {
//                   "locale": "en-US",
//                   "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-henry-speechify-english.mp3"
//               }
//           ]
//       },
//       {
//           "name": "simba-multilingual",
//           "languages": [
//               {
//                   "locale": "en-US",
//                   "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/en-US-henry-speechify-multilingual.mp3"
//               },
//               {
//                   "locale": "fr-FR",
//                   "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/fr-FR-henry-speechify-multilingual.mp3"
//               },
//               {
//                   "locale": "es-ES",
//                   "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/es-ES-henry-speechify-multilingual.mp3"
//               }
//           ]
//       }
//   ],
//   "gender": "male",
//   "preview_audio": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/previews/multilingual-henry-speechify-multilingual.mp3",
//   "avatar_image": "https://storage.googleapis.com/speechify-ai-serving-prod-centralized-voice-list/shared/avatars/en-US-henry-speechify.webp",
//   "tags": [
//       "accent:american-neutral",
//       "age:middle-aged",
//       "timbre:assertive-or-confident",
//       "timbre:deep",
//       "timbre:direct",
//       "timbre:professional",
//       "use-case:advertisement",
//       "use-case:audiobook-and-narration",
//       "use-case:social-media"
//   ]
// }
