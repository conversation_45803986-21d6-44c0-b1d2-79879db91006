import { useEffect, useState } from 'react';
import { getVoices } from '../api/get/voices';
import { getAudioSpeech } from '../api/post/audio/speech';
import { JsonView, darkStyles } from 'react-json-view-lite';
import { Spinner } from './spinner';
import { FaExternalLinkAlt } from 'react-icons/fa';

import 'react-json-view-lite/dist/index.css';

export const TestAiApi = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [audioSrc, setAudioSrc] = useState('');
  const [textToSpeech, setTextToSpeech] = useState('');
  const [voiceId, setVoiceId] = useState('henry');
  const [voiceIds, setVoiceIds] = useState(null);

  const setVoices = async () => {
    setData(null);
    setLoading(true);
    const voices = await getVoices();
    setData(voices);
    setLoading(false);
  };

  const setAudioSpeech = async () => {
    setData(null);
    setLoading(true);
    try {
      const audioSpeech = await getAudioSpeech(textToSpeech || 'Hello There!', voiceId);
      setData(audioSpeech);
      setLoading(false);
    } catch (error) {
      setData(JSON.parse(JSON.stringify(error, Object.getOwnPropertyNames(error))));
      setLoading(false);
    }
  };

  useEffect(() => {
    if (data?.audio_data) {
      const audioSrc = generateAudioSrc(data.audio_data);
      setAudioSrc(audioSrc);
    }
    return () => {
      if (audioSrc) {
        URL.revokeObjectURL(audioSrc);
      }
    };
  }, [data]);

  const init = async () => {
    const voices = await getVoices();
    const ids = voices.map((v) => {
      return v.id;
    });
    setVoiceIds(ids);
  };

  useEffect(() => {
    init();
  }, []);

  const generateAudioSrc = (base64Data) => {
    const audio = new Audio('data:audio/wav;base64,' + base64Data);
    audio.play();
    // const audioBlob = new Blob([Uint8Array.from(atob(base64Data), (c) => c.charCodeAt(0))], { type: 'audio/wav' });
    return 'data:audio/wav;base64,' + base64Data;
  };

  return (
    <main className="flex w-screen h-screen gap-2 p-4 overflow-hidden text-white bg-black">
      <ul className="flex flex-col p-4 space-y-2 border border-white rounded-xl">
        <li className="flex justify-center w-full border border-white rounded-xl">
          <button className="flex justify-center flex-1 gap-2 px-4 py-2 text-center rounded-xl" onClick={setVoices}>
            getVoices
          </button>
        </li>
        <li className="flex flex-col w-full gap-2 p-4 border-2 border-white rounded-xl">
          <button className="flex items-center justify-center w-full gap-2 px-4 py-2 border border-white rounded-xl" onClick={setAudioSpeech}>
            getAudioSpeech
          </button>
          <input
            placeholder="input='Hello There'"
            className="p-4 text-white bg-black border border-white rounded-xl"
            type="text"
            name="textToSpeech"
            id=""
            value={textToSpeech}
            onChange={(e) => setTextToSpeech(e.target.value)}
          />
          <select
            placeholder="voiceId"
            className="p-4 text-white bg-black border border-white rounded-xl"
            type="text"
            name="textToSpeech"
            id=""
            value={voiceId}
            onChange={(e) => setVoiceId(e.target.value)}
          >
            {voiceIds?.map((voice) => (
              <option value={voice} key={voice}>
                {voice}
              </option>
            ))}
          </select>
        </li>
      </ul>
      <output className="justify-center flex-1 p-4 overflow-y-scroll border border-white rounded-xl">
        {loading && (
          <div className="flex gap-2">
            Loading...
            <Spinner className="!w-6 !h-6" />
          </div>
        )}
        {data && <JsonView data={data} shouldExpandNode={() => false} style={{ ...darkStyles, container: 'bg-black' }} />}
        {audioSrc && <audio controls src={audioSrc} />}
      </output>
    </main>
  );
};
