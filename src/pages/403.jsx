import React from 'react';
import { I<PERSON>, Button, Logo } from '/src';
import { useNavigate } from 'react-router-dom';

export const Error403Page = () => {
  const navigate = useNavigate();

  return (
    <div className="h-screen flex flex-col">
      <header className="px-4 py-2.5">
        <div className="flex items-center justify-start mr-4 cursor-pointer" onClick={() => navigate('/')}>
          <Logo className="h-8" />
        </div>
      </header>
      <section className='h-full flex flex-col justify-center items-center bg-white dark:bg-gray-900 rounded-lg shadow-lg p-4 sm:p-0 sm:m-16 bg-[url("/images/errors/background-403.png")] bg-[length:100%_100%] bg-no-repeat'>
        <img src="/images/errors/text-403.png" alt="Thepass Platfom" className="w-1/2 sm:w-3/4 md:w-1/3 mb-8" />
        <p className="mb-4 text-xl tracking-tight font-medium text-gray-900 dark:text-white">Access Restricted: Supervisors Only</p>
        <p className="mb-4 text-xl font-medium text-gray-500 dark:text-gray-400">You do not have permission to view this page.</p>
        <Button
          type="button"
          gradientMonochrome="purple"
          onClick={() => {
            navigate('/');
          }}
        >
          <div className="h-5 w-5 mr-2">
            <Icon icon="material-symbols:arrow-left-alt-rounded" width="22" />
          </div>
          <p>Go Back</p>
        </Button>
      </section>
    </div>
  );
};
