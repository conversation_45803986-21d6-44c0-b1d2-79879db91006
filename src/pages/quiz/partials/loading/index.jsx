import React from 'react';
import { Spinner } from 'flowbite-react';

export const SubmissionLoading = () => {
  return (
    <div className='flex min-h-full flex-1 flex-row justify-center px-6 py-12 bg-gray-50 dark:bg-gray-900'>
      <div className='bg-white dark:bg-gray-800 lg:w-[900px] m-auto p-12 rounded-xl shadow-sm'>
        <div className='flex flex-col items-center justify-center gap-6'>
          <div className='flex gap-4 text-gray-500'>
            <Spinner /> <span>Loading...</span>
          </div>
        </div>
      </div>
    </div>
  );
};
