// React
import { useState, useContext, useEffect } from 'react';

// Context
import { SubmissionContext } from '/src/pages/quiz';

// UI
import { useNotify, Api } from '/src';

export const StepperHeader = () => {
  // Context
  const { submission } = useContext(SubmissionContext);

  // Hooks
  const { notify } = useNotify();

  // State
  const [isBookMarked, setIsBookMarked] = useState(false);

  const handleGet = async () => {
    try {
      const response = await Api.get(`stages/single/${submission.stage._id}`);

      setIsBookMarked(response.data);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  // On Mount
  useEffect(() => {
    handleGet();
  }, [submission?.stage]);

  return (
    <div className="flex flex-row justify-between items-baseline ">
      {/* Title */}
      <h1 className="text-xl font-medium text-[#798296] dark:text-white flex align-middle items-center">
        Question {submission.stage.index}
        <span className="text-gray-500 font-medium text-xl px-1"> of {submission?.quiz?.questionIds?.length}</span>
      </h1>
    </div>
  );
};
