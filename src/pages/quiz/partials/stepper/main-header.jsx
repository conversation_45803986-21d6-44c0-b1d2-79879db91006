// React
import { useContext } from 'react';

// Hooks
import { useDarkMode } from '/src/hooks/dark-mode';

// UI
import { Icon, Logo } from '/src';

// Context
import { SubmissionContext } from '/src/pages/quiz';

export const MainHeader = () => {
  // Context
  const { submission } = useContext(SubmissionContext);

  // Hooks
  const { switchDarkMode, isDark } = useDarkMode();

  return (
    <nav className="w-full px-6 py-4 space-y-4 bg-white dark:bg-gray-800 shadow-md rounded-lg">
      <div className="flex justify-between items-center">
        {/* Logo */}
        <div className="flex justify-between items-center">
          <Logo className="h-10" />
        </div>

        {/* Dark theme */}
        <button
          type="button"
          className="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 flex items-center justify-center"
          onClick={switchDarkMode}
        >
          <Icon icon={isDark ? 'ic:outline-light-mode' : 'ic:outline-dark-mode'} width="20px" />
        </button>
      </div>

      <div className="flex flex-wrap justify-between items-center gap-4">
        {/* Quiz title */}
        <div className="font-medium text-base leading-6 text-[#374151] dark:text-white">
          {submission?.quiz?.subCategory?.length > 1
            ? submission.quiz.subCategory.map((singleSubCategory) => singleSubCategory.subCategoryName).join(' & ')
            : submission?.quiz?.subCategory?.[0]?.subCategoryName || submission?.quiz?.title}
        </div>

        {/* Quiz progress */}
        <div className="min-w-[600px] flex justify-end items-center gap-2 grow">
          <div className="w-full flex h-4 bg-gray-200 dark:bg-gray-700 rounded-sm overflow-hidden">
            <div className="bg-[#8D5BF8] h-full" style={{ width: `${submission.progressPercentage}%` }} />
          </div>
          <p className="whitespace-nowrap text-[#667085] dark:text-white">{submission.progressPercentage}% Complete</p>
        </div>
      </div>
    </nav>
  );
};
