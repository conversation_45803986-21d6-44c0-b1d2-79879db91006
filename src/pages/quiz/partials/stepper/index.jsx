// React
import { useContext, useEffect, useState } from 'react';

// UI
import { Enums, Api } from '/src';

// Context
import { SubmissionContext } from '/src/pages/quiz';

// Components
import { MainHeader } from './main-header';
import { StepperHeader } from './header';
import { Question } from './question';
import { Answer } from './answer';
import { StepperFooter } from './footer';
import { Bookmark } from './bookmark';

export const SubmissionStepper = () => {
  // State
  const [questionList, setQuestionList] = useState([]);
  const [buttonsAvailability, setButtonsAvailability] = useState({ previous: false, next: false });

  // Filters State
  const [filterBookmark, setFilterBookmark] = useState(false);
  const [filterUnanswered, setFilterUnanswered] = useState(false);

  // Pagination State
  const [count, setCount] = useState();
  const [pagination, setPagination] = useState({ page: 1, size: 25 });

  // Context
  const { applicantId, submission, setLoading, handleGetSubmission, handleServerError, setSubmission } = useContext(SubmissionContext);

  const handleGet = async () => {
    try {
      const response = await Api.get(
        `stages/list/?submissionId=${submission._id}&page=${pagination?.page}&size=${pagination?.size}&filterUnanswered=${filterUnanswered}&filterBookmark=${filterBookmark}`
      );
      setCount(response?.data?.count);
      setQuestionList(response?.data?.items);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  const handleSubmitAnswer = async (answer) => {
    setLoading(true);

    try {
      const payload = {
        submissionId: submission._id,
        stageId: submission.stage._id,
        answer: answer,
      };

      if (submission.stage.question.type === 3) {
        payload.phoneScreening = true;
      }

      const response = await Api.post('submissions/progress/submit', payload);

      // Check if response has data
      if (response?.data) {
        setSubmission(response.data);
      } else {
        throw new Error('No data returned from API');
      }
    } catch (error) {
      handleServerError(error);
    } finally {
      setLoading(false);
    }
  };

  const handleOnMove = async (index, action) => {
    const isSubmit = index > submission.quiz.questionIds.length;

    if (isSubmit) {
      handleSubmitSubmission(submission.stage.answer);
    } else {
      setLoading(true);
      setButtonsAvailability({
        previous: action === 'previous',
        next: action === 'next',
      });

      try {
        const payload = {
          submissionId: submission._id,
          index,
          action,
          answer: submission.stage.answer,
          stageId: submission.stage._id,
        };

        if (submission.stage.question.type === 3) payload.phoneScreening = true;

        const submissionProgress = await Api.post('submissions/progress/step', payload);

        setSubmission(submissionProgress.data);
      } catch (error) {
        handleServerError(error);
      } finally {
        setLoading(false);
        setButtonsAvailability({ previous: false, next: false });
      }
    }
    handleGet();
  };

  const handleSubmitSubmission = async (answer) => {
    setLoading(true);

    try {
      await Api.post(`submissions/submit/${submission._id}`, { answer });

      handleGetSubmission();
    } catch (error) {
      handleServerError();
    } finally {
      setLoading(false);
    }
  };

  const handleAnswerChange = (value, key) => {
    // Radio
    if (submission.stage.question.type === Enums.QuestionType.Singlechoice) {
      handleSubmitAnswer(value);
    }

    // Checkbox
    if (submission.stage.question.type === Enums.QuestionType.Multichoice) {
      const answer = { ...submission?.stage?.answer };

      // Update
      answer[key] = value;

      handleSubmitAnswer(answer);
    }

    // Textarea
    if (submission?.stage?.question?.type === 3) {
      const updatedSubmission = { ...submission };
      updatedSubmission.stage.answer = value;
      setSubmission(updatedSubmission);
    }

    handleGet();
  };

  // On Mount
  useEffect(() => {
    handleGet();
  }, [filterBookmark, filterUnanswered, pagination?.page]);

  // Render
  return (
    <div className="flex gap-3 h-full p-4 bg-gray-50 dark:bg-gray-900">
      <div className="flex flex-col gap-4 w-full">
        <MainHeader />

        <div className="w-full flex flex-col grow py-8 px-6 bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-y-auto">
          <StepperHeader />

          <Question handleGetBookmark={handleGet} />

          <Answer value={submission?.stage?.answer} onChange={handleAnswerChange} />
          <div className=" h-full flex flex-grow justify-center items-center    ">
            <StepperFooter onMove={handleOnMove} handleGetBookmark={handleGet} buttonsAvailability={buttonsAvailability} />
          </div>
        </div>
      </div>

      <Bookmark
        filterUnanswered={filterUnanswered}
        setFilterUnanswered={setFilterUnanswered}
        filterBookmark={filterBookmark}
        setFilterBookmark={setFilterBookmark}
        questionList={questionList}
        handleOnMove={handleOnMove}
        count={count}
        pagination={pagination}
        setPagination={setPagination}
      />
    </div>
  );
};
