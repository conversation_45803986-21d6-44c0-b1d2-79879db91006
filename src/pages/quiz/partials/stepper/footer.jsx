import React, { useContext } from 'react';

import { Button, useConfirmDialog, Icon } from '/src';
import { SubmissionContext } from '/src/pages/quiz';

export const StepperFooter = ({ onMove, buttonsAvailability }) => {
  // Context
  const { loading, submission } = useContext(SubmissionContext);

  // Hooks
  const { showConfirm, hideConfirm } = useConfirmDialog();

  const isFirstStep = submission.stage.index === 1;
  const isLastStep = submission.stage.index === submission.quiz.questionIds.length;

  const ConfirmText = () => {
    return (
      <div>
        <div className="mx-auto mb-4 text-gray-400 dark:text-gray-200">
          <Icon icon="mage:question-mark-circle" width="70" className="text-primaryPurple" />
        </div>

        <p>Are you sure you want to submit the {submission?.quiz?.phoneScreening ? 'screening' : 'test'}?</p>
      </div>
    );
  };

  return (
    <footer className="flex w-full justify-between mt-[38px]">
      <Button
        tertiary
        label="Previous question"
        icon="material-symbols-light:arrow-left-alt-rounded"
        iconWidth="29"
        onClick={() => onMove(submission.stage.index - 1, 'previous')}
        disabled={loading || isFirstStep}
        loading={buttonsAvailability?.previous}
      />

      {isLastStep ? (
        <Button
          success
          label="Submit"
          icon="material-symbols:check-rounded"
          className='min-w-36'
          iconWidth="25"
          disabled={loading}
          loading={buttonsAvailability?.next}
          onClick={() => {
            showConfirm(ConfirmText(), {
              onConfirm() {
                hideConfirm();
                onMove(submission.stage.index + 1, 'lastStep');
              },
            });
          }}
        />
      ) : (
        <Button
          label="Next question"
          iconRight="material-symbols-light:arrow-right-alt-rounded"
          onClick={() => onMove(submission.stage.index + 1, 'next')}
          disabled={loading || isLastStep}
          loading={buttonsAvailability?.next}
          iconWidth="29"
          tertiary
        />
      )}
    </footer>
  );
};
