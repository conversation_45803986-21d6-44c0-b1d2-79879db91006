import React, { useContext, useState, useEffect } from 'react';

import { Radio, Checkbox, Textarea, Enums, Form } from '/src';

import { SubmissionContext } from '/src/pages/quiz';

// Switcher
export const Answer = (props) => {
  const { submission } = useContext(SubmissionContext);

  // Question type
  const { type } = submission.stage.question;

  if (type === Enums.QuestionType.Singlechoice) {
    return <RadioAnswer {...props} />;
  }

  if (type === Enums.QuestionType.Multichoice) {
    return <CheckboxAnswer {...props} />;
  }
  if (type === 3) {
    return <TextareaAnswer {...props} />;
  }

  return null;
};

// Answer form controls
export const CheckboxAnswer = ({ value, onChange }) => {
  // Context
  const { submission, loading } = useContext(SubmissionContext);

  // Question Options
  const { options } = submission.stage.question;

  // Limit Multichoice answer
  const handleChange = (check, option) => {
    const limitMultichoiceAnswer = () => {
      let counter = 0;
      Object.entries(value).forEach(([key, value]) => {
        value && counter++;
      });
      if (counter < 2) {
        return true;
      } else if (!check) {
        return true;
      }
      return false;
    };
    limitMultichoiceAnswer() && onChange(check, option);
  };

  return (
    <div>
      <h1 className="text-sm text-[#4B5563] font-normal mb-4 dark:text-white">Select two answers only</h1>

      <div className="flex mt-3 flex-col gap-7">
        {options.map((option) => (
          <Checkbox
            key={option.id}
            label={option.label}
            fullWidth={true}
            name={`multiChoiceAnswer${option.id}`}
            value={value[option.id]}
            onChange={(check) => handleChange(check, option.id)}
            disabled={loading}
            isCustomLabel
          />
        ))}
      </div>
    </div>
  );
};
export const RadioAnswer = ({ value, onChange }) => {
  // Context
  const { submission, loading } = useContext(SubmissionContext);

  // Question Options
  const { options } = submission.stage.question;

  return (
    <div className="flex flex-col gap-7">
      {options
        .filter((o) => !!o.label?.trim())
        .map((option) => (
          <Radio
            key={option.id}
            name={`singleChoiceAnswer${option.id}`}
            label={option.label}
            fullWidth={true}
            value={value}
            selectionValue={option.id}
            onChange={(value) => onChange(Number(value))}
            disabled={loading}
            isCustomLabel
            applicantTestView
          />
        ))}
    </div>
  );
};

// TextareaAnswer Component
export const TextareaAnswer = ({ value, onChange }) => {
  const { loading } = useContext(SubmissionContext);

  const [textareaAnswer, setTextareaAnswer] = useState(value || '');

  useEffect(() => {
    setTextareaAnswer(value || '');
  }, [value]);

  // Handle change for the answer
  const handleChange = (event) => {
    const newValue = event.target.value;
    setTextareaAnswer(newValue);
    onChange(newValue);
  };

  return (
    <div>
      <h1 className="text-sm font-normal text-[#4B5563] mb-4 dark:text-white">Type your answer here.</h1>

      <textarea
        value={textareaAnswer}
        onChange={handleChange}
        rows={8}
        className="block w-full border resize-none disabled:cursor-not-allowed disabled:opacity-50 border-gray-300 bg-gray-50 text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 p-2.5 text-sm rounded-lg focus:ring-0 focus:border-gray-300"
        disabled={loading}
      />
    </div>
  );
};
