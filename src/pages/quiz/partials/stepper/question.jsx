// React
import { useContext, useEffect, useState } from 'react';

// MD editor
import MDEditor from '@uiw/react-md-editor';

// Flowbite
import { Tooltip } from 'flowbite-react';

// UI
import { Icon, useNotify, Api } from '/src';

// Context
import { SubmissionContext } from '/src/pages/quiz';

export const Question = ({ handleGetBookmark }) => {
  // Context
  const { submission } = useContext(SubmissionContext);

  // Hooks
  const { notify } = useNotify();

  // State
  const [isBookMarked, setIsBookMarked] = useState(false);

  const handleGet = async () => {
    try {
      const response = await Api.get(`stages/single/${submission.stage._id}`);
      setIsBookMarked(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  const addtobookmark = async () => {
    try {
      await Api.put(`stages/single/${submission.stage._id}`);
      setIsBookMarked(!isBookMarked);
      handleGetBookmark();
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  // On Mount
  useEffect(() => {
    handleGet();
  }, [submission.stage]);

  return (
    <div className="flex justify-between items-start gap-4 py-2 my-1 rounded-lg select-none">
      <MDEditor.Markdown
        className="bg-white text-lg text-black font-medium   dark:bg-transparent dark:border-gray-500 dark:text-gray-300"
        source={submission?.stage?.question?.title}
        disableCopy
      />

      <div className="flex items-center cursor-pointer">
        <Tooltip content="Bookmark" placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
          <div onClick={addtobookmark}>
            <Icon
              width="20"
              outline
              icon="bxs:bookmark"
              className={`p-1.5 rounded-lg border ${
                isBookMarked
                  ? 'border-[#FBAF5559] dark:border-[#38383a] text-[#E88F26] dark:text-[#E88F26]'
                  : 'border-[#eee] dark:border-[#949494] text-[#e4e5e9] dark:text-[#e4e5e9]'
              }`}
            />
          </div>
        </Tooltip>
      </div>
    </div>
  );
};
