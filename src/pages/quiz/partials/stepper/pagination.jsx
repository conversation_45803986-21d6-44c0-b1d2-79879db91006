// UI
import { useScreenSize, StaticData } from '/src';

// Flowbite
import { Pagination } from 'flowbite-react';

export const StepperPagination = ({ count, pagination, setPagination }) => {
  // Hooks
  const screen = useScreenSize();

  return (
    count > pagination?.size && (
      <Pagination
        theme={StaticData.paginationTheme}
        currentPage={pagination?.page}
        onPageChange={(page) => setPagination((prev) => ({ ...prev, page: page }))}
        showIcons
        totalPages={Math.max(Math.ceil(count / pagination?.size), 1)}
        layout="pagination"
        previousLabel={null}
        nextLabel={null}
      />
    )
  );
};
