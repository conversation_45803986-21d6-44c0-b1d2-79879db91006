import React, { useContext, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Form, TextInput, Button, Checkbox, useForm, Api, Regex, Icon, useValidate, CustomIcon, PhoneNumberInput } from '/src';
import { FaMousePointer, FaSync, FaLock, FaEye, FaWindowRestore } from 'react-icons/fa'; // Icons related to test content

import { SubmissionContext } from '/src/pages/quiz';
import { Header } from '../../../components/header';

export const SubmissionOnboarding = () => {
  const navigate = useNavigate();
  const { id } = useParams();

  // Hooks
  const { isRequired, validateRegex, minLength, maxLength, countryCodeNumberValid } = useValidate();

  // Context
  const { applicantId, randomId, submission, loading, setLoading, handleGetSubmission, notify } = useContext(SubmissionContext);

  // Form
  const { form, setFieldValue, setFormValue } = useForm({
    name: '',
    email: '',
    mobileNumber: '',
    seniorityLevel: submission?.applicant?.seniorityLevel,
    track: submission.quiz?.category?.categoryId,
  });
  const [isAgreedOnTerms, setIsAgreedOnTerms] = useState(false);

  // Methods
  const handleStartSubmission = async () => {
    setLoading(true);
    try {
      let newApplicantId = applicantId;
      let submissionId = id;
      if (newApplicantId === undefined) {
        const response = await Api.post('applicants/single/custom/create', {
          ...form,
          randomId,
        });

        newApplicantId = response.data._id;

        // Create new submission with this applicant
        const submissionResponse = await Api.post('submissions/custom/create', {
          applicantId: newApplicantId,
          quizId: submission.quiz._id,
          randomId,
        });
        submissionId = submissionResponse.data.submissionId;
      } else {
        submissionId = submission._id;
      }

      const payload = {
        submissionId: submissionId,
        data: form,
      };

      await Api.post('submissions/progress/start', payload);

      await handleGetSubmission(submissionId);

      navigate(`/test/${submissionId}`, { replace: true });
    } catch (error) {
      notify.error(error.response.data.message);
    } finally {
      setLoading(false);
    }
  };
  const handlePopulateData = () => {
    setFormValue({
      name: submission.applicant?.name,
      email: submission.applicant?.email,
      mobileNumber: submission.applicant?.mobileNumber,
      seniorityLevel: submission.applicant?.seniorityLevel || submission.quiz?.difficulty,
      track: submission.quiz?.category?.categoryId,
    });
  };

  const instructionsData = [
    {
      number: 1,
      icon: <FaMousePointer />,
      title: 'Mouse Usage',
      content: "Only use the mouse or laptop touchpad, don't press on the keyboard.",
    },
    {
      number: 2,
      icon: <FaSync />,
      title: 'Page Reload',
      content: "Don't refresh or reload the page.",
    },
    {
      number: 3,
      icon: <FaLock />,
      title: 'IP Address',
      content: "Don't change your IP address during the test.",
    },
    {
      number: 4,
      icon: <FaWindowRestore />,
      title: 'Window Focus',
      content: 'Stay focused on this window; avoid switching to other tabs or applications.',
    },
  ];

  // On Mount
  useEffect(() => {
    handlePopulateData();
  }, []);

  // Render
  return (
    <div className="flex min-h-full flex-1 justify-center bg-gray-50 px-6 py-6 dark:bg-gray-900">
      <Header showThemeIcon={true} />
      <div className="lg:w-[1100px] my-8 p-8 space-y-2 rounded-xl shadow-[0px_0px_14px_0px_rgba(195,195,195,0.22)] dark:shadow-[0px_0px_14px_0px_rgba(195,195,195,0.08)] bg-white dark:bg-gray-800">
        <div className="flex flex-col gap-2 w-full">
          {/* If there is categoryName then we are in "Technical test", Otherwise we are in "Screening test" */}
          <p className="text-3xl md:text-4xl font-bold dark:text-white">
            Welcome to {submission?.quiz?.category?.categoryName ? `${submission?.quiz?.category?.categoryName} Test` : submission?.quiz?.title}.
          </p>

          {/* Test details */}
          <div className="flex flex-col mt-1 sm:flex-row gap-5 dark:bg-gray-700  bg-gray-100 bg-opacity-80 justify-center  py-4 px-6 rounded-md dark:text-white w-full">
            {/* Number of Questions */}
            <div className="flex items-center gap-2 w-full md:w-auto">
              <CustomIcon definedIcon="questions" className="text-primaryPurple" />

              <p className="text-lg font-medium">{submission.quiz.questionIds.length} Questions</p>
            </div>
            {/*  Duration */}
            <div className="flex items-center gap-2 w-full md:w-auto">
              <Icon width={26} icon="uit:stopwatch" className="self-center text-[#3B82F6]" />
              <p className="text-lg font-medium">{submission?.quiz?.duration} Min</p>
            </div>
          </div>
        </div>
        <div className=" bg-white dark:bg-gray-800 p-2  rounded-lg dark:text-white">
          <Form onSubmit={handleStartSubmission}>
            <div className="flex flex-col">
              {/* Form Header */}
              <div className="flex flex-col mb-4 gap-3">
                <h2 className="text-2xl font-semibold text-gray-700 dark:text-gray-200">Applicant Details</h2>
                {(!submission?.applicant?.name || !submission?.applicant?.email || !submission?.applicant?.mobileNumber) && (
                  <p className="text-base text-gray-500 dark:text-gray-400">Please fill in the following details to proceed with your application.</p>
                )}
              </div>

              {/* Form Inputs */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
                {/* Name Input */}
                <TextInput
                  name="name"
                  label="Name"
                  placeholder="Enter your name"
                  value={form.name}
                  disabled={!!submission?.applicant?.name || loading}
                  onChange={setFieldValue('name')}
                  className="mb-4"
                  validators={[isRequired(), validateRegex(Regex.name), minLength(2), maxLength(50)]}
                />

                {/* Email Input */}
                <TextInput
                  name="email"
                  label="Email"
                  placeholder="Enter your email"
                  type="email"
                  value={form.email}
                  disabled={!!submission?.applicant?.email || loading}
                  onChange={setFieldValue('email')}
                  className="mb-4"
                  validators={[isRequired(), validateRegex(Regex.email)]}
                />

                {/* Mobile Number Input */}
                <PhoneNumberInput
                  label="Mobile Number"
                  value={form.mobileNumber}
                  onChange={setFieldValue('mobileNumber')}
                  requiredLabel
                  validators={[countryCodeNumberValid()]}
                  disabled={!!submission?.applicant?.mobileNumber || loading}
                />
              </div>
            </div>
            <div className="col-start-1	col-end-3 my-3">
              <div>
                {/* Instructions */}
                <div className=" w-full border dark:border-[#4c4e56d4] p-6 rounded-xl overflow-hidden dark:bg-gray-700 dark:border-gray-700">
                  <p className="text-[22px] font-medium mb-5 dark:text-white">Instructions</p>
                  <div className="flex flex-col gap-5">
                    {instructionsData.map((instruction) => (
                      <div key={instruction.number} className="flex gap-3 items-start">
                        <div className="py-1 bg-gray-100 dark:bg-gray-800 text-sm md:text-base rounded-full size-8 min-w-8 md:size-9 md:min-w-9 flex justify-center items-center">
                          {instruction.icon}
                        </div>
                        <div className="md:flex">
                          <p className="font-medium whitespace-nowrap mt-1 md:mt-1.5 min-w-44 dark:text-white">{instruction.title}:</p>
                          <div className="font-medium mt-1.5 w-full">
                            <p className="text-gray-500 dark:text-gray-400">{instruction.content}</p>
                          </div>
                        </div>
                      </div>
                    ))}

                    {/* Agree on terms? */}
                    <Checkbox
                      className="my-2 shadow-md rounded p-2 dark:border-red-100"
                      name="terms"
                      label=" I agree and understand that violating these instructions may result in test termination or cancellation."
                      value={isAgreedOnTerms}
                      onChange={setIsAgreedOnTerms}
                    />

                    <div className="flex justify-end">
                      <Button
                        type="submit"
                        className="text-end"
                        label={submission?.quiz?.category?.categoryName ? 'Start Test' : 'Start Screening'}
                        icon="ic:sharp-play-circle-outline"
                        disabled={loading || !isAgreedOnTerms}
                        loading={loading}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
};
