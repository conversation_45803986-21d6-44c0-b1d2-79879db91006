import React, { useEffect, createContext } from 'react';
import { useImmer as useState } from 'use-immer';
import { useParams } from 'react-router-dom';

// Frontend
import { useNotify, useEventListener, Enums, Api, useDarkMode } from '/src';

// Components
import { SubmissionLoading } from './partials/loading';
import { SubmissionOnboarding } from './partials/onboarding';
import { SubmissionStepper } from './partials/stepper';
import { SubmissionFinish } from './partials/finish';
import { SubmissionLocked } from './partials/locked';
import { SubmissionExpired } from './partials/expired';
import { MobileDevice } from './partials/mobileDevice';

// Submission Context
export const SubmissionContext = createContext();

export const QuizPage = () => {
  // State
  const [submission, setSubmission] = useState(null);
  const [loading, setLoading] = useState(false);

  // Hooks
  const { id } = useParams();
  const { notify } = useNotify();
  const { setCachedDarkMode } = useDarkMode();

  // Computed
  const isSubmissionLoggable = submission && submission.startedAt && !submission.submittedAt && !submission.locked;

  // Methods
  const handleGetSubmission = async (submissionId = null) => {
    try {
      let response;
      if (submissionId) {
        response = await Api.get(`submissions/progress/${submissionId}`);
        response = response.data;
      } else {
        response = await Api.get(`submissions/progress/${id}`);
        response = response.data;
      }

      // Update
      setSubmission(response);

      if (!response.submittedAt && response.applicantId) {
        const payload = {
          applicantId: response.applicantId,
          submissionId: response._id,
          type: Enums.Logs.WindowRefresh,
          date: {},
        };

        await Api.post(`logs/create`, payload);
      }
    } catch (error) {
      handleServerError(error);
    }
  };

  const handleServerError = (error) => {
    if (error.response?.data.message.includes('submission-locked')) {
      setSubmission({ locked: true });
    } else {
      notify.error(error.response.data.message);
    }
  };

  // Handle listeners
  useEventListener('visibilitychange', async () => {
    if (isSubmissionLoggable && document.visibilityState !== 'visible') {
      const payload = {
        applicantId: submission.applicantId,
        submissionId: submission._id,
        type: Enums.Logs.WindowSwitched,
        date: {},
        stageId: submission.stage?._id,
      };

      await Api.post(`logs/create`, payload);
    }
  });
  useEventListener('keydown', async ({ keyCode }) => {
    if (isSubmissionLoggable) {
      const payload = {
        applicantId: submission.applicantId,
        submissionId: submission._id,
        type: Enums.Logs.KeyboardKeyDown,
        date: { keyCode },
        stageId: submission.stage?._id,
      };

      await Api.post(`logs/create`, payload);
    }
  });
  // listen for PrtSc key, as it only fires keyup event
  // This code works only in Windows!
  useEventListener('keyup', async ({ keyCode }) => {
    if (isSubmissionLoggable && keyCode === 44) {
      const payload = {
        applicantId: submission.applicantId,
        submissionId: submission._id,
        type: Enums.Logs.KeyboardKeyDown,
        date: { keyCode },
        stageId: submission.stage?._id,
      };

      await Api.post(`logs/create`, payload);
    }
  });
  useEventListener('contextmenu', async () => {
    if (isSubmissionLoggable) {
      const payload = {
        applicantId: submission.applicantId,
        submissionId: submission._id,
        type: Enums.Logs.ContextMenu,
        date: {},
        stageId: submission.stage?._id,
      };

      await Api.post(`logs/create`, payload);
    }
  });
  // Listen for refresh
  useEventListener('beforeunload', async () => {
    if (isSubmissionLoggable) {
      const payload = {
        applicantId: submission.applicantId,
        submissionId: submission._id,
        type: Enums.Logs.WindowRefresh,
        date: {},
        stageId: submission.stage?._id,
      };

      await Api.post(`logs/create`, payload);
    }
  });

  // Detect Device User Agent
  const [isMobile, setIsMobile] = useState(false);
  const userAgent = navigator.userAgent.toLowerCase();
  const detectDevice = () => {
    if (userAgent.match(/mobile|android|iphone|ipad|ipod|tablet/i)) {
      setIsMobile(true);
    } else {
      setIsMobile(false);
    }
  };

  // On Mount
  useEffect(() => {
    handleGetSubmission();
    detectDevice();
  }, [id]);

  // useEffect(() => {
  //   setCachedDarkMode();
  // }, []);

  const render = () => {
    // Open From Mobile
    if (isMobile) {
      return <MobileDevice />;
    }

    // Expired

    if (submission?.expired) {
      return <SubmissionExpired />;
    }

    // Done
    if (submission?.submittedAt) {
      return <SubmissionFinish isPhoneScreening={submission?.quiz?.phoneScreening} />;
    }

    // Locked

    if (submission?.locked) {
      return <SubmissionLocked />;
    }

    // Quiz
    if (submission?.startedAt) {
      return <SubmissionStepper />;
    }

    // Intro
    if (submission?.randomId || submission?.applicant) {
      return <SubmissionOnboarding />;
    }

    // Loading
    return <SubmissionLoading />;
  };

  return (
    <SubmissionContext.Provider
      value={{
        notify,
        loading,
        applicantId: submission?.applicant._id,
        randomId: submission?.randomId,
        submission,
        setLoading,
        setSubmission,
        handleGetSubmission,
        handleServerError,
      }}
    >
      {render()}
    </SubmissionContext.Provider>
  );
};
