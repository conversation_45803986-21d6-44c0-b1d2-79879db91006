import { useEffect, useContext, useState, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useNotify, Api } from '/src';
import { AppContext } from '/src/components/provider';
import PaymentForm from '../../components/paymentForm';

export const PaymentPage = () => {
  const { planId } = useParams();
  const navigate = useNavigate();
  const { notify } = useNotify();
  const { userData } = useContext(AppContext);
  const [loading, setLoading] = useState(true);
  const [plan, setPlan] = useState(null);
  const [error, setError] = useState(false);
  const fetchAttempted = useRef(false);
  const redirectAttempted = useRef(false);

  // Fetch plan details
  useEffect(() => {
    const fetchPlan = async () => {
      if (fetchAttempted.current) return;

      try {
        fetchAttempted.current = true;
        setLoading(true);
        const response = await Api.get(`plans/single/${planId}`);
        setPlan(response.data);
      } catch (error) {
        console.error('Error fetching plan:', error);
        setError(true);
        notify.error(error?.response?.data?.message || 'Failed to load plan details');
      } finally {
        setLoading(false);
      }
    };

    if (planId) {
      fetchPlan();
    }
  }, [planId, notify]);

  // Redirect if not logged in - with safeguard against infinite loops
  useEffect(() => {
    if (!userData?.access_token && !redirectAttempted.current) {
      redirectAttempted.current = true;
      notify.error('You must be logged in to make a payment');
      navigate('/auth/login', {
        state: { redirectAfter: `/payment/${planId}` },
      });
    }
  }, [userData, navigate, planId, notify]);

  // Redirect on error
  useEffect(() => {
    if (error && !loading) {
      navigate('/pricing');
    }
  }, [error, loading, navigate]);

  const formatKey = (key) => {
    return key
      .replace(/([A-Z]+)([A-Z][a-z])/g, '$1 $2')
      .replace(/([a-z\d])([A-Z])/g, '$1 $2')
      .replace(/^./, (str) => str.toUpperCase());
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (!plan) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <h1 className="text-2xl font-bold mb-4">Plan not found</h1>
        <button onClick={() => navigate('/pricing')} className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700">
          Back to Plans
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Complete Your Payment</h1>

      {/* Plan summary */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
        <h2 className="text-2xl font-semibold mb-4">{plan?.name}</h2>
        <div className="text-3xl font-bold mb-6">
          ${plan?.price} <span className="text-sm font-normal">/month</span>
        </div>

        {/* Plan features */}
        {plan?.features && (
          <div className="mt-4 space-y-2">
            <h3 className="text-lg font-medium mb-2">Plan Features:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {Object.entries(plan.features)
                .filter(([key, value]) => {
                  const isActive = typeof value === 'number' ? value > 0 : !!value;
                  return isActive;
                })
                .map(([key, value]) => {
                  const formattedKey = formatKey(key);
                  return (
                    <div key={key} className="flex items-center gap-2">
                      <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      <p className="text-gray-700 dark:text-gray-300">
                        {typeof value === 'number' && value > 0 && value} {formattedKey}
                        {typeof value === 'string' && `: ${value.charAt(0).toUpperCase() + value.slice(1)}`}
                      </p>
                    </div>
                  );
                })}
            </div>
          </div>
        )}
      </div>

      {/* Payment form */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <PaymentForm
          amount={plan?.price * 100}
          description={`Subscription for ${plan?.name} plan`}
          planId={planId}
          organizationId={userData?.organizationId}
        />
      </div>
    </div>
  );
};
