// React
import { memo, useContext } from 'react';

// Core
import { Enums } from '/src';

// Context
import { SubmissionAiContext } from '/src/pages/quiz-ai';

// Hooks
import { useTypingEffect } from '../../../hooks/use-typing-effect';

const Message = ({ message, isLastMessage }) => {
  // Context
  const { notify, loading, setLoading, submissionAi, setSubmissionAi, handleGetSubmissionAi, aiIsTyping, setAiIsTyping, isSpeaking, setIsSpeaking } =
    useContext(SubmissionAiContext);

  const selectedAiModel = Enums.AiAvatarModel?.find((singleEnum) => singleEnum.value === submissionAi?.interview?.avatarName);

  const displayedText = isLastMessage && !loading ? useTypingEffect(message.text) : message.text;

  return (
    <div
      className={`flex items-start gap-3 ${
        message.type === 1 ? '' : 'ml-auto max-w-64 my-5 border bg-[#f3f5f9] rounded-xl rounded-br-none py-2  px-4'
      }`}
    >
      {message.type === 1 && (
        <span>
          <img src={`/images/models/${selectedAiModel?.iconPath}`} className="size-8 min-w-8 object-cover rounded-full" alt={selectedAiModel?.name} />
        </span>
      )}
      <div className="grid h-full">{displayedText}</div>
    </div>
  );
};

export default memo(Message);
