import React, { memo } from 'react';
import { Icon } from '/src';

const MainInterviewButton = ({ name, onClick, icon, className, iconWidth, disabled, customClass }) => {
  const MainButtonStyle = ` ${customClass} transition-all h-14 w-14 flex justify-center items-center rounded-full !bg-[#212223]' + ' ' + className`;

  return (
    <div
      className={`dark:text-white text-black font-semibold  flex flex-col  items-center justify-center  ${
        disabled ? '!cursor-not-allowed  opacity-20 ' : ''
      }  `}
    >
      <button
        onClick={onClick}
        disabled={disabled}
        className={`${MainButtonStyle}  ${disabled ? '!cursor-not-allowed' : 'cursor-pointer hover:bg-white/5'}`}
      >
        <Icon icon={icon} width={iconWidth} />
      </button>
      <span className="text-sm font-medium mt-1 block">{name}</span>
    </div>
  );
};
export default memo(MainInterviewButton);
