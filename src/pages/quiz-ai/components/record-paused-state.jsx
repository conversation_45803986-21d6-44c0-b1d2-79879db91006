// React
import { memo, useContext } from 'react';
import WavesurferPlayer from '@wavesurfer/react';

// Components
import MainInterviewButton from './interview-button';

// Context
import { SubmissionAiContext } from '/src/pages/quiz-ai';

const IsPausedState = ({ isPlaying, onPlayPause, audioURL, onReady, audioRef, recordingTime, setIsPlaying, isPaused }) => {
  // Context
  const { notify, loading, setLoading, submissionAi, setSubmissionAi, handleGetSubmissionAi } = useContext(SubmissionAiContext);

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div
      className={`isPlaying flex w-60 bg-[#212223] rounded-full justify-between py-2 items-center mx-3 px-4 gap-4 ${isPaused ? 'flex' : 'hidden'}`}
    >
      {audioURL && (
        <>
          <MainInterviewButton
            icon={isPlaying ? 'solar:pause-bold' : 'solar:play-bold'}
            className={`!size-8 !mt-1`}
            onClick={onPlayPause}
            width={15}
            disabled={loading}
          />
          <div className="flex gap-2 h-fit w-[100px]">
            <audio ref={audioRef} src={audioURL}></audio>
            <WavesurferPlayer
              height={40}
              width={100}
              waveColor="#fff"
              url={audioURL}
              onReady={onReady}
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
            />
          </div>
        </>
      )}

      <div className={`text-white`}>{formatTime(recordingTime)}</div>
    </div>
  );
};

export default memo(IsPausedState);
