import { useContext } from 'react';
import { SubmissionAiContext } from '/src/pages/quiz-ai';
import { Icon } from '/src';

const DifficultyLevel = ({ level }) => {
  const difficultyLevels = {
    1: 'Intern',
    2: 'Fresh',
    3: 'Junior',
    4: 'Mid-level',
    5: 'Senior',
  };
  return <span className="font-semibold">{difficultyLevels[level]}</span>;
};

const WelcomeSection = ({ category, subCategory, difficulty, allowedSkips }) => (
  <div className="flex flex-col gap-2 w-full mb-4">
    <p className="text-3xl md:text-4xl font-bold text-black dark:text-white">Welcome to {category} AI interview.</p>
  </div>
);

const InterviewInfo = ({ numOfQuestions, duration, allowedSkips }) => (
  <div className="flex flex-col sm:flex-row gap-5  dark:bg-gray-700  bg-gray-100 bg-opacity-80   justify-center  py-4 px-6 rounded-md text-black  dark:text-white  w-full">
    {/* Number of Questions */}
    <div className="flex items-center gap-2 w-full md:w-auto">
      <Icon width={22} icon="ph:question-light" className="self-center text-primaryPurple " />
      <p className="text-md font-semibold ">{numOfQuestions} Questions</p>
    </div>
    {/* Interview Duration */}
    <div className="flex items-center gap-2 w-full md:w-auto">
      <Icon width={22} icon="uit:stopwatch" className="self-center text-[#3B82F6]" />
      <p className="text-md font-medium">{duration} minutes</p>
    </div>
    {/* Allowed Skips */}
    <div className="flex items-center gap-2 w-full md:w-auto">
      <Icon width={20} icon="mdi:skip-next" className="self-center text-[#34D399]" />
      <p className="text-md font-semibold">{allowedSkips} Skips</p>
    </div>
  </div>
);

export const Header = () => {
  const { submissionAi } = useContext(SubmissionAiContext);

  return (
    <div className="flex flex-col gap-2 w-full py-2 px-6  dark:bg-transparent bg-white rounded-lg ">
      {/* Welcome Section */}
      <WelcomeSection
        category={submissionAi.interview.category.categoryName}
        subCategory={submissionAi.interview.subCategory.subCategoryName}
        difficulty={submissionAi.interview.difficulty}
        allowedSkips={submissionAi.interview.allowedSkips}
      />
      {/* Interview Info Section */}
      <InterviewInfo
        numOfQuestions={submissionAi.interview.numOfQuestions}
        duration={submissionAi.interview.duration}
        allowedSkips={submissionAi.interview.allowedSkips}
      />
    </div>
  );
};
