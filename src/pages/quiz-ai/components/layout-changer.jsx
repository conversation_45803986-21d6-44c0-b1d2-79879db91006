import React, { memo, useState } from 'react';
import { Popover, Radio, Label } from 'flowbite-react';
import MainInterviewButton from './interview-button';
import { Icon } from '/src';

const radioTheme = {
  root: {
    base: 'h-4 w-4 border border-gray-100 text-primaryPurple focus:ring-2 focus:ring-[#43454b]',
  },
};

const popoverTheme = {
  content: 'z-50 overflow-hidden rounded-[7px]',
  arrow: {
    base: 'absolute h-2 w-2 z-0 rotate-45 mix-blend-lighten bg-[#43454b] border border-[#43454b]',
    placement: '-4px',
  },
};

const LayoutOption = memo(({ label, value, currentLayout, setCurrentLayout, icon, customeIcon }) => (
  <Label className="flex gap-5 items-center justify-between hover:bg-white/5 text-white transition-all duration-300 px-3 py-2.5 rounded-md">
    <div className="flex gap-4 items-center">
      <Radio onChange={() => setCurrentLayout(value)} theme={radioTheme} value={value} checked={currentLayout === value} name="layouts" />
      <span className="font-semibold">{label}</span>
    </div>
    {customeIcon ? (
      <div className="w-[31px] h-[27px] border-2 border-white rounded-md relative">
        <div className="absolute bottom-1 right-1 border-2 border-white rounded-sm size-2"></div>
      </div>
    ) : (
      <Icon icon={icon} className="text-3xl" />
    )}
  </Label>
));

export const LayoutChanger = ({ currentLayout = 'sidebar', setCurrentLayout }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Popover
      open={isOpen}
      onOpenChange={setIsOpen}
      theme={popoverTheme}
      className="bg-[#212223] border rounded-md overflow-hidden border-[#43454b] !-top-2 select-none"
      aria-labelledby="default-popover"
      content={
        <div className="w-56 text-sm text-gray-500 dark:text-gray-400">
          <div className="text-white bg-[#212223] px-4 pb-2 pt-4">
            <h3 id="default-popover" className="font-semibold text-base">
              Change layout
            </h3>
          </div>
          <div className="px-2 flex flex-col gap-2 py-3 transition-all duration-300 text-white">
            <LayoutOption
              label="Sidebar"
              value="sidebar"
              currentLayout={currentLayout}
              setCurrentLayout={setCurrentLayout}
              icon="bi:layout-sidebar-reverse"
            />
            <LayoutOption
              label="Spotlight"
              value="spotlight"
              currentLayout={currentLayout}
              setCurrentLayout={setCurrentLayout}
              customeIcon={true}
              s
            />
            <LayoutOption label="Grid" value="grid" currentLayout={currentLayout} setCurrentLayout={setCurrentLayout} icon="radix-icons:view-grid" />
          </div>
        </div>
      }
    >
      <div>
        <MainInterviewButton
          name="Layout"
          icon="tabler:layout-dashboard"
          iconWidth="25"
          className={isOpen && '!bg-white hover:!bg-white/90 text-black'}
        />
      </div>
    </Popover>
  );
};
