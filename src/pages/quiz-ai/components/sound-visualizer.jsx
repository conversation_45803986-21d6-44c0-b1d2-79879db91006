import React, { memo, useEffect } from 'react';
import { Visualizer } from 'react-sound-visualizer';
import { useScreenSize } from '/src';

const SoundVisualizer = ({ audio }) => {
  // Hook
  const screen = useScreenSize();

  return (
    <Visualizer strokeColor="#fff" audio={audio} mode={'continuous'}>
      {({ canvasRef, start }) => {
        useEffect(() => {
          if (audio) {
            if (start) {
              start();
            }
          }
        }, [audio, start]);

        return <canvas ref={canvasRef} width={screen.gt.md() ? 200 : screen.lt.xs() ? 100 : 150} height={60} />;
      }}
    </Visualizer>
  );
};

export default memo(SoundVisualizer);
