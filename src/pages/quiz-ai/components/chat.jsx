import React, { useEffect, useRef, memo } from 'react';
import { Question } from './question';
import Message from './message';
import { Icon } from '/src';

const Chat = ({ loading, chat, transcript, currentQuestion, sendManualReply, isSpeaking }) => {
  const chatEndRef = useRef(null);
  // Scroll to the bottom of the chat
  const scrollToBottom = () => {
    if (chatEndRef.current) {
      chatEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Scroll to bottom when chat or transcript changes
  useEffect(() => {
    scrollToBottom();
  }, [chat, transcript]);

  return (
    <div className="mt-6 overflow-y-auto overflow-x-hidden grid max-h-[calc(100vh-77px-97px-24px-110px)] pb-10">
      {chat.map((message, index) => (
        <Message key={index} message={message} isLastMessage={index === chat.length - 1} loading={loading} />
      ))}
      {currentQuestion && !isSpeaking && <Question question={currentQuestion} loading={loading} sendManualReply={sendManualReply} />}
      {transcript && (
        <div className="border bg-[#f3f5f9]  rounded-xl rounded-br-none py-2 px-4 ml-auto max-w-64 truncate whitespace-break-spaces my-5">
          {transcript}
        </div>
      )}
      {loading && (
        <Message
          message={{
            type: 1,
            text: <Icon className="mt-1 text-2xl text-gray-500" icon="svg-spinners:3-dots-bounce" />,
          }}
          isLastMessage={false}
        />
      )}
      <div ref={chatEndRef}></div>
    </div>
  );
};

export default memo(Chat);
