// React
import { memo, useEffect, useRef, forwardRef, useImperativeHandle, useContext } from 'react';

// Core
import { Enums } from '/src';

// Context
import { SubmissionAiContext } from '/src/pages/quiz-ai';

// Components
import { TalkingHead } from './talking-head/module';
import VirtualBackground from '/images/virtual-office.jpg';
import { VITE_APP_AI_GOOGLE_API_KEY } from 'src/configs/api';

const TalkingHeadComponent = forwardRef(({ setLoadingProgression, setIsFinished, processStatus }, ref) => {
  // Context
  const { notify, loading, setLoading, submissionAi, setSubmissionAi, handleGetSubmissionAi, aiIsTyping, setAiIsTyping, isSpeaking, setIsSpeaking } =
    useContext(SubmissionAiContext);

  const selectedAiModel = Enums.AiAvatarModel?.find((singleEnum) => singleEnum.value === submissionAi?.interview?.avatarName);
  const selectedLanguage = submissionAi?.interview?.avatarLang;

  const voiceConfig = Enums.AiAvatarModelLanguages.find((v) => v.lang === selectedLanguage && v.gender === selectedAiModel?.gender);

  const ttsVoice = voiceConfig?.voice;
  const ttsLang = voiceConfig?.langCode;

  const avatarRef = useRef(null);
  const headRef = useRef(null);
  const handleStartSpeaking = () => {
    setIsSpeaking(true);
  };
  const handleStopSpeaking = () => {
    setIsSpeaking(false);
  };

  useImperativeHandle(ref, () => ({
    async handleSpeak(transcript) {
      try {
        if (transcript && headRef.current) {
          await headRef.current.speakText(transcript);

          if (processStatus === 'Finished') {
            setIsFinished(true);
          }
        }
      } catch (error) {
        // console.error(error); // From G
      }
    },
  }));

  useEffect(() => {
    const loadAvatar = async () => {
      try {
        if (!headRef.current) {
          headRef.current = new TalkingHead(avatarRef.current, {
            ttsEndpoint: 'https://eu-texttospeech.googleapis.com/v1beta1/text:synthesize',
            ttsApikey: VITE_APP_AI_GOOGLE_API_KEY,
            lipsyncModules: ['en', 'fi', 'ar'],
            cameraView: 'head',
            modelPixelRatio: 2,
            cameraDistance: 1.5,
            cameraX: -0.1,
            cameraY: 0.4,
            cameraRotateEnabled: false,
            onStartSpeaking: handleStartSpeaking,
            onStopSpeaking: handleStopSpeaking,
          });

          await headRef.current.showAvatar(
            {
              url: `/models/${selectedAiModel?.modelPath}`,
              body: 'M',
              avatarMood: 'happy',
              ttsLang: ttsLang,
              ttsVoice: ttsVoice,
              lipsyncLang: 'en', // Use the appropriate lip-sync language
            },
            (ev) => {
              if (ev.lengthComputable) {
                const val = Math.min(100, Math.round((ev.loaded / ev.total) * 100));
                setLoadingProgression(val);
              }
            }
          );
        }
      } catch (error) {
        // console.error(error); // From G
      }
    };
    loadAvatar();
  }, []); // Empty dependency array ensures this runs only once

  return (
    <div className="relative z-0 w-full h-full mx-auto overflow-hidden text-white bg-gray-600 rounded-lg pointer-events-none md:rounded-2xl">
      <div id="stars"></div>
      <div id="stars2"></div>
      <div id="stars3"></div>
      <img src={VirtualBackground} className="absolute left-0 top-0 h-full w-full z-10 blur-[6px]" alt="" />

      <div id="avatar" ref={avatarRef} className="block w-full relative z-50 h-full test "></div>
    </div>
  );
});

export default memo(TalkingHeadComponent);
