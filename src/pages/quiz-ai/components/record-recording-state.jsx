import React, { useEffect, memo } from 'react';
import SoundVisualizer from './sound-visualizer';
const IsRecordingState = ({ isRecording, recordingTime, audio }) => {
  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };
  return (
    <div className={`${isRecording ? 'flex gap-3 justify-center mt-4 mx-3 md:mx-1 lg:mx-3 min-w-32 md:w-52 lg:w-60 ' : 'hidden'}`}>
      <div className={`dark:text-white animate-pulse text-lg`}>{formatTime(recordingTime)}</div>
      <div className="-mt-3">
        <SoundVisualizer audio={audio} />
      </div>
    </div>
  );
};

export default memo(IsRecordingState);
