import React, { useContext, useRef, useState, useEffect, useCallback, memo } from 'react';
import { useParams } from 'react-router-dom';
import { Api } from '/src';

// Components
import MainInterviewButton from '../../components/interview-button';
import { LayoutChanger } from '../../components/layout-changer';
import IsRecordingState from '../../components/record-recording-state';
import IsPausedState from '../../components/record-paused-state';

// Context
import { SubmissionAiContext } from '/src/pages/quiz-ai';
import { uploadToS3 } from '../../../../services/uploadS3';

// 1. Memoize the component to prevent unnecessary re-renders
export const StepperAiFooter = memo(
  ({
    recordingMode,
    setRecordingMode,
    textAnswer,
    setTextAnswer,
    isLoaded,
    isSpeaking,
    isFinished,
    currentLayout,
    setCurrentLayout,
    isChatVisible,
    setIsChatVisible,
    loading,
    setLoading,
    result,
    setResult,
    isRecording,
    setIsRecording,
    recordingTime,
    setRecordingTime,
    isPaused,
    setIsPaused,
    sendManualReply,
    getAvailableSkips,
    currentQuestion,
    systemLogoUrl,
    isListening,
    transcript,
    startListening,
    stopListening,
    pauseListening,
    resumeListening,
    clearTranscript,
    isConverting,
    onAnswerChanged,
  }) => {
    const { notify, submissionAi, setSubmissionAi, handleGetSubmissionAi } = useContext(SubmissionAiContext);
    const { id } = useParams();
    const audioRef = useRef(null);
    const videoRef = useRef(null);
    const mediaRecorderRef = useRef(null);
    const animationFrameRef = useRef(null);
    const canvasRef = useRef(null);

    const [mediaRecorder, setMediaRecorder] = useState(null);
    const [wavesurfer, setWavesurfer] = useState(null);
    const [stream, setStream] = useState(null);
    const [audioURL, setAudioURL] = useState(null);
    const [isUploading, setIsUploading] = useState(false);

    const [recordedChunks, setRecordedChunks] = useState([]);
    const [isPlaying, setIsPlaying] = useState(false);
    const [audioChunks, setAudioChunks] = useState([]);
    const answerIndexRef = useRef(1);

    // Create a single stable logo image reference
    const logoImageRef = useRef(null);

    // Load the logo image once on component mount
    useEffect(() => {
      // Only load if not already loaded
      if (!logoImageRef.current) {
        const img = new Image();
        img.src = '/images/Thepass-1.svg'; // Use fixed path to logo
        img.onload = () => {
          logoImageRef.current = img;
        };
        img.onerror = () => {
          console.error('Failed to load logo image');
          logoImageRef.current = null;
        };
      }
    }, []); // Empty dependency array - only run once on mount

    // 2. Use useCallback for functions to prevent them from being recreated on each render
    const handleInputChange = useCallback(
      (e) => {
        setTextAnswer(e.target.value);
        if (onAnswerChanged) {
          onAnswerChanged(e.target.value);
        }
      },
      [setTextAnswer, onAnswerChanged]
    );

    const sendFunction = useCallback(async () => {
      try {
        setLoading(true);
        const payload = {
          interviewId: id,
          userAnswerText: textAnswer,
        };
        const response = await Api.post('ai-interview/single/talk', payload);
        setResult(response.data);
        setTextAnswer('');
        handleGetSubmissionAi();
      } catch (error) {
        console.error('Error uploading audio:', error);
      } finally {
        setLoading(false);
        setRecordingMode(false);
      }
    }, [id, textAnswer, setLoading, setResult, setTextAnswer, handleGetSubmissionAi, setRecordingMode]);

    const stopVideoRecording = () => {
      // Cancel the animation frame to stop drawing
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }

      // Stop the media recorder if it's recording
      if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
        mediaRecorderRef.current.stop();
        setIsRecording(false);
        stopListening();
      }

      // Stop all tracks in the stream
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
        setStream(null);
      }
    };

    // 3. Use useRef for values that shouldn't trigger re-renders
    const currentQuestionRef = useRef(currentQuestion);

    // 4. Update the ref when the prop changes, but don't cause a re-render
    useEffect(() => {
      currentQuestionRef.current = currentQuestion;
    }, [currentQuestion]);

    // 5. Use useRef for the canvas drawing function to prevent it from causing re-renders
    const drawCanvasRef = useRef((ctx) => {
      if (!ctx) return;

      // Clear the canvas
      const canvas = canvasRef.current;
      if (!canvas) return;

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // First, draw the webcam feed as the background
      if (videoRef.current && stream) {
        try {
          // Draw the webcam video onto the canvas
          ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
        } catch (e) {
          console.error('Error drawing video to canvas:', e);
        }
      }

      // Draw the logo in the top left
      const logoSize = 100;
      const logoX = 40;
      const logoY = 40;

      // Use the ref for the logo image
      if (logoImageRef.current) {
        ctx.drawImage(logoImageRef.current, logoX, logoY, logoSize, logoSize);
      } else {
        // Fall back to the Logo component
        renderLogoToCanvas(ctx, logoX, logoY, logoSize);
      }

      // Draw the question at the bottom middle
      const question = currentQuestionRef.current;
      if (question) {
        // Set font properties
        ctx.font = 'bold 22px Arial';

        // Calculate text dimensions and positioning
        const maxWidth = canvas.width * 0.8; // 80% of canvas width
        const lineHeight = 30;
        const padding = 20;

        // Function to wrap text and return array of lines
        const wrapText = (text, maxWidth) => {
          const words = text.split(' ');
          const lines = [];
          let currentLine = words[0];

          for (let i = 1; i < words.length; i++) {
            const word = words[i];
            const width = ctx.measureText(currentLine + ' ' + word).width;

            if (width < maxWidth) {
              currentLine += ' ' + word;
            } else {
              lines.push(currentLine);
              currentLine = word;
            }
          }

          lines.push(currentLine);
          return lines;
        };

        // Wrap the text
        const lines = wrapText(question, maxWidth);

        // Calculate background dimensions
        const totalTextHeight = lines.length * lineHeight;
        const backgroundWidth = maxWidth + padding * 2;
        const backgroundHeight = totalTextHeight + padding * 2;
        const backgroundX = (canvas.width - backgroundWidth) / 2;
        const backgroundY = canvas.height - backgroundHeight - 20; // 20px from bottom

        // Draw background with rounded corners
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.beginPath();

        // Use roundRect if available, otherwise use a fallback
        if (ctx.roundRect) {
          ctx.roundRect(backgroundX, backgroundY, backgroundWidth, backgroundHeight, 10);
        } else {
          // Fallback for browsers that don't support roundRect
          const radius = 10;
          ctx.moveTo(backgroundX + radius, backgroundY);
          ctx.lineTo(backgroundX + backgroundWidth - radius, backgroundY);
          ctx.quadraticCurveTo(backgroundX + backgroundWidth, backgroundY, backgroundX + backgroundWidth, backgroundY + radius);
          ctx.lineTo(backgroundX + backgroundWidth, backgroundY + backgroundHeight - radius);
          ctx.quadraticCurveTo(
            backgroundX + backgroundWidth,
            backgroundY + backgroundHeight,
            backgroundX + backgroundWidth - radius,
            backgroundY + backgroundHeight
          );
          ctx.lineTo(backgroundX + radius, backgroundY + backgroundHeight);
          ctx.quadraticCurveTo(backgroundX, backgroundY + backgroundHeight, backgroundX, backgroundY + backgroundHeight - radius);
          ctx.lineTo(backgroundX, backgroundY + radius);
          ctx.quadraticCurveTo(backgroundX, backgroundY, backgroundX + radius, backgroundY);
        }

        ctx.fill();

        // Draw text
        ctx.fillStyle = 'white';
        ctx.textAlign = 'center';

        lines.forEach((line, index) => {
          const y = backgroundY + padding + index * lineHeight;
          ctx.fillText(line, canvas.width / 2, y + 20);
        });
      }
    });

    // 6. Update the startRecording function to use the drawCanvasRef
    const startRecording = useCallback(async () => {
      setRecordingMode(true);
      setIsRecording(true);

      try {
        // Request camera and microphone access
        const mediaStream = await navigator.mediaDevices.getUserMedia({
          audio: true,
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            facingMode: 'user',
          },
        });

        setStream(mediaStream);

        // Create a new video element and set it up
        if (videoRef.current) {
          videoRef.current.srcObject = mediaStream;

          // Wait for video to be ready before proceeding
          await new Promise((resolve) => {
            videoRef.current.onloadedmetadata = () => {
              videoRef.current
                .play()
                .then(resolve)
                .catch((e) => {
                  console.error('Error playing video:', e);
                  resolve(); // Resolve anyway to continue
                });
            };
          });
        }

        // Set up canvas with correct dimensions
        const canvas = canvasRef.current;
        if (!canvas) {
          console.error('Canvas ref is null');
          return;
        }

        // Set canvas size to match video dimensions for better quality
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;

        const ctx = canvas.getContext('2d');
        if (!ctx) {
          console.error('Could not get canvas context');
          return;
        }

        // Create a stream from the canvas
        const canvasStream = canvas.captureStream(30); // 30 FPS

        // Add audio track from original stream to canvas stream
        mediaStream.getAudioTracks().forEach((track) => {
          canvasStream.addTrack(track);
        });

        // Create recorder from canvas stream
        const recorder = new MediaRecorder(canvasStream, {
          mimeType: 'video/webm;codecs=vp9,opus',
        });

        setMediaRecorder(recorder);
        mediaRecorderRef.current = recorder;
        setRecordedChunks([]);

        // Set up animation frame for drawing
        const animate = () => {
          if (videoRef.current && videoRef.current.readyState >= 2) {
            try {
              // Clear the canvas first
              ctx.clearRect(0, 0, canvas.width, canvas.height);

              // Draw video frame to canvas - this is the critical part
              ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);

              // Draw logo and other elements on top
              if (logoImageRef.current) {
                const logoSize = 100;
                ctx.drawImage(logoImageRef.current, 60, 60, logoSize, logoSize);
              }

              // Draw question text if available
              if (currentQuestionRef.current) {
                drawQuestionText(ctx, currentQuestionRef.current, canvas);
              }
            } catch (e) {
              console.error('Error drawing to canvas:', e);
            }
          } else {
            // If video isn't ready, draw a subtle waiting indicator
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Add text explaining the issue
            ctx.fillStyle = 'white';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Initializing camera...', canvas.width / 2, canvas.height / 2);
          }

          // Continue animation loop
          animationFrameRef.current = requestAnimationFrame(animate);
        };

        // Start animation loop
        animationFrameRef.current = requestAnimationFrame(animate);

        // Set up recorder events
        recorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            setRecordedChunks((prev) => [...prev, event.data]);
          }
        };

        // Start recording
        recorder.start(100);
        setIsPaused(false);
        setRecordingTime(0);
        setAudioChunks([]);
        startListening();
      } catch (error) {
        console.error('Error starting video recording:', error);
        setIsRecording(false);
        setRecordingMode(false);
        notify.error('Failed to access camera. Please check your permissions.');
      }
    }, [
      setRecordingMode,
      setIsRecording,
      setStream,
      setMediaRecorder,
      setRecordedChunks,
      setIsPaused,
      setRecordingTime,
      setAudioChunks,
      startListening,
      notify,
    ]);

    const toggleRecording = () => {
      startRecording();
      startStop();
      if (isRecording) {
        setIsRecording(false);
      } else {
        setIsRecording(true);
      }
    };

    const startStop = () => {
      if (isListening) {
        stopListening();
      } else {
        startListening();
      }
    };

    const deleteRecording = () => {
      setIsRecording(false);
      deletTranscript();
      setAudioChunks([]);
      setAudioURL(null);
      setRecordedChunks([]);
      setRecordingTime(0);
      stopListening();
      setRecordingMode(false);
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
        setStream(null);
      }
    };

    const handleRecordButtomEvenet = () => {
      if (recordingMode) {
        sendRecording();
      } else if (textAnswer) {
        sendFunction();
      } else {
        startRecording();
      }
    };

    const onPlayPause = () => {
      if (wavesurfer) {
        wavesurfer.playPause();
      }
    };

    const onReady = (ws) => {
      // use any
      setWavesurfer(ws);
      setIsPlaying(false);
    };

    const pauseRecording = () => {
      if (mediaRecorderRef.current) {
        mediaRecorderRef.current.stop();
        setIsPaused(true);
        setIsRecording(false);
        pauseListening();
      }
    };

    const resumeRecording = async () => {
      resumeListening();
      if (stream) {
        const newMediaRecorder = new MediaRecorder(stream);
        setMediaRecorder(newMediaRecorder);
        mediaRecorderRef.current = newMediaRecorder;

        newMediaRecorder.ondataavailable = (e) => {
          if (e.data.size > 0) {
            setAudioChunks((prev) => [...prev, e.data]);
            const blob = new Blob([...audioChunks, e.data], { type: 'audio/wav' });
            const url = URL.createObjectURL(blob);
            setAudioURL(url);
          }
        };

        newMediaRecorder.start();
        setIsRecording(true);
        setIsPaused(false);
      } else {
        await startRecording();
      }
    };
    
    const sendRecording = async () => {
      try {
        setLoading(true);
        setIsUploading(true); // Keep this to indicate a background process is starting
        stopVideoRecording();

        // Make sure we have recorded chunks before creating a blob
        if (recordedChunks.length === 0) {
          throw new Error('No video data recorded');
        }

        // --- IMMEDIATE API CALL (No s3Key needed here initially) ---
        const initialPayload = {
          interviewId: id,
          userAnswerText: transcript,
        };
        // Send the text answer immediately
        const response = await Api.post('ai-interview/single/talk', initialPayload);
        setResult(response.data); // User sees the result much faster

        deleteRecording(); // Clean up local recording data
        handleGetSubmissionAi();
        if (onAnswerChanged) onAnswerChanged(transcript);
        answerIndexRef.current += 1; // Increment for the next answer

        setLoading(false); // User is no longer "loading" or blocked
        setRecordingMode(false); // User can proceed with the interview

        // --- BACKGROUND VIDEO UPLOAD (Fire-and-Forget) ---
        // Only upload video if recordInterview is true
        if (submissionAi?.interview?.recordInterview) {
          const videoBlob = new Blob(recordedChunks, { type: 'video/webm' });

          // Use an IIFE for async background upload.
          // We don't await this, so it runs in parallel.
          (async () => {
            try {
              // Pass the answerIndexRef.current *before* it was incremented for this answer.
              // Or better, capture it in a variable if you need it to correlate logs.
              // For a fire-and-forget, the exact index might not even matter much if the backend doesn't use the S3 key.
              const s3Key = await uploadToS3(videoBlob, id, answerIndexRef.current - 1);
              console.log('Video uploaded to S3 in background:', s3Key);
            } catch (uploadError) {
              console.error('Error uploading video in background:', uploadError);
              // Log this error to a monitoring service.
              // No need to notify the user if the video isn't critical for immediate backend processing.
            } finally {
              // This setIsUploading(false) will happen when the background upload finishes.
              // It could be used to clear a *subtle* background indicator.
              setIsUploading(false);
            }
          })();
        } else {
          // If recordInterview is false, no upload was initiated, so set to false immediately.
          setIsUploading(false);
        }
      } catch (error) {
        console.error('Error sending recording:', error);
        notify.error(`Error sending recording: ${error.message || 'Unknown error'}`);
        setLoading(false); // Ensure loading is off even on error
        setRecordingMode(false); // Ensure recording mode is off on error
        setIsUploading(false); // Ensure uploading state is reset
      }
    };

    const deletTranscript = () => {
      clearTranscript();
      setRecordingMode(false);
      if (onAnswerChanged) {
        onAnswerChanged('');
      }
    };

    const renderLogoToCanvas = (ctx, x, y, size) => {
      // Create a temporary div to render the Logo component
      const tempDiv = document.createElement('div');
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.top = '-9999px';
      document.body.appendChild(tempDiv);

      // Render the Logo component to the div
      const logoElement = document.createElement('img');
      logoElement.src = '/images/Thepass-1.svg'; // Use the same path as in the Logo component
      logoElement.className = 'h-16 w-16';
      tempDiv.appendChild(logoElement);

      // Wait for the image to load
      logoElement.onload = () => {
        // Draw the logo on the canvas
        ctx.drawImage(logoElement, x, y, size, size);

        // Clean up
        document.body.removeChild(tempDiv);
      };
    };

    // 7. Clean up animation frame on component unmount
    useEffect(() => {
      return () => {
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
          animationFrameRef.current = null;
        }
      };
    }, []);

    // Define drawFunction using the drawCanvasRef
    const drawFunction = useCallback((ctx) => {
      if (drawCanvasRef.current) {
        drawCanvasRef.current(ctx);
      }
    }, []);

    // Helper function to draw question text
    const drawQuestionText = (ctx, question, canvas) => {
      // Set font properties
      ctx.font = 'bold 22px Arial';

      // Calculate text dimensions and positioning
      const maxWidth = canvas.width * 0.8; // 80% of canvas width
      const lineHeight = 30;
      const padding = 20;

      // Function to wrap text and return array of lines
      const wrapText = (text, maxWidth) => {
        const words = text.split(' ');
        const lines = [];
        let currentLine = words[0] || '';

        for (let i = 1; i < words.length; i++) {
          const word = words[i];
          const width = ctx.measureText(currentLine + ' ' + word).width;

          if (width < maxWidth) {
            currentLine += ' ' + word;
          } else {
            lines.push(currentLine);
            currentLine = word;
          }
        }

        lines.push(currentLine);
        return lines;
      };

      // Wrap the text
      const lines = wrapText(question, maxWidth);

      // Calculate background dimensions
      const totalTextHeight = lines.length * lineHeight;
      const backgroundWidth = maxWidth + padding * 2;
      const backgroundHeight = totalTextHeight + padding * 2;
      const backgroundX = (canvas.width - backgroundWidth) / 2;
      const backgroundY = canvas.height - backgroundHeight - 20; // 20px from bottom

      // Draw background with rounded corners
      ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
      ctx.beginPath();

      // Use roundRect if available, otherwise use a fallback
      if (ctx.roundRect) {
        ctx.roundRect(backgroundX, backgroundY, backgroundWidth, backgroundHeight, 10);
      } else {
        // Fallback for browsers that don't support roundRect
        const radius = 10;
        ctx.moveTo(backgroundX + radius, backgroundY);
        ctx.lineTo(backgroundX + backgroundWidth - radius, backgroundY);
        ctx.quadraticCurveTo(backgroundX + backgroundWidth, backgroundY, backgroundX + backgroundWidth, backgroundY + radius);
        ctx.lineTo(backgroundX + backgroundWidth, backgroundY + backgroundHeight - radius);
        ctx.quadraticCurveTo(
          backgroundX + backgroundWidth,
          backgroundY + backgroundHeight,
          backgroundX + backgroundWidth - radius,
          backgroundY + backgroundHeight
        );
        ctx.lineTo(backgroundX + radius, backgroundY + backgroundHeight);
        ctx.quadraticCurveTo(backgroundX, backgroundY + backgroundHeight, backgroundX, backgroundY + backgroundHeight - radius);
        ctx.lineTo(backgroundX, backgroundY + radius);
        ctx.quadraticCurveTo(backgroundX, backgroundY, backgroundX + radius, backgroundY);
      }

      ctx.fill();

      // Draw text
      ctx.fillStyle = 'white';
      ctx.textAlign = 'center';

      lines.forEach((line, index) => {
        const y = backgroundY + padding + index * lineHeight;
        ctx.fillText(line, canvas.width / 2, y + 20);
      });
    };

    return (
      <div className="py-4 w-full px-4 flex flex-col md:flex-row justify-center gap-8 md:gap-4 items-center md:items-start border-t dark:border-[#4c4e56d4] dark:border-gray-700 pt-4 mt-3 overflow-y-auto">
        {/* Video element for camera feed - hidden but accessible */}
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted
          className="hidden"
          style={{
            position: 'absolute',
            left: '-9999px',
            top: '-9999px',
            width: '640px',
            height: '480px',
          }}
        />

        {/* Canvas for recording with overlays */}
        <canvas
          ref={canvasRef}
          className="absolute top-0 left-0 z-50 w-full h-full"
          style={{
            opacity: 0, // Make it invisible
            pointerEvents: 'none',
            display: isRecording ? 'block' : 'none', // Only create when recording
          }}
        />

        {recordingMode ? (
          <div className="rounded-lg md:rounded-2xl h-14 flex justify-center items-start px-3">
            <MainInterviewButton
              disabled={isConverting || loading}
              name="Delete"
              onClick={deleteRecording}
              icon="octicon:trash-16"
              iconWidth="20"
              className="!bg-[#e33939]"
            />
            <IsRecordingState isRecording={isRecording} recordingTime={recordingTime} audio={stream} />
            <IsPausedState
              isPaused={isPaused}
              isPlaying={isPlaying}
              onPlayPause={onPlayPause}
              audioURL={audioURL}
              onReady={onReady}
              audioRef={audioRef}
              recordingTime={recordingTime}
              setIsPlaying={setIsPlaying}
            />
            {isRecording && !isPaused && (
              <MainInterviewButton name="Pause" onClick={pauseRecording} icon="solar:pause-bold" iconWidth="15" disabled={loading} />
            )}
            {isPaused && audioURL && (
              <MainInterviewButton name="Resume" onClick={resumeRecording} icon="solar:microphone-2-outline" iconWidth="23" disabled={loading} />
            )}
          </div>
        ) : (
          <input
            type="text"
            value={textAnswer}
            onChange={handleInputChange}
            className="col-span-7 w-full max-w-[600px] border-0 dark:text-white text-[#667085] rounded-full h-14 dark:bg-[#363839] shadow-sm bg-gray-200 bg-opacity-60  px-4 md:px-7 placeholder:dark:text-white  text-sm focus:outline-none focus:ring-0"
            placeholder="Send a message"
          />
        )}
        {recordingMode || textAnswer ? <div className="hidden md:block mx-4 h-10 w-px mt-3 dark:bg-[#212223] bg-[#d4d4d4] "></div> : null}
        <div className="flex gap-3 sm:gap-8 items-center">
          <div>
            <MainInterviewButton
              name={recordingMode || textAnswer ? 'Send' : 'Talk'}
              onClick={handleRecordButtomEvenet}
              disabled={!isLoaded.current || loading || result?.processStatus === 'Finished' || isSpeaking || isConverting}
              icon={recordingMode || textAnswer ? 'iconoir:send' : 'solar:microphone-2-outline'}
              iconWidth="25"
              customClass="dark:bg-primaryPurple bg-primaryPurple bg-opacity-80  text-white hover:text-black dark:text-white hover:bg-opacity-50 dark:bg-primaryPurple dark:hover:bg-opacity-60"
            />
          </div>

          <MainInterviewButton
            name={isChatVisible ? 'Hide text' : 'Show text'}
            onClick={() => setIsChatVisible((prev) => !prev)}
            icon="solar:chat-dots-outline"
            iconWidth="25"
            className={isChatVisible ? '!bg-white hover:!bg-white/90 text-black' : ''}
            customClass={'dark:hover:border dark:hover:border-black border hover:bg-gray-200/60 dark:hover:bg-[#374151]/60 dark:hover:border-none '}
          />

          {getAvailableSkips()?.showAvailableSkips && (
            <MainInterviewButton
              disabled={
                isSpeaking ||
                isFinished ||
                !isLoaded.current ||
                loading ||
                recordingMode ||
                isConverting ||
                getAvailableSkips()?.availableSkips === 0 ||
                getAvailableSkips()?.isIntroduceYourself
              }
              name="Skip"
              onClick={() => sendManualReply('skip')}
              icon="solar:skip-next-linear"
              iconWidth="20"
              customClass={'dark:hover:border dark:hover:border-black border hover:bg-gray-200/60 dark:hover:bg-[#374151]/60 dark:hover:border-none'}
            />
          )}
        </div>
      </div>
    );
  }
);

// Create a separate component for the canvas
const RecordingCanvas = memo(({ isRecording, canvasRef, drawFunction }) => {
  // Set up the animation frame
  useEffect(() => {
    if (isRecording && canvasRef.current) {
      const ctx = canvasRef.current.getContext('2d');
      if (!ctx) return;

      const animate = () => {
        drawFunction(ctx);
        return requestAnimationFrame(animate);
      };

      const animationId = requestAnimationFrame(animate);

      return () => {
        cancelAnimationFrame(animationId);
      };
    }
  }, [isRecording, canvasRef, drawFunction]);

  return (
    <canvas
      ref={canvasRef}
      className="absolute top-0 left-0 z-50 w-full h-full"
      style={{
        opacity: 0, // Make it invisible
        pointerEvents: 'none',
        display: isRecording ? 'block' : 'none', // Only create when recording
      }}
    />
  );
});
