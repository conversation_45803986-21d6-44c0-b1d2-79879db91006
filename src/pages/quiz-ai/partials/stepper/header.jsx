import { useDarkMode } from '/src/hooks/dark-mode';
import { Icon, Logo } from '/src';

// Helper function to get the current question index
const getCurrentQuestionIndex = (result, start) => {
  if (!result) return start?.currentQuestionIndex;
  return result.currentQuestionIndex;
};

export const StepperAiHeader = ({ result, start, getAvailableSkips, showThemeIcon }) => {
  // Hook
  const { switchDarkMode, isDark } = useDarkMode();

  const currentQuestionIndex = getCurrentQuestionIndex(result, start);

  return (
    <nav className="dark:bg-[#171717] mb-1  px-4 border-b pt-[14px] pb-[10px]  dark:border-[#4c4e56d4]   dark:border-gray-700 h-[77px] flex items-center justify-between bg-lightgraybg">
      <div className="flex md:mx-5 justify-between items-center  ">
        <div className="flex gap-4 items-center h-fit w-full ">
          <div className="flex items-center cursor-pointer">
            <Logo className="h-8 md:h-11" />
          </div>

          <div className="flex flex-col justify-center gap-1 pl-4">
            <div className="flex gap-2 text-lg md:text-xl font-medium dark:text-white">
              {!start && !result ? (
                <span className="md:w-40 bg-[#212223] rounded-full h-6 animate-pulse"></span>
              ) : (
                <>
                  <p>Question</p>
                  <p>
                    {currentQuestionIndex}/{start?.totalQuestions}
                  </p>
                </>
              )}
            </div>

            {getAvailableSkips()?.showAvailableSkips && (
              <div className="flex gap-2 text-lg md:text-xl font-medium dark:text-white">
                {!start && !result ? (
                  <span className="w-52 bg-[#212223] rounded-full h-4 animate-pulse block mt-2"></span>
                ) : (
                  <p className="text-sm text-zinc-400">{getAvailableSkips()?.availableSkips} Questions can be skipped</p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* <!-- Dark --> */}
      {showThemeIcon && (
        <button
          type="button"
          className="text-gray-500 mx-8 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 flex items-center justify-center"
          onClick={switchDarkMode}
        >
          <Icon icon={isDark ? 'ic:outline-light-mode' : 'ic:outline-dark-mode'} width="20px" />
        </button>
      )}
    </nav>
  );
};
