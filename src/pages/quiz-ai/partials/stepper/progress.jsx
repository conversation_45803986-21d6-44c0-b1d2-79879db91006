// Flowbite
import { Progress } from 'flowbite-react';
import { Logo } from '/src';

export const StepperAiProgress = ({ isLoaded, loadingProgression }) => {
  // Themes
  const customTheme = {
    bar: 'space-x-2 rounded-full text-center font-medium leading-none text-cyan-300 text-white',
  };

  return (
    <div
      className={`flex flex-col justify-center opacity-100 items-center bg-[#171717]  min-h-[550px] py-[100px] absolute top-0 left-0 w-full h-full  
        ${isLoaded.current ? '-z-10 !opacity-0' : 'z-[51]'}`}
    >
      <div>
        <Logo className="h-[250px] animate-pulse" icon/>
      </div>
      <div className="w-[370px] self-center mt-10">
        <Progress theme={customTheme} size="lg" color="purple" labelProgress progressLabelPosition="inside" progress={loadingProgression} />
      </div>
    </div>
  );
};
