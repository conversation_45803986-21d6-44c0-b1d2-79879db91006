// React
import { useState, useEffect, useRef, useContext } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

// Context
import { SubmissionAiContext } from '/src/pages/quiz-ai';
import { Enums } from '/src';

// UI
import { Api, useSpeech } from '/src';

// Components
import { StepperAiHeader } from './header';
import { StepperAiQuestion } from './question';
import { StepperAiFooter } from './footer';
import { StepperAiProgress } from './progress';

export const SubmissionAiStepper = () => {
  // Hooks
  const { id } = useParams();
  const navigate = useNavigate();

  // Context
  const { notify, loading, setLoading, submissionAi, setSubmissionAi, handleGetSubmissionAi, aiIsTyping, setAiIsTyping, isSpeaking, setIsSpeaking } =
    useContext(SubmissionAiContext);

  // Ref
  const isLoaded = useRef(false);
  const hasStarted = useRef(false);
  const modelRef = useRef();
  const intervalRef = useRef(null);

  // State
  const [isRecording, setIsRecording] = useState(false);
  const [loadingProgression, setLoadingProgression] = useState(0);
  const [isFinished, setIsFinished] = useState(false);
  const [textAnswer, setTextAnswer] = useState('');
  const [start, setStart] = useState(null);
  const [result, setResult] = useState(null);
  const [currentLayout, setCurrentLayout] = useState('grid');
  const [recordingMode, setRecordingMode] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [isChatVisible, setIsChatVisible] = useState(false);

  // Define the selected lang
  const selectedLanguage = submissionAi?.interview.avatarLang;
  const voiceConfig = Enums.AiAvatarModelLanguages.find((v) => v.lang === selectedLanguage);
  const langCode = voiceConfig?.langCode;

  isLoaded.current = loadingProgression === 100 ? true : false;
  const { isListening, transcript, startListening, stopListening, pauseListening, resumeListening, clearTranscript, isConverting } = useSpeech({
    interimResults: true,
    lang: langCode,
    continuous: true,
  });

  // @TODO: To be checked twice and thrice, then delete it when implementing isSpeaking
  // if (isFinished && !isSpeaking) {
  //   handleGetSubmissionAi();
  // }

  const handleStart = async () => {
    try {
      setLoading(true);

      navigate(`/interview/${submissionAi.interview._id}`, { replace: true });
      const response = await Api.post(`ai-interview/single/start/${submissionAi.interview._id}`);
      setStart(response.data);
    } catch (error) {
      notify.error(error.response.data.message);
    } finally {
      setLoading(false);
    }
  };

  // Speak when result transcript changes
  const getTranscript = (selectedResult) => {
    if (!selectedResult) return '';

    const { currentQuestion, transcript } = selectedResult;

    if (currentQuestion?.type === 'mcq') {
      return `${transcript} ${currentQuestion.options.toString()}`;
    }

    if (currentQuestion?.type === 'true_false') {
      return `${transcript} is that true or false ?`;
    }

    return transcript || '';
  };

  const sendManualReply = async (text) => {
    setTextAnswer(() => text);
    try {
      setLoading(true);
      const payload = {
        interviewId: id,
        userAnswerText: text,
      };
      const response = await Api.post('ai-interview/single/talk', payload);
      setResult(response.data);
      setTextAnswer('');
    } catch (error) {
      console.error('Error uploading audio:', error);
    } finally {
      setLoading(false);
      setRecordingMode(false);
      handleGetSubmissionAi();
    }
  };

  const getAvailableSkips = () => {
    if (start?.availableSkips > 0)
      return {
        showAvailableSkips: true,
        availableSkips: result?.availableSkips >= 0 ? result?.availableSkips : start?.availableSkips,
        isIntroduceYourself: result ? result?.chat.length === 1 : start?.chat?.length === 1,
      };
  };

  useEffect(() => {
    const selectedResult = result || start;
    const transcript = getTranscript(selectedResult);

    if (isLoaded.current && transcript) {
      modelRef?.current?.handleSpeak(transcript);
    }
  }, [result?.transcript, start?.transcript, isLoaded.current, modelRef]);

  useEffect(() => {
    if (!hasStarted.current && isLoaded.current) {
      handleStart();
      hasStarted.current = true;
    }
  }, [isLoaded.current]);

  useEffect(() => {
    if (isRecording && !isPaused) {
      intervalRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
    }
  }, [isRecording, isPaused]);

  return (
    <div className="grid h-full grid-rows-[auto_1fr_auto] ">
      {/* Progress */}
      <StepperAiProgress isLoaded={isLoaded} loadingProgression={loadingProgression} />
      {/* Header */}
      <StepperAiHeader start={start} result={result} showThemeIcon={true} getAvailableSkips={getAvailableSkips} />
      {/* Question */}
      <StepperAiQuestion
        isLoaded={isLoaded}
        currentLayout={currentLayout}
        setCurrentLayout={setCurrentLayout}
        isFinished={isFinished}
        setIsFinished={setIsFinished}
        result={result}
        setResult={setResult}
        setLoadingProgression={setLoadingProgression}
        modelRef={modelRef}
        isRecording={isRecording}
        isChatVisible={isChatVisible}
        start={start}
        textAnswer={textAnswer}
        setTextAnswer={setTextAnswer}
        recordingMode={recordingMode}
        setRecordingMode={setRecordingMode}
        sendManualReply={sendManualReply}
        transcript={transcript}
      />

      <StepperAiFooter
        recordingMode={recordingMode}
        setRecordingMode={setRecordingMode}
        textAnswer={textAnswer}
        setTextAnswer={setTextAnswer}
        isLoaded={isLoaded}
        isSpeaking={isSpeaking}
        isFinished={isFinished}
        currentLayout={currentLayout}
        setCurrentLayout={setCurrentLayout}
        isChatVisible={isChatVisible}
        setIsChatVisible={setIsChatVisible}
        loading={loading}
        setLoading={setLoading}
        result={result}
        setResult={setResult}
        isRecording={isRecording}
        setIsRecording={setIsRecording}
        recordingTime={recordingTime}
        setRecordingTime={setRecordingTime}
        sendManualReply={sendManualReply}
        isPaused={isPaused}
        setIsPaused={setIsPaused}
        start={start}
        getAvailableSkips={getAvailableSkips}
        isListening={isListening}
        transcript={transcript}
        startListening={startListening}
        stopListening={stopListening}
        pauseListening={pauseListening}
        resumeListening={resumeListening}
        clearTranscript={clearTranscript}
        isConverting={isConverting}
        currentQuestion={start?.transcript}
      />
    </div>
  );
};
