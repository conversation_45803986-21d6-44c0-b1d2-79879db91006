// React
import { useContext } from 'react';

// Core
import { Enums } from '/src';

// Context
import { SubmissionAiContext } from '/src/pages/quiz-ai';

// Components
import TalkingHeadComponent from '../../components/3d-model/the-model';
import Camera from '../../components/camera';
import Chat from '../../components/chat';

import { motion } from 'framer-motion';

export const StepperAiQuestion = ({
  isLoaded,
  currentLayout,
  isFinished,
  setIsFinished,
  result,
  setResult,
  setLoadingProgression,
  modelRef,
  isRecording,
  isChatVisible,
  start,
  textAnswer,
  setTextAnswer,
  recordingMode,
  setRecordingMode,
  sendManualReply,
  transcript,
}) => {
  // Themes
  const layoutsStyles = {
    sidebar: {
      container: 'grid grid-cols-12 gap-3 w-full h-full',
      model: 'w-full h-full col-span-8 md:col-span-9 lg:col-span-10  relative',
      camera: 'aspect-video w-full col-span-4 md:col-span-3 lg:col-span-2',
    },
    spotlight: {
      container: 'grid w-full h-full grid-cols-1  relative',
      model: 'relative',
      camera: 'absolute bottom-3 right-4 md:bottom-5 md:right-5 w-[160px]  md:w-[300px] aspect-video',
    },
    grid: {
      container: 'grid grid-cols-1 sm:grid-cols-2 gap-3 relative w-full',
      model: ' h-[30dvh] md:h-full aspect-video w-full',
      camera: 'h-[30dvh] md:h-full aspect-video w-full relative',
    },
  };

  // Context
  const { notify, loading, setLoading, submissionAi, setSubmissionAi, handleGetSubmissionAi, aiIsTyping, setAiIsTyping, isSpeaking, setIsSpeaking } =
    useContext(SubmissionAiContext);

  const selectedAiModel = Enums.AiAvatarModel?.find((singleEnum) => singleEnum.value === submissionAi?.interview?.avatarName);

  // State
  const chat = result?.chat || start?.chat || [];

  return (
    <div className="grid grid-cols-1 gap-10 !overflow-hidden mt-3">
      {/* Main view */}
      <motion.div layout transition={{ duration: 0.2 }} className={`${isLoaded.current ? 'flex' : 'hidden'} relative h-full flex items-center`}>
        <motion.div layout transition={{ duration: 0.2 }} className={layoutsStyles[currentLayout].container}>
          <motion.div layout transition={{ duration: 0.2 }} className={layoutsStyles[currentLayout].model}>
            <TalkingHeadComponent
              setIsFinished={setIsFinished}
              isSpeaking={isSpeaking}
              transcript={result?.transcript}
              setLoadingProgression={setLoadingProgression}
              processStatus={result?.processStatus}
              ref={modelRef}
            />
            <div className="flex items-center absolute bottom-1 left-1 rounded-md bg-black/50  text-[#f9f9f9]  py-1 px-3 shadow-lg">
              <div className="flex flex-col">
                <span className="font-semibold text-sm md:text-base capitalize">{selectedAiModel?.value}</span>
                <span className="text-xs text-gray-300 md:text-sm">Technical Expert</span>
              </div>
            </div>
          </motion.div>
          <motion.div layout transition={{ duration: 0.2 }} className={layoutsStyles[currentLayout].camera}>
            <Camera isSpeaking={isRecording} />
            {false ? (
              <div className="absolute left-4 bottom-4 bg-black/15 rounded-full py-2 px-4 text-base text-white max-w-96">
                {/* @TODO: Write the applicant name here and edit the if condition */}
              </div>
            ) : null}
          </motion.div>
        </motion.div>
        <div
          className={`w-full h-full transition-all duration-300 z-0 top-0 right-0 absolute lg:relative ${
            isChatVisible ? `max-w-[358px] p-4 ml-3` : `max-w-0 overflow-hidden`
          }`}
        >
          <div
            className={`h-full transition-all overflow-hidden bg-white border border-gray-300 p-4 rounded-lg md:rounded-2xl absolute top-0 right-0 w-[80%] md:w-[358px] duration-300 ${
              isChatVisible ? `translate-x-0` : `translate-x-[150%] md:translate-x-[120%]`
            }`}
          >
            {/* chat */}
            <h2 className="text-xl font-semibold">Chat</h2>
            <Chat
              chat={chat}
              loading={loading}
              transcript={transcript || textAnswer}
              isSpeaking={isSpeaking}
              recordingMode={recordingMode}
              sendManualReply={sendManualReply}
              currentQuestion={result?.currentQuestion || start?.currentQuestion}
            />
          </div>
        </div>
      </motion.div>
    </div>
  );
};
