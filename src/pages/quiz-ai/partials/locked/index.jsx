import { Icon } from '/src';

export const SubmissionAiLocked = () => {
  return (
    <div className="flex min-h-full flex-1 flex-row justify-center px-6 py-12 bg-gray-50 dark:bg-gray-900">
      <div className="dark:bg-gray-800 lg:w-[80%] m-auto p-20 rounded-xl shadow-[0_-1px_4px_rgba(0,0,0,0.02),0px_2px_10px_rgba(0,0,0,0.05)]">
        <div className="flex flex-col justify-center items-center text-center dark:text-white p-4 pt-0 rounded-md ">
          <div>
            <img src="/images/locked.svg" className="w-[239px] h-[250px]" alt="" srcset="" />
          </div>
          <h1 className="font-bold text-3xl leading-7  mt-4 text-[#150050] dark:text-white">Interview is Locked</h1>
          <span className="text-[#6C757D] dark:text-gray-200 text-[24px] mb-8 leading-7  w-4/6 font-medium text-center mt-4">
            This interview has not started yet.
            <span className="font-bold"> Please check your email for start and end times.</span>
          </span>
        </div>
      </div>
    </div>
  );
};
