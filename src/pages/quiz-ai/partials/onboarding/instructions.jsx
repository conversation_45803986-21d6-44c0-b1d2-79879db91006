import { Button } from '/src';
import { FaCamera, FaMicrophone, FaQuestionCircle, FaRecordVinyl, FaLock, FaForward } from 'react-icons/fa'; // Example icon imports

export const Instructions = ({ setShowApplicantForm }) => {
  const instructionsData = [
    {
      number: 1,
      icon: <FaCamera />,
      title: 'Camera Access',
      content: 'Please allow camera access in your browser settings.',
    },
    {
      number: 2,
      icon: <FaMicrophone />,
      title: 'Microphone Access',
      content: 'Please allow microphone access in your browser settings.',
    },
    {
      number: 3,
      icon: <FaQuestionCircle />,
      title: 'Answering Questions',
      content: 'You will be presented with a series of questions to answer.',
    },
    {
      number: 4,
      icon: <FaRecordVinyl />,
      title: 'Recording Responses',
      content: 'Record your answers by pressing the record button.',
    },
    {
      number: 5,
      icon: <FaLock />,
      title: 'One Attempt Only',
      content: 'Each question allows for only one response; there are no second chances.',
    },
    {
      number: 6,
      icon: <FaForward />,
      title: 'Skipping Questions',
      subContent: [
        'You can skip a limited number of questions by saying "skip" or pressing the skip button.',
        'Exceeding the allowed number of skips will result in a score of 0 for any additional skipped questions.',
      ],
    },
  ];

  return (
    <div>
      <div className="mt-8 w-full border dark:border-[#4c4e56d4]  dark:bg-gray-700 dark:border-gray-700 p-6 rounded-xl overflow-hidden ">
        <p className="text-[22px] font-medium mb-5 text-black dark:text-white">Instructions.</p>
        <div className="flex flex-col gap-4">
          {instructionsData.map((instruction) => (
            <div key={instruction.number} className={`flex gap-3 items-start`}>
              <div className="py-1 px-2 bg-gray-100 dark:bg-gray-800 dark:text-white text-black  text-sm md:text-base rounded-full size-8 min-w-8 md:size-9 md:min-w-9 flex justify-center items-center">
                {instruction.icon} {/* Use icon here */}
              </div>
              <div className="md:flex gap-2">
                <p className="dark:text-white text-black font-medium whitespace-nowrap mt-1 md:mt-1.5 min-w-44">{instruction.title}:</p>
                <div className={`font-medium mt-1.5 ${instruction.subContent && 'w-full'}`}>
                  {instruction.content ? (
                    <p className="text-gray-500 dark:text-gray-400 ">{instruction.content}</p>
                  ) : (
                    <div className="">
                      {instruction.subContent.map((e, index) => (
                        <div key={index} className="text-gray-500 dark:text-gray-400 flex gap-2 items-start">
                          <span className="text-xl"> • </span> {/* Bullet Point */}
                          <p className="mt-1">{e}</p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-center gap-5 mt-10">
        <Button
          tertiary
          form="applicantForm"
          onClick={() => setShowApplicantForm(true)}
          className="p-1 mt-1"
          label="Next: Applicant’s Details"
          iconRight="fluent:arrow-right-20-filled"
          iconWidth="20"
        />
      </div>
    </div>
  );
};
