// React
import { useEffect, useState, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

// Context
import { SubmissionAiContext } from '/src/pages/quiz-ai';

// UI
import { Api } from '/src';

// Components
import { Header } from '../../components/header';
import { ApplicantData } from './applicant-data';
import { Instructions } from './instructions';
import { OnboardingHeader } from './onboarding-header';

export const SubmissionAiOnboarding = () => {
  // Hooks
  const { id } = useParams();
  const navigate = useNavigate();

  // Context
  const { notify, loading, setLoading, submissionAi, setSubmissionAi, handleGetSubmissionAi } = useContext(SubmissionAiContext);

  const [isShowApplicantForm, setShowApplicantForm] = useState(false);
  const [cameraAccess, setCameraAccess] = useState(null);
  const [micAccess, setMicAccess] = useState(null);

  const requestCameraAccess = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      setCameraAccess(true);
      stream.getTracks().forEach((track) => track.stop()); // Stop the tracks to free the resources
    } catch (error) {
      console.error('Error accessing camera:', error);
      setCameraAccess(false);
    }
  };

  const requestMicAccess = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setMicAccess(true);
      stream.getTracks().forEach((track) => track.stop()); // Stop the tracks to free the resources
    } catch (error) {
      console.error('Error accessing microphone:', error);
      setMicAccess(false);
    }
  };

  const handleStart = async (interviewId) => {
    // if (!cameraAccess) {
    //   return notify.error('Please allow camera access'), requestCameraAccess();
    // }
    if (!micAccess) {
      return notify.error('Please allow microphone access'), requestMicAccess();
    }
    try {
      setLoading(true);
      const response = await Api.post(`ai-interview/single/start/${interviewId}`);
      if (response.data) {
        navigate(`/interview/${interviewId}`, { replace: true });
        await handleGetSubmissionAi();
      }
    } catch (error) {
      notify.error(error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    requestCameraAccess();
    requestMicAccess();
  }, []);

  return (
    <>
      <OnboardingHeader showThemeIcon={true} />
      <div className="flex flex-col  rounded-xl items-center pt-16 pb-5 md:pt-24 min-h-screen text-white overflow-y-auto max-w-[800px] mx-auto shadow-[0px_0px_14px_0px_rgba(195,195,195,0.22)] dark:shadow-[0px_0px_14px_0px_rgba(195,195,195,0.08)] bg-white dark:bg-gray-800 p-6">
        <Header />
        {isShowApplicantForm ? (
          <ApplicantData handleStart={handleStart} setShowApplicantForm={setShowApplicantForm} />
        ) : (
          <Instructions setShowApplicantForm={setShowApplicantForm} />
        )}
      </div>
    </>
  );
};
