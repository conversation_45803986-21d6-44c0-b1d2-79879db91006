export const UnsupportedBrowser = () => {
  return (
    <div className="h-[700px] flex justify-center items-center relative">
      <svg className="absolute" width="100%" height="100%" viewBox="0 0 1420 702" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M1342 330C1342 330 833.74 256.477 636.5 -201.5C306.809 -451.824 289 -507.5 233 -201.5C341.642 282.67 -178.106 -44.1609 -95.7706 272.248C-2.19022 631.871 608.526 638.602 712.5 880.5C869.92 1077.02 962.535 1069.87 962.535 1069.87C1194.49 950.521 1161.67 888.871 1349.59 862.311C1537.5 835.752 1605.68 389.834 1342 330Z"
          fill="#9061F9"
          fill-opacity="0.08"
        />
      </svg>
      <div className="flex flex-col gap-3">
        <img src="/public/images/Lock.png" width={250} className="self-center" />
        <p className="text-lg font-medium self-center">Browser Not Supported</p>
        <p className="text-[#848B98] text-lg font-medium">Please use a different browser for the best experience.</p>
      </div>
    </div>
  );
};
