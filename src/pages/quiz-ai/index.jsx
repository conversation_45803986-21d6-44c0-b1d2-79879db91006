// React
import { useEffect, useState, createContext } from 'react';
import { useParams } from 'react-router-dom';
// UI
import { useNotify, Api, Enums, useEventListener, useDarkMode } from '/src';

// Components
import { SubmissionAiLoading } from './partials/loading/index';
import { SubmissionAiOnboarding } from './partials/onboarding';
import { SubmissionAiStepper } from './partials/stepper';
import { SubmissionAiFinish } from './partials/finish/index';
import { SubmissionAiLocked } from './partials/locked';
import { SubmissionAiExpired } from './partials/expired';
import { MobileAiDevice } from './partials/mobileDevice';
import { UnsupportedBrowser } from './partials/firefox';

// Submission Ai Context
export const SubmissionAiContext = createContext();

export const QuizAiPage = () => {
  // Hooks
  const { id } = useParams();
  const { notify } = useNotify();
  const { setCachedDarkMode } = useDarkMode();

  // State
  const [submissionAi, setSubmissionAi] = useState(null);
  const [loading, setLoading] = useState(false);
  const [aiIsTyping, setAiIsTyping] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);

  // Computed
  const isSubmissionLoggable =
    submissionAi && submissionAi.interview?.startedAt && !submissionAi?.interview?.submittedAt && !submissionAi?.interview?.locked;

  // Detect Device User Agent
  const [isMobile, setIsMobile] = useState(false);
  const [isFirefox, setIsFirefox] = useState(false);
  const userAgent = navigator.userAgent.toLowerCase();
  const detectDevice = () => {
    setIsMobile(userAgent.match(/mobile|android|iphone|ipad|ipod|tablet/i));
    setIsFirefox(userAgent.indexOf('firefox') > -1);
  };

  const handleGetSubmissionAi = async () => {
    try {
      setLoading(true);
      const response = await Api.get(`ai-interview/single/details/${id}`); // id maybe randomId or interviewId
      setSubmissionAi(response.data); // {interview} || {interview, applicant}
    } catch (error) {
      notify.error(error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle listeners
  useEventListener('visibilitychange', async () => {
    if (isSubmissionLoggable && document.visibilityState !== 'visible') {
      const payload = {
        applicantId: submissionAi?.interview?.applicantId,
        interviewId: submissionAi?.interview?._id,
        type: Enums.Logs.WindowSwitched,
        date: {},
      };

      await Api.post(`logs/interview/create`, payload);
    }
  });
  useEventListener('keydown', async ({ keyCode }) => {
    if (isSubmissionLoggable) {
      const payload = {
        applicantId: submissionAi?.interview?.applicantId,
        interviewId: submissionAi?.interview?._id,
        type: Enums.Logs.KeyboardKeyDown,
        date: { keyCode },
      };

      await Api.post(`logs/interview/create`, payload);
    }
  });
  // listen for PrtSc key, as it only fires keyup event
  // This code works only in Windows!
  useEventListener('keyup', async ({ keyCode }) => {
    if (isSubmissionLoggable && keyCode === 44) {
      const payload = {
        applicantId: submissionAi?.interview?.applicantId,
        interviewId: submissionAi?.interview?._id,
        type: Enums.Logs.KeyboardKeyDown,
        date: { keyCode },
      };

      await Api.post(`logs/interview/create`, payload);
    }
  });
  useEventListener('contextmenu', async () => {
    if (isSubmissionLoggable) {
      const payload = {
        applicantId: submissionAi?.interview?.applicantId,
        interviewId: submissionAi?.interview?._id,
        type: Enums.Logs.ContextMenu,
        date: {},
      };

      await Api.post(`logs/interview/create`, payload);
    }
  });
  // Listen for refresh
  useEventListener('beforeunload', async () => {
    if (isSubmissionLoggable) {
      alert('beforeunload');
      const payload = {
        applicantId: submissionAi?.interview?.applicantId,
        interviewId: submissionAi?.interview?._id,
        type: Enums.Logs.WindowRefresh,
        date: {},
      };

      await Api.post(`logs/interview/create`, payload);
    }
  });

  const aiHasFinished = () => !aiIsTyping && !isSpeaking;

  // On Mount
  useEffect(() => {
    handleGetSubmissionAi();
    detectDevice();
  }, [id]);

  // useEffect(() => {
  //   setCachedDarkMode();
  // }, []);

  const render = () => {
    // // Open From Mobile
    // if (isMobile) {
    //   return <MobileAiDevice />;
    // }

    // Web Speech API is not supported in Firefox
    if (isFirefox) {
      return <UnsupportedBrowser />;
    }

    // Expired
    if (submissionAi?.interview?.expired) {
      return <SubmissionAiExpired />;
    }

    // Done
    if (submissionAi?.interview?.submittedAt && aiHasFinished()) {
      return <SubmissionAiFinish />;
    }

    // Locked
    if (submissionAi?.interview?.locked) {
      return <SubmissionAiLocked />;
    }

    // Quiz
    if (submissionAi?.interview?.startedAt) {
      return <SubmissionAiStepper />;
    }

    // Intro
    if (submissionAi?.applicant?.email || submissionAi?.interview?.randomId) {
      return <SubmissionAiOnboarding />;
    }

    // Loading
    return <SubmissionAiLoading />;
  };

  useEffect(() => {
  }, [submissionAi]);

  return (
    <SubmissionAiContext.Provider
      value={{
        notify,
        loading,
        setLoading,
        submissionAi,
        setSubmissionAi,
        handleGetSubmissionAi,
        aiIsTyping,
        setAiIsTyping,
        isSpeaking,
        setIsSpeaking,
      }}
    >
      {render()}
      {submissionAi?.interview?.startedAt}
    </SubmissionAiContext.Provider>
  );
};
