import { Outlet } from 'react-router-dom';
import React, { useState, useEffect } from 'react';
import { useDarkMode } from '/src';
import { Header } from '../../global/components/header';
import { Sidebar } from '../../global/components/sidebar';

import { useScreenSize } from '/src';

export const MainLayout = () => {
  const { setCachedDarkMode } = useDarkMode();

  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const [isShowMenuIcon, setIsShowMenuIcon] = useState(false);

  const screen = useScreenSize();

  // useEffect(() => {
  //   setCachedDarkMode();
  // }, []);

  useEffect(() => {
    if (!screen.lt.md()) {
      setIsDrawerVisible(false);
    }
  }, [screen.size]);

  return (
    <div className="flex flex-col min-h-screen antialiased">
      {/* Header */}
      <Header showThemeIcon={true} isDrawerVisible={isDrawerVisible} setIsDrawerVisible={setIsDrawerVisible} hideTitleOnSM={true} />

      {/* <!-- Sidebar --> */}
      <Sidebar isDrawerVisible={isDrawerVisible} setIsDrawerVisible={setIsDrawerVisible} />

      <Outlet />
    </div>
  );
};
