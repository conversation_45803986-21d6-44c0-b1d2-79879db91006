import React from "react";
import { AUTH_ROUTE_PATH } from "/src/configs/router";

import { UnProtectedRoute } from "../components/un-protected-route";
import { MainLayout } from "../layouts/main";
import { LoginPage } from "../pages/login";
import { Register } from "../pages/register";

export default [
  {
    path: AUTH_ROUTE_PATH,
    element: (
      <UnProtectedRoute>
        <MainLayout />
      </UnProtectedRoute>
    ),
    children: [
      {
        path: "login",
        element: <LoginPage />,
      },
      {
        path: "register",
        element: <Register />,
      },
    ],
  },
];
