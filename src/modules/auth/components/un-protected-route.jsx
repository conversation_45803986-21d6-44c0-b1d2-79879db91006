// React
import { useContext } from 'react';
import { Navigate } from 'react-router-dom';

// Context
import { AppContext } from '/src/components/provider';

export const UnProtectedRoute = ({ children }) => {
  const { userData } = useContext(AppContext) || {};

  /* super-admin*/
  const isSuperAdmin = userData?.roles?.some((role) => ['super-admin'].includes(role));

  /* admin */
  const isAdmin = userData?.roles?.some((role) => ['admin'].includes(role));

  /* content-creator */
  const canViewQuestions = userData?.roles?.some((role) => ['content-creator'].includes(role));
  const canViewCustomTests = userData?.roles?.some((role) => ['content-creator'].includes(role));

  /* hr */
  // const canViewTests = userData?.roles?.some((role) => ['hr'].includes(role)); // Test bank
  const canViewApplicants = userData?.roles?.some((role) => ['hr'].includes(role));

  if (isSuperAdmin) {
    return <Navigate to="/app/dashboard-superadmin" replace />;
  } else if (isAdmin) {
    return <Navigate to="/app/dashboard" replace />;
  } else if (canViewCustomTests) {
    return <Navigate to="/app/tests/list/setup" replace />;
  } else if (canViewQuestions) {
    return <Navigate to="/app/questions" replace />;
    // }
    // else if (canViewTests) {
    //   return <Navigate to="/app/tests/list/prepared" replace />;
  } else if (canViewApplicants) {
    return <Navigate to="/app/applicants" replace />;
  } else {
    return children;
  }
};
