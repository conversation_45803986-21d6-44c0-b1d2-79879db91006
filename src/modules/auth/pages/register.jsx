import React, { useState, useContext } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Form, TextInput, Button, useForm, useNotify, useValidate, Regex, Icon, Logo, Checkbox, Api } from '/src';
import { AppContext } from '/src/components/provider';
import { GoogleOAuthProvider, GoogleLogin } from '@react-oauth/google';
import { VITE_GOOGLE_CLIENT_ID } from 'src/configs/api';

export const Register = () => {
  const { isRequired, minLength, maxLength, validateRegex } = useValidate();
  const { updateUser } = useContext(AppContext);
  const { notify } = useNotify();
  const location = useLocation();
  const navigate = useNavigate();
  const redirectAfter = location.state?.redirectAfter || '/app/dashboard';

  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [confirmPassword, setConfirmPassword] = useState('');
  const [termsAccepted, setTermsAccepted] = useState(false);

  const { form, setFieldValue } = useForm({
    name: '',
    email: '',
    password: '',
  });

  const clientId = `${VITE_GOOGLE_CLIENT_ID}.apps.googleusercontent.com`;

  const handleGoogleAccountLoginSuccess = async (credentialResponse) => {
    try {
      const { credential } = credentialResponse;
      const { data } = await Api.post('auth/google-login', { idToken: credential });

      if (data?.access_token) {
        localStorage.setItem('userData', JSON.stringify(data));
        updateUser(data);
        navigate('/app');
      }
    } catch (error) {
      notify.error(error?.response?.data?.message || 'Google login failed');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!termsAccepted) {
      notify.error('You must accept the terms of service to register');
      return;
    }

    if (form.password !== confirmPassword) {
      notify.error('Passwords do not match');
      return;
    }

    try {
      setLoading(true);
      const { data } = await Api.post('organizations/single', form);
      if (data?.access_token) {
        localStorage.setItem('userData', JSON.stringify(data));
        updateUser(data);
        navigate(redirectAfter);
      }
    } catch (error) {
      notify.error(error?.response?.data?.message || 'Registration failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen grid place-items-center bg-gray-50 dark:bg-gray-900 px-4">
      <div className="w-full max-w-md bg-white dark:bg-gray-800 rounded-xl shadow p-6 space-y-6">
        <div className="flex items-center justify-center mb-4">
          <a onClick={() => navigate('/')} className="cursor-pointer">
            <Logo className="h-12" />
          </a>
        </div>

        <h1 className="text-2xl font-bold text-center text-gray-900 dark:text-white">Create an Account</h1>

        <Form onSubmit={handleSubmit} className="space-y-5">
          <TextInput
            name="name"
            label="Full Name"
            placeholder="Your name"
            disabled={loading}
            value={form.name}
            onChange={setFieldValue('name')}
            validators={[isRequired(), minLength(3), maxLength(100), validateRegex(Regex.name)]}
          />

          <TextInput
            name="email"
            label="Email Address"
            placeholder="<EMAIL>"
            disabled={loading}
            value={form.email}
            onChange={setFieldValue('email')}
            validators={[isRequired(), validateRegex(Regex.email)]}
          />

          <div className="relative">
            <TextInput
              label="Password"
              name="password"
              placeholder="Enter password"
              type={showPassword ? 'text' : 'password'}
              autoComplete="new-password"
              disabled={loading}
              value={form.password}
              onChange={setFieldValue('password')}
              validators={[isRequired(), minLength(6)]}
              rightIcon={() => {}}
            />
            <span onClick={() => setShowPassword(!showPassword)} className="absolute top-8 right-3 cursor-pointer">
              <Icon icon={showPassword ? 'mdi:eye-outline' : 'mdi:eye-off-outline'} width="24" />
            </span>
          </div>

          <div className="relative">
            <TextInput
              label="Confirm Password"
              name="confirmPassword"
              placeholder="Re-enter password"
              type={showConfirmPassword ? 'text' : 'password'}
              disabled={loading}
              value={confirmPassword}
              onChange={setConfirmPassword}
              validators={[isRequired()]}
              rightIcon={() => {}}
            />
            <span onClick={() => setShowConfirmPassword(!showConfirmPassword)} className="absolute top-8 right-3 cursor-pointer">
              <Icon icon={showConfirmPassword ? 'mdi:eye-outline' : 'mdi:eye-off-outline'} width="24" />
            </span>
            {form.password !== confirmPassword && confirmPassword && <p className="text-sm text-red-500 mt-1">Passwords do not match</p>}
          </div>

          <Checkbox
            name="terms"
            value={termsAccepted}
            onChange={setTermsAccepted}
            label={
              <span>
                I agree to the{' '}
                <span
                  className="text-purple-600 font-medium cursor-pointer hover:underline"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate('/terms');
                  }}
                >
                  terms of service
                </span>
              </span>
            }
          />

          <Button type="submit" label="Register" icon="mdi:send" loading={loading} className="w-full" gradientMonochrome="purple" />

          <div className="relative flex items-center my-2">
            <hr className="flex-grow border-gray-300 dark:border-gray-600" />
            <span className="mx-2 text-sm text-gray-500 dark:text-gray-400">OR</span>
            <hr className="flex-grow border-gray-300 dark:border-gray-600" />
          </div>

          <GoogleOAuthProvider clientId={clientId}>
            <GoogleLogin
              onSuccess={handleGoogleAccountLoginSuccess}
              onError={() => notify.error('Google login failed')}
              text="signup_with"
              shape="rectangular"
              size="large"
            />
          </GoogleOAuthProvider>

          <p className="text-center text-sm text-gray-600 dark:text-gray-300">
            Already have an account?{' '}
            <span onClick={() => navigate('/auth/login')} className="text-purple-600 cursor-pointer hover:underline">
              Login here
            </span>
          </p>
        </Form>
      </div>
    </div>
  );
};
