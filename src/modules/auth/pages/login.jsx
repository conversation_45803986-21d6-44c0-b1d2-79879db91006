import React, { useState, useContext } from 'react';
import { useNavigate } from 'react-router-dom';

import { Form, TextInput, Button, useForm, useNotify, useValidate, Icon, Regex, Api, Logo } from '/src';
import { AppContext } from '/src/components/provider';
import { VITE_GOOGLE_CLIENT_ID } from 'src/configs/api';
import { GoogleLogin, GoogleOAuthProvider } from '@react-oauth/google';

export const LoginPage = () => {
  const { updateUser } = useContext(AppContext);
  // State
  const [loading, setLoading] = useState(false);
  const { notify } = useNotify();
  const [showPassword, setShowPassword] = useState(false);

  // Form
  const { form, setFieldValue } = useForm({
    email: '',
    password: '',
  });

  // Hooks
  const { isRequired, minLength, validateRegex } = useValidate();
  const navigate = useNavigate();

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const { data } = await Api.post('auth/login', form);
      notify('Login succeeded!');
      localStorage.setItem('userData', JSON.stringify(data));
      updateUser(data);
    } catch (error) {
      notify.error(error.response.data.statusCode === 401 ? 'Invalid Email or Password' : error.response.data.message);
      setLoading(false);
    }
  };

  const clientId = `${VITE_GOOGLE_CLIENT_ID}.apps.googleusercontent.com`;

  const handleGoogleAccountLoginSuccess = async (credentialResponse) => {
    try {
      const { credential } = credentialResponse;

      const { data } = await Api.post('auth/google-login', { idToken: credential });

      if (data?.access_token) {
        localStorage.setItem('userData', JSON.stringify(data));
        updateUser(data);
        navigate('/app');
      }
    } catch (error) {
      notify.error(error?.response?.data?.message || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  const handleShow = () => {
    setShowPassword(!showPassword);
  };

  return (
    <section className="bg-gray-50 dark:bg-gray-900 h-screen">
      <div className="flex flex-col items-center justify-center px-6 py-8 mx-auto h-full lg:py-0">
        <a
          onClick={() => navigate('/')}
          className="flex items-center justify-start mb-6 text-2xl font-semibold text-gray-900 dark:text-white cursor-pointer"
        >
          <Logo className="h-12" />
        </a>
        <div className="w-full bg-white rounded-xl shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700">
          <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
            <h1 className="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white">Login To Your Account</h1>
            <Form className="flex max-w-md flex-col gap-5" onSubmit={handleSubmit}>
              <TextInput
                name="email"
                label="Email"
                placeholder="Email"
                disabled={loading}
                value={form.email}
                onChange={setFieldValue('email')}
                validators={[isRequired(), validateRegex(Regex.email)]}
              />

              <div className="w-full relative">
                <TextInput
                  name={'password'}
                  label="Password"
                  placeholder="Password"
                  type={showPassword ? 'text' : 'password'}
                  disabled={loading}
                  value={form.password}
                  onChange={setFieldValue('password')}
                  validators={[isRequired(), minLength(3)]}
                  className="!w-full block "
                  rightIcon={() => {}}
                />

                <span className="flex justify-around items-center absolute -right-9 top-7  cursor-pointer" onClick={() => handleShow()}>
                  <Icon
                    className="mr-10 p-5 w-8 h-8 rounded-md cursor-pointer  text-gray-500 dark:text-gray-400"
                    width="25"
                    icon={!showPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                  />
                </span>
              </div>

              <Button type="submit" label="Login" icon="mdi:send" disabled={loading} loading={loading} className="mt-4" gradientMonochrome="purple" />

              {/* Google Sign In */}
              <div className="w-full">
                <GoogleOAuthProvider clientId={clientId}>
                  <GoogleLogin
                    onSuccess={handleGoogleAccountLoginSuccess}
                    onError={() => notify.error('Google login failed')}
                    text="signin_with"
                    shape="rectangular"
                    size="large"
                  />
                </GoogleOAuthProvider>
              </div>
            </Form>
          </div>
        </div>
      </div>
    </section>
  );
};
