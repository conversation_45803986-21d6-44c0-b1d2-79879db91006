// Pages
import { LandingGlobal } from '../modules/landing/pages';
import { PricingGlobal } from '../modules/pricing/pages';
import { ContactUsGlobal } from '../modules/contact-us/pages';
import { GlobalLayout } from '../layouts/main';

// Modules
import programmingTestPages from '../modules/programming-test/routes';
import { TermsOfServicePage } from '../modules/terms/pages';

export default [
  {
    path: '/',
    element: <GlobalLayout />,
    loader() {
      return {
        // icon: 'mi:home',
        svg: (
          <svg width="22" height="19" viewBox="0 0 22 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1.25 9.99982L10.204 1.79107C10.644 1.38865 11.356 1.38865 11.795 1.79107L20.75 9.99982M3.5 7.93732V17.2186C3.5 17.7878 4.004 18.2498 4.625 18.2498H8.75V13.7811C8.75 13.2118 9.254 12.7498 9.875 12.7498H12.125C12.746 12.7498 13.25 13.2118 13.25 13.7811V18.2498H17.375C17.996 18.2498 18.5 17.7878 18.5 17.2186V7.93732M7.25 18.2498H15.5"
              stroke="#656C7B"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ),
        tooltip: 'Home',
      };
    },
    children: [
      {
        path: '',
        element: <LandingGlobal />,
      },

      // Modules
      ...programmingTestPages,

      // Pages
      {
        path: 'pricing',
        element: <PricingGlobal />,
      },
      {
        path: 'contact-us',
        element: <ContactUsGlobal />,
      },
      {
        path: 'terms',
        element: <TermsOfServicePage />,
      },
    ],
  },
];
