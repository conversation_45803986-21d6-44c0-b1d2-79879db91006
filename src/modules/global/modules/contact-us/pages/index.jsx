import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Form, TextInput, Textarea, Button, useForm, useNotify, useValidate, Api, Dialog } from '/src';

export const ContactUsGlobal = () => {
  // State
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState();
  const { notify } = useNotify();

  // Form
  const { form, setFieldValue } = useForm({
    fullName: '',
    email: '',
    message: '',
  });

  // Hooks
  const { isRequired, minLength, validateRegex } = useValidate();
  const navigate = useNavigate();

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const type = 'contact-us';
      const response = await Api.post('/mails/contact', { type, ...form });
      setData(response);
    } catch (error) {
      notify.error(error.response.data.message);
    } finally {
      setLoading(false);
    }
  };

  const onClose = () => {
    setData(null);
    navigate('/');
  };

  return (
    <section className="flex flex-col items-center justify-center px-6 mx-auto h-full dark:bg-darkGrayBackground">
      <h1 className="text-center font-semibold capitalize  tracking-tight text-gray-900 text-2xl md:text-4xl dark:text-white md:w-2/4 pb-2">
        Get in Touch with us .
      </h1>

      <p className="text-center  tracking-tight  text-gray-400 text-sm md:text-lg dark:text-white md:w-2/5 pt-1 pb-10">
        Got any questions about our services? Please fill out the contact form and we'll get back to you quickly .
      </p>

      <div className="w-full bg-white rounded-xl shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700 border border-1">
        <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
          <Form className="flex max-w-md flex-col gap-5" onSubmit={handleSubmit}>
            <TextInput
              name="fullName"
              label="Name"
              placeholder="Name"
              disabled={loading}
              value={form.fullName}
              onChange={setFieldValue('fullName')}
              validators={[isRequired(), validateRegex(/^[a-zA-Z\s]+$/)]}
            />

            <TextInput
              name="email"
              label="Email"
              placeholder="Email"
              disabled={loading}
              value={form.email}
              onChange={setFieldValue('email')}
              validators={[isRequired(), validateRegex(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/)]}
            />

            <Textarea
              label="Message"
              name="message"
              placeholder="Leave us a message.."
              type="textarea"
              rows="4"
              value={form.message}
              onChange={setFieldValue('message')}
            />

            <Button
              type="submit"
              label="Send a message"
              // icon="mdi:send"
              disabled={loading}
              loading={loading}
            />
          </Form>
        </div>
      </div>
      {data && (
        <Dialog show popup size="lg" onClose={onClose}>
          <div className="space-y-6 text-center">
            <img className="mx-auto mt-4 mb-10" src="/public/images/check.png" />
            <div className="space-y-5">
              <p className="text-gray-900 dark:text-white text-xl">Your message has been successfully sent!</p>
              <p className="text-gray-500 dark:text-white">We will contact you as soon as possible</p>
            </div>

            <Button className="w-full" label="Ok" onClick={onClose} />
          </div>
        </Dialog>
      )}
    </section>
  );
};
