import React from 'react';
import { Icon } from '/src';

export const ServicesCard = ({ data, index }) => {
  // Determine blob colors based on the feature
  const getBlobColors = (title) => {
    switch (title) {
      case 'Effortless Exam Experience':
        return {
          blob1: 'bg-pastelPurple',
          blob2: 'bg-purple-400',
        };
      case 'In-Depth Progress Analysis':
        return {
          blob1: 'bg-pastelOrange',
          blob2: 'bg-pastelYellow',
        };
      case 'Customizable PDF Reports':
        return {
          blob1: 'bg-pastelBlue',
          blob2: 'bg-blue-300',
        };
      case 'Advanced Security Monitoring':
        return {
          blob1: 'bg-pastelRed',
          blob2: 'bg-pastelPink',
        };
      default:
        return {
          blob1: 'bg-pastelPink',
          blob2: 'bg-pastelPurple',
        };
    }
  };

  const { blob1, blob2 } = getBlobColors(data.title);

  return (
    <div
      // data-aos="fade-up" // Animation type
      // data-aos-delay={index * 300} // Delay for staggered effect
      className="relative dark:bg-darkBackgroundCard flex flex-col items-center w-[100%] h-[100%] mx-auto px-4 py-6 rounded-2xl bg-white text-center shadow-lg border border-1 dark:border-none"
    >
      <div
        className={` dark:hidden absolute w-32 block h-32 bottom-0 right-0 ${blob1} rounded-t-full rounded-l-full opacity-10 overflow-hidden`}
      ></div>
      <div className={`dark:hidden absolute w-24 block  h-24 bottom-20 right-0 ${blob2} rounded-l-full opacity-10 overflow-hidden`}></div>

      {/* Icon with adjusted spacing */}
      <div className="flex justify-center items-center text-white absolute w-20 h-20 rounded-full" style={{ backgroundColor: data.backgroundColor }}>
        <Icon icon={data.icon} width="30" />
      </div>

      {/* Adjust title spacing */}
      <div className="dark:text-primaryPurple text-[#7E3AF2] font-medium text-xl md:text-2xl leading-normal mt-24 mb-2">{data.title}</div>

      {/* Adjust content spacing */}
      <div className="dark:text-[#d6dbe3b3] text-[#696984] font-normal text-base md:text-lg max-w-xs mt-4">{data.data}</div>
    </div>
  );
};
