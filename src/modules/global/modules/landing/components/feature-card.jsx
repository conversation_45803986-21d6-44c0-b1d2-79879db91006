import React, { useEffect } from 'react';
import { <PERSON><PERSON>, Button } from '/src';
import AOS from 'aos';
import 'aos/dist/aos.css';
import { useNavigate } from 'react-router-dom';

export const FeatureCard = ({ FeatureData, setAllImagesLoaded, borderDirection, hideDivider }) => {
  const navigate = useNavigate();

  useEffect(() => {
    AOS.init({ duration: 1000, once: true });
  }, []);

  return (
    <>
      <div className={`grid xl:grid-cols-2  my-5  justify-center items-center lg:items-start gap-x-40 p-5`}>
        {/* Image section - will switch sides based on flexDirection prop */}

        {FeatureData.flexDirection === 'lg:flex-row-reverse' ? (
          <>
            {/* Text section first when image should be on right */}
            <div className="flex flex-col gap-5 lg:text-left self-center">
              <div dangerouslySetInnerHTML={{ __html: FeatureData.title }} />

              {FeatureData.isQuestion && (
                <ul className="flex flex-col gap-8 mt-5">
                  {FeatureData.description.map((desc, index) => (
                    <li key={index} className="flex max-md:flex-col items-center text-center md:text-start gap-2 md:grid grid-cols-6">
                      <div className="col-span-1 flex justify-center items-center lg:w-14 lg:h-14 w-20 h-20 px-1 rounded-full dark:bg-darkBackgroundCard bg-white dark:shadow-[0px_4px_14px_0px_rgba(195,195,195,0.08)] shadow-lg">
                        <Icon icon={desc.icon} className="text-primaryPurple hidden lg:block text-center" width="30" />
                        <Icon icon={desc.icon} className="text-primaryPurple lg:hidden block text-center" width="40" />
                      </div>
                      <span className="col-span-5 dark:text-grayTextOnDarkMood lg:ml-4 2xl:m-0 text-[#838383] font-normal leading-normal tracking-wide text-lg md:text-lg lg:text-lg">
                        <span dangerouslySetInnerHTML={{ __html: desc.text }} />
                      </span>
                    </li>
                  ))}
                </ul>
              )}

              {/* Button Handling */}
              {(FeatureData.isTestFeature || FeatureData.isInterviewFeature) && (
                <div className="flex justify-center lg:justify-start">
                  <Button
                    onClick={() => navigate(FeatureData.isTestFeature ? '/programming-test' : '/auth/login')}
                    outline
                    label={FeatureData.isTestFeature ? 'Explore Our Programming Tests' : 'Schedule An AI Interview'}
                  />
                </div>
              )}
            </div>

            {/* Image on right */}
            <div className="m-auto">
              <img
                src={FeatureData.lightImageSrc}
                alt={FeatureData.lightImageAlt}
                className={`my-auto xl:block hidden rounded-3xl border-${FeatureData.borderDirection}-[20px] h-full w-full`}
                onLoad={() => setAllImagesLoaded((prev) => ({ ...prev, imageTwo: true }))}
              />
            </div>
          </>
        ) : (
          <>
            {/* Image on left */}
            <div className="m-auto">
              <img
                src={FeatureData.lightImageSrc}
                alt={FeatureData.lightImageAlt}
                className={`my-auto xl:block hidden rounded-3xl border-${FeatureData.borderDirection}-[20px] h-full w-full`}
                onLoad={() => setAllImagesLoaded((prev) => ({ ...prev, imageTwo: true }))}
              />
            </div>

            {/* Text section second when image should be on left */}
            <div className="flex flex-col gap-5 lg:text-left self-center">
              <div dangerouslySetInnerHTML={{ __html: FeatureData.title }} />

              {FeatureData.isQuestion && (
                <ul className="flex flex-col gap-8 mt-5">
                  {FeatureData.description.map((desc, index) => (
                    <li key={index} className="flex max-md:flex-col items-center text-center md:text-start gap-2 md:grid grid-cols-6">
                      <div className="col-span-1 flex justify-center items-center lg:w-14 lg:h-14 w-20 h-20 px-1 rounded-full dark:bg-darkBackgroundCard bg-white dark:shadow-[0px_4px_14px_0px_rgba(195,195,195,0.08)] shadow-lg">
                        <Icon icon={desc.icon} className="text-primaryPurple hidden lg:block text-center" width="30" />
                        <Icon icon={desc.icon} className="text-primaryPurple lg:hidden block text-center" width="40" />
                      </div>
                      <span className="col-span-5 dark:text-grayTextOnDarkMood lg:ml-4 2xl:m-0 text-[#838383] font-normal leading-normal tracking-wide text-lg md:text-lg lg:text-lg">
                        <span dangerouslySetInnerHTML={{ __html: desc.text }} />
                      </span>
                    </li>
                  ))}
                </ul>
              )}

              {/* Button Handling */}
              {(FeatureData.isTestFeature || FeatureData.isInterviewFeature) && <div className="flex justify-center lg:justify-start"></div>}
            </div>
          </>
        )}
      </div>
      {!hideDivider && <div className="border-b-2 w-64 mx-auto mb-14 border-primaryPurple  lg:mb-12"></div>}
    </>
  );
};
