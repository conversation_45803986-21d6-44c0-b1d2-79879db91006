import React from 'react';

export const PaymentMethods = () => {
  const paymentMethods = [
    { name: 'Visa', logo: '/images/payment/visa.svg' },
    { name: 'MasterCard', logo: '/images/payment/mastercard.svg' },
    { name: '<PERSON><PERSON>', logo: '/images/payment/mada.svg' },
    { name: 'Apple Pay', logo: '/images/payment/apple.svg' },
  ];

  return (
    <div className="py-10 bg-gray-50 dark:bg-gray-800/30">
      <div className="w-full sm:w-[85%] md:w-[75%] mx-auto">
        <p className="text-center text-base text-gray-600 dark:text-gray-300 mb-6 font-medium">Secure Payment Methods</p>
        <div className="flex justify-center items-center gap-8 flex-wrap">
          {paymentMethods.map((method) => (
            <div
              key={method.name}
              className="flex items-center justify-center p-2 bg-white dark:bg-gray-700 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <img src={method.logo} alt={`${method.name} logo`} className={`object-contain opacity-90 h-14`} title={method.name} />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
