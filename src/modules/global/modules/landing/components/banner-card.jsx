// React
import { useNavigate } from 'react-router-dom';
import { Button } from '/src';

// React icons
import { FaCheckCircle } from 'react-icons/fa';

export const BannerCard = () => {
  const navigate = useNavigate();
  // Text color : Dark blue, Background gradient instead (#6e8fca) will ( e7f1fb) and gradient instead of #9d66d7 will be (#dbc1f7).   //text-darkBlueText
  return (
    <div className="bg-gradient-to-br from-[#dbc1f7] to-[#e7f1fb] p-8 shadow-lg flex items-center justify-center h-auto lg:h-64 overflow-hidden">
      <div className="flex flex-col lg:flex-row items-center justify-around w-full p-8 space-y-8 lg:space-y-0">
        {/* Text Section */}
        <div className="flex flex-col items-start space-y-4 text-left">
          <h1 className="text-2xl sm:text-3xl font-bold text-darkBlueText">What are you waiting for?</h1>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <div className="w-6">
                <FaCheckCircle className="text-darkBlueText text-lg sm:text-xl" />
              </div>
              <h2 className="text-base sm:text-lg text-darkBlueText">Save your time and effort with AI-powered tests.</h2>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-6">
                <FaCheckCircle className="text-darkBlueText text-lg sm:text-xl" />
              </div>
              <h2 className="text-base sm:text-lg text-darkBlueText">Streamline candidate evaluation.</h2>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-6">
                <FaCheckCircle className="text-darkBlueText text-lg sm:text-xl" />
              </div>
              <h2 className="text-base sm:text-lg text-darkBlueText">Make better hiring decisions faster.</h2>
            </div>
          </div>
          <Button onClick={() => navigate('/auth/login')} className="px-4" label="Get Started" />
        </div>

        {/* Image Section - Hidden on smaller screens */}
        <div className="hidden lg:block flex-shrink-0 pl-6">
          <img src="/images/landing/save-time.png" alt="Landing screen" className="h-80 w-80 rounded-3xl" />
        </div>
      </div>
    </div>
  );
};
