// React
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

// Core
import { Button } from '/src';

// Components
import { ServicesCard } from '../components/services-card';
import { FeatureCard } from '../components/feature-card';
import { BannerCard } from '../components/banner-card';
import { PaymentMethods } from '../components/payment-methods';

export const LandingGlobal = () => {
  // React
  const navigate = useNavigate();
  const location = useLocation();

  // State
  const [isAllImagesLoaded, setAllImagesLoaded] = useState({
    imageOne: false,
    imageTwo: false,
  });

  const FeaturesData = [
    {
      isQuestion: true,
      isTestFeature: true,
      flexDirection: 'lg:flex-row',
      lightImageSrc: '/images/landing/test.png',
      lightImageAlt: 'Test Automation Image',
      borderDirection: 'r',
      title: `<h2 class="dark:text-[#BB86FC]  text-center lg:text-left font-bold text-lg sm:text-2xl max-sm:text-xl lg:text-3xl"> <span class="text-primaryPurple">Pre-Built</span> & <span class="text-primaryPurple">Custom </span> Tests Tailored to Your Needs.</h2>`,
      // subtitle: 'Use our library of pre-defined tests by experts or create your own to match specific job requirements.',
      description: [
        {
          icon: 'iconoir:select-window',
          text: '<span class="font-medium  lg:text-xl sm:text-xl text-[#1C2434] dark:text-[#838398]">Select Test:</span>  Choose from our extensive library of pre-built tests.',
        },
        {
          icon: 'uil:create-dashboard',
          text: '<span class="font-medium  lg:text-xl sm:text-xl text-[#1C2434] dark:text-[#838398]">Create Custom Test:</span> Customize tests based on your specific needs.',
        },
        {
          icon: 'clarity:assign-user-solid',
          text: '<span class="font-medium  lg:text-xl sm:text-xl text-[#1C2434] dark:text-[#838398]">Assign to Applicant:</span> Easily assign tests to applicants and track their progress.',
        },
      ],
      isdiv: '<div className="border-b-2 w-80 mx-auto border-primaryPurple lg:mb-12"></div>',
    },

    {
      isQuestion: true,
      isInterviewFeature: true,
      flexDirection: 'lg:flex-row-reverse',
      lightImageSrc: '/images/landing/AI-interview.png',
      lightImageAlt: 'AI-powered interview interface',
      borderDirection: 'l',
      title: `<h2 class="dark:text-[#BB86FC]  text-center lg:text-left font-bold text-lg sm:text-2xl max-sm:text-xl lg:text-3xl"> <span class="text-primaryPurple"> AI-Powered </span>  Interviews: The Future of Recruitment.</h2>`,
      // subtitle: 'Easily create AI-powered interviews tailored to your job needs.',
      description: [
        {
          icon: 'fluent:brain-circuit-24-filled',
          text: '<span class="font-medium  lg:text-xl sm:text-xl text-[#1C2434] dark:text-[#838398]">Effortless Question Generation:</span> Instantly create interview questions tailored to your needs.',
        },
        {
          icon: 'gridicons:customize',
          text: '<span class="font-medium  lg:text-xl sm:text-xl text-[#1C2434] dark:text-[#838398]">Fully Customizable:</span> Set categories, difficulty levels, and time constraints to target the most relevant skills',
        },
        {
          icon: 'fluent:note-add-24-regular',
          text: '<span class="font-medium  lg:text-xl sm:text-xl text-[#1C2434] dark:text-[#838398]">Personalized Touch:</span> Add specific areas of focus or additional notes to guide the interview.',
        },
        // {
        //   icon: 'mdi:chart-line-variant',
        //   text: '<span class="font-medium lg:text-lg text-darkBlueText dark:text-[#838398]">Smart Insights:</span> Gain comprehensive analytics on candidate performance, highlighting strengths and areas for improvement.',
        // },
      ],
    },
  ];

  const ServicesData = [
    {
      title: 'Effortless Exam Experience',
      data: 'Easily manage applicants with a user-friendly interface, ensuring a smooth process from start to finish.',
      icon: 'eos-icons:system-ok-outlined',
      backgroundColor: '#7e3af2',
    },
    {
      title: 'In-Depth Progress Analysis',
      data: 'Track detailed progress, highlighting individual strengths and weaknesses for targeted improvement.',
      icon: 'tabler:report',
      backgroundColor: '#F48C06',
    },
    {
      title: 'Customizable PDF Reports',
      data: 'Generate clear and professional PDF reports, summarizing applicant performance with customizable options.',
      icon: 'teenyicons:pdf-outline',
      backgroundColor: '#29B9E7',
    },
    {
      title: 'Advanced Security Monitoring',
      data: 'Detect and flag suspicious behaviors, including cheating, to ensure a fair and secure testing environment.',
      icon: 'pepicons-pop:monitor-loop',
      backgroundColor: '#E63757',
    },
  ];

  useEffect(() => {
    if (location.hash) {
      if (Object.values(isAllImagesLoaded)?.every((value) => value)) {
        const id = location.hash.slice(1); // Remove the "#" symbol
        const element = document.getElementById(id);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }
    }
  }, [location.hash, isAllImagesLoaded]);

  return (
    <div className="relative">
      {/* Hero Section */}
      <div className="w-[80%] sm:w-[85%] md:w-[75%] mx-auto">
        <div className="text-black dark:text-white flex flex-col lg:flex-row lg:justify-between lg:gap-x-5 items-center">
          <div className="flex flex-col justify-center gap-4 flex-1 order-2 lg:order-1">
            <div className="text-2xl text-center lg:text-start sm:text-3xl max-sm:text-2xl md:text-3xl lg:text-3xl 2xl:text-4xl font-bold dark:text-grayTextOnDarkMood leading-snug sm:leading-normal">
              Transform Your <span className="text-primaryPurple">Recruitment</span> and <span className="text-primaryPurple">Evaluation</span>{' '}
              Process with Thepass<span className="text-primaryPurple">.</span>
            </div>
            <div className="text-lg lg:text-start sm:text-lg text-center md:text-xl font-normal dark:text-[#d6dbe3b3] text-['#1C2434']">
              Effortlessly find the best applicants with our <span className="font-medium">cutting-edge tests</span> and{' '}
              <span className="font-medium">AI-driven interviews.</span>
            </div>
            <div className="flex justify-center lg:justify-start">
              <Button onClick={() => navigate('/programming-test')} className="px-4 mb-5" label="Explore Tests" />
            </div>
          </div>
          <div className="order-1 lg:order-2 w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg mb-8 lg:mb-0">
            <img
              src="/images/landing/hero-section.png"
              alt="Landing screen"
              className="w-full h-auto object-cover"
              onLoad={() => setAllImagesLoaded((prev) => ({ ...prev, imageOne: true }))}
            />
          </div>
        </div>
      </div>

      {/* Why Tech Pass Section */}
      <div id="why-thepass" className="pt-20 relative bg-pastelPurple bg-opacity-15">
        <div className="dark:hidden absolute lg:w-[400px] lg:h-[400px] md:w-[350px] md:h-[350px] max-sm:w-[200px] max-sm:h-[200px] w-[250px] h-[250px] right-0 bg-pastelPink  rounded-b-full  rounded-l-full top-0  opacity-5 overflow-hidden"></div>

        <div className="dark:hidden absolute lg:w-[400px] lg:h-[400px] md:w-[350px] md:h-[350px] max-sm:w-[200px] max-sm:h-[200px] w-[250px] h-[250px] left-0 bg-pastelPurple rounded-r-full rounded-t-full  bottom-0  opacity-10 overflow-hidden"></div>

        <div className="w-full sm:w-[85%] md:w-[75%] mx-auto text-center ">
          <p className="font-semibold dark:text-white mb-4 text-xl sm:text-3xl max-sm:text-3xl max-xs:text-sm xs:text-xl md:text-3xl">Why Thepass?</p>
          <div className="border-b-4 w-24 mx-auto border-primaryPurple lg:mb-4"></div>
          <p className="hidden lg:block text-[#2f327dbd] font-medium text-xl dark:text-[#d6dbe3b3]">
            Do <span className="text-primaryPurple text-opacity-90 font-bold"> More</span> With Less, Recruit{' '}
            <span className="text-primaryPurple  text-opacity-90 font-bold"> Smarter </span> Today
          </p>
          <div>
            {FeaturesData.map((FeatureData, index) => (
              <div key={index}>
                <FeatureCard
                  FeatureData={FeatureData}
                  key={FeatureData.title}
                  setAllImagesLoaded={setAllImagesLoaded}
                  hideDivider={index === FeaturesData.length - 1} // Hide divider for the last feature (second section)
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Benefits */}
      <div id="benefits" className="py-10 pt-20">
        <div className="w-full sm:w-[90%] md:w-[85%] mx-auto text-center">
          <p className="font-medium dark:text-grayTextOnDarkMood mb-4 text-2xl sm:text-2xl lg:text-3xl md:leading-normal">Our Benefits</p>
          <div className="border-b-4 w-28 mx-auto border-primaryPurple lg:mb-4"></div>
          <p className="hidden lg:block text-[#2f327dbd] dark:text-[#d6dbe3b3] font-medium text-xl">
            Beyond a Test Platform? Discover the <span className="text-primaryPurple font-bold">Difference</span>
          </p>
          <div className="mt-10 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-6">
            {ServicesData.map((data, index) => (
              <div className="p-4">
                <ServicesCard data={data} index={index} key={index} />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Payment Methods */}
      <PaymentMethods />

      {/* Banner */}
      <div>
        <BannerCard />
      </div>
    </div>
  );
};
