import { useContext, useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Api, useNotify } from '/src';
import { AppContext } from '/src/components/provider';

export default function PaymentCallback() {
  const navigate = useNavigate();
  const { notify } = useNotify();
  const [loading, setLoading] = useState(true);
  const [searchParams] = useSearchParams();

  const id = searchParams.get('id');
  const planId = searchParams.get('planId');
  const organizationId = searchParams.get('organizationId');

  const { updateUser, userData } = useContext(AppContext);

  useEffect(() => {
    const verifyPayment = async () => {
      if (!id) {
        notify.error('No payment ID found in the URL');
        navigate('/pricing');
        return;
      }

      try {
        setLoading(true);

        // Step 1: Verify payment
        const { data } = await Api.post('/subscription/create-verify', {
          paymentId: id,
          planId,
          organizationId,
        });

        // Merge new features into existing user data
        const updatedUser = {
          ...userData,
          features: data.features,
        };

        localStorage.setItem('userData', JSON.stringify(updatedUser));
        updateUser(updatedUser);

        notify('Payment successful!');
        navigate('/app/dashboard');
      } catch (error) {
        notify.error(error?.response?.data?.message || 'Payment failed or could not be confirmed.');
        navigate('/pricing');
      } finally {
        setLoading(false);
      }
    };

    verifyPayment();
  }, [id, navigate, notify, planId, organizationId, updateUser]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <h1 className="text-2xl font-bold mb-4">Verifying your payment...</h1>
      {loading && <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>}
    </div>
  );
}
