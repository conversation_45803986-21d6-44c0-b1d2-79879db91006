import React, { useEffect, useRef, useState } from 'react';
import { Icon, useFetchList } from '/src';
import { PriceCard } from './price-card';

export const TabsSlider = () => {
  //Ref
  const scrollContainerRef = useRef(null);

  //State
  const [activePriceCategory, setActivePriceCategory] = useState('Yearly');
  const [controlButtons, setControlButtons] = useState(true);
  const [isAtStart, setIsAtStart] = useState(true);
  const [isAtEnd, setIsAtEnd] = useState(false);
  const [activeIndex, setActiveIndex] = useState(3);
  const [loading, setLoading] = useState(false);
  const [pricingCategory, setPricingCategory] = useState([]);

  // Fetch plans from API
  const { ready, list: plans } = useFetchList('plans/list', {
    pagination: {
      page: 1,
      size: 10,
    },
  });

  // Transform API data into pricing categories
  useEffect(() => {
    if (plans && plans.length > 0) {
      // Create pricing categories based on billing cycles
      // Add Enterprise plan locally
      const enterprisePlan = {
        type: 'ENTERPRISE',
        planId: 'enterprise-plan',
        features: {
          tests: 'unlimited',
          applicants: 'unlimited',
          users: 'unlimited',
          exportPdf: true,
          assignTest: 1,
          exportReport: true,
          assignInterview: 1,
          assignScreening: true,
        },
      };

      const categories = [
        {
          details: [
            ...plans.map((plan) => ({
              type: plan.name.toUpperCase(),
              price: plan.price.toString(),
              planId: plan._id,
              features: plan.features,
              currency: plan.currency,
            })),
            // Add Enterprise plan at the end
            enterprisePlan,
          ],
        },
        // {
        //   title: 'Yearly',
        //   details: plans.map((plan) => ({
        //     type: plan.name.toUpperCase(),
        //     price: (plan.price * 12 * 0.7).toFixed(2).toString(), // 30% discount for yearly
        //     originalPrice: (plan.price * 12).toFixed(2).toString(),
        //     planId: plan._id || plan.id,
        //   })),
        // },
      ];

      setPricingCategory(categories);
    }
  }, [plans]);

  //methods
  const handleSelectCategory = (item, index) => {
    setActivePriceCategory(item);
    setActiveIndex(index);
    setLoading(true);
    //run scroll if i choose the button when it is not on boundaries
    if (
      activeIndex != index &&
      !(activeIndex == pricingCategory.length - 1 && index == pricingCategory.length - 2) &&
      !(activeIndex == 0 && index == 1)
    ) {
      scroll(index > activeIndex ? 'next' : 'prev');
    }
  };

  const checkScrollPosition = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setIsAtStart(scrollLeft === 0);
      setIsAtEnd(Math.round(scrollLeft + clientWidth) >= scrollWidth);
    }
  };

  const scroll = (direction) => {
    if (scrollContainerRef.current) {
      const { scrollLeft, clientWidth } = scrollContainerRef.current;
      const scrollAmount = direction === 'next' ? clientWidth : -clientWidth;
      scrollContainerRef.current.scrollTo({
        left: scrollLeft + scrollAmount * 2,
        behavior: 'smooth',
      });
    }
  };

  useEffect(() => {
    //initially start at the left
    if (scrollContainerRef.current) {
      const { scrollWidth, clientWidth } = scrollContainerRef.current;
      scrollContainerRef.current.scrollTo({
        left: scrollWidth + clientWidth,
        behavior: 'smooth',
      });
      scrollContainerRef.current.addEventListener('scroll', checkScrollPosition);
    }
    if (window.innerWidth < 560) {
      setControlButtons(true);
    } else {
      setControlButtons(false);
    }
  }, []);

  // Custom debounce function
  const throttle = (func, limit) => {
    //throttle
    let inThrottle;
    return function (...args) {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  };

  useEffect(() => {
    //listen when screen get smaller to start from right again
    const handleResize = () => {
      if (window.innerWidth < 560 && scrollContainerRef.current) {
        const { scrollWidth, clientWidth } = scrollContainerRef.current;
        scrollContainerRef.current.scrollTo({
          left: scrollWidth + clientWidth,
          behavior: 'smooth',
        });
        checkScrollPosition();
        setControlButtons(true);
      } else {
        setControlButtons(false);
      }
    };

    const throttleHandleResize = throttle(handleResize, 300);

    window.addEventListener('resize', throttleHandleResize);

    // Cleanup event listener on component unmount
    return () => {
      window.removeEventListener('resize', throttleHandleResize);
    };
  }, []);

  if (!ready || pricingCategory.length === 0) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <>
      {/* @FIXME: will be updated once we have a monthly  yearly plans */}
      {/* buttons */}
      {/* <div
        className={`mx-auto w-fit dark:bg-darkBackgroundCard dark:shadow-none shadow-[0px_4px_14px_0px_rgba(195,195,195,0.22)] ${
          !controlButtons ? 'flex' : 'grid grid-cols-10'
        } rounded-3xl bg-white p-1 justify-center items-center`}
      >
        <button
          className={`w-fit text-gray-500 ${isAtStart ? 'opacity-50 cursor-not-allowed' : 'hover:text-gray-800'} ${
            controlButtons ? 'block' : 'hidden'
          }`}
          onClick={() => scroll('prev')}
        >
          <Icon icon="ep:arrow-left-bold" />
        </button>
        <div className={`${!controlButtons ? 'w-fit' : ''} col-span-8 overflow-hidden scroll-smooth`} ref={scrollContainerRef}>
          <div className="w-[413px] md:w-[531px] justify-end flex flex-row shrink md:gap-[10px] md:p-1">
            {pricingCategory.map((item, index) => (
              <button
                key={index}
                onClick={() => handleSelectCategory(item.title, index)}
                className={`px-3 py-2 md:px-[22px] md:py-[11px] text-sm font-medium text-secondaryGray  ${
                  index === activeIndex ? 'bg-primaryPurple text-white rounded-3xl' : ''
                }`}
              >
                {item.title}
              </button>
            ))}
          </div>
        </div>
        <button
          className={`justify-self-center w-fit text-gray-500 ${isAtEnd ? 'opacity-50 cursor-not-allowed' : 'hover:text-gray-300'} ${
            controlButtons ? 'block' : 'hidden'
          }`}
          onClick={() => scroll('next')}
        >
          <Icon icon="ep:arrow-right-bold" />
        </button>
      </div> */}

      {/* cards */}
      <div className="grid grid-cols-1 mb-14 mt-10 gap-6 md:flex md:flex-wrap md:justify-center md:gap-6 lg:grid lg:grid-cols-3 lg:my-16">
        {pricingCategory.map((items) => {
          return (
            // items.title === activePriceCategory &&
            items['details'].map((item, index) => {
              return <PriceCard key={index} data={item} setLoading={setLoading} loading={loading} />;
            })
          );
        })}
      </div>
    </>
  );
};
