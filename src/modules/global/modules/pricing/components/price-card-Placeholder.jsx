import React from 'react';

export const PriceCardPlaceholder = () => {
  return (
    <div className="rounded-2xl bg-white p-6 animate-pulse">
      <div className="pb-6 border-b-[1px] border-gray-200">
        <div className="mb-6 w-20 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        <div className="mb-2 w-36 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
      </div>
      <div className="py-6">
        <ul className="flex flex-col gap-3">
          <li className="flex gap-3">
            <div className="w-4 h-4 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            <div className="w-36 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </li>
          <li className="flex gap-3">
            <div className="w-4 h-4 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            <div className="w-36 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </li>
          <li className="flex gap-3">
            <div className="w-4 h-4 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            <div className="w-36 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </li>
          <li className="flex gap-3">
            <div className="w-4 h-4 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            <div className="w-36 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </li>
          <li className="flex gap-3">
            <div className="w-4 h-4 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            <div className="w-36 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </li>
          <li className="flex gap-3">
            <div className="w-4 h-4 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            <div className="w-36 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </li>
        </ul>
      </div>
      <button className="flex justify-center px-5 py-4 border-[1px] border-gray-200 rounded-xl w-full">
        <div className="w-36 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
      </button>
    </div>
  );
};
