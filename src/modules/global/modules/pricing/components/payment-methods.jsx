import React from 'react';

export const PaymentMethods = () => {
  const paymentMethods = [
    { name: 'Visa', logo: '/images/payment/visa.svg' },
    { name: 'MasterCard', logo: '/images/payment/mastercard.svg' },
    { name: 'American Express', logo: '/images/payment/amex.svg' },
    { name: 'PayPal', logo: '/images/payment/paypal.svg' },
    { name: 'Apple Pay', logo: '/images/payment/applepay.svg' },
  ];

  return (
    <div className="mt-6">
      <p className="text-center text-sm text-gray-500 dark:text-gray-400 mb-3">Accepted Payment Methods</p>
      <div className="flex justify-center items-center gap-4 flex-wrap">
        {paymentMethods.map((method) => (
          <div key={method.name} className="h-8 flex items-center">
            <img src={method.logo} alt={`${method.name} logo`} className="h-full object-contain" title={method.name} />
          </div>
        ))}
      </div>
    </div>
  );
};
