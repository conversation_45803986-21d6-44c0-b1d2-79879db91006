import React, { useEffect, useContext } from 'react';
import { Icon, Button } from '/src';
import { PriceCardPlaceholder } from './price-card-Placeholder';
import { useNavigate } from 'react-router-dom';
import { AppContext } from '/src/components/provider';

export const PriceCard = ({ data, loading, setLoading }) => {
  const navigate = useNavigate();
  const { userData } = useContext(AppContext);

  // @FIXME:
  // Generate dynamic features based on plan data
  const generateFeatures = () => {
    const features = [];

    // Add features based on plan data
    if (data.features) {
      // Tests
      if (data.features.tests > 0 || data.features.tests === 'unlimited') {
        features.push({
          icon: 'material-symbols:done',
          details: `${data.features.tests === 'unlimited' ? 'Unlimited' : data.features.tests} tests`,
          active: true,
        });
      } else {
        features.push({
          icon: 'material-symbols:close',
          details: 'No tests included',
          active: false,
        });
      }

      // Applicants
      if (data.features.applicants > 0 || data.features.applicants === 'unlimited') {
        features.push({
          icon: 'material-symbols:done',
          details: `${data.features.applicants === 'unlimited' ? 'Unlimited' : data.features.applicants} applicants`,
          active: true,
        });
      } else {
        features.push({
          icon: 'material-symbols:close',
          details: 'No applicants included',
          active: false,
        });
      }

      // Users
      if (data.features.users > 0 || data.features.users === 'unlimited') {
        features.push({
          icon: 'material-symbols:done',
          details: `${data.features.users === 'unlimited' ? 'Unlimited' : data.features.users} user${
            data.features.users !== 1 ? 's' : ''
          } (hiring manager)`,
          active: true,
        });
      } else {
        features.push({
          icon: 'material-symbols:close',
          details: 'No users included',
          active: false,
        });
      }

      // PDF reports
      features.push({
        icon: data.features.exportPdf ? 'material-symbols:done' : 'material-symbols:close',
        details: 'PDF reports',
        active: !!data.features.exportPdf,
      });

      // Assign Test
      features.push({
        icon: data.features.assignTest > 0 ? 'material-symbols:done' : 'material-symbols:close',
        details: 'Test multiple applicants with a single link',
        active: data.features.assignTest > 0,
      });

      // Export Report
      features.push({
        icon: data.features.exportReport ? 'material-symbols:done' : 'material-symbols:close',
        details: 'Access to given & correct answers',
        active: !!data.features.exportReport,
      });

      // Assign Interview
      features.push({
        icon: data.features.assignInterview > 0 ? 'material-symbols:done' : 'material-symbols:close',
        details: 'AI Interviews',
        active: data.features.assignInterview > 0,
      });

      // Assign Screening
      features.push({
        icon: data.features.assignScreening ? 'material-symbols:done' : 'material-symbols:close',
        details: 'Custom branding',
        active: !!data.features.assignScreening,
      });
    }
    return features;
  };

  const features = generateFeatures();

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 500);
    return () => clearTimeout(timer);
  }, [loading, setLoading]);

  const activeFeatures = (feature) => {
    return feature.active ? '' : 'opacity-50';
  };

  const handleSubscribe = () => {
    if (userData?.access_token) {
      // User is logged in, redirect to payment
      navigate(`/payment/${data.planId}`);
    } else {
      // User is not logged in, redirect to register
      navigate('/auth/register', {
        state: { redirectAfter: `/payment/${data.planId}` },
      });
    }
  };

  const isFree = parseFloat(data.price) === 0;
  const isPriceValid = parseFloat(data.price) > 0;
  const isEnterprise = data.type === 'ENTERPRISE';

  if (loading) {
    return <PriceCardPlaceholder />;
  }

  return (
    <div
      className={`mx-auto md:mx-0 max-w-80 ${
        loading ? 'opacity-0' : 'opacity-100'
      } transition-opacity duration-300 ease-in dark:bg-darkBackgroundCard shadow-[0px_4px_14px_0px_rgba(195,195,195,0.22)] dark:shadow-none rounded-2xl bg-white text-left p-6`}
    >
      <div className="pb-6 border-b-[1px] border-[rgb(229,231,235)] dark:border-gray-600">
        <p className="text-primaryPurple font-medium text-base mb-4 flex items-center ">
          {data.type}{' '}
          {data.offer && <span className="text-black text-xs ml-3 px-2 py-1 bg-[#eadffd] dark:bg-[#eadffdd1] rounded-xl">{data.offer}% off</span>}
        </p>
        <p className={`${data.originalPrice ? '' : 'md:mb-8'} mb-2 text-4xl font-semibold dark:text-grayTextOnDarkMood`}>
          {isFree ? 'Free' : isEnterprise ? '' : `$${data.price}`}
        </p>
        {!isFree && !isEnterprise && data.originalPrice && (
          <p className="line-through text-secondaryGray dark:text- font-medium text-base">${data.originalPrice}</p>
        )}
        {isEnterprise && (
          <p className="text-secondaryGray dark:text- font-medium text-base">Let’s build a plan that fits your team’s hiring goals.</p>
        )}
      </div>
      <div className="py-6">
        <ul className="flex flex-col gap-3">
          {features.map((feature, index) => {
            return (
              <li key={index} className={`flex gap-3 ${activeFeatures(feature)}`}>
                <Icon
                  icon={feature.icon}
                  className={`self-baseline p-1 ${feature.active ? 'bg-[#EADFFD] text-primaryPurple' : 'bg-gray-200 text-gray-400'} rounded-full`}
                />
                <span className="dark:text-grayTextOnDarkMood self-start text-grayDetail font-normal text-sm">{feature.details}</span>
              </li>
            );
          })}
        </ul>
      </div>

      {/* Enterprise plan: show Contact Us button */}
      {isEnterprise && (
        <Button className="w-full" onClick={() => navigate('/contact-us')} label="Contact Us" />
      )}

      {/* Only show Subscribe button for paid plans (non-Enterprise) */}
      {!isFree && !isEnterprise && isPriceValid && <Button outline className="w-full" onClick={handleSubscribe} label="Subscribe" />}

      {/* For free plans, show a different button */}
      {isFree && <Button className="w-full bg-green-600 hover:bg-green-700" onClick={() => navigate('/auth/register')} label="Get Started Free" />}
    </div>
  );
};
