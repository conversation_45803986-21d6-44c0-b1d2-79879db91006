// Components
import { TabsSlider } from '../components/tabs-slider';

export const PricingGlobal = () => {
  // Hooks

  return (
    <div>
      <div className="text-center flex flex-col gap-3 md:gap-4 lg:gap-6 mb-3 md:mb-4 lg:mb-6 mx-auto w-fit">
        <h1 className="text-2xl md:text-3xl 2xl:text-4xlxl font-semibold dark:text-grayTextOnDarkMood leading-normal">
          Find the <span className="text-primaryPurple">Thepass</span> plan for you.
        </h1>
        <p className="text-xl font-normal dark:text-[#d6dbe3b3] lg:text-2xl text-secondaryGray">Select the perfect plan for your preferences</p>
      </div>

      <div className="grid justify-center">
        <TabsSlider />
      </div>
    </div>
  );
};
