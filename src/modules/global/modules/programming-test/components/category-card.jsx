// UI
import { Card, useScreenSize } from '/src';

// Flowbite
import { Tooltip } from 'flowbite-react';

// Components
import { CategoryCardData } from './category-card-data';

export const CategoryCard = ({ singleBlock, showMoreInfo, creationSubmissionsDialog }) => {
  const screen = useScreenSize();

  return (
    <Card className="h-full min-h-[320px] dark:text-white !p-0 !pb-4 rounded-lg overflow-hidden relative">
      {/* Subcategory Name */}
      <div className="py-4 relative  border-gray-100 border-b-2 dark:border-gray-700 rounded-t-lg ">
        <h2 className="text-lg font-medium text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-purple-800 text-center truncate px-4">
          {singleBlock?.subCategoryName}
        </h2>

        {screen.gt.lg() && (
          <Tooltip
            content={singleBlock?.subCategoryName}
            placement="bottom"
            arrow={false}
            className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 w-fit text-xs"
          >
            <div className="w-full h-full absolute left-0 top-0"></div>
          </Tooltip>
        )}
      </div>

      <div className="px-2">
        {singleBlock?.quiz?.length > 0 ? (
          singleBlock?.quiz
            // URGENTLY: DON'T REMOVE SLICE(0, 3)
            ?.slice(0, 3)
            .map((quiz) => <CategoryCardData test={quiz} key={quiz._id} creationSubmissionsDialog={creationSubmissionsDialog} />)
        ) : (
          <p className="text-gray-500">No test available</p>
        )}
      </div>

      {singleBlock?.quiz?.length > 3 && (
        <button className="hover:underline pt-2 pl-4 text-base text-darkBlueText dark:text-gray-400" onClick={() => showMoreInfo(singleBlock?._id)}>
          View All
        </button>
      )}
    </Card>
  );
};
