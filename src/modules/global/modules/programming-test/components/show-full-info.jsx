import { Modal } from 'flowbite-react';

// Components
import { CategoryCardData } from './category-card-data';

export const ShowFullInfo = ({ onClose, singleDetails, creationSubmissionsDialog }) => {
  return (
    <Modal show popup size="md" onClose={onClose} dismissible className="z-[70]">
      <div className="max-h-screen overflow-y-auto">
        <div className="bg-[#F8FAFC] dark:bg-[#3E3D4B] py-2 px-4 rounded-t-2xl">
          <Modal.Header>
            <p className="text-center font-semibold text-lg text-darkBlueText dark:text-grayTextOnDarkMood tracking-wide pb-1">
              {singleDetails?.subCategoryName}
            </p>
          </Modal.Header>
        </div>

        <Modal.Body className="overflow-visible w-full p-2 pb-6 pt-4 border-gray-100 border-2 dark:border-gray-700 dark:bg-darkBackgroundCard rounded-b-md">
          <div className="h-72 pl-1 pr-5 overflow-y-auto">
            {singleDetails?.quiz.map((test, index) => {
              return <CategoryCardData test={test} key={index} back={true} creationSubmissionsDialog={creationSubmissionsDialog} />;
            })}
          </div>
        </Modal.Body>
      </div>
    </Modal>
  );
};
