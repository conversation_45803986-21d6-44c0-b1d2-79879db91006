import React, { useState, useEffect } from 'react';
import { FaCheckCircle, FaChevronDown, FaChevronUp } from 'react-icons/fa';

export const TestDetailsGlobal = ({ details, expandedAll }) => {
  const [expandedSections, setExpandedSections] = useState({});

  // Sync individual sections' state with the "expand all/collapse all" action
  useEffect(() => {
    const newState = {};
    details.subcategoryDetails.forEach((_, index) => {
      newState[index] = expandedAll; // Set all sections to expanded or collapsed based on expandedAll prop
    });
    setExpandedSections(newState);
  }, [expandedAll, details.subcategoryDetails]);

  return (
    <div className="w-full max-w-5xl mx-auto bg-white dark:bg-gray-800 rounded-xl mt-4 py-3 border-1 border border-gray-200 dark:border-none ">
      {/* Programming Test Includes */}
      <div className="space-y-3">
        {details.subcategoryDetails.map((sub, subIndex) => (
          <div
            key={subIndex}
            className={`py-3 ${subIndex === details.subcategoryDetails.length - 1 ? '' : 'border-b border-gray-200 dark:border-gray-700'}`}
          >
            {/* Subcategory Header */}
            <div className="flex justify-between items-center px-6  flex-wrap">
              <div className="text-lg font-medium text-gray-700 dark:text-white">
                {sub.subCategoryName}
                <span className="text-sm font-normal text-gray-500 dark:text-gray-400 mx-2">
                  {' '}
                  ( {sub.count}
                  {sub.count > 1 ? ' Questions' : ' Question'} )
                </span>
              </div>
              <button
                onClick={() => setExpandedSections((prev) => ({ ...prev, [subIndex]: !prev[subIndex] }))}
                className="text-gray-700 dark:text-gray-300 text-opacity-90 hover:underline focus:outline-none text-sm font-medium flex items-center"
              >
                {expandedSections[subIndex] ? 'Hide Topics' : 'Show Topics'}
                {expandedSections[subIndex] ? <FaChevronUp className="ml-2 " /> : <FaChevronDown className="ml-2 " />}
              </button>
            </div>

            {/* Subcategory Topics */}
            {expandedSections[subIndex] && (
              <div className="mt-3 space-y-1 px-6">
                {sub.topics.map((topic, index) => (
                  <div key={index} className="flex gap-2 items-center pb-1">
                    <FaCheckCircle className="text-gray-500  dark:text-gray-600 text-sm" />
                    <p className="flex items-center font-medium text-[base] text-gray-500 dark:text-gray-300">{topic}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
