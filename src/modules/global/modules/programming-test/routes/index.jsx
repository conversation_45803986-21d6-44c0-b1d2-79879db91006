// React
import { Navigate } from 'react-router-dom';

// Components
import { ProgrammingTestMainLayout } from '../layouts/main';
import { TestDetails } from '../pages/single';
import { ProgrammingTestsPage } from '../pages';

export default [
  {
    path: 'programming-test',
    element: <ProgrammingTestMainLayout />,
    loader() {
      return {
        label: 'Programming Tests',
      };
    },
    children: [
      {
        path: '',
        element: <ProgrammingTestsPage />,
      },
      {
        path: ':quizId',
        element: <TestDetails />,
        loader() {
          return {
            customBreadcrumb: true,
          };
        },
      },
    ],
  },
];
