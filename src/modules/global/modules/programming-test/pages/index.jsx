// React
import { useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';

// UI
import { Icon, useNotify, Api, useFetchList, useScreenSize, NoDataFound, NoDataMatches, ToggleFilter, StaticData } from '/src';

// Flowbite
import { Pagination, Spinner } from 'flowbite-react';

// Components
import { AppContext } from 'src/components/provider';
import { ShowFullInfo } from '../components/show-full-info';
import { CategoryCard } from '../components/category-card';
import { CardPlaceholder } from '../components/card-placeholder';

export const ProgrammingTestsPage = () => {
  // Hooks
  const navigate = useNavigate();
  const { notify } = useNotify();
  const { userData } = useContext(AppContext);
  const screen = useScreenSize();

  // User data
  const isNotPermitted = userData?.roles?.some((role) => ['hr'].includes(role));

  // State
  const [isShowFullInfo, setShowFullInfo] = useState(false);
  const [blockId, setBlockId] = useState();
  const [backupList, setBackupList] = useState([]);

  // Hooks
  const { ready, loading, setLoading, list, count, filters, setFilters, search, pagination, refresh } = useFetchList('blocks/list/custom', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: {
      category: {
        label: 'Category',
        lookup: 'category',
        techPassProgrammingTest: true,
      },
      subCategory: {
        label: 'Sub Category',
        lookup: 'subcategory',
        parentLookup: { key: 'category', fieldName: 'categoryId', fieldValue: null },
        techPassProgrammingTest: true,
      },
      topic: {
        label: 'Topic',
        lookup: 'topic',
        parentLookup: { key: 'subCategory', fieldName: 'subcategoryId' },
        techPassProgrammingTest: true,
      },
    },
  });

  // Pagination
  const { page, size } = pagination;
  const pagesCount = Math.max(Math.ceil(count / size), 1);
  const showingText = `${count ? page * size - size + 1 : count} - ${page * size > count ? count : page * size}`;
  const isPaginationActive = !!pagination.update;
  const paginationLimit = 20;

  const showMoreInfo = (blockId) => {
    setBlockId(blockId);
    setShowFullInfo(true);
  };

  const creationSubmissionsDialog = async (test) => {
    try {
      await Api.get(`quizzes/single/custom/${test._id}`);
      navigate(`/programming-test/${test._id}`);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  if (!ready) {
    return <CardPlaceholder />;
  }

  return (
    <div>
      {/* Headers */}
      <div className="space-y-2 text-center">
        <h2 className="text-4xl font-bold text-gray-900 dark:text-white">Explore Our Test Library</h2>
        <div className="text-[17px] max-sm:text-xs text-[#798296] dark:text-gray-400">
          Select a test and easily assign it to an applicant via email or copy link and send directly
        </div>
      </div>

      <div className="flex flex-wrap justify-center items-center gap-x-6 gap-y-3 py-3 bg-white dark:bg-darkGrayBackground sticky top-16 z-10">
        {/* Search bar */}
        <div className="relative w-full sm:w-[250px] md:w-[400px] lg:w-[600px]">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Icon icon="carbon:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search for a test"
            className="bg-gray-white border truncate border-gray-200 text-gray-800 text-[13.5px] rounded-lg block w-full pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white focus:ring-0 focus:border-gray-300"
            value={search.value}
            onInput={(e) => search.update(e.target.value)}
          />
        </div>

        {/* NB: Filter is complete and ready, just waiting for the backend to finish its work */}
        {/* Filters Button */}
        {/* <ToggleFilter filters={filters} resultsFound={count} /> */}

        <div className={`${isNotPermitted ? 'hidden' : 'hidden sm:block'} h-8 w-px bg-gray-300 dark:bg-gray-700`} />

        <button
          onClick={() => navigate(userData ? '/app/tests/create' : '/auth/login')}
          className={`px-4 py-3 max-sm:py-2 rounded-lg font-medium text-white bg-gradient-to-r from-purple-500 to-blue-500 transition-all duration-300 hover:from-purple-600 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-purple-800 text-nowrap ${
            isNotPermitted && 'hidden'
          }`}
        >
          Customize Your Own Test
        </button>
      </div>

      {/* Blocks */}
      {list?.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4 mt-2">
          {list?.map((singleBlock) => (
            <CategoryCard
              key={singleBlock._id}
              singleBlock={singleBlock}
              showMoreInfo={showMoreInfo}
              creationSubmissionsDialog={creationSubmissionsDialog}
            />
          ))}
        </div>
      )}

      {/* No data created || No results found */}
      {!list.length && (
        <div className="my-8">
          {backupList.length > 0 ? (
            <NoDataMatches />
          ) : (
            <NoDataFound
              noDataFound={{
                customIcon: 'tests',
                message: 'No tests created yet',
              }}
              width="60"
              height="60"
            />
          )}
        </div>
      )}

      {/* Loading */}
      {loading && (
        <div className="absolute z-50 left-0 right-0 bottom-0 top-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80">
          <Spinner size="lg" color="purple" />
        </div>
      )}

      {/* Pagination */}
      {isPaginationActive && (
        <nav
          // className="flex flex-row justify-between items-center space-y-0 p-4 bg-white dark:bg-darkGrayBackground bottom-0 sticky z-20"
          className="flex justify-center items-center px-4 my-1 sticky bottom-0 z-20"
          aria-label="Table navigation"
        >
          {/* <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
            Showing <span className="font-semibold text-gray-900 dark:text-white">{showingText}</span> of{' '}
            <span className="font-semibold text-gray-900 dark:text-white">{count}</span>
          </span> */}
          {count > paginationLimit && (
            <Pagination
              theme={StaticData.paginationTheme}
              currentPage={page}
              onPageChange={(page) => pagination.update({ page })}
              showIcons
              totalPages={pagesCount}
              layout={screen.gt.md() ? 'pagination' : 'navigation'}
              previousLabel={<span className="hidden sm:block">Previous</span>}
              nextLabel={<span className="hidden sm:block">Next</span>}
            />
          )}
        </nav>
      )}

      {/* Show Card Of All Tests */}
      {isShowFullInfo && (
        <ShowFullInfo
          singleDetails={list?.find((test) => test._id === blockId)}
          creationSubmissionsDialog={creationSubmissionsDialog}
          onClose={() => setShowFullInfo(false)}
        />
      )}
    </div>
  );
};
