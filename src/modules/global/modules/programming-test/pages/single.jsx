// React
import { useEffect, useState, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

// UI
import { Form, Regex, useValidate, Button, TextInput, useForm, useNotify, Api, EnumText, Icon } from '/src';

// React icons
import { FaUserGraduate, FaUser, FaStar, FaMedal } from 'react-icons/fa';

// Context
import { AppContext } from '/src/components/provider';

// Components
import { TestDetailsGlobal } from '../components/details';
import { GlobalBreadcrumb } from '../../../components/breadcrumb';

export const TestDetails = () => {
  // State
  const [isCreateApplicant, setIsCreateApplicant] = useState(false);
  const [details, setDetails] = useState(null);
  const [testUrl, setTestUrl] = useState(null);
  const [expandedAll, setExpandedAll] = useState(false); // State to control expand/collapse for all sections

  // Hooks
  const { isRequired, minLength, maxLength, validateRegex } = useValidate();
  const { userData } = useContext(AppContext);
  const { quizId } = useParams();
  const { notify } = useNotify();
  const navigate = useNavigate();

  // Form
  const { form, setFieldValue } = useForm({
    quizId: quizId,
    name: '',
    email: '',
    // willSendEmail: false,
  });

  // Methods
  const generateDescription = () => {
    const totalCategories = details.subcategoryDetails.length;
    const totalTopics = details.subcategoryDetails.reduce((sum, sub) => sum + sub.topics.length, 0);

    let difficultyText = '';
    switch (details.difficulty) {
      case 1:
        difficultyText = 'suitable for interns';
        break;
      case 2:
        difficultyText = 'ideal for fresh graduates';
        break;
      case 3:
        difficultyText = 'great for junior-level candidates';
        break;
      case 4:
        difficultyText = 'targeted for mid-level professionals';
        break;
      case 5:
        difficultyText = 'challenging for senior professionals';
        break;
      default:
        difficultyText = '';
    }

    return (
      <>
        This test covers{' '}
        <span className="font-semibold">
          {' '}
          {totalCategories} {totalCategories > 1 ? 'categories' : 'category'}{' '}
        </span>{' '}
        and includes{' '}
        <span className="font-semibold">
          {' '}
          {totalTopics} {totalTopics > 1 ? 'topics' : 'topic'}{' '}
        </span>
        . It is <span className="font-semibold">{difficultyText} </span>, ensuring the right balance of knowledge this level.
      </>
    );
  };

  const handleAddApplicant = async () => {
    try {
      const response = await Api.post(`submissions/custom/create`, form);
      setTestUrl(response.data);
      setIsCreateApplicant(true);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  const quizDetails = async () => {
    try {
      const response = await Api.get(`quizzes/single/custom/${quizId}`);
      setDetails(response.data);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  const handleSubscribe = () => {
    if (userData?.access_token) {
      navigator.clipboard.writeText(testUrl.quizUrl);
      notify('Link copied');
    } else {
      navigate('/pricing');
    }
  };

  let difficultyIcon;
  let difficultyColor;
  let iconSize = 'text-[15px]';

  if (details) {
    switch (details.difficulty) {
      case 1:
        difficultyIcon = <FaUserGraduate className={`${iconSize} text-teal-700`} />; // Intern
        difficultyColor = ' text-teal-700 ';
        break;

      // Star Icon fresh level
      case 2:
        difficultyIcon = <FaUser className={`${iconSize} text-sky-800`} />; // Fresh
        difficultyColor = 'text-sky-800 ';
        break;
      // Medal Star junior
      case 3:
        difficultyIcon = <FaStar className={`${iconSize} text-amber-700`} />; // Junior
        difficultyColor = ' text-amber-700 ';
        break;
      // betetr medal star midlevel
      case 4:
        difficultyIcon = <FaMedal className={`${iconSize} text-orange-700`} />; // Mid-level
        difficultyColor = 'text-orange-700';
        break;

      // Tropy icon for senior with star
      case 5:
        difficultyIcon = <Icon icon="solar:crown-star-bold" width={18} className={`${iconSize} text-red-800`} />; // Senior
        difficultyColor = 'text-red-800';
        break;
      default:
        difficultyIcon = null;
    }
  }

  useEffect(() => {
    if (quizId) {
      quizDetails();
    }
  }, []);

  // useEffect(() => {
  //   setFieldValue('willSendEmail')(false);
  // }, [form.name, form.email]);

  return (
    <div className="min-w-[80vw] max-w-[967px] mx-3 sm:mx-11 lg:mx-auto space-y-6">
      <GlobalBreadcrumb />

      {details && (
        <div className="flex flex-col lg:flex-row lg:justify-between bg-white dark:bg-darkBackgroundCard rounded-lg py-5 px-4 lg:py-10 lg:px-8 shadow-[0px_0px_14px_0px_rgba(195,195,195,0.22)] dark:shadow-[0px_0px_14px_0px_rgba(195,195,195,0.08)]">
          {/* Left Side: Test Details */}
          <div className="lg:w-2/3 lg:pr-6 flex-grow">
            {/* Allow the left side to grow */}
            <div className="flex flex-col justify-between items-start mb-8">
              {/* Test Title */}
              <div>
                <h1 className="text-2xl md:text-3xl font-bold capitalize text-gray-900 dark:text-white mb-3">{details.title} Test</h1>
                {/* Test Meta Information */}
                <div className="text-base text-gray-500 dark:text-gray-300 space-x-4 flex flex-wrap items-center mb-1">
                  {/* Difficuluty */}
                  <span className="flex  gap-1 items-center">
                    <p> {difficultyIcon} </p>
                    <EnumText name="QuizDifficulty" value={details.difficulty} className="" /> Level
                  </span>
                  <span className="text-gray-400 dark:text-gray-300">•</span> {/* Separator */}
                  {/* Duration */}
                  <div className="flex gap-2 items-center">
                    <span>
                      {' '}
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="size-5">
                        <path
                          fill-rule="evenodd"
                          d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 6a.75.75 0 0 0-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 0 0 0-1.5h-3.75V6Z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </span>
                    <span className="text-gray-500 dark:text-gray-300">{details.duration} mins</span>
                  </div>
                  <span className="text-gray-400 dark:text-gray-300">•</span> {/* Separator */}
                  {/* Questions */}
                  <div className="flex gap-1 items-center">
                    <span>
                      <svg width="22" height="22" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M35.2969 16.0625C34.7031 15.4531 34.0938 14.8125 33.8594 14.2656C33.625 13.7188 33.6406 12.9063 33.625 12.0781C33.6094 10.5625 33.5781 8.82812 32.375 7.625C31.1719 6.42188 29.4375 6.39062 27.9219 6.375C27.0938 6.35937 26.25 6.34375 25.7344 6.14062C25.2188 5.9375 24.5469 5.29688 23.9375 4.70312C22.8594 3.67188 21.625 2.5 20 2.5C18.375 2.5 17.1406 3.67188 16.0625 4.70312C15.4531 5.29688 14.8125 5.90625 14.2656 6.14062C13.7188 6.375 12.9063 6.35937 12.0781 6.375C10.5625 6.39062 8.82812 6.42188 7.625 7.625C6.42188 8.82812 6.39062 10.5625 6.375 12.0781C6.35937 12.9063 6.34375 13.75 6.14062 14.2656C5.9375 14.7812 5.29688 15.4531 4.70312 16.0625C3.67188 17.1406 2.5 18.375 2.5 20C2.5 21.625 3.67188 22.8594 4.70312 23.9375C5.29688 24.5469 5.90625 25.1875 6.14062 25.7344C6.375 26.2812 6.35937 27.0938 6.375 27.9219C6.39062 29.4375 6.42188 31.1719 7.625 32.375C8.82812 33.5781 10.5625 33.6094 12.0781 33.625C12.9063 33.6406 13.75 33.6562 14.2656 33.8594C14.7812 34.0625 15.4531 34.7031 16.0625 35.2969C17.1406 36.3281 18.375 37.5 20 37.5C21.625 37.5 22.8594 36.3281 23.9375 35.2969C24.5469 34.7031 25.1875 34.0938 25.7344 33.8594C26.2812 33.625 27.0938 33.6406 27.9219 33.625C29.4375 33.6094 31.1719 33.5781 32.375 32.375C33.5781 31.1719 33.6094 29.4375 33.625 27.9219C33.6406 27.0938 33.6562 26.25 33.8594 25.7344C34.0625 25.2188 34.7031 24.5469 35.2969 23.9375C36.3281 22.8594 37.5 21.625 37.5 20C37.5 18.375 36.3281 17.1406 35.2969 16.0625ZM20 30C19.6292 30 19.2666 29.89 18.9583 29.684C18.65 29.478 18.4096 29.1851 18.2677 28.8425C18.1258 28.4999 18.0887 28.1229 18.161 27.7592C18.2334 27.3955 18.412 27.0614 18.6742 26.7992C18.9364 26.537 19.2705 26.3584 19.6342 26.286C19.9979 26.2137 20.3749 26.2508 20.7175 26.3927C21.0601 26.5346 21.353 26.775 21.559 27.0833C21.765 27.3916 21.875 27.7542 21.875 28.125C21.875 28.6223 21.6775 29.0992 21.3258 29.4508C20.9742 29.8025 20.4973 30 20 30ZM21.25 22.3594V22.5C21.25 22.8315 21.1183 23.1495 20.8839 23.3839C20.6495 23.6183 20.3315 23.75 20 23.75C19.6685 23.75 19.3505 23.6183 19.1161 23.3839C18.8817 23.1495 18.75 22.8315 18.75 22.5V21.25C18.75 20.9185 18.8817 20.6005 19.1161 20.3661C19.3505 20.1317 19.6685 20 20 20C20.6181 20 21.2223 19.8167 21.7362 19.4733C22.2501 19.13 22.6506 18.6419 22.8871 18.0709C23.1236 17.4999 23.1855 16.8715 23.065 16.2653C22.9444 15.6592 22.6467 15.1023 22.2097 14.6653C21.7727 14.2283 21.2158 13.9306 20.6097 13.81C20.0035 13.6895 19.3751 13.7514 18.8041 13.9879C18.2331 14.2244 17.745 14.6249 17.4017 15.1388C17.0583 15.6527 16.875 16.2569 16.875 16.875C16.875 17.2065 16.7433 17.5245 16.5089 17.7589C16.2745 17.9933 15.9565 18.125 15.625 18.125C15.2935 18.125 14.9755 17.9933 14.7411 17.7589C14.5067 17.5245 14.375 17.2065 14.375 16.875C14.375 15.8161 14.6739 14.7787 15.2373 13.8821C15.8007 12.9856 16.6057 12.2662 17.5598 11.8069C18.5139 11.3475 19.5782 11.1668 20.6305 11.2855C21.6827 11.4041 22.68 11.8174 23.5078 12.4778C24.3356 13.1381 24.9602 14.0187 25.3097 15.0182C25.6592 16.0178 25.7195 17.0957 25.4837 18.128C25.2478 19.1603 24.7254 20.105 23.9764 20.8536C23.2275 21.6021 22.2824 22.1241 21.25 22.3594Z"
                          fill="#D07EAA"
                        />
                      </svg>
                    </span>{' '}
                    <span className="text-gray-500 dark:text-gray-300">
                      {' '}
                      {details.numOfQuestions} {details.numOfQuestions > 1 ? ' Questions' : 'Question'}{' '}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-2">
              <div className="flex justify-between items-center flex-wrap">
                <p className="text-xl font-semibold text-gray-900 dark:text-white">What's Inside?</p>

                <button className="hover:underline focus:outline-none text-primaryPurple font-medium" onClick={() => setExpandedAll(!expandedAll)}>
                  {expandedAll ? 'Collapse all sections' : 'Expand all sections'}
                </button>
              </div>
              <p className="text-[#808080] dark:text-gray-400"> {generateDescription()}</p>
            </div>
            {/* Details component */}
            <TestDetailsGlobal details={details} expandedAll={expandedAll} />
          </div>
          {/* Right Side: Add Applicant Form */}
          <div className="lg:w-1/3 h-fit flex-none border dark:bg-gray-800 border-gray-200 dark:border-none p-6  rounded-2xl">
            {!isCreateApplicant ? (
              <>
                <h1 className="text-lg font-medium leading-tight tracking-tight text-[#111827] lg:text-xl dark:text-white mb-6">
                  Enter Applicant's Details
                </h1>
                <Form className="flex flex-col gap-5" onSubmit={handleAddApplicant}>
                  <TextInput
                    name="name"
                    label="Name"
                    placeholder="Enter applicant's name"
                    value={form.name}
                    onChange={setFieldValue('name')}
                    validators={[isRequired(), minLength(3), maxLength(100), validateRegex(Regex.name)]}
                  />
                  <TextInput
                    name="email"
                    label="Email"
                    placeholder="Enter applicant's email"
                    value={form.email}
                    onChange={setFieldValue('email')}
                    validators={[isRequired(), validateRegex(Regex.email)]}
                  />

                  <div className="pt-2 space-y-4">
                    <div className="flex gap-2">
                      <Button type="submit" label="Create Test" className="w-full" />
                    </div>
                  </div>
                </Form>
              </>
            ) : (
              <>
                <div className="flex justify-center items-center mb-6">
                  {userData?.access_token ? (
                    <img src="/images/celebration.gif" alt="My GIF" className="w-[145px] h-[145px]" />
                  ) : (
                    <img src="/images/Vector.svg" alt="done mark" />
                  )}
                </div>
                <div className="flex justify-center items-center mb-6">
                  <p className="text-base md:text-xl font-medium text-[#374151] dark:text-[#D6DBE3] text-center">
                    {userData?.access_token ? 'You can now copy the test link directly' : 'Test Created Successfully!'}
                  </p>
                </div>
                <Form className="flex flex-col gap-4" onSubmit={handleSubscribe}>
                  <div
                    className={`${
                      !userData?.access_token && 'select-none cursor-not-allowed'
                    } overflow-hidden p-4 rounded-lg bg-[#F4F4F4] dark:bg-gray-700 dark:text-white text-sm font-medium text-black mb-2 break-all`}
                  >
                    {userData?.access_token ? (
                      testUrl.quizUrl
                    ) : (
                      <>
                        {testUrl.quizUrl.slice(0, Math.ceil(testUrl.quizUrl.length - 30))}
                        <span className="blur-sm">{testUrl.quizUrl.slice(Math.ceil(testUrl.quizUrl.length - 10))}</span>
                      </>
                    )}
                  </div>

                  {!userData?.access_token && (
                    <div className="flex justify-start">
                      <p className="text-base font-medium text-secondaryGray dark:text-[#D6DBE3]">Please subscribe to get the complete link!</p>
                    </div>
                  )}
                  <div className="flex gap-2">
                    <Button type="submit" label={userData?.access_token ? 'Copy Link' : 'Subscribe'} className="cursor-pointer w-full" />
                  </div>
                  {!userData?.access_token && (
                    <p className="flex flex-col text-center text-base font-medium text-secondaryGray dark:text-grayTextOnDarkMood md:flex-row md:gap-2 justify-center">
                      Already Subscribed?
                      <span className="font-normal text-primaryPurple cursor-pointer" onClick={() => navigate('../../auth/login')}>
                        Login here
                      </span>
                    </p>
                  )}
                </Form>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
