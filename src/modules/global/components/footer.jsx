import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Logo } from '/src';

export const Footer = () => {
  const navigate = useNavigate();

  const menuItems = [
    {
      label: 'Programming Tests',
      path: '/programming-test',
      visible: true,
    },
    {
      label: 'Pricing',
      path: '/pricing',
      visible: true,
    },
    {
      label: 'Contact Us',
      path: '/contact-us',
      visible: true,
    },
    {
      label: 'Terms of Service',
      path: '/terms',
      visible: true,
    },
  ];

  return (
    <div className="dark:bg-darkBackgroundCard border-t dark:border-none border-t-gray-200 dark:border-gray-600 p-5 shadow-md">
      <div className="container mx-auto flex flex-col sm:flex-row justify-between items-center">
        {/* Logo and Thepass text */}
        <div className="flex items-center mb-3 sm:mb-0">
          <Logo className="h-9" />
        </div>

        {/* Navigation items */}
        <div className="flex items-center space-x-2 sm:space-x-3 mb-3 sm:mb-0">
          {menuItems
            .filter((item) => item.visible)
            .map((item, index) => (
              <React.Fragment key={item.path}>
                <Link
                  to={item.path}
                  className="text-sm font-normal leading-5 tracking-tighter text-gray-500 dark:text-gray-400 hover:text-primaryPurple dark:hover:text-primaryPurple transition-colors duration-300 ease-in-out cursor-pointer"
                >
                  {item.label}
                </Link>

                {index < menuItems.length - 1 && <span className="border-r border-gray-200 dark:border-gray-500 h-4 sm:h-5"></span>}
              </React.Fragment>
            ))}
        </div>

        {/* Copyright */}
        <div className="text-xs sm:text-sm font-[500] text-gray-400 dark:text-gray-400">© 2024 Thepass</div>
      </div>
    </div>
  );
};
