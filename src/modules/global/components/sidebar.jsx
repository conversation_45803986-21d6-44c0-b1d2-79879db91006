import React, { useContext } from 'react';
import { useLocation, Link } from 'react-router-dom';

import { Icon, useScreenSize } from '/src';
import { AppContext } from '/src/components/provider';

export const Sidebar = ({ isDrawerVisible, setIsDrawerVisible }) => {
  // Hooks
  const location = useLocation();
  const screen = useScreenSize();
  const { userData } = useContext(AppContext);

  // Computed
  const getActiveClasess = (itemPath) => {
    if (location.pathname.includes(itemPath)) {
      return 'bg-gray-100 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-700';
    }
    return '';
  };

  const handleNavigate = (event, path) => {
    screen.lt.xl() ? setIsDrawerVisible(false) : {};
    path == '/Contact-us' ? event.preventDefault() : '';
  };

  // Items
  const menuItems = [
    {
      label: 'Why Thepass',
      path: '/#why-thepass',
      parentPath: '/#why-thepass',
      icon: 'mdi:lightbulb-outline',
      visible: true,
    },
    {
      label: 'Benefits',
      path: '/#benefits',
      parentPath: '/#why-thepass',
      icon: 'mdi:check-circle-outline',
      visible: true,
    },

    {
      label: 'Programming Tests',
      path: '/programming-test',
      icon: 'mdi:code-tags',
    },
    {
      label: 'Pricing',
      path: '/pricing',
      icon: 'mdi:currency-usd',
    },
    {
      label: 'Contact Us',
      path: '/contact-us',
      icon: 'mdi:phone-outline',
    },
    {
      label: 'Terms of Service',
      path: '/terms',
      icon: 'mdi:file-document-outline',
      visible: true,
    },
    {
      label: 'Login',
      path: '/auth/login',
      icon: 'mdi:login',
      showAtMd: false,
      hideIfLoggedIn: userData?.access_token,
    },
    {
      label: 'Signup',
      path: '/auth/register',
      icon: 'mdi:register-outline',
      showAtMd: false,
      hideIfLoggedIn: userData?.access_token,
    },
    {
      label: 'View Account',
      path: '/auth/login',
      icon: 'mdi:account-circle-outline', // Represents user account or profile
      showAtMd: false,
      hideIfLoggedIn: !userData?.access_token,
    },
  ];

  return (
    <aside
      id="drawer-navigation"
      className={`fixed top-0 left-0 z-[55] w-64 h-screen pt-14 transition-transform bg-white border-r border-gray-200 dark:bg-darkBackgroundCard dark:border-[#374151] ${
        isDrawerVisible ? '-translate-x-0' : '-translate-x-full'
      } md:hidden mt-3 bg-lightgraybg`}
      aria-label="Sidenav"
    >
      <div className={`overflow-y-auto py-5 px-3 h-full  bg-lightgraybg dark:bg-darkBackgroundCard`}>
        <ul className="space-y-2">
          {menuItems.map((item) =>
            item.hideIfLoggedIn ? (
              ''
            ) : item.showAtMd && !screen.lt.sm() ? (
              ''
            ) : (
              <li key={item.path} className="border-b border-gray-200 dark:border-gray-700">
                <Link
                  to={item.path}
                  onClick={(event) => handleNavigate(event, item.path)}
                  className={`flex items-center p-2 text-base font-medium text-gray-900 rounded-lg dark:text-white group dark:hover:bg-gray-700 hover:bg-gray-100
                    ${getActiveClasess(item.path)}`}
                >
                  <Icon
                    icon={item.icon}
                    className={`${'text-gray-500'} transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white`}
                    width="24"
                  />
                  <span className="ml-2 ">{item.label}</span>
                </Link>
              </li>
            )
          )}
        </ul>

        {/* <ul className='pt-5 mt-5 space-y-2 border-t border-gray-200 dark:border-gray-700'>
          <li>
            <a
              href='#'
              className='flex items-center p-2 text-base font-medium text-gray-900 rounded-lg transition duration-75 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white group'
            >
              <Icon icon='material-symbols:support' width='22' />
              <span className='ml-3'>Help</span>
            </a>
          </li>
        </ul> */}
      </div>
    </aside>
  );
};
