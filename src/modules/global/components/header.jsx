import React, { useEffect, useContext } from 'react';

import { useDarkMode } from '/src/hooks/dark-mode';
import { useLocation, Link, useNavigate } from 'react-router-dom';

import { Icon, useScreenSize, Button, Logo } from '/src';
import { AppContext } from '/src/components/provider';

export const Header = ({ showThemeIcon, isDrawerVisible, setIsDrawerVisible, hideTitleOnSM }) => {
  // Hooks
  const { switchDarkMode, isDark } = useDarkMode();
  const location = useLocation();
  const navigate = useNavigate();
  const screen = useScreenSize();
  const { userData } = useContext(AppContext);

  // Computed
  const getActiveClasess = (itemPath) => {
    if (location.pathname.includes(itemPath) || location.hash.includes(itemPath.slice(1))) {
      return 'text-primaryPurple dark:text-primaryPurple';
    }
    return 'text-black dark:text-white';
  };

  // Items
  const menuItems = [
    {
      label: 'Why Thepass',
      path: '/#why-thepass',
      icon: 'mdi:information-outline',
      visible: true,
    },
    {
      label: 'Benefits',
      path: '/#benefits',
      icon: 'mdi:information-outline',
      visible: true,
    },
    {
      label: 'Tests',
      path: '/programming-test',
      icon: 'ri:mail-send-line',
      visible: true,
    },
    {
      label: 'Pricing',
      path: '/pricing',
      icon: 'arcticons:priceconverter',
      visible: true,
    },
    {
      label: 'Contact Us',
      path: '/contact-us',
      icon: 'healthicons:contact-support',
      visible: true,
    },
    {
      label: 'Terms of Service',
      path: '/terms',
      icon: 'mdi:file-document-outline',
      visible: true,
    },
  ];

  //methods
  const handleNavigate = () => {
    if (userData?.access_token) {
      navigate('/app/dashboard');
    } else {
      navigate('/auth/register');
    }
  };

  return (
    <nav className="bg-white dark:bg-darkBackgroundCard mb-1 border-b border-[#f4f4f4] px-4 pt-[14px] pb-[10px] dark:border-[#374151] fixed left-0 right-0 top-0 z-[60]">
      <div className="md:mx-6 lg:mx-auto lg:max-w-[89%] xl:max-w-[87%]">
        <div className="flex justify-between ">
          <div className="flex justify-between items-center ">
            {screen.lt.md() && (
              <button
                onClick={() => setIsDrawerVisible(!isDrawerVisible)}
                className="p-2 mr-2 text-gray-600 rounded-lg cursor-pointer hover:text-gray-900 hover:bg-gray-100 focus:bg-gray-100 dark:focus:bg-gray-700 focus:ring-2 focus:ring-gray-100 dark:focus:ring-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
              >
                <Icon icon="streamline:interface-setting-menu-1-button-parallel-horizontal-lines-menu-navigation-three-hamburger" />
              </button>
            )}
            <div className="flex items-center justify-between cursor-pointer" onClick={() => navigate('/')}>
              <Logo className="h-10" icon={screen.gt.sm() && screen.lt.lg() ? true : false} />
            </div>
          </div>
          <div className="hidden md:flex place-items-center ">
            <ul className="flex flex-row flex-2 items-center md:gap-6  lg:gap-12 xl:gap-16">
              {menuItems
                .filter((item) => item.visible)
                .map((item) => (
                  <li key={item.path}>
                    <Link
                      to={item.path}
                      className={`cursor-pointer text-base font-medium transition-all duration-75 ease-in ${getActiveClasess(item.path)}`}
                    >
                      <span className="hover:text-primaryPurple dark:hover:text-primaryPurple max-md:text-xs max-lg:text-xs md:text-sm  2xl:text-base lg:text-base">
                        {item.label}
                      </span>
                    </Link>
                  </li>
                ))}
            </ul>
          </div>

          <div className="flex items-center gap-4 md:gap-3 xl:gap-4">
            {/* <!-- Dark --> */}
            {/* {showThemeIcon && (
              <button
                type="button"
                className="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 flex items-center justify-center"
                onClick={switchDarkMode}
              >
                <Icon icon={isDark ? 'ic:outline-light-mode' : 'ic:outline-dark-mode'} width="20px" />
              </button>
            )} */}

            {!location.pathname.includes('login') && (
              <Button
                label={!userData?.access_token ? 'Login' : 'View Account'}
                onClick={() => navigate('/auth/login')}
                type="button"
                className={hideTitleOnSM && 'hidden md:block'}
              />
            )}

            {!location.pathname.includes('register') && !userData?.access_token && (
              <Button label="Signup" onClick={() => navigate('/auth/register')} type="button" className={hideTitleOnSM && 'hidden md:block'} />
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};
