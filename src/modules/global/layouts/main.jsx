import React, { useEffect, useState } from 'react';
import { Outlet } from 'react-router-dom';
import { useLocation } from 'react-router-dom';

// Hooks
import { useDarkMode, useScreenSize } from '/src';

// Components
import { Header } from '../components/header';
import { Sidebar } from '../components/sidebar';
import { Footer } from '../components/footer';

export const GlobalLayout = () => {
  // State
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);

  // Hooks
  const screen = useScreenSize();
  const location = useLocation();

  // Methods
  const { setCachedDarkMode } = useDarkMode();

  // On Mount
  // useEffect(() => {
  //   setCachedDarkMode();
  // }, []);

  // Effect
  useEffect(() => {
    if (!screen.lt.md()) {
      setIsDrawerVisible(false);
    }
  }, [screen.size]);

  return (
    <div className="flex flex-col min-h-screen antialiased">
      {/* Header */}
      <Header showThemeIcon={true} isDrawerVisible={isDrawerVisible} setIsDrawerVisible={setIsDrawerVisible} hideTitleOnSM={true} />

      {/* <!-- Sidebar --> */}
      <Sidebar isDrawerVisible={isDrawerVisible} setIsDrawerVisible={setIsDrawerVisible} />

      {/* Content */}
      <main
        className={`flex-1 transition-all pt-24 p-4 ${isDrawerVisible && screen.gt.lg() ? 'ml-64' : ''}`}
        onClick={() => (screen.lt.lg() ? setIsDrawerVisible(false) : {})}
      >
        <Outlet />

        {/* Overlaw */}
        {isDrawerVisible && screen.lt.xl() && <div className="absolute z-50 top-0 left-0 right-0 bottom-0" />}
      </main>

      {/* footer  */}
      <Footer />
    </div>
  );
};
