import React, { useContext, useEffect, useState } from 'react';
import { Outlet } from 'react-router-dom';
import { useLocation } from 'react-router-dom';

// Hooks
import { useDarkMode, useScreenSize } from '/src';

// Context
import { AppContext } from '/src/components/provider';

// Components
import { AppHeader } from '../components/header';
import { AppSidebar } from '../components/sidebar';
import { AppBreadcrumb } from '../components/breadcrumb';
// import { AppJumbotron } from '../components/jumbotron';

export const AppMainLayout = () => {
  // Context
  const { userData, sidebarFilter, setSidebarFilter, sidebarSearch, setSidebarSearch } = useContext(AppContext);

  // State
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const location = useLocation();
  const currentUrl = location.pathname;

  // Hooks
  const screen = useScreenSize();
  // Methods
  const { setCachedDarkMode } = useDarkMode();
  const isReport = currentUrl.includes('pdf');

  // On Mount
  // useEffect(() => {
  //   setCachedDarkMode();
  // }, []);

  useEffect(() => {
    if (screen.lt.xl()) {
      setIsDrawerVisible(false);
    } else if (isReport) {
      setIsDrawerVisible(false);
    } else {
      setIsDrawerVisible(true);
    }
  }, [screen.size]);

  // Use these styles to make black overlay (less than 1280px) visible and not scrollable
  // Make the scroll in list pages only be smooth
  useEffect(() => {
    if (isDrawerVisible && screen.lt.xl()) {
      document.querySelector('html').classList.add('overflow-x-hidden');
    } else {
      document.querySelector('html').classList.remove('overflow-x-hidden');
    }
  }, [isDrawerVisible]);

  return (
    <div className="min-h-screen antialiased">
      {/* Header */}
      <AppHeader isDrawerVisible={isDrawerVisible} setIsDrawerVisible={setIsDrawerVisible} />

      {/* <!-- Sidebar --> */}
      <AppSidebar isDrawerVisible={isDrawerVisible} setIsDrawerVisible={setIsDrawerVisible} />

      {/* Content */}
      <main
        className={`transition-all pt-[70px] ${isReport ? '' : 'p-4'}  ${isDrawerVisible && screen.gt.lg() ? 'pl-[240px]' : ''}`}
        onClick={() => (screen.lt.xl() ? setIsDrawerVisible(false) : {})}
      >
        <div>
          {/* <!-- Breadcrumb --> */}
          <AppBreadcrumb />

          {/* <!-- Jumbotron --> */}
          {/* <AppJumbotron /> */}

          <Outlet />
        </div>

        {/* Overlay */}
        {isDrawerVisible && screen.lt.xl() && <div className="absolute z-50 top-0 left-0 right-0 bottom-0 bg-black/50" />}
      </main>
    </div>
  );
};
