import React from 'react';
import { Navigate } from 'react-router-dom';
import { APP_ROUTE_PATH } from '/src/configs/router';

import { ProtectedRoute } from '/src/modules/auth/components/protected-route';
import { AppMainLayout } from '../layouts/main';

// Modules
import dashboardPages from '../modules/dashboard/routes';
import dashboardSuperAdminPages from '../modules/dashboard-super-admin/routes';
import organizationsPages from '../modules/organizations/routes';
import plansPages from '../modules/plans/routes';
import quizzesPages from '../modules/quizzes/routes';
import questionsPages from '../modules/questions/routes';
import submissionsPages from '../modules/submissions/routes';
import applicantsPages from '../modules/applicants/routes';
import rolesPages from '../modules/roles/routes';
import usersPages from '../modules/users/routes';
import interviewsPages from '../modules/interviews/routes';
import phoneScreening from '../modules/phone-screening/routes';
import assessments from '../modules/assessments/routes';
import avatarsPages from '../modules/avatars/routes';
import assessmentReportPages from '../modules/assessment-report/routes';
// Temp for test
import { StudioPage } from '../modules/studio';

export default [
  {
    path: APP_ROUTE_PATH,
    element: (
      <ProtectedRoute>
        <AppMainLayout />
      </ProtectedRoute>
    ),
    children: [
      // Default
      {
        path: '',
        element: <Navigate to="/app/dashboard" />,
      },

      // Modules
      ...dashboardPages,
      ...dashboardSuperAdminPages,
      ...organizationsPages,
      ...plansPages,
      ...rolesPages,
      ...questionsPages,
      ...quizzesPages,
      ...submissionsPages,
      ...interviewsPages,
      ...applicantsPages,
      ...usersPages,
      ...phoneScreening,
      ...assessments,
      ...avatarsPages,
      ...assessmentReportPages,

      // Studio
      {
        path: '/app/studio',
        element: <StudioPage />,
      },
    ],
  },
];
