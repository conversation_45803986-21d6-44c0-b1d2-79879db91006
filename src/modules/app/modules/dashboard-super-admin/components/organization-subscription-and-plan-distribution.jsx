// Core
import { useEffect, useState } from 'react';
import { ChartsDonut, Icon, useNotify, Api } from '/src';

export const OrganizationSubscriptionAndPlanDistribution = () => {
  const [subscriptionData, setSubscriptionData] = useState(null);
  const { notify } = useNotify();

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get('superAdmin/subscription/overview');
      setSubscriptionData(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  // !*guide to define colors for plans or if there is any incoming plan
  const getColorByIndex = (index) => {
    const colors = ['#D4C8FB', '#AC7FF4', '#7F7FF4', '#4F4FD9', '#3939B8'];
    return colors[index % colors.length];
  };

  //!*guide  Process the data for the chart
  const getChartData = () => {
    if (!subscriptionData || !subscriptionData.subscriptionStatistics.length) return [];

    const plans = subscriptionData.subscriptionStatistics[0]?.plans || [];
    return plans.map((plan, index) => ({
      name: plan.planName,
      value: plan.percentage,
      color: getColorByIndex(index),
    }));
  };

  // metrics data for the bottom section of the chart
  const getMetricsData = () => {
    if (!subscriptionData || !subscriptionData.plansDistribution.length) return [];

    const planDistribution = subscriptionData.plansDistribution[0] || {};

    const metrics = [
      {
        name: 'Renewals',
        value: planDistribution.renewalPercentage || 0,
        gain: planDistribution.renewalPercentage > 0,
        lose: planDistribution.renewalPercentage < 0,
      },
      {
        name: 'Upgrade',
        value: planDistribution.upgradePercentage || 0,
        gain: planDistribution.upgradePercentage > 0,
        lose: planDistribution.upgradePercentage < 0,
      },
      {
        name: 'Downgrade',
        value: planDistribution.downgradePercentage || 0,
        gain: planDistribution.downgradePercentage > 0,
        lose: planDistribution.downgradePercentage < 0,
      },
      {
        name: 'Cancelation',
        value: planDistribution.cancellationPercentage || 0,
        gain: false, // Cancelation is typically not seen as a positive
        lose: planDistribution.cancellationPercentage > 0,
      },
    ];

    return metrics.filter((metric) => metric.value !== undefined);
  };

  const centeredTextOfChart = () => (
    <>
      {subscriptionData?.subscriptionStatistics && subscriptionData.subscriptionStatistics.length > 0 && (
        <div>
          <div className="text-2xl font-bold dark:text-white">{subscriptionData.subscriptionStatistics[0]?.totalCount || 0}</div>
          <div className="text-sm text-gray-500">Subscriptions</div>
        </div>
      )}
    </>
  );

  const rightData = () => {
    const chartData = getChartData();
    return chartData.map((item) => (
      <div key={item.name} className="flex items-center space-y-0.5 dark:text-white">
        <div className="size-3 mr-3 mt-0.5 rounded-sm" style={{ backgroundColor: item.color }}></div>
        <span className="w-32 mr-2">{item.name}</span>
        <span className="font-medium">{item.value}%</span>
      </div>
    ));
  };

  const bottomData = () => {
    const metricsData = getMetricsData();
    return (
      <div className="grid grid-cols-1 xssm:grid-cols-2 sm:grid-cols-4 gap-4">
        {metricsData.map((metric) => (
          <div key={metric.name} className="space-y-2">
            <div className="text-sm font-medium text-gray-500 dark:text-white">{metric.name}</div>
            <div className="flex items-center gap-6">
              <div className="font-bold text-gray-700 dark:text-white">{metric.value}%</div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  useEffect(() => {
    handleGet();
  }, []);
  const chartData = getChartData();

  return (
    <div className="w-full h-full">
      {false ? (
        <ChartsDonut
          data={chartData}
          centeredTextOfChart={centeredTextOfChart}
          rightData={rightData}
          rightDataStyles="xslg:w-2/5"
          bottomData={bottomData}
          innerRadius={60}
        />
      ) : (
        <div className={`flex flex-col mb-10 items-center text-center justify-center h-full w-full `}>
          <Icon icon="iconoir:warning-circle" className="dark:text-gray-500 text-gray-400" width="50" />
          <p className={`text-gray-400 mt-2 text-base`}>No Organization Subscriptions & Plan Distribution Found</p>
        </div>
      )}
    </div>
  );
};
