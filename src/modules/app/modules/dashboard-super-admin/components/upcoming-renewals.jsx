// Core
import { useEffect, useState } from 'react';

import { Icon, useNotify, Api, Enums, EnumText, NoDataFound } from '/src';

export const UpcomingRenewals = () => {
  // Hooks
  const notify = useNotify();
  // state
  const [upcomingRenewalsData, setUpcomingRenewalsData] = useState([]);

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get(`superAdmin/organizations/subscriptions`);

      setUpcomingRenewalsData(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  // Effects
  useEffect(() => {
    handleGet();
  }, []);
  return (
    <div className="grid  gap-4 px-1 py-3">
      {' '}
      {/* grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 */}
      {false ? (
        upcomingRenewalsData?.upcomingRenewal?.map((item) => (
          <div key={item._id} className="px-4 py-5 border border-gray-200 dark:border-gray-600 rounded-3xl shadow-lg space-y-4">
            <div className="flex gap-3">
              <div>
                <p className="text-gray-600 dark:text-white text-lg font-semibold">{item.organizationName}</p>
                <p className="text-gray-400 text-sm">{item.planName}</p>
              </div>
            </div>
            <div className="flex gap-2  xssm:justify-between justify-evenly dark:text-white">
              <div className="flex items-center gap-1 border rounded-lg px-3 py-1.5 text-sm">
                <span className="text-md font-semibold">{item.daysLeft}</span>
                <span className="text-nowrap">days left</span>
              </div>

              <div className="flex items-center gap-1 text-nowrap">
                <span className="text-md font-semibold">{item.currency}</span>
                <span className="text-md font-semibold">{item.price}</span>

                <span className="text-sm text-gray-500 dark:text-white pl-1">
                  <EnumText name="BillingCycle" value={item.billingCycle} />
                </span>
              </div>
            </div>
          </div>
        ))
      ) : (
        <div className={`flex flex-col m-0 items-center text-center justify-center h-full w-full `}>
          <Icon icon="iconoir:warning-circle" className="dark:text-gray-500 text-gray-400" width="50" />
          <p className={`text-gray-400 mt-2`}>No Upcoming Renwals Found</p>
        </div>
      )}
    </div>
  );
};
