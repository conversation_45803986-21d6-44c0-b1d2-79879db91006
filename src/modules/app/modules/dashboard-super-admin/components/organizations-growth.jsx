// Core
import { useEffect, useState } from 'react';
import { ChartsWavy, useNotify, Api } from '/src';

export const OrganizationsGrowth = () => {
  const notify = useNotify();
  // state
  const [growthData, setGrowthData] = useState(null);

  // Methods
  const getMonthName = (monthNumber, locale = 'en-US') => {
    // Adjust month number to start from 1 (January) instead of 0
    const date = new Date(2023, monthNumber - 1, 1);
    return date.toLocaleString(locale, { month: 'long' });
  };

  const handleGet = async () => {
    try {
      const response = await Api.get('superAdmin/organizations/growth');
      setGrowthData(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  const formatChartData = () => {
    if (growthData) {
      // First, create a placeholder entry
      const result = [
        {
          x: '', // Placeholder label
          a: 0, // Zero value to not affect the chart scale
        },
      ];

      // Then add entries for all months (1-12)
      for (let monthNumber = 1; monthNumber <= 12; monthNumber++) {
        // Find the data for this month, or use a default value
        const monthData = growthData.find((item) => item.month === monthNumber) || { month: monthNumber, count: 0 };

        result.push({
          x: getMonthName(monthData.month),
          a: monthData.count,
        });
      }

      return result;
    }
    return [];
  };

  const chartLabels = {
    a: 'count',
  };
  // effect
  useEffect(() => {
    handleGet();
  }, []);

  const chartdata = formatChartData();
  return (
    <div className="p-2">
      <div className="-ml-8">
        <ChartsWavy data={chartdata} dataKeys={chartLabels} />
      </div>
    </div>
  );
};
