// Core
import { useEffect, useState } from 'react';
import { ChartsDonut, Icon, useNotify, Api } from '/src';

export const OrganizationsCards = () => {
  // state
  const [oranizationEngagementData, setOranizationEngagementData] = useState([]);
  const { notify } = useNotify();
  // Methods
  const handleGet = async () => {
    try {
      const organizationsResponse = await Api.get('superAdmin/organizations');
      const engagementResponse = await Api.get('superAdmin/engagement');

      const formatedData = [
        {
          title: 'Total Organizations',
          value: organizationsResponse.data?.totalCount,
          icon: (
            <svg width="35" height="36" viewBox="0 0 35 36" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                opacity="0.21"
                d="M0 18C0 8.33502 7.83502 0.5 17.5 0.5C27.165 0.5 35 8.33502 35 18C35 27.665 27.165 35.5 17.5 35.5C7.83502 35.5 0 27.665 0 18Z"
                fill="#8280FF"
              />
              <path
                opacity="0.587821"
                fillRule="evenodd"
                clipRule="evenodd"
                d="M12.0554 14.1111C12.0554 15.8293 13.4483 17.2222 15.1665 17.2222C16.8848 17.2222 18.2776 15.8293 18.2776 14.1111C18.2776 12.3929 16.8848 11 15.1665 11C13.4483 11 12.0554 12.3929 12.0554 14.1111ZM19.8332 17.2224C19.8332 18.5111 20.8779 19.5557 22.1665 19.5557C23.4552 19.5557 24.4999 18.5111 24.4999 17.2224C24.4999 15.9337 23.4552 14.8891 22.1665 14.8891C20.8779 14.8891 19.8332 15.9337 19.8332 17.2224Z"
                fill="#8280FF"
              />
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M15.1535 18.7778C11.4813 18.7778 8.46848 20.6651 8.16701 24.3772C8.15059 24.5794 8.53728 25.0001 8.73234 25.0001H21.5806C22.1649 25.0001 22.174 24.5299 22.1649 24.3778C21.937 20.5614 18.8774 18.7778 15.1535 18.7778ZM26.4101 25.0003H23.4112C23.4111 23.2496 22.8325 21.6339 21.8561 20.3341C24.5058 20.363 26.6693 21.7026 26.8321 24.5337C26.8386 24.6477 26.8321 25.0003 26.4101 25.0003Z"
                fill="#8280FF"
              />
            </svg>
          ),
          customChild: (
            <div className="flex flex-wrap gap-5">
              <div className="flex items-center gap-1">
                <div className="w-[10px] h-[10px] bg-[#4880FF] rounded-full"></div>
                <span className="text-sm dark:text-white">
                  New
                  <span className="text-base ml-2 font-semibold">{organizationsResponse.data.lastNewCount}</span>
                </span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-[10px] h-[10px] bg-[#52C93F] rounded-full"></div>
                <span className="text-sm dark:text-white">
                  Active
                  <span className="text-base ml-2 font-semibold">{organizationsResponse.data.active}</span>
                </span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-[10px] h-[10px] bg-[#FF0000] rounded-full"></div>
                <span className="text-sm dark:text-white">
                  At Risk <span className="text-base ml-2 font-semibold">{organizationsResponse.data.lowEngagement}</span>
                </span>
              </div>
            </div>
          ),
        },
        {
          title: 'Feature Engagement',
          value: `${engagementResponse.data.current}%`,
          differencePercentage: engagementResponse.data.current - engagementResponse.data.lastMonthScore,
          icon: (
            <svg width="35" height="36" viewBox="0 0 35 36" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                opacity="0.21"
                fillRule="evenodd"
                clipRule="evenodd"
                d="M35 18C35 27.665 27.165 35.5 17.5 35.5C7.83502 35.5 0 27.665 0 18C0 8.33502 7.83502 0.5 17.5 0.5C27.165 0.5 35 8.33502 35 18Z"
                fill="#4AD991"
              />
              <path
                d="M11.1481 24.3518H24.7592C25.2603 24.3518 25.6666 24.7581 25.6666 25.2593C25.6666 25.7604 25.2603 26.1667 24.7592 26.1667H10.2407C9.73951 26.1667 9.33325 25.7604 9.33325 25.2593V10.7407C9.33325 10.2396 9.73951 9.83333 10.2407 9.83333C10.7418 9.83333 11.1481 10.2396 11.1481 10.7407V24.3518Z"
                fill="#4AD991"
              />
              <path
                opacity="0.5"
                d="M14.5323 20.4352C14.1895 20.8008 13.6153 20.8194 13.2497 20.4766C12.8841 20.1339 12.8656 19.5596 13.2083 19.194L16.6111 15.5644C16.9426 15.2108 17.4934 15.1801 17.8621 15.4947L20.5478 17.7865L24.047 13.3542C24.3575 12.9609 24.9281 12.8937 25.3215 13.2043C25.7148 13.5148 25.7819 14.0854 25.4714 14.4787L21.3881 19.651C21.0691 20.055 20.4784 20.1131 20.0868 19.779L17.3428 17.4374L14.5323 20.4352Z"
                fill="#4AD991"
              />
            </svg>
          ),
          customChild: (
            <div className="space-y-1">
              <span className="text-[#656575] dark:text-[#d1d1d1] text-sm">
                last month usage
                <span className="text-base ml-2 font-semibold">{engagementResponse.data.lastMonthScore}%</span>
              </span>
              {/* <button className="px-2 py-1 bg-[#F4F5F7] dark:bg-[#374151] text-sm text-[#656575] dark:text-white w-fit h-fit rounded-sm">
                View Low Engagement Organizations
              </button> */}
            </div>
          ),
        },
        {
          tempPlaceHolder: true,
          title: 'Total Tickets',
          value: '100',
          differencePercentage: -2.5,
          icon: (
            <svg width="35" height="35" viewBox="0 0 35 35" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                opacity="0.21"
                fillRule="evenodd"
                clipRule="evenodd"
                d="M35 17.5C35 27.165 27.165 35 17.5 35C7.83502 35 0 27.165 0 17.5C0 7.83502 7.83502 0 17.5 0C27.165 0 35 7.83502 35 17.5Z"
                fill="#FFD78A"
              />
              <path
                d="M22.4887 9.00351H18.098L8.00366 19.071L15.9118 26.9966L25.9962 16.9385V12.5035L22.4887 9.00351ZM25.3299 16.6616L15.913 26.0535L8.94616 19.0716L18.373 9.66913H22.2124L25.3299 12.7791V16.661V16.6616Z"
                fill="#E84C29"
              />
              <path
                d="M20.667 12.9991C20.667 13.7323 21.2645 14.3291 22.0001 14.3291C22.7357 14.3291 23.3332 13.7329 23.3332 12.9991C23.3332 12.2654 22.7357 11.6685 22.0001 11.6685C21.2645 11.6685 20.667 12.2654 20.667 12.9991ZM22.6664 12.9991C22.6664 13.3648 22.367 13.6629 22.0001 13.6629C21.6332 13.6629 21.3339 13.3654 21.3339 12.9991C21.3339 12.6329 21.6326 12.3348 22.0001 12.3348C22.3676 12.3348 22.6664 12.6329 22.6664 12.9991Z"
                fill="#E84C29"
              />
            </svg>
          ),
          customChild: (
            <div className="space-y-2">
              <div className="flex gap-5">
                <div className="flex items-center gap-1">
                  <div className="w-[10px] h-[10px] bg-[#4880FF] rounded-full"></div>
                  <p className="text-sm dark:text-white">New 10</p>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-[10px] h-[10px] bg-[#52C93F] rounded-full"></div>
                  <p className="text-sm dark:text-white">Active 690</p>
                </div>
              </div>
              <div className="flex gap-2 justify-between">
                <p className="text-[#656575] dark:text-[#d1d1d1] text-sm"> Avg. response time 2 days</p>
                <button className="px-3 py-1 bg-[#F4F5F7] dark:bg-[#374151] text-sm text-[#656575] dark:text-white w-fit h-fit rounded-sm">
                  View
                </button>
              </div>
            </div>
          ),
        },
        {
          tempPlaceHolder: true,
          title: 'Churn Rate',
          value: '40%',
          differencePercentage: -2.5,
          icon: (
            <svg width="35" height="35" viewBox="0 0 35 35" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                opacity="0.3"
                fillRule="evenodd"
                clipRule="evenodd"
                d="M35 17.5C35 27.165 27.165 35 17.5 35C7.83502 35 0 27.165 0 17.5C0 7.83502 7.83502 0 17.5 0C27.165 0 35 7.83502 35 17.5Z"
                fill="#FF9066"
              />
              <path
                opacity="0.78"
                fillRule="evenodd"
                clipRule="evenodd"
                d="M16.6922 14.0114C16.7092 13.7901 16.8937 13.6192 17.1157 13.6192C17.3339 13.6192 17.5166 13.7846 17.5384 14.0017L17.889 17.5081L20.4038 18.9451C20.5321 19.0184 20.6112 19.1548 20.6112 19.3025C20.6112 19.5739 20.3531 19.771 20.0913 19.6996L16.7321 18.7835C16.5007 18.7204 16.3467 18.5019 16.3651 18.2627L16.6922 14.0114Z"
                fill="#FF9066"
              />
              <path
                opacity="0.901274"
                fillRule="evenodd"
                clipRule="evenodd"
                d="M13.5206 9.05809C13.2565 8.74334 12.7466 8.86381 12.6512 9.26347L11.8594 12.5833C11.7817 12.9089 12.0398 13.2174 12.374 13.1985L15.7891 13.005C16.2 12.9817 16.4084 12.4996 16.1439 12.1844L15.3602 11.2504C16.0396 11.0182 16.7601 10.897 17.4999 10.897C21.1511 10.897 24.111 13.8569 24.111 17.5081C24.111 21.1593 21.1511 24.1192 17.4999 24.1192C13.8487 24.1192 10.8888 21.1593 10.8888 17.5081C10.8888 16.8951 10.9718 16.2948 11.1337 15.7178L9.63591 15.2977C9.43871 16.0007 9.33325 16.7421 9.33325 17.5081C9.33325 22.0184 12.9896 25.6747 17.4999 25.6747C22.0102 25.6747 25.6666 22.0184 25.6666 17.5081C25.6666 12.9977 22.0102 9.34141 17.4999 9.34141C16.3655 9.34141 15.285 9.57272 14.3032 9.99077L13.5206 9.05809Z"
                fill="#FF9066"
              />
            </svg>
          ),
          customChild: (
            <div className="flex items-center gap-3">
              <p className="text-[#656575] text-sm dark:text-[#d1d1d1]">80% of churned orgs reported poor test quality</p>
              <button className="px-3 py-1 bg-[#F4F5F7] dark:bg-[#374151] text-sm text-[#656575] dark:text-white w-fit h-fit rounded-sm">View</button>
            </div>
          ),
        },
        ,
      ];

      setOranizationEngagementData(formatedData);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  useEffect(() => {
    handleGet();
  }, []);

  return (
    <>
      {Array.isArray(oranizationEngagementData) &&
        oranizationEngagementData.map((blockData) => (
          <div className="h-full space-y-4 p-4 dark:border dark:border-gray-600 rounded-xl shadow-[0_20px_50px_rgba(0,0,0,0.06)]">
            {/* Header */}
            <div className="flex justify-between items-start border-b border-[#A3A3A333] pb-3">
              <p className="text-[#656575] dark:text-white text-lg font-medium">{blockData?.title}</p>
              {blockData?.icon}
            </div>
            {/* Middle Body */}
            {blockData.tempPlaceHolder ? (
              <div className={`flex flex-col text-center `}>
                <Icon icon="iconoir:warning-circle" className="dark:text-gray-500 text-gray-400" width="50" />
                <p className={`text-gray-400 mt-2`}>No Upcoming Renwals Found</p>
              </div>
            ) : (
              <>
                <div className="flex flex-wrap gap-2 justify-between items-center">
                  <p className="text-3xl font-bold dark:text-white">{blockData.value}</p>
                  {blockData.differencePercentage !== undefined && !isNaN(blockData.differencePercentage) && (
                    <div className="flex gap-3">
                      <p className={`${blockData.differencePercentage > 0 ? 'text-[#52C93F]' : 'text-[#FF0000]'}`}>
                        {blockData.differencePercentage > 0 ? <span>&uarr;</span> : <span>&darr;</span>} {Math.abs(blockData.differencePercentage)}%
                      </p>
                      <p className="text-[#606060] dark:text-[#d1d1d1]">past week</p>
                    </div>
                  )}
                </div>
                {/* Custom Child */}
                <div>{blockData.customChild}</div>
              </>
            )}
          </div>
        ))}
    </>
  );
};
