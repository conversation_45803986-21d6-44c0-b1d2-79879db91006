import { useEffect, useState } from 'react';
import { isValid, format } from 'date-fns';

import { useNotify, Api, Table, Icon } from '/src';
import { Tooltip } from 'flowbite-react';

export const OrganizationsAtRisk = () => {
  const [data, setData] = useState();

  const { notify } = useNotify();

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get('superAdmin/organizations/risk');
      setData(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  const formatDate = (customDate) => {
    const date = new Date(customDate || Date.now());
    if (!isValid(date)) {
      return 'Invalid date';
    }
    return format(date, 'dd MMMM , yyyy');
  };

  const handleEnagement = (engagement) => {
    if (engagement === 'low') {
      return {
        text: 'low',
        color: 'text-red-700',
        background: 'bg-red-100',
        border: 'border-red-300',
      };
    } else if (engagement === 'medium') {
      return {
        text: 'medium',
        color: 'text-orange-700',
        background: 'bg-orange-100',
        border: 'border-orange-300',
      };
    } else if (engagement === 'high') {
      return {
        text: 'high',
        color: 'text-green-700',
        background: 'bg-green-100',
        border: 'border-green-300',
      };
    } else if (engagement === 'inactive') {
      return {
        text: 'inactive',
        color: 'text-gray-700',
        background: 'bg-gray-100',
        border: 'border-gray-300',
      };
    } else if (engagement === 'active') {
      return {
        text: 'active',
        color: 'text-green-700',
        background: 'bg-green-100',
        border: 'border-green-300',
      };
    } else {
      return {
        text: '—',
        color: 'text-gray-700',
        background: 'bg-gray-100',
        border: 'border-gray-300',
      };
    }
  };

  useEffect(() => {
    handleGet();
  }, []);

  return (
    <>
      <Table
        ready={!!data}
        loading={!data}
        hideJumbotron={true}
        columns={[
          {
            key: 'organization',
            label: 'Organization Name',
            width: '20%',
            primary: true,
          },
          {
            key: 'email',
            label: 'Organization Email',
            width: '20%',
            primary: true,
          },
          {
            key: 'plan',
            label: 'Plan',
            width: '20%',
            primary: true,
          },
          {
            key: 'employees',
            label: 'Employees',
            width: '15%',
          },
          {
            key: 'engagement',
            label: 'Engagement',
            width: '15%',
          },
          {
            key: 'lastActive',
            label: 'Last Active',
            width: '25%',
          },
        ]}
        rows={
          data?.slice(0, 5)?.map((item) => ({
            _id: item.id,
            organization: item.name,
            plan: item.subscriptionPlanName,
            employees: item.numberOfUsers,
            engagement: item.engagement,
            lastActive: item.lastActive,
            ...item,
          })) || []
        }
        slots={{
          organization: (_, row) => (
            <div className="flex gap-3 items-center">
              <img src="/images/avatar-male-img.png" className="w-10 rounded-full border" alt="Logo" />
              <p className="capitalize font-medium">{row.name}</p>
            </div>
          ),
          email: (_, row) => (
            <div className="flex relative gap-2">
              <div className="w-full">
                <div className="flex items-center gap-2 relative">
                  <div className="truncate max-w-[85%]">
                    {row?.email ? (
                      <Tooltip
                        content={row.email}
                        placement="bottom"
                        arrow={false}
                        className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                      >
                        <span className="text-gray-600 truncate">{row.email}</span>
                      </Tooltip>
                    ) : (
                      <span className="text-gray-600">—</span>
                    )}
                  </div>
                  {row?.email && (
                    <span
                      onClick={() => {
                        navigator.clipboard.writeText(row.email);
                        notify('Email copied');
                      }}
                      className="inline-block cursor-pointer text-gray-500"
                    >
                      <Tooltip
                        content="Copy Email"
                        placement="bottom"
                        arrow={false}
                        className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                      >
                        <Icon icon="ooui:copy-ltr" className="relative text-[#798296] text-base" />
                      </Tooltip>
                    </span>
                  )}
                </div>
              </div>
            </div>
          ),
          plan: (_, row) => (
            <>
              <p className="capitalize text-black dark:text-white text-nowrap">{row.subscriptionPlanName}</p>
              <p className="text-nowrap">{row.endDate}</p>
            </>
          ),
          engagement: (_, row) => (
            <div
              className={`${handleEnagement(row.engagement).background} ${handleEnagement(row.engagement).border} ${
                handleEnagement(row.engagement).color
              } rounded-full w-fit px-2 py-0.5 border font-medium capitalize text-sm`}
            >
              {row.engagement}
            </div>
          ),
          lastActive: (_, row) => (
            <div className="flex gap-2 items-center">
              <p className="text-lg">{formatDate(row.lastActive)}</p>
              {/* <div
                className={`${handleEnagement('inactive').background} ${handleEnagement('inactive').border} ${
                  handleEnagement('inactive').color
                } rounded-full w-fit px-2 py-0.5 border capitalize text-sm`}
              >
                {handleEnagement('inactive').text}
              </div> */}
            </div>
          ),
        }}
        rowKey="_id"
      />
    </>
  );
};
