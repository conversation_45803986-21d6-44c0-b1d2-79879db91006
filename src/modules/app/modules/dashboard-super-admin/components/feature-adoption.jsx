// Core
import { Icon, ChartsWavy } from '/src';

// Flowbite
import { Rating } from 'flowbite-react';
import { useEffect, useState } from 'react';
import { useNotify, Api } from '/src';

export const FeatureAdoption = () => {
  const [assessmentData, setAssessmentData] = useState(null);
  const { notify } = useNotify();

  // Methods

  const handleGet = async () => {
    try {
      const response = await Api.get('superAdmin/assessment/overview');
      setAssessmentData(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
      setData(allData);
    }
  };

  //  !* Transform API data into chart data format
  // !* guide  *to match data from backend to each line in chart
  const transformChartData = () => {
    if (!assessmentData?.customTestGrowth) return [];

    // !* destructing
    const { customTestCount, predefinedTestCount, interviewCount } = assessmentData.customTestGrowth;

    // Create data for all months (1-12)
    return Array.from({ length: 12 }, (_, i) => {
      const month = i + 1;
      const monthItem = customTestCount.find((item) => item.month === month);
      const interviewItem = interviewCount.find((item) => item.month === month);
      const predefinedItem = predefinedTestCount.find((item) => item.month === month);

      return {
        x: new Date(2024, month - 1).toLocaleString('default', { month: 'short' }),
        a: interviewItem?.count || 0,
        b: monthItem?.count || 0,
        c: predefinedItem?.count || 0,
      };
    });
  };
  const chartsdata = transformChartData();

  const chartLabels = {
    a: 'AI Interviews',
    b: 'Custom Assessments',
    c: 'Predefined Assessments',
  };

  const colorType = (type) => {
    if (type === 1) return 'bg-[#5DC6C0]';
    else if (type === 2) return 'bg-[#6599F7]';
    else if (type === 3) return 'bg-[#AF52DE]';
  };

  useEffect(() => {
    handleGet();
  }, []);

  const getFeatureData = () => {
    if (!assessmentData?.assessmentUsage) return [];
    const { interviewUsedPercentage, customTestUsagePercentage, predefinedTestUsagePercentage } = assessmentData.assessmentUsage;
    return [
      {
        type: 1,
        title: 'AI Interviews',
        percentage: interviewUsedPercentage,
      },
      {
        type: 2,
        title: 'Custom Assessments',
        percentage: customTestUsagePercentage,
      },
      {
        type: 3,
        title: 'Predefined Assessments',
        percentage: predefinedTestUsagePercentage,
      },
    ];
  };
  const featureData = getFeatureData();

  return (
    <div className="p-2">
      <div className="-ml-8">
        <ChartsWavy data={chartsdata} dataKeys={chartLabels} />
      </div>

      <div className="space-y-2">
        {featureData?.map((item) => {
          return (
            <div className="flex justify-between pt-2 dark:text-white text-sm font-medium">
              <div className="flex items-start gap-2.5">
                {item?.type && <p className={`size-3 ${colorType(item?.type)} rounded-[2px] mt-1`} />}
                <div className="space-y-1">
                  {item?.title && <p>{item?.title}</p>}
                  <div className="flex gap-2">
                    {item?.percentage && <p className="text-gray-500 text-xs">{item?.percentage}% Usage</p>}
                    {item?.percentage < 50 && <Icon icon="pajamas:warning-solid" className="text-[#E3C400]" width={14} />}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
