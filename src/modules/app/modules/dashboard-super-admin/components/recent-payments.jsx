// Core
import { useEffect, useState } from 'react';
import { Icon, useNotify, Api, Enums, EnumText } from '/src';

export const RecentPayments = () => {
  // Hooks
  const notify = useNotify();
  // state
  const [recentPaymentsData, setRecentPaymentsData] = useState([]);

  //  Methods
  const handleGet = async () => {
    try {
      const response = await Api.get(`superAdmin/organizations/subscriptions`);

      setRecentPaymentsData(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  // Effects
  useEffect(() => {
    handleGet();
  }, []);

  return (
    <div className="sm:flex 2xl:block gap-4 space-y-3 sm:space-y-0 2xl:space-y-3">
      {false ? (
        recentPaymentsData?.recentPayment?.map((itemData) => (
          <div className="py-3 px-4 w-full border border-[#E1E4E8] dark:border-[#4A5462] rounded-xl flex items-center gap-2">
            <div className="space-y-1 w-full">
              <div className="flex justify-between">
                <p className="font-medium dark:text-white">{itemData.organizationName}</p>
                <p className="dark:text-white">{itemData.price}</p>
              </div>
              <div className="flex justify-between">
                <p className="text-[#656575] dark:text-[#bebec2] text-sm">{itemData.planName}</p>
                <p className="text-[#656575] dark:text-[#bebec2] text-sm">
                  <EnumText name="BillingCycle" value={itemData.billingCycle} />
                </p>
              </div>
            </div>
          </div>
        ))
      ) : (
        <div className={`flex flex-col m-0 items-center text-center justify-center h-full w-full `}>
          <Icon icon="iconoir:warning-circle" className="dark:text-gray-500 text-gray-400" width="50" />
          <p className={`text-gray-400 mt-2`}>No Recent Payments Found</p>
        </div>
      )}
    </div>
  );
};
