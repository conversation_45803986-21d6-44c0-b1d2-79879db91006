// Core
import { ChartsLinear } from '/src';

export const SatisfactionRate = () => {
  const chartsData = [
    { x: 1, uptime: 30 },
    { x: 2, uptime: 55 },
    { x: 3, uptime: 30 },
    { x: 4, uptime: 75 },
    { x: 5, uptime: 65 },
    { x: 6, uptime: 90 },
    { x: 7, uptime: 95 },
    { x: 8, uptime: 85 },
    { x: 9, uptime: 80 },
    { x: 10, uptime: 90 },
  ];

  const chartsDataLabels = [{ dataKey: 'uptime', stroke: '#3b82f6', fill: 'url(#colorUptime)', name: 'Uptime' }];

  return (
    <div className="p-2">
      <div className="-ml-8">
        <ChartsLinear data={chartsData} labels={chartsDataLabels} hideLegend={true} />
      </div>
    </div>
  );
};
