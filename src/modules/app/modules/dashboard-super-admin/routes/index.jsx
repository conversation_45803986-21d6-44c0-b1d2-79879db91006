// React
import { Navigate, Outlet } from 'react-router-dom';

// Components
import { DashboardSuperAdminMainLayout } from '../layouts/main';
import { DashboardSuperAdmin } from '../pages';

export default [
  {
    path: 'dashboard-superadmin',
    element: <DashboardSuperAdminMainLayout />,
    children: [
      {
        path: '',
        element: <Navigate to="statistics" />,
      },
      {
        path: '',
        element: <Outlet />,
        loader() {
          return {
            label: 'Dashboard Super Admin',
          };
        },
        children: [
          {
            path: 'statistics',
            element: <DashboardSuperAdmin />,
            loader() {
              return {
                label: 'Statistics',
                icon: '',
                title: 'Welcome Back, Osama',
                subtitle: 'Here is the information about your organizations.',
              };
            },
          },
        ],
      },
    ],
  },
];
