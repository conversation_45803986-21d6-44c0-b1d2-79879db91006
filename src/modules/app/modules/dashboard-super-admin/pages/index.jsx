// Core
import { Icon, Jumbotron } from '/src';
import { useNavigate } from 'react-router-dom';

// Components
import { OrganizationsCards } from '../components/organizations-cards';
import { UpcomingRenewals } from '../components/upcoming-renewals';
import { RecentPayments } from '../components/recent-payments';
import { OrganizationsAtRisk } from '../components/organizations-at-risk';
import { OrganizationsGrowth } from '../components/organizations-growth';
import { OrganizationSubscriptionAndPlanDistribution } from '../components/organization-subscription-and-plan-distribution';
import { FeatureAdoption } from '../components/feature-adoption';
import { SatisfactionRate } from '../components/satisfaction-rate';
import { ComplaintReason } from '../components/complaint-reason';
import { SystemHealthStability } from '../components/system-health-stability';

export const DashboardSuperAdmin = () => {
  const navigate = useNavigate();

  // Function to navigate to organizations list with specific tab
  const navigateToOrganizationsWithTab = (tabIndex) => {
    navigate('/app/organizations/list', { state: { activeTab: tabIndex } });
  };

  const cardClassNames = 'p-3 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg';

  const CardTitle = ({ title, number, viewAll, viewAllPath, tabIndex }) => {
    return (
      <div className="flex justify-between">
        <div className="flex items-center gap-3">
          {title && <p className="font-semibold dark:text-white">{title}</p>}
          {number && <p className="px-1.5 py-0.5 border border-[#E9EAEB] bg-[#F9F5FF] rounded-full text-[#6941C6] text-xs font-medium">{number}</p>}
        </div>
        {viewAll && (
          <p
            className="flex justify-center items-center gap-1 text-gray-500 dark:text-white text-sm font-semibold cursor-pointer"
            onClick={() => {
              if (tabIndex !== undefined) {
                navigateToOrganizationsWithTab(tabIndex);
              } else {
                navigate(viewAllPath || '/app/organizations/list');
              }
            }}
          >
            View All
            <Icon icon="material-symbols:arrow-forward-ios-rounded" />
          </p>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <Jumbotron />

      {/* Statistics */}
      <main className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <OrganizationsCards />
      </main>

      {/* UpcomingRenewals */}
      <main className="grid grid-cols-1 2xl:grid-cols-9 gap-4">
        <div className={`h-full 2xl:col-span-6 ${cardClassNames}`}>
          <CardTitle title="Upcoming Renewals" number={''} viewAll tabIndex={1} />
          <UpcomingRenewals />
        </div>
        <div className={`h-full min-h-40 2xl:col-span-3 ${cardClassNames} space-y-4`}>
          <CardTitle title="Recent Payments" number={''} viewAll tabIndex={1} />
          <RecentPayments />
        </div>
      </main>

      <main className="grid grid-cols-1 gap-4">
        <div className={`h-full lg:col-span-6 ${cardClassNames}`}>
          <CardTitle title="Organizations at Risk" number={''} viewAll tabIndex={0} />
          <OrganizationsAtRisk />
        </div>
      </main>

      <main className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div className={`h-full ${cardClassNames}`}>
          <CardTitle title="Organizations Growth" />
          <OrganizationsGrowth />
        </div>
        <div className={`h-full ${cardClassNames}`}>
          <CardTitle title="Organization Subscriptions & Plan Distribution" />
          <OrganizationSubscriptionAndPlanDistribution />
        </div>
      </main>

      <main className="grid grid-cols-1 gap-4">
        {/* <div className={`h-full ${cardClassNames}`}>
          <CardTitle title="Available predefiend Assessment" />
        </div> */}
        <div className={`h-full ${cardClassNames}`}>
          <CardTitle title="Feature Adoption" />

          <FeatureAdoption />
        </div>
      </main>

      {/* <main className="grid grid-cols-1 lg:grid-cols-9 gap-4">
        <div className={`lg:col-span-6 ${cardClassNames}`}>
          <CardTitle title="Satisfaction Rate" />
          <SatisfactionRate />
        </div>
        <div className={`lg:col-span-3 ${cardClassNames}`}>
          <CardTitle title="Complaints Reasons" />
          <ComplaintReason />
        </div>
      </main>

      <main className="h-96 grid grid-cols-1 gap-4">
        <div className={`h-full lg:col-span-6 ${cardClassNames}`}>
          <CardTitle title="System Health & Stability" />
          <SystemHealthStability />
        </div>
      </main> */}
    </div>
  );
};
