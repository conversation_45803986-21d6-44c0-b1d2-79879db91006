import React, { useRef, useState } from 'react';
import { En<PERSON>, Button, ImageUploader, Jumbotron, useForm } from '/src';
import { UploadDialog } from './../../organizations/components/list/avatar/upload-dialog';

export const AvatarComponent = () => {
  // State
  const [uploadDiologVisible, setUploadDiologVisible] = useState(false);

  // Form state
  const { form, setFieldValue } = useForm({
    avatarName: Enums?.AiAvatarModel[0]?.value || '',
  });

  return (
    <>
      <div className="px-3 py-4 border border-[#F4F4F4] rounded-lg space-y-4">
        {/* <Jumbotron /> */}

        <h1 className="text-[35px] font-medium text-[#000000]">Avatars Management </h1>
        <span className="text-[#8c939f] text-lg">Create, customize, and manage avatars that represent your brand and team</span>
        <div className="flex items-center justify-between">
          <div className="flex gap-2 items-center">
            <p className="font-medium text-xl dark:text-white">Avatars</p>
            <div className="py-[1px] px-2 text-white bg-[#8D5BF8] rounded-lg text-md">{Enums?.AiAvatarModel.length}</div>
          </div>
          <Button label="Personalize Avatar" size="sm" onClick={() => setUploadDiologVisible(true)} />
        </div>
        <div className="grid xssm:grid-cols-2 md:grid-cols-4 gap-8">
          {Enums?.AiAvatarModel?.map((model) => (
            <div
              key={model.value}
              className="flex items-center p-2 rounded-xl cursor-pointer transition-colors bg-[#F8F5FF]"
              onClick={() => setFieldValue('avatarName')(model?.value)}
            >
              <img src={`/images/models/${model?.iconPathOut}`} alt={model.value} className=" h-28 w-28 rounded-full object-cover mr-2" />
              <div>
                <p className="text-xl font-medium capitalize">{model?.value}</p>
                <p className="text-base text-gray-600">Technical Manager</p>
              </div>
            </div>
          ))}
        </div>
      </div>
      {uploadDiologVisible && <UploadDialog onClose={() => setUploadDiologVisible(false)} />}
    </>
  );
};
