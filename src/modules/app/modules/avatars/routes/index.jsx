import { Navigate } from 'react-router-dom';
import { AvatarsMainLayout } from '../layouts/main';
import { AvatarComponent } from '../pages/list';

export default [
  {
    path: 'avatars',
    element: <AvatarsMainLayout />,

    children: [
      {
        path: '',
        element: <Navigate to="/app/avatars/list" />,
      },

      {
        path: 'list',
        element: <AvatarComponent />,
        loader() {
          return {
            icon: 'material-symbols:person-raised-hand-rounded',
            title: 'Avatars List',
            subtitle: 'Create, customize, and manage avatars that represent your brand and team.',
          };
        },
      },
    ],
  },
];
