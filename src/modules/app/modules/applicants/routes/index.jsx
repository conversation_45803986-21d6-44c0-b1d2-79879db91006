import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';

import { ApplicantsMainLayout } from '../layouts/main';

import { ApplicantsListPage } from '../pages/list';
import { ApplicantProgressSingle } from '../pages/single';

export default [
  {
    path: 'applicants',
    element: <ApplicantsMainLayout />,
    // loader() {
    //   return {
    //     label: 'Applicants',
    //   };
    // },
    children: [
      // Default
      {
        path: '',
        element: <Navigate to="/app/applicants/list" />,
      },

      // Routes
      {
        path: '',
        element: <Outlet />,
        loader() {
          return {
            label: 'Applicant Managament',
            icon: 'material-symbols:person-raised-hand-rounded',
            title: 'Applicant Management',
            subtitle: 'Review and manage all applicants',
          };
        },
        children: [
          {
            path: 'list',
            element: <ApplicantsListPage />,
          },

          {
            path: 'progress/:id',
            element: <ApplicantProgressSingle />,
            loader() {
              return {
                label: 'Profile',
                icon: 'material-symbols:person-raised-hand-rounded',
                title: 'Applicant',
                subtitle: 'Review and show progress',
              };
            },
          },
        ],
      },
    ],
  },
];
