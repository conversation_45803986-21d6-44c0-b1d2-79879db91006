// React
import { useState, useContext, useEffect } from 'react';

// Context
import { AppContext } from '/src/components/provider';

// Flowbite
import { Tooltip } from 'flowbite-react';

// React Suite
import 'rsuite/dist/rsuite-no-reset.min.css';
import { DatePicker, DateRangePicker } from 'rsuite';
const { beforeToday } = DateRangePicker;

// React icons
import { FaUserGraduate, FaUser, FaStar, FaMedal } from 'react-icons/fa';

// Components
import {
  Table,
  useFetchList,
  useNotify,
  useConfirmDialog,
  Api,
  Icon,
  useScreenSize,
  EnumText,
  ResultStatus,
  useForm,
  Form,
  MultiSelect,
  RadioGroup,
  Button,
  CustomIcon,
  SubscribeDialog,
  SidebarFilterDrawer,
  SidebarFilterPage,
} from '/src';
import { ApplicantsSingleDialog } from '../components/single-dialog';
import { AssignScreening } from '../components/assign-screening';
import { AssignTest } from '../components/assign-test';
import { AssignIntreview } from '../components/assign-interview';

/*
  drawerFilter={{
    element,             : UI Component
    count,               : All results
    filterCountNumber,   : Number of selected filters
    drawerClearAll,      : Form clear all function
    isShowDrawerFilter,  : State of toggle
    setShowDrawerFilter, : Fuction of toggling
  }}
*/

export const ApplicantsListPage = () => {
  // Hook
  const screen = useScreenSize();

  // State
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [needSubscription, setNeedSubscription] = useState(false);
  const [isAssignTestVisible, setAssignTestVisibility] = useState(false);
  const [isScreeningVisible, setIsScreeningVisible] = useState(false);
  const [isAssignInterviewTestVisible, setAssignInterviewTestVisible] = useState(false);
  const [applicantDetails, setApplicantDetails] = useState(false);
  const [handleGet, setHandleGet] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState({});

  const [backupList, setBackupList] = useState([]);
  const ORIGIN = window.location.origin;

  // User Data
  const { userData, sidebarFilter, setSidebarFilter, sidebarSearch, setSidebarSearch } = useContext(AppContext);
  const isPermitted = userData?.roles.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  // @FIXME: Needs to improve this logic
  const isSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));

  // Hooks
  const initialFilters = {
    // ...(userData.trackId
    //   ? {}
    //   : {
    //       category: {
    //         label: 'Interest',
    //         lookup: 'category',
    //       },
    //     }),
    // difficulty: {
    //   label: 'Difficulty',
    //   enum: 'QuestionDifficulty',
    // },
    seniortyLevel: {
      label: 'Seniorty Level',
      enum: 'QuizDifficulty',
    },
  };
  const filterFeedData = Object.keys(initialFilters);
  const { ready, loading, setLoading, list, count, filters, setFilters, search, pagination, refresh } = useFetchList('applicants/list', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: initialFilters,
  });
  const { notify } = useNotify();
  const { showConfirm, hideConfirm } = useConfirmDialog();

  const ConfirmText = (value) => {
    return (
      <div>
        <div className="flex mx-auto p-4 mb-7 bg-[#ddd1f8] w-24 h-24 rounded-full">
          <div className="flex mx-auto mb-7 bg-[#cab6f5] w-16 h-16 justify-center rounded-full">
            <Icon icon="hugeicons:archive-02" className="text-[#9061F9]" width="40" />
          </div>
        </div>
        {value ? (
          <p>
            Once confirmed, {value} applicant{value > 1 && 's'} will be archived permanently!
          </p>
        ) : (
          <p>Once confirmed, This applicant will be archived permanently!</p>
        )}
      </div>
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return '—';

    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      day: 'numeric',
      month: '2-digit',
      year: 'numeric',
    }).format(date);
  };

  const formatTime = (dateString) => {
    if (!dateString) return '';

    const date = new Date(dateString);
    const hours = date.getHours();
    const minutes = date.getMinutes();

    const period = hours >= 12 ? 'PM' : 'AM';
    const formattedHours = hours % 12 || 12;
    const formattedMinutes = minutes < 10 ? '0' + minutes : minutes;

    return `${formattedHours}:${formattedMinutes} ${period}`;
  };

  // Delete Applicant
  const handleDelete = async (row) => {
    showConfirm(ConfirmText(), {
      async onConfirm() {
        try {
          await Api.delete(`applicants/single/${row._id}`);
          hideConfirm();
          refresh(true);
          notify('Applicant deleted successfully!');
        } catch (error) {
          hideConfirm();
          notify.error(error.response.data.message);
        }
      },
    });
  };

  // Delete all selected ids
  const handleArchiveSelectedIds = async () => {
    if (selectedIds.length) {
      showConfirm(ConfirmText(selectedIds.length), {
        async onConfirm() {
          try {
            setLoading(true);
            await Api.delete('applicants/multi', { ids: selectedIds });
            setSelectedIds([]);
            refresh(true);
            notify('Applicants deleted successfully!');
          } catch (error) {
            notify.error(error.response.data.message);
          } finally {
            hideConfirm();
            setLoading(false);
          }
        },
      });
    }
  };

  const downloadDocument = async (id, type) => {
    try {
      const response = await Api.get(`${type === 'interview' ? 'ai-interview' : 'submissions'}/stages/report/${id}`, {
        responseType: 'blob',
      });
      const url = window.URL.createObjectURL(
        new Blob([response.data], {
          type: response.headers['content-type'],
        })
      );
      const a = document.createElement('a');
      a.href = url;
      a.download = `${type === 'submission' ? 'test' : type}-report.pdf`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      notify.error(error.response?.data.message);
    }
  };

  const actionsResponsiveButtons = (row) => [
    // {
    //   label: 'Edit',
    //   customIcon: 'edit',
    //   color: 'text-black dark:text-white w-[19px] h-[19px]',
    //   onClick: () => {
    //     setCreateDialogVisibility(true);
    //     setHandleGet(row._id);
    //   },
    // },
    {
      label: 'Archive',
      customIcon: 'archive',
      iconWidth: '18',
      color: 'text-black dark:text-white',
      onClick: () => {},
    },
  ];

  /* === Drawer filter === */
  // State
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);
  const [collapseFilter, setCollapseFilter] = useState({ applicantFilter: true, assignmentFilter: true });
  const [filterCountNumber, setFilterCountNumber] = useState(0);

  // Form
  // const { form, setFieldValue, setFormValue, resetForm } = useForm({
  //   // Applicant Filter
  //   createdAt: '',
  //   track: '',
  //   seniorityLevel: '',
  //   phase: '',
  //   warnings: '',
  //   recommended: null,

  //   // Assignment Filter
  //   type: '',
  //   category: '',
  //   subCategory: '',
  //   difficulty: '',
  //   grade: '',
  //   startDate: '',
  //   dueDate: '',
  // });

  // // Methods drawer filter
  // const HandleCollapseFilter = ({ onClick, label, actionLabel }) => (
  //   <div className="flex justify-between items-center p-3 bg-[#F9F8FFA3] dark:bg-darkGrayBackground border-y border-[#E8E8E8] dark:border-gray-700">
  //     <span className="text-[#494C54] dark:text-white text-sm font-semibold">{label}</span>
  //     <span className="text-[#8A43F9] text-xs font-semibold cursor-pointer" onClick={onClick}>
  //       {actionLabel}
  //     </span>
  //   </div>
  // );

  // const isAnyFilterApplied = () => {
  //   return (
  //     form.createdAt ||
  //     form.track.length > 0 ||
  //     form.seniorityLevel.length > 0 ||
  //     form.phase.length > 0 ||
  //     form.recommended ||
  //     form.warnings.length > 0 ||
  //     form.type.length > 0 ||
  //     form.category.length > 0 ||
  //     form.subCategory.length > 0 ||
  //     form.difficulty.length > 0 ||
  //     form.grade.length > 0 ||
  //     form.startDate ||
  //     form.dueDate
  //   );
  // };

  const clearFilter = () => {
    // resetForm();
    setFilters({});
  };

  // const applyFilter = () => {
  //   const cleanedForm = Object.fromEntries(Object.entries(form)?.filter(([_, value]) => value !== undefined && value !== null && value !== ''));
  //   if (!(cleanedForm?.seniorityLevel ?? [])?.includes(1)) {
  //     delete cleanedForm?.phase;
  //   }
  //   setFilters(cleanedForm);

  //   let totalLength = 0;
  //   for (const key in cleanedForm) {
  //     if (Array.isArray(cleanedForm[key])) {
  //       totalLength += cleanedForm[key].length;
  //     } else if (typeof cleanedForm[key] === 'string' || typeof cleanedForm[key] === 'number') {
  //       totalLength += 1;
  //     } else if (cleanedForm[key] instanceof Date) {
  //       totalLength += 1;
  //     }
  //   }
  //   setFilterCountNumber(totalLength);
  // };

  // useEffect(() => {
  //   if (!(form.seniorityLevel ?? [])?.includes(1)) {
  //     setFieldValue('phase')('');
  //   }
  // });

  // useEffect(() => {
  //   applyFilter();
  // }, [form]);

  // const drawerFilter = (
  //   <Form className="space-y-2 overflow-y-auto mb-2 h-full">
  //     {/* Applicant Filter */}
  //     <HandleCollapseFilter
  //       className="font-semibold"
  //       label="Applicant Filter"
  //       actionLabel={
  //         collapseFilter?.applicantFilter ? (
  //           <CustomIcon definedIcon={'arrowDown'} width={40} height={40} />
  //         ) : (
  //           <CustomIcon definedIcon={'arrowUp'} width={40} height={40} />
  //         )
  //       }
  //       onClick={() => setCollapseFilter((prev) => ({ ...prev, applicantFilter: !prev.applicantFilter }))}
  //     />

  //     {collapseFilter?.applicantFilter && (
  //       <div className="px-3 space-y-5">
  //         <div>
  //           <div className="flex w-full justify-between">
  //             <p className="my-2 text-[13px] text-inputLabel dark:text-inputDarkLabel font-semibold">Join Date</p>
  //           </div>
  //           <DatePicker
  //             format="dd/MM/yyyy"
  //             placeholder="Enter a Join Date"
  //             className="w-full"
  //             value={form?.createdAt || null}
  //             onChange={(value) => setFieldValue('createdAt')(value)}
  //             oneTap
  //             showMeridiem
  //           />
  //         </div>
  //         <MultiSelect
  //           label="Applicant’s Track"
  //           name="track"
  //           placeholder="Search for category"
  //           value={form.track}
  //           onChange={setFieldValue('track')}
  //           lookup="category"
  //           optionValueKey="_id"
  //           optionLabelKey="name"
  //           showSingleClear={form.track.length >= 2}
  //           handleSingleClear={() => setFieldValue('track')('')}
  //           customSize="[13px]"
  //           customWeight="semibold"
  //         />
  //         <MultiSelect
  //           label="Applicant’s Level"
  //           name="seniorityLevel"
  //           placeholder="Search for level"
  //           value={form.seniorityLevel}
  //           onChange={setFieldValue('seniorityLevel')}
  //           lookup="$QuizDifficulty"
  //           showSingleClear={form.seniorityLevel.length >= 2}
  //           handleSingleClear={() => setFieldValue('seniorityLevel')('')}
  //           customSize="[13px]"
  //           customWeight="semibold"
  //         />
  //         {form?.seniorityLevel?.includes(1) && (
  //           <MultiSelect
  //             label="Phase"
  //             name="phase"
  //             placeholder="Search for phase"
  //             value={form.phase}
  //             onChange={setFieldValue('phase')}
  //             lookup="$InternPhase"
  //             showSingleClear={form.phase.length >= 2}
  //             handleSingleClear={() => setFieldValue('phase')('')}
  //           />
  //         )}

  //         <RadioGroup
  //           label="Recommendation"
  //           name="recommended"
  //           value={form.recommended}
  //           onChange={setFieldValue('recommended', Number)}
  //           lookup="$Recommended"
  //           className="text-sm text-inputLabel dark:text-inputDarkLabel  pb-1"
  //           showSingleClear={form.recommended}
  //           handleSingleClear={() => setFieldValue('recommended')('')}
  //           customSize="[12px]"
  //         />

  //         <MultiSelect
  //           label="Warnings"
  //           name="warnings"
  //           placeholder="Search for warnings"
  //           value={form.warnings}
  //           onChange={setFieldValue('warnings')}
  //           lookup="$SubmissionWarning"
  //           handleSingleClear={() => setFieldValue('warnings')('')}
  //           showSingleClear={form.warnings.length >= 2}
  //           customSize="[13px]"
  //           customWeight="semibold"
  //         />
  //       </div>
  //     )}

  //     {/* Assignment Filter */}
  //     <HandleCollapseFilter
  //       label="Assessment Filter"
  //       actionLabel={
  //         collapseFilter?.assignmentFilter ? (
  //           <CustomIcon definedIcon={'arrowDown'} width={40} height={40} />
  //         ) : (
  //           <CustomIcon definedIcon={'arrowUp'} width={40} height={40} />
  //         )
  //       }
  //       onClick={() => setCollapseFilter((prev) => ({ ...prev, assignmentFilter: !prev.assignmentFilter }))}
  //     />

  //     {!collapseFilter?.assignmentFilter && (
  //       <div className="px-3 space-y-3">
  //         <MultiSelect
  //           label="Assessment Type"
  //           name="type"
  //           placeholder="Search for type"
  //           value={form.type}
  //           onChange={setFieldValue('type')}
  //           lookup="$AssignmentType"
  //           handleSingleClear={() => setFieldValue('type')('')}
  //           customSize="[13px]"
  //           customWeight="semibold"
  //           showSingleClear={form.type.length >= 2}
  //         />

  //         <MultiSelect
  //           label="Category"
  //           name="category"
  //           placeholder="Search for category"
  //           value={form.category}
  //           customSize="[13px]"
  //           customWeight="semibold"
  //           onChange={(value) => {
  //             setFieldValue('category')(value);
  //             setFieldValue('subCategory')([]);
  //           }}
  //           lookup="category"
  //           optionValueKey="_id"
  //           optionLabelKey="name"
  //           handleSingleClear={() => setFieldValue('category')('')}
  //           showSingleClear={form.category.length >= 2}
  //         />

  //         <MultiSelect
  //           label="Subcategory"
  //           name="subCategory"
  //           placeholder="Search for subcategory"
  //           value={form.subCategory}
  //           onChange={setFieldValue('subCategory')}
  //           lookup="subcategory"
  //           params={{ categoryId: form.category }}
  //           optionValueKey="_id"
  //           optionLabelKey="name"
  //           disabled={form.category.length <= 0}
  //           // disabledMessage="Please select category first"
  //           handleSingleClear={() => setFieldValue('subCategory')('')}
  //           showSingleClear={form.subCategory.length >= 2}
  //           customSize="[13px]"
  //           customWeight="semibold"
  //         />

  //         <MultiSelect
  //           label="Assessment Difficulty"
  //           name="difficulty"
  //           placeholder="Search for difficulty"
  //           value={form.difficulty}
  //           onChange={setFieldValue('difficulty')}
  //           lookup="$QuizDifficulty"
  //           handleSingleClear={() => setFieldValue('difficulty')('')}
  //           showSingleClear={form.difficulty.length >= 2}
  //           customSize="[13px]"
  //           customWeight="semibold"
  //         />

  //         <MultiSelect
  //           label="Assessment Score"
  //           name="grade"
  //           placeholder="Search for score"
  //           value={form.grade}
  //           onChange={setFieldValue('grade')}
  //           lookup="$grade"
  //           handleSingleClear={() => setFieldValue('grade')('')}
  //           showSingleClear={form.grade.length >= 2}
  //           customSize="[13px]"
  //           customWeight="semibold"
  //         />

  //         <div>
  //           <div className="flex w-full justify-between">
  //             <p className="my-2  text-inputLabel dark:text-inputDarkLabel text-[13px] font-semibold">Assessment Due Date</p>
  //           </div>
  //           <div className="flex justify-between gap-2">
  //             <DatePicker
  //               format="dd/MM/yyyy hh:mm aa"
  //               placeholder="Start date"
  //               className="w-full"
  //               value={form?.startDate || null}
  //               onChange={setFieldValue('startDate')}
  //               showMeridiem
  //               placement="autoVerticalStart"
  //             />
  //             <DatePicker
  //               format="dd/MM/yyyy hh:mm aa"
  //               placeholder="End date"
  //               className="w-full"
  //               value={form?.dueDate || null}
  //               onChange={setFieldValue('dueDate')}
  //               showMeridiem
  //               placement="autoVerticalEnd"
  //             />
  //           </div>
  //         </div>
  //       </div>
  //     )}

  //     {/* <div className="flex gap-2 px-3 pb-1">
  //       <Button label="Clear All" outline onClick={clearFilter} />
  //       <Button label="Apply Filter" onClick={applyFilter} />
  //     </div> */}
  //   </Form>
  // );

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
    // setSidebarSearch(search);
    // setSidebarFilter({ filterFeedData, setFilters });
  }, [list]);

  // Use these styles to make black overlay visible and not scrollable
  // Make the scroll in list pages only be smooth
  useEffect(() => {
    if (isAssignTestVisible || isScreeningVisible || isShowDrawerFilter || isAssignInterviewTestVisible) {
      document.querySelector('html').classList.add('overflow-x-hidden');
    } else {
      document.querySelector('html').classList.remove('overflow-x-hidden');
    }
  }, [isAssignTestVisible, isScreeningVisible, isShowDrawerFilter, isAssignInterviewTestVisible]);

  return (
    <>
      {/* {ready && (
        <div className="hidden 2xl:block w-[270px] h-[calc(100vh-100px)] fixed overflow-y-auto">
          <SidebarFilterPage
            filterData={{
              filterFeedData,
              setFilters,
            }}
          />
        </div>
      )} */}

      {/* <div className={`${ready && '2xl:pl-72'}`}> */}
      <Table
        ready={ready}
        loading={loading}
        title="Applicants"
        addButtonLabel={isPermitted ? 'Create Applicant' : ''}
        searchPlaceholder={screen.customScreen ? 'Search by name or email...' : 'Name, email or mobile'}
        count={count}
        search={search}
        filters={filters}
        // setFilters={setFilters}
        // filterFeedData={filterFeedData}
        // drawerFilter={{
        //   filterCountNumber: filterCountNumber,
        //   isShowDrawerFilter: isShowDrawerFilter,
        //   setShowDrawerFilter: setShowDrawerFilter,
        // }}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        onClickAdd={
          isPermitted &&
          (() => {
            if (isSuperAdmin || userData?.features?.applicants > 0) {
              setCreateDialogVisibility(true), setHandleGet(false);
            } else {
              setNeedSubscription(true);
            }
          })
        }
        slots={{
          applicantName: (_, row) => {
            const element = (
              <div className="overflow-hidden space-y-2">
                <div
                  style={{ wordBreak: 'break-word' }}
                  className={`lg:truncate font-medium text-[14px] ${!showMoreMap[row._id] && 'truncate sm:overflow-visible sm:whitespace-normal'}`}
                >
                  {row.name || '—'}
                </div>
              </div>
            );

            return (
              <div className="flex gap-1">
                <div className="relative flex gap-2 w-full">
                  <div className="w-fit max-w-full">
                    <div className="font-medium capitalize text-[#101828] dark:text-gray-100 flex gap-2 items-start">{element}</div>
                    {screen.gt.md() && (
                      <Tooltip content={element} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                        <div className="w-full h-full absolute left-0 top-0"></div>
                      </Tooltip>
                    )}
                  </div>
                </div>
              </div>
            );
          },

          applicantEmail: (_, row) => {
            return (
              <div className="flex relative gap-2">
                <div className="w-full">
                  <div className="flex items-center gap-2 relative">
                    <div className="truncate max-w-[85%]">
                      {row?.email ? (
                        <Tooltip
                          content={row.email}
                          placement="bottom"
                          arrow={false}
                          className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                        >
                          <span className="text-[#535862] text-sm font-medium dark:text-gray-400 truncate">{row.email}</span>
                        </Tooltip>
                      ) : (
                        <span className="text-[#626874] dark:text-gray-400">—</span>
                      )}
                    </div>
                    {row?.email && (
                      <span
                        onClick={() => {
                          navigator.clipboard.writeText(row.email);
                          notify('Email copied');
                        }}
                        className="inline-block cursor-pointer text-gray-500 dark:text-gray-400"
                      >
                        <Tooltip
                          content="Copy Email"
                          placement="bottom"
                          arrow={false}
                          className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                        >
                          <Icon icon="ooui:copy-ltr" className="relative text-[#798296] text-base" />
                        </Tooltip>
                      </span>
                    )}
                  </div>
                </div>
              </div>
            );
          },

          // track: (_, row) => {
          //   return (
          //     <div className="flex relative gap-2">
          //       <div className="w-full">
          //         <div className="flex items-center gap-2 relative">
          //           <div className="truncate max-w-[85%]">
          //             {row?.trackName ? (
          //               <Tooltip
          //                 content={row.trackName}
          //                 placement="bottom"
          //                 arrow={false}
          //                 className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
          //               >
          //                 <span className="text-[#535862] text-sm font-normal dark:text-gray-400 truncate">{row.trackName}</span>
          //               </Tooltip>
          //             ) : (
          //               <span className="text-[#626874] dark:text-gray-400">—</span>
          //             )}
          //           </div>
          //         </div>
          //       </div>
          //     </div>
          //   );
          // },

          position: (_, row) => {
            // const element = <div className={`${!showMoreMap[row._id] && ' truncate sm:overflow-visible sm:whitespace-normal'}`}>{row.trackName}</div>;
            let seniorityIcon;
            let seniorityColor;
            let iconSize = 'text-sm';

            switch (row.seniorityLevel) {
              case 1:
                seniorityIcon = <FaUserGraduate className={`${iconSize} text-teal-700`} />;
                seniorityColor = 'text-teal-700';
                break;
              // Star Icon fresh level
              case 2:
                seniorityIcon = <FaUser className={`${iconSize} text-sky-800`} />;
                seniorityColor = 'text-sky-800';
                break;
              // Medal Star junior
              case 3:
                seniorityIcon = <FaStar className={`${iconSize} text-amber-700`} />;
                seniorityColor = 'text-amber-700';
                break;
              // betetr medal star due
              case 4:
                seniorityIcon = <FaMedal className={`${iconSize} text-orange-700`} />;
                seniorityColor = 'text-orange-700';
                break;
              // Tropy icon for senior with star
              case 5:
                seniorityIcon = <Icon icon="solar:crown-star-bold" width={18} className={`${iconSize} text-red-800`} />;
                seniorityColor = 'text-red-800';
                break;
              default:
                seniorityIcon = null;
            }

            return (
              <>
                <div className={`flex sm:block sm:space-y-0.5 space-y-2 sm:flex-nowrap  items-center gap-1 ${showMoreMap[row._id] && '!flex-wrap'}`}>
                  {/* <div className="flex gap-x-1  relative  overflow-hidden">
                    <div
                      style={{ wordBreak: 'break-word' }}
                      className={`text-[#4B5563]   text-sm font-medium capitalize dark:text-grayTextOnDarkMood lg:truncate ${
                        !showMoreMap[row._id] && 'truncate sm:overflow-visible sm:whitespace-normal'
                      }`}
                    >
                      {element}
                    </div>
                    {screen.gt.md() && (
                      <Tooltip content={element} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900">
                        <div className="w-full h-full absolute left-0 top-0"></div>
                      </Tooltip>
                    )}
                  </div> */}
                  {/* {!showMoreMap[row._id] && row.seniorityLevel && (
                  <span className="sm:hidden  flex items-center justify-center self-center !m-0   px-2 ">-</span>
                )} */}
                </div>
                <span className={`flex items-center font-semibold py-1 capitalize ${seniorityColor} !m-0`}>
                  <span className="mr-1 flex items-center justify-center">{seniorityIcon}</span>
                  <EnumText name="QuizDifficulty" value={row.seniorityLevel} />
                </span>
              </>
            );
          },

          assignmentOverview: (_, row) => {
            // data?.title       => Test, Screening
            // data?.quiz?.title => Interview
            const element = row?.lastAssessment?.data?.title || row?.lastAssessment?.data?.quiz?.title || '—';
            return (
              <>
                {row.lastAssessment?.data ? (
                  <div className="space-y-2 ">
                    <div className={`flex gap-3 ${showMoreMap[row._id] && 'flex-wrap lg:flex-nowrap'} items-center max-w-full`}>
                      <div className="relative max-w-[65%]">
                        <div
                          className={`w-full break-words text-[#111827] text-sm font-normal capitalize dark:text-grayTextOnDarkMood lg:truncate ${
                            !showMoreMap[row._id] && 'truncate sm:overflow-visible sm:whitespace-normal'
                          }`}
                        >
                          {element}
                          {screen.gt.md() && (
                            <Tooltip
                              content={element}
                              placement="bottom"
                              arrow={false}
                              className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                            >
                              <div className="w-[90%] h-full absolute left-0 top-0"></div>
                            </Tooltip>
                          )}
                        </div>
                      </div>
                      <div className="dark:bg-[#8485884c] text-[13px] font-normal  bg-[#eceeef] dark:text-[#e1e1e1] text-[#667085] py-[2px] px-2 rounded-md w-fit h-fit">
                        {row.lastAssessment?.type === 'submission'
                          ? 'Test'
                          : row.lastAssessment?.type.charAt(0).toUpperCase() + row.lastAssessment?.type.slice(1)}
                      </div>
                    </div>
                    <div className="flex items-center  ">
                      <ResultStatus test={row.lastAssessment.data} hideMissed hideTime hideScore={row.lastAssessment?.type === 'screening'} />
                      {row?.lastAssessment?.type !== 'screening' && !row.lastAssessment.data.startedAt && row.lastAssessment.data.submittedAt && (
                        <p className="text-[#9CA3AF] italic font-normal">No score recorded</p>
                      )}

                      {row.lastAssessment.data.startedAt && row.lastAssessment?.data?.submittedAt && row.lastAssessment?.type !== 'screening' && (
                        <p
                          onClick={() => {
                            if (row.lastAssessment?.type === 'submission') {
                              window.open(`/app/tests/pdf/${row.lastAssessment.data._id}?type=submissions`, '_blank', 'noopener,noreferrer');
                            } else if (row.lastAssessment?.type === 'interview') {
                              window.open(`/app/tests/pdf/${row.lastAssessment.data._id}?type=interviews`, '_blank', 'noopener,noreferrer');
                            }
                          }}
                          className="flex items-center cursor-pointer underline hover:no-underline
                             text-[13px] dark:text-[#bfc0c3] text-[#667085] gap-1 ml-2"
                        >
                          <svg width="14" height="20" viewBox="0 0 12 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M11.0156 6.82812H10.7812V4.01562C10.7812 4.01562 10.7812 4.01562 10.7812 4.00156C10.7801 3.98082 10.7761 3.96033 10.7695 3.94063V3.91953C10.7588 3.89445 10.7437 3.87145 10.725 3.85156L7.9125 1.03906C7.89261 1.02037 7.86961 1.0053 7.84453 0.994531H7.82344C7.80008 0.981739 7.77472 0.973021 7.74844 0.96875H0.703125C0.640965 0.96875 0.581351 0.993443 0.537397 1.0374C0.493443 1.08135 0.46875 1.14096 0.46875 1.20312V6.82812H0.234375C0.172215 6.82812 0.112601 6.85282 0.0686469 6.89677C0.0246931 6.94073 0 7.00034 0 7.0625V11.75C0 11.8122 0.0246931 11.8718 0.0686469 11.9157C0.112601 11.9597 0.172215 11.9844 0.234375 11.9844H0.46875V14.7969C0.46875 14.859 0.493443 14.9186 0.537397 14.9626C0.581351 15.0066 0.640965 15.0312 0.703125 15.0312H10.5469C10.609 15.0312 10.6686 15.0066 10.7126 14.9626C10.7566 14.9186 10.7812 14.859 10.7812 14.7969V11.9844H11.0156C11.0778 11.9844 11.1374 11.9597 11.1814 11.9157C11.2253 11.8718 11.25 11.8122 11.25 11.75V7.0625C11.25 7.00034 11.2253 6.94073 11.1814 6.89677C11.1374 6.85282 11.0778 6.82812 11.0156 6.82812ZM7.96875 1.76797L9.98203 3.78125H7.96875V1.76797ZM10.3125 14.5625H0.9375V11.9844H10.3125V14.5625ZM3.06328 11.0047V7.87578H3.80625C3.90788 7.87128 4.00931 7.88838 4.10384 7.92595C4.19838 7.96352 4.28387 8.02071 4.35469 8.09375C4.48388 8.23924 4.5519 8.429 4.54453 8.62344V9.07812C4.54616 9.17639 4.52789 9.27397 4.49084 9.365C4.45378 9.45603 4.3987 9.53862 4.32891 9.60781C4.26186 9.67882 4.18077 9.73508 4.09078 9.77301C4.00079 9.81094 3.90389 9.82971 3.80625 9.82812H3.53672V11L3.06328 11.0047ZM4.92422 11.0047V7.87344H5.66484C5.76288 7.87152 5.86023 7.89014 5.95064 7.92808C6.04105 7.96602 6.12253 8.02245 6.18984 8.09375C6.25957 8.16251 6.31463 8.24469 6.35169 8.33533C6.38875 8.42597 6.40705 8.52318 6.40547 8.62109V10.2617C6.40692 10.3602 6.38835 10.4579 6.35088 10.5489C6.31341 10.64 6.25783 10.7225 6.1875 10.7914C6.12019 10.8627 6.03871 10.9191 5.9483 10.9571C5.85788 10.995 5.76053 11.0136 5.6625 11.0117L4.92422 11.0047ZM7.93125 9.20234V9.67109H7.29375V11H6.825V7.87344H8.16797V8.34219H7.29375V9.19531L7.93125 9.20234ZM10.3125 6.82812H0.9375V1.4375H7.5V4.01562C7.5 4.07779 7.52469 4.1374 7.56865 4.18135C7.6126 4.22531 7.67221 4.25 7.73438 4.25H10.3125V6.82812Z"
                              fill="#798296"
                            />
                          </svg>
                          Open Report
                        </p>
                      )}
                      {!row.lastAssessment.data.submittedAt && (
                        <p
                          onClick={() => {
                            navigator.clipboard.writeText(
                              `${
                                row.lastAssessment.type === 'submission' || row.lastAssessment.type === 'screening'
                                  ? `${ORIGIN}/test`
                                  : `${ORIGIN}/interview`
                              }/${row.lastAssessment.data._id}`
                            );
                            notify('Link copied');
                          }}
                          className="flex cursor-pointer underline hover:no-underline text-[13px] dark:text-[#bfc0c3] text-[#667085] "
                        >
                          <div className="flex items-center gap-2">
                            Copy Link
                            <Tooltip
                              content="Copy Link"
                              placement="bottom"
                              arrow={false}
                              className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                            >
                              <Icon icon="ooui:copy-ltr" className="relative z-10 text-[#798296] text-base" />
                            </Tooltip>{' '}
                          </div>
                        </p>
                      )}
                    </div>
                  </div>
                ) : (
                  <p className="text-[#6B7280] font-medium">Not assigned</p>
                )}
              </>
            );
          },

          assessmentDueDate: (_, row) => {
            return (
              <div className="space-y-3">
                <div className="text-[#374151] text-sm">
                  <p className="dark:text-white font-medium">
                    {row.startDate && row.dueDate ? (
                      <>
                        {/* Start Date */}
                        <div className="flex items-center mb-3">
                          <span className="text-[#6B7280] text-[13.5px] font-semibold mr-2 dark:text-[#D1D5DB]">Start:</span>
                          <span className="text-[#1F2937] dark:text-white text-sm font-medium">{`${formatDate(row.startDate)} ${formatTime(
                            row.startDate
                          )}`}</span>
                        </div>

                        {/* End Date */}
                        <div className="flex items-center">
                          <span className="text-[#6B7280] text-[13.5px] font-semibold mr-2 dark:text-[#D1D5DB]">End:</span>
                          <span className="text-[#1F2937] text-sm font-medium dark:text-white">{`${formatDate(row.dueDate)} ${formatTime(
                            row.dueDate
                          )}`}</span>
                        </div>
                      </>
                    ) : (
                      <span className="text-[#9CA3AF] italic font-normal">No due date available</span>
                    )}
                  </p>
                </div>

                {row.lastAssessment?.data && <ResultStatus test={row.lastAssessment.data} hideTime hideScore />}

                {/* {!row.lastAssessment?.data?.submittedAt && (
                  <div className="dark:bg-[#831b1180]/40  bg-[#FEF3F2] text-[#D92D20] w-max rounded-3xl p-1 px-2 font-normal text-xs">
                    <p>Missed Deadline</p>
                  </div>
                )} */}
                {/* <div className="dark:bg-[#6f652d76] bg-[#FFF3E0] text-[#FB8C00] w-max rounded-3xl p-1 px-2 font-normal text-xs">
                  <p>Over Due</p>
                </div> */}
              </div>
            );
          },
        }}
        columns={[
          {
            key: 'applicantName',
            label: 'Name',
            primary: true,
            width: '18%',
          },
          {
            key: 'applicantEmail',
            label: 'Email',
            primary: true,
            width: '20%',
          },
          {
            key: 'position',
            label: 'Seniority Level',
            primary: true,
            width: '18%',
          },

          // {
          //   key: 'track',
          //   label: 'Track',
          //   primary: true,
          //   width: '18%',
          // },

          {
            key: 'applicantAverageScore',
            label: 'Average Score',
            primary: true,
            width: '14%',
          },
          // {
          //   key: 'assignmentOverview',
          //   label: 'Last Assessment Overview',
          //   primary: true,
          //   width: '22%',
          // },
          // {
          //   key: 'assessmentDueDate',
          //   label: 'Last Assessment Due Date',
          //   width: '20%',
          // },
          // {
          //   key: 'overAll',
          //   label: 'Overall Performance',
          //   width: '18%',
          // },
          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_, row) {
              return [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  path: `/app/applicants/progress/${row._id}`,
                },
                // {
                //   label: 'Assign',
                //   customIcon: 'assign',
                //   iconWidth: '22',
                //   iconHeight: '22',
                //   color: 'text-black dark:text-white',
                //   onClick: () => {
                //     notify.error('Please update the position and level for this applicant');
                //   },
                //   dropDown: [
                //     {
                //       label: 'Screening',
                //       customIcon: 'screening',
                //       color: 'text-black dark:text-white',
                //       onClick: () => {
                //         if (isSuperAdmin || userData?.features?.assignScreening > 0) {
                //           setIsScreeningVisible(true);
                //           setApplicantDetails(row);
                //         } else {
                //           setNeedSubscription(true);
                //         }
                //       },
                //     },
                //     {
                //       label: 'Test',
                //       customIcon: 'tests',
                //       color: 'text-black dark:text-white',
                //       onClick: () => {
                //         if (isSuperAdmin || userData?.features?.assignTest > 0) {
                //           setAssignTestVisibility(true);
                //           setApplicantDetails(row);
                //         } else {
                //           setNeedSubscription(true);
                //         }
                //       },
                //     },
                //     {
                //       label: 'Interview',
                //       color: 'text-black dark:text-white',
                //       customIcon: 'interview',
                //       element: (
                //         <span
                //           className={`w-24 text-xs px-2 py-1 rounded-full shadow-md font-bold tracking-wide bg-magic-gradient bg-[length:200%]  text-white overflow-hidden`}
                //         >
                //           AI Magic ✨
                //         </span>
                //       ),
                //       onClick: () => {
                //         if (isSuperAdmin || userData?.features?.assignInterview > 0) {
                //           setAssignInterviewTestVisible(true);
                //           setApplicantDetails(row);
                //         } else {
                //           setNeedSubscription(true);
                //         }
                //       },
                //     },
                //   ],
                // },
                // ...(screen.gt.md()
                //   ? [
                //       {
                //         label: 'More',
                //         customIcon: 'more',
                //         color: 'text-black dark:text-white',

                //         dropDown: actionsResponsiveButtons(row),
                //       },
                //     ]
                //   : actionsResponsiveButtons(row)),
                // ...actionsResponsiveButtons(row),
              ];
            },
          },
        ]}
        // multiSelectedRow={{
        //   selectedIds: selectedIds,
        //   setSelectedIds: setSelectedIds,
        //   handleArchiveSelectedIds: handleArchiveSelectedIds,
        // }}
        noDataFound={{
          customIcon: 'applicant',
          message: 'No applicants created yet',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        // actions={[
        //   {
        //     label: 'Generate Assessment Link',
        //     dropdownlist: [
        //       {
        //         label: 'Screening',
        //         customIcon: 'screening',
        //         onClick: () => {
        //           if (isSuperAdmin || userData?.features?.assignScreening > 0) {
        //             setIsScreeningVisible(true);
        //           } else {
        //             setNeedSubscription(true);
        //           }
        //         },
        //       },
        //       {
        //         label: 'Test',
        //         customIcon: 'tests',
        //         onClick: () => {
        //           if (isSuperAdmin || userData?.features?.assignTest > 0) {
        //             setAssignTestVisibility(true);
        //           } else {
        //             setNeedSubscription(true);
        //           }
        //         },
        //       },
        //       {
        //         label: 'Interview',
        //         customIcon: 'interview',
        //         element: (
        //           <span
        //             className={`w-24 text-xs px-2 py-1 rounded-full shadow-md font-bold tracking-wide bg-magic-gradient bg-[length:200%]  text-white overflow-hidden`}
        //           >
        //             AI Magic ✨
        //           </span>
        //         ),
        //         onClick: () => {
        //           if (isSuperAdmin || userData?.features?.assignInterview > 0) {
        //             setAssignInterviewTestVisible(true);
        //           } else {
        //             setNeedSubscription(true);
        //           }
        //         },
        //       },
        //     ],
        //   },
        // ]}
      />
      {/* </div> */}

      {/* Create new applicant */}
      {isCreateDialogVisible && <ApplicantsSingleDialog onClose={() => setCreateDialogVisibility(false)} onCreate={refresh} id={handleGet} />}

      {/* Need Subscription */}
      {needSubscription && <SubscribeDialog onClose={() => setNeedSubscription(false)} />}

      {/* Assign test */}
      {isAssignTestVisible && (
        <AssignTest
          isAssignTestVisible={isAssignTestVisible}
          setAssignTestVisibility={setAssignTestVisibility}
          applicantDetails={applicantDetails}
          setApplicantDetails={setApplicantDetails}
          refreshMainTable={refresh}
        />
      )}

      {/* Assign screening */}
      {isScreeningVisible && (
        <AssignScreening
          isAssignTestVisible={isScreeningVisible}
          setAssignTestVisibility={setIsScreeningVisible}
          applicantDetails={applicantDetails}
          setApplicantDetails={setApplicantDetails}
          refreshMainTable={refresh}
        />
      )}

      {/* Assign ai-interview */}
      {isAssignInterviewTestVisible && (
        <AssignIntreview
          applicantDetails={applicantDetails}
          setApplicantDetails={setApplicantDetails}
          onClose={() => {
            setAssignInterviewTestVisible(false);
            setApplicantDetails(false);
          }}
          refreshMainTable={refresh}
        />
      )}

      {/* Filter Drawer */}
      {isShowDrawerFilter && (
        <SidebarFilterDrawer
          drawerFilter={{
            // element: drawerFilter,
            // count: count,
            drawerClearAll: clearFilter,
            // isShowDrawerFilter: isShowDrawerFilter,
            setShowDrawerFilter: setShowDrawerFilter,
            // isAnyFilterApplied: isAnyFilterApplied,
          }}
          filterData={{
            filterFeedData,
            setFilters,
          }}
        />
      )}
    </>
  );
};
