// React
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

// UI
import { Icon, ToggleFilter, useFetchList, NoDataFound, ScrollableTabs, NoDataMatches, useScreenSize, StaticData } from '/src';

// Components
import { TestCard } from '../components/test-card';
import { ApplicantData } from '../components/applicant-data';
import { PhoneScreening } from '../components/phone-screening';
import { TestCardPlaceholder } from '../components/test-card-placeholder';

// Flowbite
import { Pagination, Spinner } from 'flowbite-react';

export const ApplicantProgressSingle = () => {
  // State
  const [isShowPhoneScreening, setShowPhoneScreening] = useState(false);
  const [activeTab, setActiveTab] = useState(1);

  // Hooks
  const { id } = useParams();
  const screen = useScreenSize();

  const TabsItemComponent = (propertyKeyObject, endpoint, title) => {
    // Tabs Component State
    const [backupCount, setBackupCount] = useState(0);

    const { ready, loading, setLoading, list, count, filters, search, pagination, refresh, handleDates } = useFetchList(endpoint, {
      search: '',
      pagination: {
        page: 1,
        size: 10,
      },
      filters: {
        dueDate: {
          label: 'Due Date',
          enum: 'SubmissionDueDate',
        },
        status: {
          label: 'Status',
          enum: 'SubmissionStatus',
        },
        grade: {
          label: 'Score Range',
          enum: 'grade',
        },
        Warnings: {
          label: 'Warnings',
          enum: 'SubmissionWarning',
        },
      },
      id: id,
    });

    // Pagination
    const { page, size } = pagination;
    const pagesCount = Math.max(Math.ceil(count / size), 1);
    const showingText = `${count ? page * size - size + 1 : count} - ${page * size > count ? count : page * size}`;
    const isPaginationActive = !!pagination.update;

    const noDataFound = {
      customIcon:
        propertyKeyObject === 'submissions'
          ? 'tests'
          : propertyKeyObject === 'interviews'
          ? 'interview'
          : propertyKeyObject === 'screening'
          ? 'screening'
          : propertyKeyObject === 'warnings'
          ? 'warningLarge'
          : '—',

      message:
        list?.length === 0 && propertyKeyObject === 'submissions'
          ? 'No tests assigned yet'
          : propertyKeyObject === 'interviews'
          ? 'No interviews assigned yet'
          : propertyKeyObject === 'screening'
          ? 'No screening assigned yet'
          : propertyKeyObject === 'warnings'
          ? 'No tests has warnings'
          : '—',
    };

    const handleFilterCountNumber = () => {
      let filterCount = 0;
      if (filters?.length > 0) {
        filters.map((subFilter) =>
          subFilter.options.map((subFilterOption) => {
            // Count regular options with truthy value
            if (subFilterOption.value) filterCount++;

            // Check if the option is a date picker (e.g., "Pick a Date")
            if (subFilterOption.label === 'Pick a Date') {
              if (handleDates.startDate.value) filterCount++;
              if (handleDates.endDate.value) filterCount++;
            }
          })
        );
      } else if (drawerFilter?.filterCountNumber > 0) {
        filterCount = drawerFilter?.filterCountNumber;
      }

      return filterCount;
    };

    useEffect(() => {
      if (backupCount === 0) {
        setBackupCount(count);
      }
    }, [count]);

    return {
      ready,
      loading,
      setLoading,
      count,
      backupCount,
      pagination,
      refresh,
      title,
      component: (
        <>
          {backupCount > 0 && (
            <>
              {(handleFilterCountNumber() > 0 || search.value) && (
                <div className="dark:text-white text-base py-2">
                  <span className="font-semibold text-base">{count} </span> <span className="capitalize">{title}</span> found
                </div>
              )}
              <div className="flex w-full flex-row items-center space-x-3 space-y-0 justify-between">
                {/* Search bar */}
                <div className="relative w-full shadow-sm">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <Icon icon="carbon:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder={`Search for ${title || '—'} by name...`}
                    className="bg-gray-white placeholder-[#6670859e] border truncate border-gray-200 text-gray-800 text-[13.5px] rounded-lg  block w-full  pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white focus:ring-0 focus:border-gray-300"
                    value={search.value}
                    onInput={(e) => search.update(e.target.value)}
                  />
                </div>

                {/* Filters */}
                <ToggleFilter filters={filters} handleDates={handleDates} />
              </div>
            </>
          )}

          {list?.length ? (
            list?.map((data) => (
              <TestCard
                type={
                  data?.type === 'test'
                    ? 'submissions'
                    : data?.type === 'interview'
                    ? 'interviews'
                    : data?.type === 'screening'
                    ? 'screening'
                    : propertyKeyObject
                }
                test={data}
                key={data?._id}
              />
            ))
          ) : (
            <div className="flex justify-center align-middle   min-h-[calc(70vh-4rem)] items-center ">
              <div className=" w-2/4 space-y-2  ">
                {/* No data created || No results found */}
                {backupCount > 0 ? <NoDataMatches message="No results found" /> : <NoDataFound noDataFound={noDataFound} width="70" height="70" />}
              </div>
            </div>
          )}
        </>
      ),
      pagination: isPaginationActive && count > size && (
        <nav
          // className="flex flex-row justify-between items-center space-y-0 px-4 pt-1 pb-2 bg-white dark:bg-darkGrayBackground bottom-0 sticky z-20"
          className="flex justify-center items-center px-4 my-1 sticky bottom-0 z-20"
          aria-label="Table navigation"
        >
          {/* <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
            Showing <span className="font-semibold text-gray-900 dark:text-white">{showingText}</span> of{' '}
            <span className="font-semibold text-gray-900 dark:text-white">{count}</span>
          </span> */}
          {count > size && (
            <Pagination
              theme={StaticData.paginationTheme}
              currentPage={page}
              onPageChange={(page) => pagination.update({ page })}
              showIcons
              totalPages={pagesCount}
              layout={screen.gt.md() ? 'pagination' : 'navigation'}
              previousLabel={<span className="hidden sm:block">Previous</span>}
              nextLabel={<span className="hidden sm:block">Next</span>}
            />
          )}
        </nav>
      ),
    };
  };

  const tabs = [
    TabsItemComponent('screening', 'submissions/phone-screening-tests', 'screening'),
    TabsItemComponent('submissions', 'submissions/applicants/list', 'tests'),
    TabsItemComponent('interviews', 'ai-interview/applicants/list', 'interviews'),
    TabsItemComponent('warnings', 'applicants/warning/list', 'warnings'),
  ];

  // Use these styles to make black overlay visible and not scrollable
  // Make the scroll in list pages only be smooth
  useEffect(() => {
    if (isShowPhoneScreening) {
      document.querySelector('html').classList.add('overflow-x-hidden');
    } else {
      document.querySelector('html').classList.remove('overflow-x-hidden');
    }
  }, [isShowPhoneScreening]);

  return (
    <>
      <div className="relative">
        <ApplicantData setShowPhoneScreening={setShowPhoneScreening} />

        <div className="my-2">
          <ScrollableTabs
            data={tabs}
            selectedTab={{
              activeTab: activeTab,
              setActiveTab: setActiveTab,
            }}
            fullWidth
          />
        </div>

        {tabs[activeTab]?.ready ? (
          <>
            {tabs[activeTab]?.component}

            {tabs[activeTab]?.pagination}

            {tabs[activeTab]?.loading && (
              <div className="absolute z-50 left-0 right-0 bottom-0 top-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80">
                <Spinner size="lg" color="purple" />
              </div>
            )}
          </>
        ) : (
          <TestCardPlaceholder />
        )}
      </div>

      {isShowPhoneScreening && <PhoneScreening isShowPhoneScreening={isShowPhoneScreening} setShowPhoneScreening={setShowPhoneScreening} />}
    </>
  );
};
