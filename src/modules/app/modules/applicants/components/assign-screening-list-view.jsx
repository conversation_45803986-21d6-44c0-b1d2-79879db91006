// React
import { useEffect } from 'react';

// Core
import { useFetchList, useConfirmDialog, Drawer } from '/src';

// Flowbite
import { Spinner } from 'flowbite-react';

export const AssignScreeningListView = ({
  screeningData,
  setScreeningData,
  questionsListData,
  setQuestionsListData,
  applicantDetails,
  setApplicantDetails,
  handleUpdate,
  handleSubmit,
  difficulty,
  onClose,
  handleLoading,
}) => {
  // Hooks
  const { showConfirm, hideConfirm } = useConfirmDialog();
  const { ready, loading, setLoading, list, count, filters, search, pagination, refresh } = useFetchList(
    'quizzes/single/phoneScreening/difficulty-level',
    {
      search: '',
      pagination: {
        page: 1,
        size: 10,
      },
      difficulty: difficulty,
    }
  );

  // Pagination
  const { page, size } = pagination;
  const pagesCount = Math.max(Math.ceil(count / size), 1);
  const showingText = `${count ? page * size - size + 1 : count} - ${page * size > count ? count : page * size}`;
  const isPaginationActive = !!pagination.update;

  const confirmText = () => (
    <div className="flex flex-col w-full items-center gap-2">
      <svg width="66" height="66" viewBox="0 0 66 66" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="5" y="5" width="56" height="56" rx="28" fill="#F0E7FF" />
        <rect x="5" y="5" width="56" height="56" rx="28" stroke="#F8F4FF" strokeWidth="10" />
        <g clipPath="url(#clip0_8621_5254)">
          <path
            d="M38.8085 26.3846C38.2939 25.4066 37.4884 24.5917 36.4904 24.0006C35.4929 23.4121 34.2888 23.0528 32.982 23.0528C31.3711 23.0488 30.0328 23.4685 29.0227 24.0497C28.0085 24.6288 27.5713 25.3031 27.5713 25.3031C27.4008 25.451 27.3047 25.6662 27.3087 25.8912C27.3133 26.1166 27.4171 26.3282 27.5928 26.4687L28.9964 27.593C29.2825 27.822 29.692 27.8134 29.9681 27.5727C29.9681 27.5727 30.1406 27.2611 30.6809 26.9524C31.2243 26.6458 31.9288 26.3988 32.982 26.3955C33.9006 26.3935 34.7016 26.7363 35.2482 27.2047C35.5198 27.4368 35.7227 27.6967 35.847 27.9344C35.9723 28.1741 36.0181 28.3836 36.0174 28.5428C36.0148 29.0805 35.9103 29.4322 35.7595 29.7319C35.6445 29.9557 35.4943 30.1543 35.301 30.3469C35.0125 30.6353 34.6214 30.9019 34.1825 31.1469C33.7432 31.3948 33.2698 31.613 32.7914 31.8766C32.2454 32.1789 31.6675 32.6132 31.2406 33.265C31.0277 33.5872 30.8613 33.9558 30.7556 34.3447C30.6484 34.7339 30.6001 35.1421 30.6001 35.5584C30.6001 36.0026 30.6001 36.3673 30.6001 36.3673C30.6001 36.786 30.9395 37.1255 31.3583 37.1255H33.1849C33.6036 37.1255 33.9431 36.786 33.9431 36.3673C33.9431 36.3673 33.9431 36.0026 33.9431 35.5584C33.9431 35.398 33.9614 35.2946 33.979 35.2289C34.0091 35.1308 34.0261 35.1062 34.0754 35.0466C34.1258 34.9898 34.2276 34.903 34.4152 34.7992C34.6894 34.6451 35.13 34.4369 35.6289 34.1674C36.3758 33.7589 37.2838 33.2046 38.0497 32.29C38.4306 31.8335 38.7674 31.2838 38.9998 30.6506C39.2342 30.0174 39.3608 29.3056 39.3602 28.5428C39.3595 27.7699 39.1499 27.036 38.8085 26.3846Z"
            fill="#7E3AF2"
          />
          <path
            d="M32.273 38.9297C31.1342 38.9297 30.2109 39.8533 30.2109 40.9917C30.2109 42.1298 31.1343 43.0531 32.273 43.0531C33.4111 43.0531 34.334 42.1298 34.334 40.9917C34.334 39.8533 33.4111 38.9297 32.273 38.9297Z"
            fill="#7E3AF2"
          />
        </g>
        <defs>
          <clipPath id="clip0_8621_5254">
            <rect width="20" height="20" fill="white" transform="translate(23.334 23.0527)" />
          </clipPath>
        </defs>
      </svg>

      <p className="text-xl dark:text-white">Are you sure?</p>
      <p className="items-center text-[#626262] font-light justify-center dark:text-white">
        Once confirmed, these changes will be applied globally to the main template.
      </p>
    </div>
  );

  const handleSaveTest = (id) => {
    showConfirm(confirmText(), {
      onConfirm() {
        setQuestionsListData((prev) =>
          prev?.map((question) =>
            question?._id === id ? { ...question, title: question?.pendingTitle, pendingTitle: '', isEditMode: false } : question
          )
        );
        handleUpdate(id);
        hideConfirm();
      },
      confirmLabel: 'Confirm',
      cancelLabel: 'Cancel',
    });
  };

  useEffect(() => {
    setScreeningData(list);
    setQuestionsListData(list?.questions);
  }, [list]);

  useEffect(() => {
    setLoading(handleLoading);
  }, [handleLoading]);

  if (!ready) {
    return <Spinner />;
  }

  return (
    <>
      <Drawer.Body className="border dark:border-gray-700 rounded-lg h-full overflow-y-auto">
        <div className="flex flex-col xslg:flex-row justify-between xslg:items-center gap-3 p-4 bg-[#F8FAFC] border-b border-[#F2F4F7] dark:border-gray-700 dark:bg-darkBackgroundCard rounded-t-lg">
          {/* Test details */}
          <Drawer.Body.TestDetails
            label={`${screeningData?.title} Details`}
            duration={screeningData?.duration}
            totalQuestions={screeningData?.numOfQuestions}
          />
        </div>

        <div className="space-y-3 p-4">
          {/* Questions Array */}
          {questionsListData?.map((row) => (
            <Drawer.Body.QuestionOfScreening
              key={row?._id}
              index={row?.index}
              row={row}
              setQuestionsListData={setQuestionsListData}
              canEditQuestion
              handleSaveTest={handleSaveTest}
            />
          ))}
        </div>

        <Drawer.Footer
          className="sticky top-full"
          isPaginationActive={isPaginationActive}
          paginationData={{
            showingText: showingText,
            count: count,
            size: size,
            onPageChange: pagination.update,
            currentPage: page,
            pagesCount: pagesCount,
          }}
        />
      </Drawer.Body>

      <Drawer.Footer>
        <Drawer.Footer.Button label="Cancel" onClick={onClose} tertiary disabled={loading} />
        <Drawer.Footer.Button
          label={applicantDetails?.email ? 'Assign Screening' : 'Generate Link'}
          onClick={handleSubmit}
          loading={loading}
          disabled={loading || questionsListData?.find((question) => question?.isEditMode)}
          mainButton
        />
      </Drawer.Footer>
    </>
  );
};
