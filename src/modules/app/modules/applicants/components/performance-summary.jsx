// Core
import { Dialog, CustomIcon } from '/src';

export const PerformanceSummaryDialog = ({ onClose, applicantDetails }) => {
  return (
    <Dialog size="lg" show popup modalHeader="Performance Summary" onClose={onClose} overflowVisible={true}>
      <div className="space-y-4">
        {/* <div className="flex flex-col bg-white dark:bg-darkBackgroundCard !p-0 rounded-lg space-y-4"> */}
        {/* Strengths Section */}
        {/* <div className="flex flex-col gap-4 px-4 pt-4">
            <div className="flex items-center gap-4">
              <span className="w-3 h-3 rounded-full bg-[#4aa264]"></span>
              <h3 className="text-base font-medium text-green-800 dark:text-green-600">Strengths:</h3>
            </div>

            <ScrollableTabs data={tabsStrengthsData} />
          </div> */}

        {/* <hr className="border-t border-gray-200 dark:border-gray-700" /> */}

        {/* Weaknesses Section */}
        {/* <div className="flex flex-col gap-4 px-4 pb-4">
            <div className="flex items-center gap-4">
              <span className="w-3 h-3 rounded-full bg-[#8A3229]"></span>
              <h3 className="text-base font-medium text-red-800 dark:text-[#950C02]">Weaknesses:</h3>
            </div>

            <ScrollableTabs data={tabsWeaknessesData} />
          </div> */}
        {/* </div> */}

        {/* English Proficiency */}
        {/* {applicantDetails?.averageEnglishScore > 0 && (
          <div className="flex flex-col xslg:flex-row sm:items-center gap-2 py-3 px-2 text-[#667085] rounded-md bg-opacity-30 dark:bg-darkBackgroundCard">
            <div className="flex items-center gap-2">
              <CustomIcon definedIcon="en" />
              <p className="text-sm dark:text-gray-400 font-medium ">English Proficiency</p>
            </div>
            <CompetenceScore percentage={applicantDetails?.averageEnglishScore} />
          </div>
        )} */}

        {/* Attitude Score */}
        {/* {applicantDetails?.averageAttitudeScore > 0 && (
          <div className="flex flex-col xslg:flex-row sm:items-center gap-2 py-3 px-2 text-[#667085] rounded-md bg-opacity-30 dark:bg-darkBackgroundCard">
            <div className="flex items-center gap-2">
              <CustomIcon definedIcon="en" />
              <p className="text-sm dark:text-gray-400 font-medium ">Attitude Score</p>
            </div>
            <CompetenceScore percentage={applicantDetails?.averageAttitudeScore} />
          </div>
        )} */}

        {/* Total warnings */}
        <div className="flex flex-col items-start gap-2 grow px-2 min-w-40">
          <div className="flex items-center  gap-2">
            <CustomIcon definedIcon="warning" width="18" height="18" />
            <p className="text-lg font-medium text-[#667085] dark:text-darkHalfGray ">Total Warnings</p>
            <p className="text-xl  font-medium text-[#C62828] dark:text-white pl-4 ">
              {applicantDetails?.totalWeirdBeauvoir + applicantDetails?.totalMissed + applicantDetails?.totalOverDue}
            </p>
          </div>

          <div className="flex flex-row items-center flex-wrap gap-x-4 gap-y-1 px-1">
            <div className="flex space-x-2 items-center">
              <span className="bg-[#FFAA40] w-3 h-3 block rounded-full"></span>
              <p className="text-sm font-medium text-[#667085]">Overdue</p>
              <p className="text-[15px] dark:text-white font-semibold">{applicantDetails?.totalOverDue}</p>
            </div>
            <div className="flex space-x-2 items-center">
              <span className="bg-[#8A3229] w-3 h-3 block rounded-full"></span>
              <p className="text-sm font-medium text-[#667085]">Cheating Behavior</p>
              <p className="text-[15px] dark:text-white font-semibold">{applicantDetails?.totalWeirdBeauvoir}</p>
            </div>
            <div className="flex space-x-2 items-center">
              <span className="bg-[#C62828] w-3 h-3 block rounded-full"></span>
              <p className="text-sm font-medium text-[#667085]">Missed Deadline</p>
              <p className="text-[15px] dark:text-white font-semibold">{applicantDetails?.totalMissed}</p>
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  );
};
