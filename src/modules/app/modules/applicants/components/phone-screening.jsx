// React
import { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { api } from '../../../../../services/axios';

// Flowbite
import { ToggleSwitch } from 'flowbite-react';

// UI
import { Icon, Api, useNotify, Drawer, EnumText, Button } from '/src';
import { PhoneScreeningPlaceholder } from './phone-screening-placeholder';
import { PhoneSkeleton } from './phone-screening-skeleton';

export const PhoneScreening = ({ isShowPhoneScreening, setShowPhoneScreening }) => {
  // State
  const [details, setDetails] = useState();
  const [toggleSwitch, setToggleSwitch] = useState(false);
  // const [isExpanded, setExpanded] = useState({});

  // Params
  const { id } = useParams();
  const { notify } = useNotify();

  // Methods
  const onClose = () => setShowPhoneScreening(false);
  const handleJoinDate = (date) => {
    const [mon, day, year] = new Date(date).toDateString().slice(4).split(' ');
    return `${day} ${mon}, ${year}`;
  };

  const handleJoinTime = (date) => {
    const newDate = new Date(date);

    if (!isNaN(newDate)) {
      const timeFormatter = new Intl.DateTimeFormat('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      });

      return timeFormatter.format(newDate);
    }
    return '-';
  };

  const handleGet = async () => {
    try {
      const response = await Api.get(`submissions/phone-screening-tests/${id}`);
      setDetails(response?.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  const downloadDocument = async () => {
    try {
      const response = await api.get(`submissions/stages/report/${details[0]._id}`, {
        responseType: 'blob',
      });
      const url = window.URL.createObjectURL(
        new Blob([response.data], {
          type: response.headers['content-type'],
        })
      );
      const a = document.createElement('a');
      a.href = url;
      a.download = 'phone-screening-report.xlsx';
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      notify.error(error.response?.data.message);
    }
  };

  const filteredQuestions = () =>
    toggleSwitch ? details[0]?.questionsWithAnswers?.filter((question) => question.answer === '') : details[0]?.questionsWithAnswers;

  useEffect(() => {
    if (id) {
      handleGet();
    }
  }, []);

  return (
    <Drawer onClose={onClose}>
      <Drawer.SingleView>
        <Drawer.Header headerLabel="Screening Details" onClose={onClose}>
          {details && details?.length > 0 && (
            <div className="flex flex-col md:flex-row gap-2.5 text-[#667085] dark:text-white text-sm font-medium">
              <div className="flex flex-row gap-1.5">
                <Icon icon="material-symbols:check-circle-outline-rounded" width={20} />
                <span>Submitted</span>
                {handleJoinDate(details[0]?.submission?.submittedAt)} at {handleJoinTime(details[0]?.submission?.submittedAt)}
              </div>

              <span className="hidden md:block">•</span>

              <div className="flex flex-col xssm:flex-row gap-2.5">
                <div className="flex flex-row gap-1.5">
                  <Icon icon="mynaui:clock-three" width={20} />
                  <span>{details[0]?.submission?.timeTaken}</span> mins taken
                </div>

                <span className="hidden xssm:block">•</span>

                <div className="flex flex-row gap-1">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M12.5 9.4875L10.8812 7.86875L10 8.75L12.5 11.25L16.25 7.5L15.3687 6.61875L12.5 9.4875ZM5 11.25H6.25V12.5H5V11.25ZM7.5 5.625H4.375V6.875H6.875V8.125H5V10H6.25V9.375H7.5C7.66576 9.375 7.82473 9.30915 7.94194 9.19194C8.05915 9.07473 8.125 8.91576 8.125 8.75V6.25C8.125 6.08424 8.05915 5.92527 7.94194 5.80806C7.82473 5.69085 7.66576 5.625 7.5 5.625Z"
                      fill="#667085"
                    />
                    <path
                      d="M11.085 18.75L10 18.125L12.5 13.75H16.25C16.9419 13.75 17.5 13.1919 17.5 12.5V5C17.5 4.30813 16.9419 3.75 16.25 3.75H3.75C3.05813 3.75 2.5 4.30813 2.5 5V12.5C2.5 13.1919 3.05813 13.75 3.75 13.75H9.375V15H3.75C3.08696 15 2.45107 14.7366 1.98223 14.2678C1.51339 13.7989 1.25 13.163 1.25 12.5V5C1.25 3.61875 2.36875 2.5 3.75 2.5H16.25C17.6313 2.5 18.75 3.61875 18.75 5V12.5C18.75 13.163 18.4866 13.7989 18.0178 14.2678C17.5489 14.7366 16.913 15 16.25 15H13.2281L11.085 18.75Z"
                      fill="#667085"
                    />
                  </svg>
                  Answered: <span className="font-bold">{details[0]?.submission?.questionsSummary?.totalAnswers}</span>/{' '}
                  {details[0]?.submission?.questionsSummary?.totalQuestions}
                </div>
              </div>
            </div>
          )}
        </Drawer.Header>

        <Drawer.Body>
          {details ? (
            details?.length > 0 ? (
              <div className="flex flex-col h-full border rounded-lg overflow-hidden">
                <div className="flex flex-col xsmd:flex-row justify-between xsmd:items-center gap-3 p-4 bg-[#F8FAFC] border-b border-[#F2F4F7] dark:bg-darkBackgroundCard rounded-t-lg">
                  {/* Test details */}
                  <Drawer.Body.TestDetails
                    label="Screening Details"
                    enumText={<EnumText name={'QuizDifficulty'} value={details[0]?.submission?.quiz?.difficulty} />}
                    duration={details[0]?.submission?.quiz?.duration}
                    totalQuestions={details[0]?.submission?.questionsSummary?.totalQuestions}
                  />

                  {/* Buttons */}
                  <div className="flex flex-row gap-1">
                    {/* <Button
                      label="Expand All"
                      icon="ic:outline-remove-red-eye"
                      onClick={() => filteredQuestions().map((question) => setExpanded((prev) => ({ ...prev, [question.id]: !prev[question.id] })))}
                    /> */}

                    <Button tertiary label="Export Report" icon="solar:download-minimalistic-bold" onClick={downloadDocument} />
                  </div>
                </div>

                {/* ToggleSwitch */}
                <div className="flex justify-start gap-4 text-sm font-medium m-4 pr-4">
                  <span>Unanswered Questions</span>
                  <ToggleSwitch
                    sizing="sm"
                    checked={toggleSwitch}
                    value={toggleSwitch}
                    onChange={() => setToggleSwitch((prev) => !prev)}
                    color="purple"
                  />
                </div>

                {/* Questions Array */}
                <div className="px-4 pb-4 space-y-6 overflow-y-auto">
                  {!filteredQuestions()?.length && toggleSwitch ? (
                    <div className="flex flex-col items-center gap-4 mt-16">
                      <svg width="66" height="65" viewBox="0 0 66 65" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M33.2035 39C34.5685 39 35.251 38.025 35.4542 36.4528C35.6167 34.2834 36.2585 33.1419 38.8951 31.3341C41.6292 29.4044 43.1567 26.9059 43.1567 23.2659C43.1567 17.8831 39.4192 14.2188 33.9673 14.2188C29.8235 14.2188 26.6873 16.2175 25.436 19.4594C25.0557 20.3444 24.8648 21.2993 24.8754 22.2625C24.8754 23.8591 25.7001 24.8625 27.0895 24.8625C28.1945 24.8625 28.9379 24.2653 29.3807 22.7906C30.0226 20.3856 31.5135 19.0734 33.7439 19.0734C36.222 19.0734 37.9282 20.8853 37.9282 23.4772C37.9282 25.7644 37.0832 27.0725 34.5889 28.86C32.0742 30.6191 30.827 32.5731 30.827 35.5225V35.9734C30.827 37.7122 31.672 39 33.2035 39Z"
                          fill="#9CA3AF"
                        />
                        <path
                          d="M42.2347 10.2097L38.4931 6.3747L41.3978 3.53907L43.9247 6.12689L47.5403 6.0822C49.1054 6.06368 50.6584 6.35828 52.1079 6.94868C53.5575 7.53909 54.8744 8.41337 55.9811 9.52013C57.0879 10.6269 57.9622 11.9438 58.5526 13.3933C59.143 14.8429 59.4376 16.3959 59.4191 17.9609L59.3784 21.5766L61.9622 24.1034C63.0813 25.197 63.9705 26.5031 64.5775 27.9451C65.1846 29.3872 65.4974 30.936 65.4974 32.5006C65.4974 34.0653 65.1846 35.6141 64.5775 37.0561C63.9705 38.4982 63.0813 39.8043 61.9622 40.8978L59.3744 43.4247L59.4191 47.0403C59.4376 48.6054 59.143 50.1584 58.5526 51.6079C57.9622 53.0575 57.0879 54.3744 55.9811 55.4811C54.8744 56.5879 53.5575 57.4622 52.1079 58.0526C50.6584 58.643 49.1054 58.9376 47.5403 58.9191L43.9247 58.8784L41.3978 61.4622C40.3043 62.5813 38.9982 63.4705 37.5561 64.0775C36.1141 64.6846 34.5653 64.9974 33.0006 64.9974C31.436 64.9974 29.8872 64.6846 28.4451 64.0775C27.0031 63.4705 25.697 62.5813 24.6034 61.4622L22.0766 58.8744L18.4609 58.9191C16.8959 58.9376 15.3429 58.643 13.8933 58.0526C12.4438 57.4622 11.1269 56.5879 10.0201 55.4811C8.91337 54.3744 8.03909 53.0575 7.44868 51.6079C6.85828 50.1584 6.56368 48.6054 6.5822 47.0403L6.62282 43.4247L4.03907 40.8978C2.92002 39.8043 2.03082 38.4982 1.42373 37.0561C0.81664 35.6141 0.503906 34.0653 0.503906 32.5006C0.503906 30.936 0.81664 29.3872 1.42373 27.9451C2.03082 26.5031 2.92002 25.197 4.03907 24.1034L6.62689 21.5766L6.5822 17.9609C6.56368 16.3959 6.85828 14.8429 7.44868 13.3933C8.03909 11.9438 8.91337 10.6269 10.0201 9.52013C11.1269 8.41337 12.4438 7.53909 13.8933 6.94868C15.3429 6.35828 16.8959 6.06368 18.4609 6.0822L22.0766 6.12282L24.6034 3.53907C25.697 2.42002 27.0031 1.53082 28.4451 0.92373C29.8872 0.31664 31.436 0.00390625 33.0006 0.00390625C34.5653 0.00390625 36.1141 0.31664 37.5561 0.92373C38.9982 1.53082 40.3043 2.42002 41.3978 3.53907L38.4931 6.3747C37.778 5.64258 36.9237 5.06083 35.9804 4.66364C35.0372 4.26645 34.0241 4.06184 33.0006 4.06184C31.9772 4.06184 30.9641 4.26645 30.0208 4.66364C29.0776 5.06083 28.2233 5.64258 27.5081 6.3747L23.7706 10.2097L18.4081 10.1447C17.3851 10.1333 16.3701 10.3264 15.4228 10.7127C14.4754 11.0991 13.6149 11.6709 12.8916 12.3945C12.1684 13.1181 11.597 13.979 11.2112 14.9265C10.8253 15.8741 10.6327 16.8892 10.6447 17.9122L10.7097 23.2666L6.8747 27.0081C6.14258 27.7233 5.56083 28.5776 5.16364 29.5208C4.76645 30.4641 4.56184 31.4772 4.56184 32.5006C4.56184 33.5241 4.76645 34.5372 5.16364 35.4804C5.56083 36.4237 6.14258 37.278 6.8747 37.9931L10.7097 41.7306L10.6447 47.0931C10.6333 48.1162 10.8264 49.1312 11.2127 50.0785C11.5991 51.0258 12.1709 51.8864 12.8945 52.6096C13.6181 53.3329 14.479 53.9042 15.4265 54.2901C16.3741 54.6759 17.3892 54.8685 18.4122 54.8566L23.7666 54.7916L27.5081 58.6266C28.2233 59.3587 29.0776 59.9404 30.0208 60.3376C30.9641 60.7348 31.9772 60.9394 33.0006 60.9394C34.0241 60.9394 35.0372 60.7348 35.9804 60.3376C36.9237 59.9404 37.778 59.3587 38.4931 58.6266L42.2306 54.7916L47.5931 54.8566C48.6162 54.868 49.6312 54.6749 50.5785 54.2885C51.5258 53.9022 52.3864 53.3304 53.1096 52.6068C53.8329 51.8831 54.4042 51.0223 54.7901 50.0747C55.1759 49.1272 55.3685 48.1121 55.3566 47.0891L55.2916 41.7347L59.1266 37.9931C59.8587 37.278 60.4404 36.4237 60.8376 35.4804C61.2348 34.5372 61.4394 33.5241 61.4394 32.5006C61.4394 31.4772 61.2348 30.4641 60.8376 29.5208C60.4404 28.5776 59.8587 27.7233 59.1266 27.0081L55.2916 23.2706L55.3566 17.9081C55.368 16.8851 55.1749 15.8701 54.7885 14.9228C54.4022 13.9754 53.8304 13.1149 53.1068 12.3916C52.3831 11.6684 51.5223 11.097 50.5747 10.7112C49.6272 10.3253 48.6121 10.1327 47.5891 10.1447L42.2347 10.2097Z"
                          fill="#9CA3AF"
                        />
                        <path
                          d="M28.9414 44.6875C28.9414 43.6101 29.3694 42.5767 30.1313 41.8149C30.8932 41.053 31.9265 40.625 33.0039 40.625C34.0814 40.625 35.1147 41.053 35.8765 41.8149C36.6384 42.5767 37.0664 43.6101 37.0664 44.6875C37.0664 45.7649 36.6384 46.7983 35.8765 47.5601C35.1147 48.322 34.0814 48.75 33.0039 48.75C31.9265 48.75 30.8932 48.322 30.1313 47.5601C29.3694 46.7983 28.9414 45.7649 28.9414 44.6875Z"
                          fill="#9CA3AF"
                        />
                      </svg>

                      <p className="text-sm font-medium leading-4 text-[#6B7280]">All questions have been answered</p>
                    </div>
                  ) : (
                    filteredQuestions()?.map((row, index) => <Drawer.Body.QuestionOfScreening key={row?.question} index={index} row={row} />)
                  )}
                </div>
              </div>
            ) : (
              <PhoneScreeningPlaceholder />
            )
          ) : (
            <PhoneSkeleton />
          )}
        </Drawer.Body>
      </Drawer.SingleView>
    </Drawer>
  );
};
