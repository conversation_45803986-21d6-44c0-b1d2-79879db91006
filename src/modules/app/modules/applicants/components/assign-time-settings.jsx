// React
import React, { useState } from 'react';

// Core
import { Dialog, Button, useNotify, CustomIcon, useScreenSize } from '/src';

// Rsuite
import { DatePicker, DateRangePicker } from 'rsuite';

export const TimeSettingsDialog = ({ onClose, startDate, setStartDate, dueDate, setDueDate, type }) => {
  const [localStartDate, setLocalStartDate] = useState(startDate);
  const [localDueDate, setLocalDueDate] = useState(dueDate);

  const { notify } = useNotify();
  const screen = useScreenSize();

  const { beforeToday } = DateRangePicker;

  const handleSubmit = () => {
    if (localStartDate < localDueDate) {
      setStartDate(localStartDate);
      setDueDate(localDueDate);
      onClose();
    } else {
      notify.error("Start date can't be greater than due date");
    }
  };

  return (
    <Dialog size="lg" show popup onClose={onClose} overflowVisible={true}>
      <div className="space-y-6">
        <div className="space-y-3 flex-col justify-center">
          <CustomIcon definedIcon="hourglass" />
          <p className="text-[#313437] dark:text-white text-lg text-center font-medium capitalize">customize {type} availability</p>
          <p className="text-[#6B7280] dark:text-gray-300 text-center text-[15px] font-medium">
            Applicants can start the {type} at any time within the selected period.
          </p>
        </div>

        <div className="space-y-3">
          <div className="sm:flex space-y-3 sm:space-y-0 items-center gap-5 ">
            <p className="min-w-40 dark:text-white">
              Start Date & Time <span className="text-red-600 ml-[1px]">*</span>
            </p>
            <DatePicker
              format="dd/MM/yyyy hh:mm aa"
              value={localStartDate}
              onChange={(value) => setLocalStartDate(value)}
              className="w-full"
              shouldDisableDate={beforeToday()}
              placement="topStart"
              showMeridiem
            />
          </div>

          <div className="sm:flex space-y-3 sm:space-y-0 items-center gap-5">
            <p className="min-w-40 dark:text-white">
              Expiry Date & Time <span className="text-red-600 ml-[1px]">*</span>
            </p>
            <DatePicker
              format="dd/MM/yyyy hh:mm aa"
              value={localDueDate}
              onChange={(value) => setLocalDueDate(value)}
              className="w-full"
              shouldDisableDate={beforeToday()}
              placement="topStart"
              showMeridiem
            />
          </div>
        </div>

        <div className="grid grid-cols-6 gap-4">
          <Button className="col-span-2" tertiary label="Cancel" onClick={onClose} />
          <Button className="col-span-4 capitalize" label={`Confirm ${screen.gt.xs() ? type : ''} Date`} onClick={handleSubmit} />
        </div>
      </div>
    </Dialog>
  );
};
