import { useContext, useState } from 'react';

// Context
import { AppContext } from '/src/components/provider';

// Core
import { Icon, Card, ResultChart, EnumText, useScreenSize, useNotify, ResultStatus, CustomIcon, Button, Drawer, SubscribeDialog } from '/src';

import { api } from '../../../../../services/axios';

// Flowbite
import { Tooltip, ToggleSwitch } from 'flowbite-react';

export const TestCard = ({ test, type }) => {
  // Context
  const { userData } = useContext(AppContext);
  const isSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));

  // State
  const [needSubscription, setNeedSubscription] = useState(false);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [showMore, setShowMore] = useState(false);
  const [showQuestions, setShowQuestions] = useState(false);
  const [searchQuestions, setSearchQuestions] = useState('');
  const [toggleSwitch, setToggleSwitch] = useState(false);
  const [expandQuestionToggle, setExpandQuestionToggle] = useState(false);

  // Hooks
  const screen = useScreenSize();
  const { notify } = useNotify();

  const WeirdBehavior =
    test?.weirdBehavior?.tabSwitchedCount > 0 || test?.weirdBehavior?.ipChangeCount > 0 || test?.weirdBehavior?.openContextMenuCount > 0;

  const correctAnswers = test?.questionsSummary?.correctAnswers;
  const skippedQuestions = test?.questionsSummary?.skippedQuestions;
  const wrongAnswers = test?.questionsSummary?.wrongAnswers;
  const unAnsweredQuestions = test?.questionsSummary?.unAnsweredQuestions;

  const totalQuestions = test?.questionsSummary?.totalQuestions;

  const correctPercentage = Math.round((correctAnswers / totalQuestions) * 100);
  const skippedPercentage = Math.round((skippedQuestions / totalQuestions) * 100);
  const wrongPercentage = Math.round((wrongAnswers / totalQuestions) * 100);
  const unansweredPercentage = Math.round((unAnsweredQuestions / totalQuestions) * 100);

  const toggleDropdown = () => {
    setIsDropdownVisible(!isDropdownVisible);
  };

  const formatDate = (dateString) => {
    if (!dateString) return '—';

    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'medium',
      timeStyle: 'short',
    }).format(date);
  };

  const downloadDocument = async () => {
    try {
      const response = await api.get(
        `${
          type === 'submissions' ? 'submissions' : type === 'interviews' ? 'ai-interview' : type === 'screening' ? 'submissions' : '—'
        }/stages/report/${test._id}`,
        {
          responseType: 'blob',
        }
      );
      const url = window.URL.createObjectURL(
        new Blob([response.data], {
          type: response.headers['content-type'],
        })
      );
      const a = document.createElement('a');
      a.href = url;
      a.download = `${type === 'submissions' ? 'test' : type === 'interviews' ? 'interview' : type === 'screening' ? 'screening' : '—'}-report.xlsx`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      notify.error(error.response?.data.message);
    }
  };

  const statusIsNotSubmitted = () => {
    const statusIsNotSubmittedResults = () => {
      if (test?.expired) {
        if (!test?.startedAt) {
          // Missed
          return {
            title: 'Missed Deadline !',
            subTitle: `The applicant did not attempt the ${
              type === 'submissions'
                ? 'test'
                : type === 'interviews'
                ? 'interview'
                : type === 'screening'
                ? 'screening'
                : type === 'warnings'
                ? test.type
                : '—'
            } before the deadline.`,
            svg: (
              <svg width="66" height="65" viewBox="0 0 66 65" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M8.62501 32.5C8.62501 29.299 9.25549 26.1294 10.4804 23.1721C11.7054 20.2148 13.5009 17.5277 15.7643 15.2643C18.0277 13.0009 20.7148 11.2054 23.6721 9.98045C26.6294 8.75549 29.799 8.12501 33 8.12501C36.201 8.12501 39.3706 8.75549 42.3279 9.98045C45.2852 11.2054 47.9723 13.0009 50.2357 15.2643C52.4992 17.5277 54.2946 20.2148 55.5196 23.1721C56.7445 26.1294 57.375 29.299 57.375 32.5H61.4375C61.4375 26.8756 59.7697 21.3775 56.6449 16.701C53.5202 12.0245 49.0788 8.37955 43.8826 6.22719C38.6863 4.07482 32.9685 3.51166 27.4521 4.60893C21.9358 5.7062 16.8687 8.41461 12.8917 12.3917C8.91461 16.3687 6.2062 21.4358 5.10893 26.9521C4.01166 32.4685 4.57482 38.1863 6.72719 43.3826C8.87955 48.5788 12.5245 53.0202 17.201 56.1449C21.8775 59.2697 27.3756 60.9375 33 60.9375V56.875C26.5354 56.875 20.3355 54.3069 15.7643 49.7357C11.1931 45.1645 8.62501 38.9647 8.62501 32.5ZM30.9688 16.25V30.4688H16.75V34.5313H35.0313V16.25H30.9688ZM47.2188 32.5C43.4477 32.5 39.8311 33.9981 37.1646 36.6646C34.4981 39.3311 33 42.9477 33 46.7188C33 50.4898 34.4981 54.1064 37.1646 56.7729C39.8311 59.4395 43.4477 60.9375 47.2188 60.9375C50.9898 60.9375 54.6064 59.4395 57.2729 56.7729C59.9395 54.1064 61.4375 50.4898 61.4375 46.7188C61.4375 42.9477 59.9395 39.3311 57.2729 36.6646C54.6064 33.9981 50.9898 32.5 47.2188 32.5ZM45.1875 38.5938V46.7188H49.25V38.5938H45.1875ZM47.2188 48.75C46.4107 48.75 45.6357 49.071 45.0643 49.6424C44.4929 50.2138 44.1719 50.9888 44.1719 51.7969C44.1719 52.605 44.4929 53.3799 45.0643 53.9513C45.6357 54.5227 46.4107 54.8438 47.2188 54.8438C48.0268 54.8438 48.8018 54.5227 49.3732 53.9513C49.9446 53.3799 50.2656 52.605 50.2656 51.7969C50.2656 50.9888 49.9446 50.2138 49.3732 49.6424C48.8018 49.071 48.0268 48.75 47.2188 48.75Z"
                  fill="#9CA3AF"
                />
              </svg>
            ),
          };
        } else if (test?.startedAt) {
          // Overdue
          return;
        }
      } else if (test?.locked) {
        // Scheduled
        return {
          title: `${type === 'submissions' ? 'Test' : type === 'interviews' ? 'Interview' : type === 'screening' ? 'Screening' : '—'} Scheduled.`,
          subTitle: `${
            type === 'submissions' ? 'Test' : type === 'interviews' ? 'Interview' : type === 'screening' ? 'Screening' : '—'
          } will be accessible once the start time arrives.`,
          svg: (
            <svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M11.5385 28.8457C10.5185 28.8457 9.54019 29.2509 8.8189 29.9722C8.0976 30.6935 7.69238 31.6718 7.69238 32.6919V36.538C7.69238 37.5581 8.0976 38.5364 8.8189 39.2577C9.54019 39.9789 10.5185 40.3842 11.5385 40.3842H15.3847C16.4048 40.3842 17.383 39.9789 18.1043 39.2577C18.8256 38.5364 19.2308 37.5581 19.2308 36.538V32.6919C19.2308 31.6718 18.8256 30.6935 18.1043 29.9722C17.383 29.2509 16.4048 28.8457 15.3847 28.8457H11.5385ZM11.5385 32.6919V36.538H15.3847V32.6919H11.5385ZM23.077 32.6919C23.077 31.6718 23.4822 30.6935 24.2035 29.9722C24.9248 29.2509 25.9031 28.8457 26.9232 28.8457H30.7693C31.7894 28.8457 32.7677 29.2509 33.4889 29.9722C34.2102 30.6935 34.6155 31.6718 34.6155 32.6919V36.538C34.6155 37.5581 34.2102 38.5364 33.4889 39.2577C32.7677 39.9789 31.7894 40.3842 30.7693 40.3842H26.9232C25.9031 40.3842 24.9248 39.9789 24.2035 39.2577C23.4822 38.5364 23.077 37.5581 23.077 36.538V32.6919ZM26.9232 32.6919H30.7693V36.538H26.9232V32.6919ZM42.3078 28.8457C41.2877 28.8457 40.3094 29.2509 39.5881 29.9722C38.8668 30.6935 38.4616 31.6718 38.4616 32.6919V36.538C38.4616 37.5581 38.8668 38.5364 39.5881 39.2577C40.3094 39.9789 41.2877 40.3842 42.3078 40.3842H46.1539C47.174 40.3842 48.1523 39.9789 48.8736 39.2577C49.5949 38.5364 50.0001 37.5581 50.0001 36.538V32.6919C50.0001 31.6718 49.5949 30.6935 48.8736 29.9722C48.1523 29.2509 47.174 28.8457 46.1539 28.8457H42.3078ZM42.3078 32.6919V36.538H46.1539V32.6919H42.3078ZM7.69238 48.0765C7.69238 47.0564 8.0976 46.0781 8.8189 45.3568C9.54019 44.6355 10.5185 44.2303 11.5385 44.2303H15.3847C16.4048 44.2303 17.383 44.6355 18.1043 45.3568C18.8256 46.0781 19.2308 47.0564 19.2308 48.0765V51.9226C19.2308 52.9427 18.8256 53.921 18.1043 54.6423C17.383 55.3636 16.4048 55.7688 15.3847 55.7688H11.5385C10.5185 55.7688 9.54019 55.3636 8.8189 54.6423C8.0976 53.921 7.69238 52.9427 7.69238 51.9226V48.0765ZM15.3847 48.0765V51.9226H11.5385V48.0765H15.3847ZM26.9232 44.2303C25.9031 44.2303 24.9248 44.6355 24.2035 45.3568C23.4822 46.0781 23.077 47.0564 23.077 48.0765V51.9226C23.077 52.9427 23.4822 53.921 24.2035 54.6423C24.9248 55.3636 25.9031 55.7688 26.9232 55.7688H30.7693C31.7894 55.7688 32.7677 55.3636 33.4889 54.6423C34.2102 53.921 34.6155 52.9427 34.6155 51.9226V48.0765C34.6155 47.0564 34.2102 46.0781 33.4889 45.3568C32.7677 44.6355 31.7894 44.2303 30.7693 44.2303H26.9232ZM30.7693 48.0765H26.9232V51.9226H30.7693V48.0765Z"
                fill="#9CA3AF"
              />
              <path
                d="M57.6923 50.9612C57.6923 50.4511 57.4897 49.962 57.1291 49.6013C56.7684 49.2407 56.2793 49.0381 55.7693 49.0381C55.2592 49.0381 54.7701 49.2407 54.4094 49.6013C54.0488 49.962 53.8462 50.4511 53.8462 50.9612V56.565L56.3327 59.0515C56.6954 59.4019 57.1812 59.5957 57.6854 59.5913C58.1897 59.5869 58.672 59.3847 59.0285 59.0281C59.3851 58.6716 59.5873 58.1892 59.5917 57.685C59.5961 57.1808 59.4023 56.695 59.052 56.3323L57.6923 54.9727V50.9612Z"
                fill="#9CA3AF"
              />
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M11.5385 1.92308C11.5385 1.41305 11.7411 0.923903 12.1017 0.563257C12.4624 0.20261 12.9515 0 13.4615 0C13.9716 0 14.4607 0.20261 14.8214 0.563257C15.182 0.923903 15.3846 1.41305 15.3846 1.92308V11.5385C15.3846 12.0485 15.5872 12.5376 15.9479 12.8983C16.3085 13.2589 16.7977 13.4615 17.3077 13.4615C17.8177 13.4615 18.3069 13.2589 18.6675 12.8983C19.0282 12.5376 19.2308 12.0485 19.2308 11.5385V5.76923H38.4615V1.92308C38.4615 1.41305 38.6641 0.923903 39.0248 0.563257C39.3854 0.20261 39.8746 0 40.3846 0C40.8946 0 41.3838 0.20261 41.7444 0.563257C42.1051 0.923903 42.3077 1.41305 42.3077 1.92308V11.5385C42.3077 12.0485 42.5103 12.5376 42.8709 12.8983C43.2316 13.2589 43.7207 13.4615 44.2308 13.4615C44.7408 13.4615 45.2299 13.2589 45.5906 12.8983C45.9512 12.5376 46.1538 12.0485 46.1538 11.5385V5.76923H51.9231C53.4532 5.76923 54.9206 6.37706 56.0025 7.459C57.0845 8.54094 57.6923 10.0084 57.6923 11.5385V42.4423C61.0607 42.9291 64.1197 44.6736 66.2538 47.3247C68.388 49.9757 69.439 53.3367 69.1953 56.7313C68.9516 60.126 67.4313 63.3023 64.9404 65.6214C62.4494 67.9404 59.1726 69.23 55.7692 69.2308C53.5987 69.2325 51.4601 68.7087 49.5361 67.704C47.6122 66.6993 45.9601 65.2437 44.7211 63.4615H5.76923C4.23914 63.4615 2.77171 62.8537 1.68977 61.7718C0.607828 60.6898 0 59.2224 0 57.6923V11.5385C0 10.0084 0.607828 8.54094 1.68977 7.459C2.77171 6.37706 4.23914 5.76923 5.76923 5.76923H11.5385V1.92308ZM42.3077 55.7692C42.3069 52.5316 43.4729 49.4021 45.5921 46.9544C47.7113 44.5067 50.6417 42.9048 53.8462 42.4423V23.0769H3.84615V57.6923C3.84615 58.2023 4.04876 58.6915 4.40941 59.0521C4.77006 59.4128 5.2592 59.6154 5.76923 59.6154H42.8654C42.4944 58.3671 42.3066 57.0715 42.3077 55.7692ZM65.3846 55.7692C65.3846 58.3194 64.3716 60.7651 62.5683 62.5683C60.7651 64.3716 58.3194 65.3846 55.7692 65.3846C53.2191 65.3846 50.7734 64.3716 48.9701 62.5683C47.1669 60.7651 46.1538 58.3194 46.1538 55.7692C46.1538 53.2191 47.1669 50.7734 48.9701 48.9701C50.7734 47.1669 53.2191 46.1538 55.7692 46.1538C58.3194 46.1538 60.7651 47.1669 62.5683 48.9701C64.3716 50.7734 65.3846 53.2191 65.3846 55.7692Z"
                fill="#9CA3AF"
              />
            </svg>
          ),
        };
      } else if (test?.submittedAt) {
        // Submitted
        return;
      } else if (test?.startedAt) {
        // In Progress
        return {
          title: 'Pending Score...',
          subTitle: `The score will appear once the applicant completes the ${
            type === 'submissions' ? 'test' : type === 'interviews' ? 'interview' : type === 'screening' ? 'screening' : '—'
          }.`,
          svg: (
            <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M46.195 60C42.348 60 39.0854 58.7337 36.4072 56.2012C33.7291 53.6644 32.39 50.5793 32.39 46.9459C32.39 43.3125 33.7291 40.2284 36.4072 37.6938C39.0854 35.1591 42.3469 33.8918 46.1915 33.8918C50.0385 33.8918 53.3011 35.1591 55.9793 37.6938C58.6598 40.2263 60 43.3103 60 46.9459C60 50.5815 58.6598 53.6666 55.9793 56.2012C53.2988 58.7359 50.0374 60.0022 46.195 60ZM47.5203 46.4074V38.7871C47.5203 38.452 47.388 38.1583 47.1234 37.9059C46.8565 37.6579 46.5459 37.5339 46.1915 37.5339C45.8395 37.5339 45.5301 37.659 45.2632 37.9092C44.9986 38.1594 44.8663 38.452 44.8663 38.7871V46.4009C44.8663 46.7337 44.9307 47.0568 45.0595 47.3701C45.1861 47.6813 45.4001 47.9793 45.7015 48.2643L50.9646 53.2412C51.2223 53.4827 51.526 53.6144 51.8758 53.6361C52.2255 53.6579 52.5499 53.5262 52.849 53.2412C53.1481 52.9562 53.2988 52.6592 53.3011 52.3503C53.3034 52.0413 53.1539 51.7444 52.8525 51.4593L47.5203 46.4074ZM5.57722 56.7365C4.04257 56.7365 2.72994 56.2197 1.63934 55.1863C0.548748 54.1528 0.00230083 52.9138 0 51.4691V9.79059C0 8.34158 0.546448 7.10144 1.63934 6.07017C2.73224 5.03889 4.04372 4.52216 5.57377 4.51999H21.5772C21.8993 3.24504 22.616 2.17351 23.7274 1.30541C24.8387 0.435138 26.1329 0 27.61 0C29.1148 0 30.4193 0.435138 31.5237 1.30541C32.6281 2.17134 33.3414 3.24286 33.6635 4.51999H49.6428C51.1775 4.51999 52.4912 5.03671 53.5841 6.07017C54.677 7.10362 55.2223 8.34376 55.22 9.79059V25.4816C55.22 25.9451 55.0544 26.3323 54.723 26.6435C54.3917 26.9546 53.981 27.1112 53.4909 27.1134C53.0009 27.1156 52.5913 26.9589 52.2623 26.6435C51.9333 26.328 51.7688 25.9407 51.7688 25.4816V9.79385C51.7688 9.29127 51.5479 8.83002 51.1061 8.41012C50.6644 7.99021 50.1766 7.78134 49.6428 7.78352H41.415V12.4275C41.415 13.1738 41.1504 13.8004 40.6212 14.3073C40.092 14.8142 39.4363 15.0666 38.654 15.0645H16.566C15.7837 15.0645 15.128 14.8121 14.5988 14.3073C14.0696 13.8026 13.805 13.176 13.805 12.4275V7.78352H5.57722C5.04573 7.78352 4.55795 7.99238 4.11389 8.41012C3.66983 8.82785 3.44895 9.28909 3.45125 9.79385V51.4659C3.45125 52.0511 3.65027 52.532 4.04832 52.9083C4.44636 53.2847 4.956 53.4729 5.57722 53.4729H25.6842C26.1743 53.4729 26.585 53.6296 26.9163 53.9429C27.2476 54.2562 27.4121 54.6445 27.4098 55.108C27.4075 55.5714 27.243 55.9587 26.9163 56.2698C26.5896 56.5809 26.1789 56.7365 25.6842 56.7365H5.57722ZM27.6204 8.53413C28.4142 8.53413 29.0756 8.28175 29.6048 7.77699C30.134 7.27006 30.3986 6.64128 30.3986 5.89067C30.3986 5.14006 30.1306 4.51455 29.5945 4.01414C29.0584 3.51373 28.3934 3.26353 27.5997 3.26353C26.8059 3.26353 26.1444 3.51809 25.6152 4.0272C25.086 4.53631 24.8214 5.16508 24.8214 5.91352C24.8214 6.66195 25.0906 7.28637 25.629 7.78678C26.1674 8.28719 26.8312 8.53413 27.6204 8.53413Z"
                fill="#9CA3AF"
              />
            </svg>
          ),
        };
      } else {
        // Not Started
        return {
          title: ' Waiting for applicant...',
          subTitle: `The ${
            type === 'submissions' ? 'test' : type === 'interviews' ? 'interview' : type === 'screening' ? 'screening' : '—'
          } is available, but the applicant hasn’t started yet.`,
          svg: (
            <svg width="65" height="62" viewBox="0 0 65 62" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_8615_7867)">
                <path
                  d="M47.125 1.00017C44.4911 1.00131 41.89 1.53123 39.5078 2.55201C37.1256 3.57279 35.0211 5.05921 33.345 6.90493H1.625C1.19402 6.90493 0.780698 7.06045 0.475952 7.33729C0.171205 7.61413 0 7.98961 0 8.38112C0 8.77263 0.171205 9.1481 0.475952 9.42494C0.780698 9.70178 1.19402 9.85731 1.625 9.85731H4.875V17.2383C4.86709 19.3719 5.32553 21.4857 6.22373 23.4572C7.12194 25.4287 8.44205 27.2185 10.1075 28.723C11.8598 30.3063 13.9502 31.5471 16.25 32.3692V37.5359C12.8976 38.7247 10.0183 40.805 7.98881 43.5047C5.95927 46.2043 4.874 49.3977 4.875 52.6668V60.0478H1.625C1.19402 60.0478 0.780698 60.2033 0.475952 60.4801C0.171205 60.757 0 61.1325 0 61.524C0 61.9155 0.171205 62.291 0.475952 62.5678C0.780698 62.8446 1.19402 63.0002 1.625 63.0002H43.875C44.306 63.0002 44.7193 62.8446 45.0241 62.5678C45.3288 62.291 45.5 61.9155 45.5 61.524C45.5 61.1325 45.3288 60.757 45.0241 60.4801C44.7193 60.2033 44.306 60.0478 43.875 60.0478H40.625V52.6668C40.626 49.3977 39.5407 46.2043 37.5112 43.5047C35.4817 40.805 32.6024 38.7247 29.25 37.5359V32.3692C31.3562 31.592 33.2848 30.4657 34.9375 29.0478C37.062 30.869 39.6454 32.1896 42.4617 32.8939C45.278 33.5981 48.2414 33.6647 51.0926 33.0877C53.9438 32.5106 56.5959 31.3077 58.8168 29.584C61.0377 27.8603 62.7598 25.6684 63.8323 23.2002C64.9048 20.7319 65.2951 18.0625 64.9691 15.4255C64.643 12.7885 63.6105 10.2643 61.9619 8.0737C60.3133 5.88312 58.0989 4.09294 55.5124 2.85981C52.9258 1.62668 50.0461 0.988173 47.125 1.00017ZM8.125 60.0478V52.6668C8.12513 51.673 8.25053 50.6824 8.49875 49.7144H11.5375C11.7643 49.7149 11.9884 49.7585 12.1955 49.8424C12.4026 49.9263 12.5881 50.0487 12.74 50.2016C13.6534 51.0323 14.8925 51.4996 16.185 51.5006C17.4752 51.5035 18.714 51.0417 19.63 50.2164L21.6125 48.4006C21.9163 48.1302 22.3247 47.9787 22.75 47.9787C23.1754 47.9787 23.5837 48.1302 23.8875 48.4006L25.8213 50.1573C26.734 50.9776 27.9661 51.4378 29.25 51.4378C30.5339 51.4378 31.7661 50.9776 32.6788 50.1573C32.9814 49.8846 33.3899 49.7308 33.8162 49.7292H37.0663C37.2847 50.6955 37.3882 51.6804 37.375 52.6668V60.0478H8.125ZM27.1375 29.9187C26.8087 30.0127 26.5213 30.1992 26.3168 30.4513C26.1123 30.7034 26.0014 31.008 26 31.3211V38.584C26.0014 38.8971 26.1123 39.2017 26.3168 39.4538C26.5213 39.7059 26.8087 39.8924 27.1375 39.9864C29.0199 40.5227 30.7634 41.3995 32.263 42.5639C33.7627 43.7284 34.9875 45.1564 35.8638 46.7621H33.8162C32.5294 46.767 31.2969 47.234 30.3875 48.0611C30.0837 48.3316 29.6754 48.4831 29.25 48.4831C28.8247 48.4831 28.4163 48.3316 28.1125 48.0611L26.1788 46.3192C25.2661 45.4989 24.0339 45.0387 22.75 45.0387C21.4661 45.0387 20.234 45.4989 19.3213 46.3192L17.3225 48.1202C17.0198 48.3929 16.6113 48.5466 16.185 48.5483C15.9582 48.5478 15.7341 48.5042 15.527 48.4203C15.3199 48.3364 15.1344 48.214 14.9825 48.0611C14.0691 47.2304 12.83 46.7631 11.5375 46.7621H9.6525C10.5269 45.1579 11.7492 43.7307 13.246 42.5664C14.7427 41.402 16.4831 40.5245 18.3625 39.9864C18.6913 39.8924 18.9788 39.7059 19.1832 39.4538C19.3877 39.2017 19.4986 38.8971 19.5 38.584V31.3211C19.4986 31.008 19.3877 30.7034 19.1832 30.4513C18.9788 30.1992 18.6913 30.0127 18.3625 29.9187C16.1185 29.2699 14.0788 28.1409 12.415 26.6268C11.0522 25.3972 9.97137 23.9344 9.235 22.3228C8.49863 20.7113 8.12135 18.9831 8.125 17.2383V9.85731H31.2163C29.6982 12.5397 29.0381 15.5548 29.311 18.5607C29.5839 21.5666 30.7787 24.4428 32.76 26.863C31.1623 28.2523 29.2405 29.2967 27.1375 29.9187ZM48.75 30.4354V29.0478C48.75 28.6563 48.5788 28.2808 48.2741 28.004C47.9693 27.7271 47.556 27.5716 47.125 27.5716C46.694 27.5716 46.2807 27.7271 45.976 28.004C45.6712 28.2808 45.5 28.6563 45.5 29.0478V30.4354C42.2054 30.0995 39.1341 28.7566 36.7898 26.627C34.4455 24.4974 32.9673 21.7074 32.5975 18.7145H34.125C34.556 18.7145 34.9693 18.5589 35.2741 18.2821C35.5788 18.0052 35.75 17.6298 35.75 17.2383C35.75 16.8468 35.5788 16.4713 35.2741 16.1944C34.9693 15.9176 34.556 15.7621 34.125 15.7621H32.5975C32.9673 12.7691 34.4455 9.97913 36.7898 7.84951C39.1341 5.7199 42.2054 4.37704 45.5 4.04112V5.42874C45.5 5.82025 45.6712 6.19572 45.976 6.47256C46.2807 6.7494 46.694 6.90493 47.125 6.90493C47.556 6.90493 47.9693 6.7494 48.2741 6.47256C48.5788 6.19572 48.75 5.82025 48.75 5.42874V4.04112C52.0447 4.37704 55.1159 5.7199 57.4602 7.84951C59.8045 9.97913 61.2827 12.7691 61.6525 15.7621H60.125C59.694 15.7621 59.2807 15.9176 58.976 16.1944C58.6712 16.4713 58.5 16.8468 58.5 17.2383C58.5 17.6298 58.6712 18.0052 58.976 18.2821C59.2807 18.5589 59.694 18.7145 60.125 18.7145H61.6525C61.2827 21.7074 59.8045 24.4974 57.4602 26.627C55.1159 28.7566 52.0447 30.0995 48.75 30.4354ZM51.5288 19.1425C51.6811 19.2798 51.802 19.443 51.8845 19.6229C51.967 19.8028 52.0094 19.9958 52.0094 20.1906C52.0094 20.3855 51.967 20.5785 51.8845 20.7584C51.802 20.9382 51.6811 21.1015 51.5288 21.2387C51.3777 21.3771 51.198 21.4869 50.9999 21.5619C50.8019 21.6368 50.5895 21.6754 50.375 21.6754C50.1605 21.6754 49.9481 21.6368 49.7501 21.5619C49.552 21.4869 49.3723 21.3771 49.2213 21.2387L45.9713 18.2864C45.8206 18.1484 45.7015 17.9848 45.6206 17.805C45.5398 17.6251 45.4988 17.4325 45.5 17.2383V11.3335C45.5 10.942 45.6712 10.5665 45.976 10.2897C46.2807 10.0128 46.694 9.85731 47.125 9.85731C47.556 9.85731 47.9693 10.0128 48.2741 10.2897C48.5788 10.5665 48.75 10.942 48.75 11.3335V16.633L51.5288 19.1425Z"
                  fill="#9CA3AF"
                />
              </g>
              <defs>
                <clipPath id="clip0_8615_7867">
                  <rect width="65" height="62" fill="white" />
                </clipPath>
              </defs>
            </svg>
          ),
        };
      }
    };

    return (
      <div className="flex flex-col gap-3 justify-center items-center min-h-48">
        <span>{statusIsNotSubmittedResults()?.svg}</span>
        <h2 className="dark:text-white pt-0 mt-0 text-gray-500 font-semibold text-lg text-center">{statusIsNotSubmittedResults()?.title}</h2>
        <p className="text-gray-400 font-light text-base text-center">{statusIsNotSubmittedResults()?.subTitle}</p>
      </div>
    );
  };

  const englishProficiency = (percentage) => {
    const handleEnglishProficiencyColors = () => {
      if (percentage < 50)
        return {
          label: 'Poor',
          color: 'text-statusColorPoor dark:text-statusDarkColorPoor',
          bg: 'bg-statusBackgroundPoor dark:bg-statusDarkBackgroundPoor',
        };
      else if (percentage <= 80)
        return {
          label: 'Good',
          color: 'text-statusColorGood dark:text-statusDarkColorGood',
          bg: 'bg-statusBackgroundGood dark:bg-statusDarkBackgroundGood',
        };
      else if (percentage > 80)
        return {
          label: 'Excellent',
          color: 'text-statusColorExcellent dark:text-statusDarkColorExcellent',
          bg: 'bg-statusBackgroundExcellent dark:bg-statusDarkBackgroundExcellent',
        };
    };

    return (
      <div className="flex items-center gap-1">
        <p className={`px-2 text-base font-medium ${handleEnglishProficiencyColors()?.color}`}>{percentage}%</p>
        <p
          className={`px-2.5 py-[3px] text-xs rounded-full self-center ${handleEnglishProficiencyColors()?.color} ${
            handleEnglishProficiencyColors()?.bg
          }`}
        >
          {handleEnglishProficiencyColors()?.label}
        </p>
      </div>
    );
  };

  const filteredQuestions = () => (toggleSwitch ? test.questionsWithAnswers.filter((question) => question.answer === '') : test.questionsWithAnswers);

  return (
    <>
      <Card className="bg-white dark:bg-darkBackgroundCard rounded-lg mt-4 !p-4">
        <div className="bg-white dark:bg-darkBackgroundCard pb-4 dark:border-gray-700 space-y-3">
          <div className={`space-y-2 flex ${test?.status === 2 ? '' : 'grid-cols-10'} flex-wrap justify-between items-center gap-5`}>
            <div className={`flex col-span-5 gap-5 ${type === 'screening' ? 'col-span-5' : 'col-span-6'}`}>
              <p className="text-[#101828] dark:text-white text-lg font-medium line-clamp-2 md:max-w-full lg:max-w-[650px]">
                {type === 'screening' ? test.title : Array.isArray(test.subCategoryName) ? test.subCategoryName.join(' & ') : test.subCategoryName}
              </p>
              {(test?.status === 1 || test?.status === 2 || (test?.expired && test?.startedAt)) && <ResultStatus test={test} hideScore />}
            </div>

            <div className={`flex lg:justify-end  ${type === 'screening' ? 'col-span-5' : 'col-span-4'}`}>
              {/* Buttons */}
              {test?.status === 3 && (
                <div className={`flex flex-wrap gap-4 w-fit sm:justify-end ${type === 'screening' ? 'md:min-w-[480px]' : 'md:min-w-[330px]'}`}>
                  {type === 'screening' && (
                    <Button
                      onClick={() => setShowQuestions(!showQuestions)}
                      icon={showQuestions ? `iconamoon:eye-off` : 'carbon:question-answering'}
                      label={!showQuestions ? 'Show Q&A' : 'Hide Q&A'}
                      size="md"
                      tertiary
                    />
                  )}

                  <Button
                    onClick={() => setShowMore(!showMore)}
                    icon={showMore ? 'iconamoon:eye-off' : `ph:eye`}
                    label={showMore ? 'Less Details' : 'More Details'}
                    size="md"
                    className={`${showMore && 'pr-[6px]'}`}
                    tertiary
                  />

                  <div className="relative">
                    <Button onClick={toggleDropdown} size="md" icon="material-symbols:download-sharp" label="Export Report" tertiary />

                    {/* Dropdown Menu */}
                    {isDropdownVisible && (
                      <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-darkBackgroundCard dark:text-white border dark:border-gray-700 rounded shadow-md z-10">
                        <ul className="py-1 divide-y dark:divide-gray-700">
                          {type !== 'screening' && (
                            <li
                              onClick={() => {
                                if (isSuperAdmin || userData?.features?.exportReport > 0) {
                                  window.open(`/app/tests/pdf/${test._id}?type=${type}`, '_blank', 'noopener,noreferrer');
                                } else {
                                  setNeedSubscription(true);
                                }
                              }}
                              className="flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700"
                            >
                              {/* @TODO: hashed if we need to replace it later */}
                              {/* <Icon icon="formkit:filepdf" width={20} className="mr-2" /> */}
                              <svg className="mr-2" width="20" height="20" viewBox="0 0 14 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                  d="M13.4688 7.59375H13.1875V4.21875C13.1875 4.21875 13.1875 4.21875 13.1875 4.20187C13.1861 4.17698 13.1813 4.15239 13.1734 4.12875V4.10344C13.1605 4.07335 13.1424 4.04574 13.12 4.02188L9.745 0.646875C9.72113 0.624449 9.69353 0.606365 9.66344 0.593437H9.63813C9.6101 0.578087 9.57967 0.567625 9.54813 0.5625H1.09375C1.01916 0.5625 0.947621 0.592132 0.894876 0.644876C0.842132 0.697621 0.8125 0.769158 0.8125 0.84375V7.59375H0.53125C0.456658 7.59375 0.385121 7.62338 0.332376 7.67613C0.279632 7.72887 0.25 7.80041 0.25 7.875V13.5C0.25 13.5746 0.279632 13.6461 0.332376 13.6989C0.385121 13.7516 0.456658 13.7812 0.53125 13.7812H0.8125V17.1562C0.8125 17.2308 0.842132 17.3024 0.894876 17.3551C0.947621 17.4079 1.01916 17.4375 1.09375 17.4375H12.9062C12.9808 17.4375 13.0524 17.4079 13.1051 17.3551C13.1579 17.3024 13.1875 17.2308 13.1875 17.1562V13.7812H13.4688C13.5433 13.7812 13.6149 13.7516 13.6676 13.6989C13.7204 13.6461 13.75 13.5746 13.75 13.5V7.875C13.75 7.80041 13.7204 7.72887 13.6676 7.67613C13.6149 7.62338 13.5433 7.59375 13.4688 7.59375ZM9.8125 1.52156L12.2284 3.9375H9.8125V1.52156ZM12.625 16.875H1.375V13.7812H12.625V16.875ZM3.92594 12.6056V8.85094H4.8175C4.93946 8.84553 5.06117 8.86605 5.17461 8.91114C5.28806 8.95623 5.39065 9.02486 5.47563 9.1125C5.63066 9.28709 5.71228 9.5148 5.70344 9.74813V10.2937C5.70539 10.4117 5.68347 10.5288 5.639 10.638C5.59454 10.7472 5.52844 10.8463 5.44469 10.9294C5.36424 11.0146 5.26692 11.0821 5.15893 11.1276C5.05095 11.1731 4.93467 11.1957 4.8175 11.1937H4.49406V12.6L3.92594 12.6056ZM6.15906 12.6056V8.84812H7.04781C7.16545 8.84583 7.28227 8.86816 7.39077 8.91369C7.49927 8.95922 7.59704 9.02694 7.67781 9.1125C7.76148 9.19501 7.82755 9.29363 7.87203 9.40239C7.9165 9.51116 7.93846 9.62782 7.93656 9.74531V11.7141C7.93831 11.8322 7.91602 11.9495 7.87106 12.0587C7.8261 12.168 7.75939 12.267 7.675 12.3497C7.59423 12.4352 7.49645 12.503 7.38796 12.5485C7.27946 12.594 7.16264 12.6164 7.045 12.6141L6.15906 12.6056ZM9.7675 10.4428V11.0053H9.0025V12.6H8.44V8.84812H10.0516V9.41062H9.0025V10.4344L9.7675 10.4428ZM12.625 7.59375H1.375V1.125H9.25V4.21875C9.25 4.29334 9.27963 4.36488 9.33238 4.41762C9.38512 4.47037 9.45666 4.5 9.53125 4.5H12.625V7.59375Z"
                                  fill="#6B7280"
                                />
                              </svg>
                              {`${
                                type === 'submissions' ? 'Test' : type === 'interviews' ? 'Interview' : type === 'screening' ? 'Screening' : '—'
                              } Score`}
                            </li>
                          )}
                          <li
                            onClick={() => {
                              if (isSuperAdmin || userData?.features?.exportPdf > 0) {
                                downloadDocument();
                              } else {
                                setNeedSubscription(true);
                              }
                            }}
                            className="flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700"
                          >
                            {/* @TODO: hashing this icon if we need it later */}
                            {/* <Icon icon="bi:filetype-xls" width={20} className="mr-2" /> */}
                            <svg className="mr-2" width="20" height="20" viewBox="0 0 14 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M13.9216 4.57312L9.74486 0.073125C9.69699 0.0262413 9.63466 0.000181971 9.56996 0H2.97332C2.53552 -5.88587e-07 2.11561 0.187181 1.8058 0.520447C1.49599 0.853712 1.32159 1.30582 1.32089 1.7775V7.46156H1.11467C0.81904 7.46156 0.535519 7.58809 0.326479 7.81331C0.117438 8.03853 0 8.34399 0 8.6625V14.1103C0.000691275 14.4268 0.117884 14.7302 0.325871 14.9537C0.533858 15.1773 0.815655 15.3028 1.10945 15.3028H1.32351V16.2225C1.3242 16.6937 1.49824 17.1454 1.80749 17.4786C2.11674 17.8117 2.53597 17.9993 2.97332 18H12.3501C12.7874 17.9993 13.2067 17.8117 13.5159 17.4786C13.8252 17.1454 13.9992 16.6937 13.9999 16.2225V4.78125C14.0008 4.74262 13.9944 4.70421 13.9809 4.6684C13.9674 4.63259 13.9472 4.60016 13.9216 4.57312ZM1.31828 9.30375H1.11467C0.963966 9.29298 0.8227 9.22088 0.719498 9.10208C0.616296 8.98327 0.558878 8.82663 0.558878 8.66391C0.558878 8.50118 0.616296 8.34455 0.719498 8.22574C0.8227 8.10693 0.963966 8.03483 1.11467 8.02406H1.31828V9.30375ZM13.4778 16.2225C13.4771 16.5445 13.3581 16.8531 13.1468 17.0808C12.9354 17.3085 12.649 17.4368 12.3501 17.4375H2.97332C2.67399 17.4375 2.38688 17.3096 2.17498 17.0818C1.96307 16.854 1.84368 16.545 1.84299 16.2225V15.3028H10.0868C10.1561 15.3028 10.2225 15.2732 10.2714 15.2204C10.3204 15.1677 10.3479 15.0962 10.3479 15.0216V9.58219C10.3479 9.5076 10.3204 9.43606 10.2714 9.38331C10.2225 9.33057 10.1561 9.30094 10.0868 9.30094H1.84299V1.7775C1.84368 1.455 1.96307 1.14597 2.17498 0.918194C2.38688 0.690418 2.67399 0.562499 2.97332 0.5625H9.30891V3.27375C9.30788 3.50802 9.34978 3.7402 9.43219 3.95699C9.51461 4.17378 9.63593 4.37091 9.7892 4.53708C9.94247 4.70326 10.1247 4.83521 10.3254 4.92537C10.5261 5.01553 10.7413 5.06213 10.9587 5.0625H13.4778V16.2225ZM2.49821 10.9997H3.06468L3.54762 11.8434L4.02011 10.9997H4.58136L3.83477 12.2569L4.64924 13.59H4.06971L3.54762 12.6928L3.02553 13.59H2.4199L3.2422 12.2372L2.49821 10.9997ZM6.60969 13.1541V13.59H4.91028V11.0222H5.39583V13.1541H6.60969ZM8.24906 12.6816C8.20246 12.6275 8.1428 12.5886 8.07677 12.5691C8.01934 12.5466 7.88882 12.51 7.68781 12.4566C7.48911 12.4166 7.30157 12.3281 7.13962 12.1978C7.02231 12.091 6.94553 11.9414 6.92387 11.7775C6.90222 11.6137 6.93719 11.447 7.02215 11.3091C7.09588 11.191 7.20031 11.0991 7.32235 11.0447C7.47362 10.9859 7.63423 10.9601 7.79484 10.9688C8.03837 10.9499 8.2801 11.0257 8.47618 11.1825C8.55038 11.253 8.61008 11.3395 8.65144 11.4364C8.69279 11.5333 8.7149 11.6384 8.71634 11.745L8.23079 11.7675C8.2194 11.6595 8.1721 11.5596 8.09766 11.4862C8.00573 11.4159 7.89438 11.3812 7.78179 11.3878C7.66459 11.3802 7.54827 11.4138 7.45026 11.4834C7.42641 11.5014 7.40703 11.5255 7.39383 11.5534C7.38063 11.5814 7.37401 11.6124 7.37456 11.6438C7.37476 11.6743 7.38145 11.7043 7.39412 11.7316C7.40679 11.759 7.42511 11.7828 7.44765 11.8012C7.58449 11.8875 7.73495 11.9456 7.89143 11.9728C8.07151 12.0129 8.2467 12.0751 8.41352 12.1584C8.52065 12.2202 8.61071 12.3114 8.67457 12.4228C8.7407 12.5476 8.77324 12.6898 8.76855 12.8334C8.76966 12.9823 8.72968 13.1282 8.65369 13.2525C8.5763 13.381 8.4629 13.4795 8.32999 13.5337C8.16373 13.6033 7.98613 13.6358 7.8079 13.6294C7.55684 13.6467 7.30841 13.5658 7.10829 13.4016C6.93514 13.2303 6.83034 12.9934 6.81592 12.7406L7.28841 12.6928C7.30409 12.8352 7.36495 12.9673 7.4607 13.0669C7.56054 13.1522 7.68588 13.1952 7.81312 13.1878C7.93855 13.1975 8.06318 13.1597 8.16553 13.0809C8.20192 13.0518 8.23165 13.0141 8.25246 12.9708C8.27327 12.9274 8.28461 12.8794 8.28561 12.8306C8.28988 12.7779 8.27696 12.7252 8.24906 12.6816Z"
                                fill="#6B7280"
                              />
                            </svg>
                            {`${
                              type === 'submissions' ? 'Test' : type === 'interviews' ? 'Interview' : type === 'screening' ? 'Screening' : '—'
                            } Details`}
                          </li>
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Started At and Time Taken */}
          {test.status >= 2 && (
            <div className="flex flex-wrap gap-4 items-center py-2 dark:border-gray-700 border-gray-200">
              <div className="flex flex-wrap items-center gap-2 text-md text-[#667085] dark:text-gray-400">
                <Icon icon="octicon:play-24" className="text-[#667085] dark:text-gray-400" width="22" />
                <span>Started At:</span>
                <span className="px-2 text-base text-[#333333] dark:text-grayTextOnDarkMood  font-medium">{formatDate(test.startedAt)}</span>
              </div>

              {test.status === 3 && (
                <div className="flex flex-wrap items-center gap-2 text-md text-[#667085]  dark:text-gray-400">
                  <Icon icon="ph:clock-light" className="text-[#667085] dark:text-gray-400" width="22" />
                  <span>Time Taken:</span>
                  <span className="px-2 text-base text-[#333333]  dark:text-grayTextOnDarkMood  font-medium">
                    {test?.timeTaken ? Math.round(test?.timeTaken) : '0'} min
                  </span>
                  {test?.duration - test?.timeTaken < 0 && (
                    <p className="text-[#D92D20] bg-[#FEF3F2] dark:text-[#FEE2E2] dark:bg-[#712224] dark:bg-opacity-45  px-2 py-1 text-sm font-normal rounded-md ">
                      {Math.round(test?.timeTaken - test?.duration)} mins Exceeded
                    </p>
                  )}
                </div>
              )}
            </div>
          )}

          {(showMore || test?.status !== 3) && (
            <div className="space-y-3 mt-2">
              {test?.status === 3 && (
                <h2 className="font-medium text-base mt-3 dark:text-white ">
                  {type === 'submissions' ? 'Test' : type === 'interviews' ? 'Interview' : type === 'screening' ? 'Screening' : '—'} Details
                </h2>
              )}

              <div className="flex flex-col sm:flex-row justify-around gap-4 px-0.5 py-4 rounded-md  dark:bg-gray-600 dark:bg-opacity-50   bg-[#F8F9FA]">
                {screen.gt.md() ? (
                  <>
                    <div className="flex items-center gap-2 text-[#667085]">
                      <Icon icon="heroicons:chart-bar-square" className="dark:text-gray-400" width={24} />
                      <p className="text-base dark:text-gray-400 font-medium">Difficulty:</p>
                      <p className="text-base px-2 text-[#101828] font-medium dark:text-white">
                        <EnumText name={'QuizDifficulty'} value={test.difficulty} />
                      </p>
                    </div>

                    <div className="border border-[#79829666] h-5 mt-1 hidden lg:block" />

                    <div className="flex items-center gap-2 text-[#667085]">
                      <CustomIcon definedIcon="questions" className="dark:text-gray-400" />
                      <p className="text-base  dark:text-gray-400 font-medium">Total Questions:</p>
                      <p className="text-base px-2 text-[#101828] font-medium dark:text-white">{totalQuestions ?? '0'}</p>
                    </div>

                    <div className="border border-[#79829666] h-5 mt-1 hidden lg:block" />

                    <div className="flex items-center gap-2 text-[#667085]">
                      <Icon icon="mynaui:clock-three" className="dark:text-gray-400" width={24} />
                      <p className="text-base dark:text-gray-400 font-medium">Duration:</p>
                      <p className="text-base px-2 text-[#101828] font-medium dark:text-white">{test?.duration ?? '0'} min</p>
                    </div>

                    <div className="border border-[#79829666] h-5 mt-1 hidden lg:block" />

                    <div className={`flex flex-wrap sm:items-center gap-1 text-[#667085]`}>
                      <div className="flex items-center gap-2">
                        <Icon icon="material-symbols:calendar-month-rounded" className="dark:text-gray-400" width={24} />
                        <p className="text-base font-medium dark:text-gray-400">Due Date:</p>
                      </div>
                      <p className="text-base px-2 text-[#101828] font-medium dark:text-white">{formatDate(test.dueDate)}</p>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="space-y-3">
                      <div className="flex items-center gap-1 text-[#667085]">
                        <Icon icon="heroicons:chart-bar-square" className="dark:text-gray-400" width={24} />
                        <p className="text-base dark:text-gray-400 font-medium">Difficulty:</p>
                        <p className="text-base px-2 text-[#101828] font-medium dark:text-white">
                          <EnumText name={'QuizDifficulty'} value={test.difficulty} />
                        </p>
                      </div>

                      <div className="flex items-center gap-1 text-[#667085]">
                        <CustomIcon definedIcon="questions" className="dark:text-gray-400" />
                        <p className="text-base  dark:text-gray-400 font-medium">Total Questions:</p>
                        <p className="text-base px-2 text-[#101828] font-medium dark:text-white">{totalQuestions ?? '0'}</p>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-1 text-[#667085]">
                        <Icon icon="mynaui:clock-three" className="dark:text-gray-400" width={24} />
                        <p className="text-base dark:text-gray-400 font-medium">Duration:</p>
                        <p className="text-base px-2 text-[#101828] font-medium dark:text-white">{test?.duration ?? '0'} min</p>
                      </div>

                      <div className={`flex flex-wrap sm:items-center gap-1 text-[#667085]`}>
                        <div className="flex items-center gap-1">
                          <Icon icon="material-symbols:calendar-month-rounded" className="dark:text-gray-400" width={24} />
                          <p className="text-base font-medium dark:text-gray-400">Due Date:</p>
                        </div>
                        <p className="text-base px-2 text-[#101828] font-medium dark:text-white">{formatDate(test.dueDate)}</p>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}
        </div>

        {test.status === 3 ? (
          type === 'screening' ? (
            <>
              {/* TODO: Add when need */}
              {/* <div className="bg-[#C3CCD71A] bg-opacity-10 p-4 mt-3 mb-5 rounded-lg border border-[#f1f3f6] dark:border-gray-700">
              <p className="text-lg text-black font-semibold dark:text-white ">Screening Summary</p>
              <p className="text-base text-[#667085] dark:text-gray-400 pt-2">{test.description}</p>
            </div> */}

              {(showQuestions || test?.status !== 3) && (
                <div className="space-y-4">
                  <div className="flex w-full items-center px-2 justify-between rounded-lg text-nowrap">
                    <Drawer.Search onInput={(e) => setSearchQuestions(e.target.value)} className="w-full dark:rounded-md" />
                    <div className="flex gap-2 items-center align-middle px-2">
                      {/* ToggleSwitch */}
                      <div className="flex justify-start gap-3 text-sm font-medium m-4 pr-4">
                        <span className="text-[#798296] text-sm font-medium">Unanswered Questions</span>
                        <ToggleSwitch
                          sizing="sm"
                          checked={toggleSwitch}
                          value={toggleSwitch}
                          onChange={() => setToggleSwitch((prev) => !prev)}
                          color="purple"
                        />
                      </div>

                      <div
                        className="text-[#8A43F9] font-medium text-sm cursor-pointer"
                        onClick={() => setExpandQuestionToggle(!expandQuestionToggle)}
                      >
                        {expandQuestionToggle ? 'Collapse All Answers' : 'Expand All Answers'}
                      </div>
                    </div>
                  </div>
                  {!filteredQuestions()?.length && toggleSwitch ? (
                    <div className="flex flex-col items-center gap-4 mt-16">
                      <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M23.6885 0.931968C24.0152 1.04475 24.335 1.17697 24.6481 1.32864L28.3902 3.17197C28.8911 3.41851 29.4419 3.54672 30.0002 3.54672C30.5585 3.54672 31.1093 3.41851 31.6102 3.17197L35.3523 1.32864C36.2974 0.863379 37.3248 0.588846 38.376 0.520714C39.4272 0.452582 40.4816 0.592185 41.4788 0.931551C42.476 1.27092 43.3967 1.8034 44.1881 2.49859C44.9796 3.19378 45.6263 4.03806 46.0914 4.98322L46.3043 5.45572L46.4881 5.9428L47.8298 9.88905C48.1943 10.9624 49.0373 11.8024 50.1077 12.167L54.0568 13.5086C55.1371 13.8762 56.1265 14.47 56.9589 15.2503C57.7914 16.0307 58.4478 16.9797 58.8843 18.034C59.3207 19.0883 59.5272 20.2236 59.4899 21.364C59.4526 22.5045 59.1725 23.6239 58.6681 24.6474L56.8277 28.3895C56.5811 28.8904 56.4529 29.4412 56.4529 29.9995C56.4529 30.5577 56.5811 31.1086 56.8277 31.6095L58.6681 35.3516C59.172 36.375 59.4517 37.494 59.4887 38.6342C59.5258 39.7743 59.3192 40.9092 58.8827 41.9631C58.4463 43.017 57.7901 43.9657 56.9579 44.7459C56.1257 45.5261 55.1367 46.1198 54.0568 46.4874L50.1077 47.8291C49.5794 48.0093 49.0996 48.3084 48.7051 48.7033C48.3107 49.0982 48.0122 49.5785 47.8327 50.107L46.4881 54.0561C46.1205 55.136 45.5268 56.125 44.7466 56.9572C43.9664 57.7894 43.0177 58.4456 41.9638 58.882C40.9099 59.3185 39.775 59.5251 38.6349 59.488C37.4948 59.451 36.3757 59.1712 35.3523 58.6674L31.6102 56.827C31.1093 56.5804 30.5585 56.4522 30.0002 56.4522C29.4419 56.4522 28.8911 56.5804 28.3902 56.827L24.6481 58.6674C23.6247 59.1712 22.5056 59.451 21.3655 59.488C20.2254 59.5251 19.0905 59.3185 18.0366 58.882C16.9826 58.4456 16.0339 57.7894 15.2537 56.9572C14.4736 56.125 13.8799 55.136 13.5123 54.0561L12.1706 50.107C11.9901 49.5784 11.6905 49.0983 11.2951 48.7039C10.8996 48.3094 10.4188 48.0111 9.88976 47.832L5.94351 46.4874C4.86339 46.1201 3.87405 45.5266 3.04154 44.7466C2.20904 43.9665 1.55251 43.0179 1.1158 41.9639C0.679087 40.9099 0.472239 39.7749 0.509062 38.6347C0.545886 37.4944 0.825536 36.3751 1.32934 35.3516L3.17267 31.6095C3.41921 31.1086 3.54742 30.5577 3.54742 29.9995C3.54742 29.4412 3.41921 28.8904 3.17267 28.3895L1.32934 24.6474C0.825536 23.6238 0.545886 22.5045 0.509062 21.3643C0.472239 20.224 0.679087 19.089 1.1158 18.035C1.55251 16.9811 2.20904 16.0324 3.04154 15.2524C3.87405 14.4723 4.86339 13.8788 5.94351 13.5116L9.88976 12.1699C10.4189 11.9898 10.8995 11.6904 11.2945 11.2949C11.6895 10.8994 11.9882 10.4184 12.1677 9.88905L13.5093 5.9428C13.8485 4.94528 14.3809 4.02435 15.076 3.23262C15.7712 2.44089 16.6155 1.79389 17.5608 1.32856C18.5061 0.86323 19.5338 0.588701 20.5852 0.520653C21.6366 0.452605 22.6911 0.592372 23.6885 0.931968ZM17.651 7.34864L16.3093 11.2978C15.9137 12.461 15.2561 13.5178 14.3873 14.3866C13.5185 15.2554 12.4617 15.913 11.2985 16.3086L7.35226 17.6503C6.86111 17.8172 6.41124 18.0871 6.0327 18.4418C5.65417 18.7965 5.35568 19.2278 5.15718 19.7071C4.95869 20.1864 4.86475 20.7025 4.88165 21.2209C4.89854 21.7394 5.02589 22.2483 5.25517 22.7136L7.09851 26.4557C7.64145 27.5581 7.92381 28.7706 7.92381 29.9995C7.92381 31.2283 7.64145 32.4408 7.09851 33.5432L5.25517 37.2824C5.02589 37.7477 4.89854 38.2566 4.88165 38.7751C4.86475 39.2935 4.95869 39.8096 5.15718 40.2889C5.35568 40.7682 5.65417 41.1996 6.0327 41.5543C6.41124 41.9089 6.86111 42.1788 7.35226 42.3457L11.2985 43.6874C12.4617 44.083 13.5185 44.7406 14.3873 45.6094C15.2561 46.4782 15.9137 47.535 16.3093 48.6982L17.651 52.6474C17.8171 53.1391 18.0865 53.5896 18.4411 53.9687C18.7956 54.3478 19.2271 54.6466 19.7067 54.8452C20.1862 55.0439 20.7027 55.1376 21.2214 55.1202C21.7401 55.1029 22.2492 54.9747 22.7143 54.7445L26.4564 52.9011C27.5589 52.3582 28.7713 52.0758 30.0002 52.0758C31.229 52.0758 32.4415 52.3582 33.5439 52.9011L37.2831 54.7445C37.427 54.8125 37.5728 54.8718 37.7206 54.9224C38.6357 55.2339 39.6371 55.1693 40.5045 54.7427C41.372 54.316 42.0345 53.5624 42.3464 52.6474L43.6881 48.6982C44.0837 47.535 44.7413 46.4782 45.6101 45.6094C46.4789 44.7406 47.5357 44.083 48.6989 43.6874L52.6481 42.3457C52.7939 42.2952 52.9398 42.2349 53.0856 42.1649C53.5152 41.9533 53.8989 41.6592 54.2149 41.2993C54.5308 40.9394 54.7727 40.5208 54.9268 40.0674C55.0809 39.614 55.1442 39.1347 55.113 38.6568C55.0819 38.179 54.9569 37.7119 54.7452 37.2824L52.9018 33.5403C52.3594 32.4383 52.0773 31.2263 52.0773 29.998C52.0773 28.7697 52.3594 27.5578 52.9018 26.4557L54.7452 22.7136C54.9745 22.2483 55.1018 21.7394 55.1187 21.2209C55.1356 20.7025 55.0417 20.1864 54.8432 19.7071C54.6447 19.2278 54.3462 18.7965 53.9676 18.4418C53.5891 18.0871 53.1392 17.8172 52.6481 17.6503L48.6989 16.3086C47.5357 15.913 46.4789 15.2554 45.6101 14.3866C44.7413 13.5178 44.0837 12.461 43.6881 11.2978L42.3464 7.35155L42.2618 7.12989L42.1656 6.91405L41.9848 6.5903C41.5056 5.82494 40.7595 5.26461 39.8909 5.01782C39.0223 4.77104 38.0931 4.85537 37.2831 5.25447L33.541 7.0978C32.439 7.64028 31.227 7.92239 29.9987 7.92239C28.7704 7.92239 27.5585 7.64028 26.4564 7.0978L22.7143 5.25447C22.2492 5.02569 21.7405 4.89875 21.2224 4.88211C20.7043 4.86548 20.1885 4.95953 19.7096 5.15799C19.2307 5.35645 18.7996 5.65477 18.4452 6.03302C18.0907 6.41128 17.8209 6.86079 17.6539 7.35155M24.3127 36.9703L40.121 21.162C40.5113 20.7706 41.0354 20.5415 41.5877 20.5207C42.1401 20.4999 42.6798 20.689 43.0985 21.05C43.5171 21.411 43.7835 21.9171 43.8442 22.4665C43.9049 23.0159 43.7553 23.5679 43.4256 24.0115L43.2127 24.2566L25.7127 41.7565C25.317 42.1522 24.7847 42.3811 24.2254 42.3963C23.6661 42.4115 23.1222 42.2118 22.7056 41.8382L22.4868 41.6107L15.1952 32.8607C14.8426 32.4365 14.6633 31.8947 14.6932 31.3439C14.7231 30.7931 14.96 30.2739 15.3565 29.8903C15.753 29.5068 16.2797 29.2872 16.8312 29.2755C17.3827 29.2638 17.9183 29.461 18.3306 29.8274L18.5552 30.0607L24.3127 36.9703Z"
                          fill="#9CA3AF"
                        />
                      </svg>
                      <p className="text-sm font-medium leading-4 text-[#6B7280]">All questions have been answered</p>
                    </div>
                  ) : (
                    filteredQuestions()?.map(
                      (item, index) =>
                        item.question.toLowerCase().includes(searchQuestions.toLowerCase()) && (
                          <Drawer.Body.QuestionOfScreening
                            className="dark:text-white "
                            isExpandAllAnswers={expandQuestionToggle}
                            key={item._id}
                            index={index}
                            row={item}
                          />
                        )
                    )
                  )}
                </div>
              )}
            </>
          ) : (
            <>
              {/* Chart Progress Section */}
              <div className="flex flex-col lg:flex-row justify-start items-center mt-3 gap-6 w-full">
                <div className="flex flex-col items-start justify-center">
                  <div className="font-medium dark:text-white space-y-4">
                    {/* Test Charts */}
                    <ResultChart test={test} size={140} fontSize="30px" />
                  </div>
                </div>

                {(correctAnswers > 0 || wrongAnswers > 0 || unAnsweredQuestions > 0 || skippedQuestions > 0) && (
                  <div className="w-full">
                    <div className="space-y-2 w-full lg:w-7/12">
                      <h2 className="text-base font-medium text-gray-900 dark:text-white">Score Breakdown</h2>

                      {/* Breakdown Bar */}
                      <div className="flex h-3 w-full space-x-2">
                        {correctPercentage > 0 && (
                          <div
                            style={{ width: `${correctPercentage}%` }}
                            className="h-full bg-[#4AA264] dark:bg-[#3a8a3a] dark:bg-opacity-60  rounded-sm "
                          ></div>
                        )}
                        {skippedPercentage > 0 && (
                          <div style={{ width: `${skippedPercentage}%` }} className="h-full bg-pastelYellow dark:bg-pastelYellow rounded-sm"></div>
                        )}
                        {wrongPercentage > 0 && (
                          <div
                            style={{ width: `${wrongPercentage}%` }}
                            className="h-full bg-[#DC7E83] dark:bg-[#c9302c]  dark:bg-opacity-60 rounded-sm"
                          ></div>
                        )}
                        {unansweredPercentage > 0 && (
                          <div style={{ width: `${unansweredPercentage}%` }} className="h-full bg-[#cbcbcb] dark:bg-[#939090] rounded-sm"></div>
                        )}
                      </div>
                    </div>

                    {/* Breakdown Labels */}
                    <div className="flex flex-col sm:flex-row items-start justify-start gap-4 mt-2">
                      {correctAnswers > 0 && (
                        <div className="flex px items-center gap-2">
                          <div className="w-2.5 h-2.5 inline-block bg-[#4AA264] dark:bg-[#3a8a3a] dark:bg-opacity-60  rounded-full"></div>
                          <span className="text-gray-900 dark:text-gray-100 text-sm">Correct answers:</span>
                          <span className="dark:text-gray-400 font-medium">{correctAnswers}</span>
                        </div>
                      )}

                      {skippedQuestions > 0 && (
                        <div className="flex px items-center gap-2">
                          <div className="w-2.5 h-2.5 inline-block  bg-pastelYellow dark:bg-pastelYellow rounded-full"></div>
                          <span className="text-gray-900 dark:text-gray-100 text-sm">Skipped questions:</span>
                          <span className="dark:text-gray-400 font-medium">{skippedQuestions}</span>
                        </div>
                      )}
                      {wrongAnswers > 0 && (
                        <div className="flex px items-center gap-2">
                          <div className="w-2.5 h-2.5 inline-block  bg-[#DC7E83] dark:bg-[#c9302c]  dark:bg-opacity-60 rounded-full"></div>
                          <span className="text-gray-900 dark:text-gray-100 text-sm">Wrong answers:</span>
                          <span className="dark:text-gray-400 font-medium">{wrongAnswers}</span>
                        </div>
                      )}

                      {unAnsweredQuestions > 0 && (
                        <div className="flex px items-center gap-2">
                          <div className="w-2.5 h-2.5 inline-block  bg-[#cbcbcb] dark:bg-[#939090] rounded-full"></div>
                          <span className="text-gray-900 dark:text-gray-100 text-sm">Unanswered:</span>
                          <span className="dark:text-gray-400 font-medium">{unAnsweredQuestions}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* English and attitude score */}
              {test?.english_score >= 0 && test?.attitude_score >= 0 && (
                <div className="mt-4">
                  <div className="flex flex-col md:flex-row gap-4 py-3 rounded-md bg-opacity-30 dark:bg-darkBackgroundCard">
                    <div className="flex flex-col xslg:flex-row sm:items-center gap-2 text-[#667085]">
                      <div className="flex gap-2">
                        <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M20.3125 3H4.6875C3.82456 3 3.125 3.67157 3.125 4.5V19.5C3.125 20.3284 3.82456 21 4.6875 21H20.3125C21.1754 21 21.875 20.3284 21.875 19.5V4.5C21.875 3.67157 21.1754 3 20.3125 3Z"
                            stroke="#667085"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M10.9382 8.5H6.77148V15.5H10.6777M6.77148 12H10.6777M13.5423 9.5V15.5V12.25C13.5423 11.6533 13.7892 11.081 14.2288 10.659C14.6683 10.2371 15.2645 10 15.8861 10C16.5077 10 17.1038 10.2371 17.5433 10.659C17.9829 11.081 18.2298 11.6533 18.2298 12.25V15.5"
                            stroke="#667085"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        <p className="text-base dark:text-gray-400 font-medium">English Proficiency:</p>
                      </div>
                      {englishProficiency(test?.english_score)}
                    </div>

                    <div className="flex flex-col xslg:flex-row sm:items-center gap-` text-[#667085]">
                      <div className="flex gap-2">
                        <svg width="23" height="24" viewBox="0 0 25 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M6.39088 5.7778C6.1583 5.67326 5.89373 5.66539 5.65535 5.75591C5.41696 5.84643 5.2243 6.02793 5.11973 6.26049L3.03992 10.8788C2.93526 11.1111 2.92711 11.3755 3.01725 11.6138C3.1074 11.8522 3.28847 12.045 3.52069 12.1499L4.95338 12.7951C5.06847 12.8471 5.19268 12.8759 5.3189 12.8798C5.44512 12.8838 5.57089 12.8629 5.68902 12.8182C5.80715 12.7736 5.91533 12.7061 6.00738 12.6196C6.09943 12.5332 6.17354 12.4294 6.2255 12.3143L8.3053 7.69511C8.40996 7.46277 8.41811 7.19839 8.32797 6.96004C8.23782 6.7217 8.05675 6.52888 7.82453 6.42395L6.39088 5.7778ZM4.68607 11.4768C4.84444 11.5482 5.02468 11.5538 5.18715 11.4923C5.34962 11.4308 5.48101 11.3073 5.55242 11.149C5.62382 10.9906 5.62939 10.8103 5.5679 10.6479C5.50641 10.4854 5.3829 10.354 5.22453 10.2826C5.06617 10.2112 4.88593 10.2056 4.72345 10.2671C4.56098 10.3286 4.42959 10.4521 4.35819 10.6105C4.28678 10.7689 4.28121 10.9491 4.3427 11.1116C4.40419 11.274 4.52771 11.4054 4.68607 11.4768ZM21.1688 12.2759C21.2868 12.2311 21.3949 12.1634 21.4869 12.0768C21.5788 11.9902 21.6528 11.8864 21.7045 11.7712C21.7563 11.656 21.7849 11.5318 21.7887 11.4055C21.7924 11.2793 21.7713 11.1536 21.7265 11.0355L19.9332 6.30088C19.8885 6.18272 19.8209 6.07453 19.7344 5.9825C19.6478 5.89046 19.544 5.81639 19.4288 5.76451C19.3136 5.71262 19.1893 5.68395 19.063 5.68013C18.9367 5.6763 18.8109 5.6974 18.6928 5.74222L17.2245 6.29895C17.1065 6.34377 16.9984 6.4114 16.9064 6.49799C16.8145 6.58458 16.7405 6.68844 16.6888 6.80362C16.637 6.91881 16.6084 7.04307 16.6046 7.1693C16.6009 7.29554 16.622 7.42128 16.6668 7.53934L18.4611 12.2749C18.5514 12.5133 18.7327 12.7061 18.9651 12.8109C19.1975 12.9156 19.4621 12.9238 19.7005 12.8336L21.1688 12.2759ZM18.3447 7.0403C18.2643 7.07079 18.1907 7.11683 18.128 7.17579C18.0654 7.23474 18.015 7.30546 17.9796 7.38389C17.9443 7.46233 17.9248 7.54696 17.9222 7.63294C17.9196 7.71892 17.9339 7.80458 17.9644 7.88501C17.9949 7.96544 18.041 8.03909 18.0999 8.10173C18.1589 8.16437 18.2296 8.21479 18.308 8.2501C18.3865 8.28541 18.4711 8.30493 18.5571 8.30754C18.6431 8.31015 18.7287 8.29579 18.8092 8.2653C18.9716 8.20371 19.1029 8.08012 19.1742 7.9217C19.2456 7.76329 19.251 7.58303 19.1894 7.42059C19.1279 7.25814 19.0043 7.12682 18.8458 7.0555C18.6874 6.98418 18.5072 6.97871 18.3447 7.0403Z"
                            fill="#667085"
                          />
                          <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M17.196 7.33352H17.2037C17.2666 7.32803 17.3279 7.31019 17.3839 7.28103C17.44 7.25187 17.4898 7.21195 17.5304 7.16355C17.571 7.11516 17.6017 7.05924 17.6207 6.99898C17.6397 6.93873 17.6467 6.87531 17.6412 6.81237C17.6357 6.74942 17.6179 6.68817 17.5887 6.63212C17.5595 6.57607 17.5196 6.52631 17.4712 6.48568C17.4228 6.44506 17.3669 6.41436 17.3067 6.39535C17.2464 6.37633 17.183 6.36938 17.12 6.37487H17.1123L17.0873 6.37775L16.995 6.38737L16.6547 6.42006C16.3691 6.44891 15.9739 6.4941 15.5373 6.55179C14.6768 6.66525 13.6104 6.83833 12.9123 7.07102C12.5739 7.18352 12.2162 7.40179 11.8739 7.65179C11.5268 7.90564 11.1672 8.21429 10.8229 8.53448C10.212 9.11103 9.6283 9.71586 9.07389 10.347C8.671 10.8028 8.57773 11.5297 9.05081 12.0451C9.3585 12.3778 9.83927 12.7989 10.4623 12.9874C11.1133 13.1826 11.8633 13.1066 12.6297 12.5374L13.5902 11.9172L13.6075 11.9066C13.745 11.9903 13.9364 12.1345 14.1652 12.3258C14.4152 12.5354 14.6864 12.7826 14.9393 13.0201C15.2263 13.2906 15.5084 13.5663 15.7854 13.847L15.8383 13.9018L15.8518 13.9153L15.8566 13.9201L15.9133 13.9778L15.9854 14.0153C16.3729 14.2076 16.7989 14.1383 17.1297 14.0268C17.4739 13.9114 17.8162 13.7153 18.1075 13.522C18.4723 13.2765 18.8179 13.0036 19.1412 12.7056L19.1585 12.6903L19.1633 12.6854L19.1643 12.6845C19.1643 12.6845 19.0797 12.3864 18.7489 12.0383L18.4883 12.3653L18.2681 12.9999L15.8566 13.9201L16.345 13.4806L16.0873 12.7912C15.9262 12.6325 15.7633 12.4754 15.5989 12.3201C15.3393 12.0758 15.0527 11.8153 14.7816 11.5893C14.5191 11.3681 14.246 11.1585 14.0181 11.0335C13.6393 10.8258 13.2681 10.9797 13.0681 11.1095L12.0797 11.7479L12.0662 11.7576C11.5325 12.1576 11.0941 12.1729 10.7393 12.0662C10.3547 11.9508 10.0133 11.6701 9.7585 11.3931C9.67869 11.3066 9.65562 11.1403 9.79408 10.9835C10.3281 10.3759 10.8903 9.7935 11.4787 9.23833C11.8056 8.93352 12.1345 8.65179 12.4412 8.42775C12.7547 8.19891 13.0181 8.04987 13.2172 7.98352C13.8172 7.78352 14.7989 7.6191 15.6633 7.50468C16.136 7.44256 16.6098 7.38903 17.0845 7.3441L17.1739 7.33545L17.196 7.33352Z"
                            fill="#667085"
                          />
                          <path
                            d="M16.468 13.17C16.3424 13.0427 16.2158 12.9164 16.0882 12.7911L16.3459 13.4806L15.8574 13.92L18.269 12.9998L18.4892 12.3652L18.7497 12.0383L18.5045 11.9863L18.5017 11.9883L18.4882 12.0008L18.4363 12.0488C18.1657 12.293 17.8781 12.5176 17.5757 12.7209C17.3161 12.894 17.0536 13.0383 16.8228 13.1152C16.644 13.1758 16.5324 13.1806 16.468 13.17Z"
                            fill="#667085"
                          />
                          <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M7.73749 8.25796L7.54807 7.81565L7.35864 7.37335L7.3971 7.35796L7.50576 7.31181C8.03885 7.08546 8.57413 6.8643 9.11153 6.64835C9.54799 6.47224 9.98782 6.30458 10.4308 6.14546C10.6259 6.07623 10.8077 6.01662 10.9596 5.97335C11.0904 5.93585 11.2577 5.89258 11.3942 5.89258C11.5192 5.89258 11.6509 5.92142 11.7596 5.95027C11.8769 5.982 12.0058 6.02431 12.1365 6.07239C12.4 6.16854 12.6971 6.29354 12.9721 6.41469C13.2928 6.55803 13.6109 6.70742 13.926 6.86277L13.9894 6.89354L14.0067 6.90219L14.0125 6.90508C14.1266 6.96207 14.2134 7.06207 14.2538 7.18307C14.2942 7.30406 14.2849 7.43615 14.2279 7.55027C14.1709 7.66439 14.0709 7.75119 13.9499 7.79159C13.8289 7.83198 13.6968 7.82265 13.5827 7.76566L13.5779 7.76277L13.5625 7.75508L13.5029 7.72623C13.1982 7.5764 12.8907 7.43214 12.5808 7.29354C12.327 7.1792 12.0695 7.07338 11.8086 6.97623C11.7109 6.94017 11.6118 6.90809 11.5115 6.88008C11.4739 6.86943 11.4357 6.86108 11.3971 6.85508L11.3885 6.857C11.3692 6.85989 11.3183 6.87046 11.2231 6.89739C11.0639 6.94444 10.9062 6.99607 10.75 7.05219C10.3769 7.18392 9.91826 7.36181 9.46922 7.54162C8.93783 7.75445 8.40863 7.97273 7.88172 8.19642L7.77499 8.24162L7.73749 8.25796ZM7.10576 8.00508C7.05571 7.88789 7.05424 7.75563 7.10166 7.63736C7.14907 7.51908 7.24151 7.42447 7.35864 7.37431L7.54807 7.81662L7.73749 8.25796C7.67941 8.28289 7.61699 8.29612 7.55379 8.2969C7.49059 8.29768 7.42786 8.286 7.36919 8.26251C7.31051 8.23902 7.25705 8.20419 7.21184 8.16002C7.16664 8.11585 7.13059 8.0632 7.10576 8.00508ZM5.27595 11.8128C5.31932 11.7669 5.3713 11.7299 5.42894 11.7041C5.48658 11.6783 5.54874 11.6641 5.61187 11.6623C5.675 11.6605 5.73786 11.6712 5.79687 11.6937C5.85588 11.7162 5.90987 11.7502 5.95576 11.7935L5.62499 12.1426L5.29518 12.4926C5.24927 12.4492 5.21235 12.3972 5.18653 12.3396C5.16072 12.2819 5.14651 12.2198 5.14472 12.1567C5.14294 12.0935 5.15361 12.0307 5.17612 11.9717C5.19864 11.9127 5.23256 11.8587 5.27595 11.8128ZM9.67018 15.0695L12.3269 16.0657C12.5862 16.1628 12.8679 16.1833 13.1385 16.1246C13.4091 16.066 13.6571 15.9307 13.8529 15.7349L15.8625 13.7253C15.9528 13.6351 16.0752 13.5845 16.2027 13.5846C16.3303 13.5847 16.4526 13.6355 16.5428 13.7258C16.6329 13.816 16.6835 13.9384 16.6834 14.066C16.6833 14.1936 16.6326 14.3159 16.5423 14.406L14.5327 16.4157C14.2063 16.7417 13.7929 16.967 13.3419 17.0645C12.8909 17.1621 12.4214 17.1277 11.9894 16.9657L9.27787 15.9493L9.25576 15.9387C9.10871 15.8572 8.96908 15.7629 8.83845 15.657C8.68268 15.5378 8.49999 15.3878 8.30287 15.2205C7.86154 14.8431 7.42623 14.4588 6.9971 14.0676C6.47277 13.5913 5.95256 13.1105 5.43653 12.6253L5.33172 12.5272L5.29518 12.4926L5.62499 12.1426L5.95576 11.7935L5.99134 11.8272L6.09422 11.9243C6.60628 12.4055 7.12232 12.8824 7.6423 13.3551C8.0846 13.756 8.54134 14.1628 8.92403 14.4878C9.11634 14.6503 9.28653 14.7887 9.42403 14.8955C9.55095 14.9926 9.63076 15.0464 9.67018 15.0685M6.41345 17.1233C6.49519 17.0259 6.61222 16.9648 6.73892 16.9534C6.86561 16.9421 6.99165 16.9814 7.08941 17.0628L8.01153 17.832C8.21817 18.0045 8.46898 18.1158 8.73557 18.1532L9.92403 18.3205C9.98796 18.3275 10.0498 18.3472 10.106 18.3786C10.1622 18.41 10.2115 18.4523 10.251 18.503C10.2905 18.5538 10.3194 18.6119 10.336 18.6741C10.3527 18.7362 10.3567 18.801 10.3478 18.8647C10.3389 18.9284 10.3173 18.9897 10.2843 19.0449C10.2514 19.1002 10.2076 19.1482 10.1557 19.1862C10.1038 19.2242 10.0449 19.2514 9.98227 19.2662C9.91968 19.281 9.85475 19.2831 9.79133 19.2724L8.60191 19.106C8.15757 19.0436 7.73956 18.8581 7.39518 18.5705L6.47307 17.8012C6.42452 17.7607 6.38445 17.7111 6.35512 17.6551C6.3258 17.5991 6.30781 17.5378 6.30219 17.4749C6.29656 17.4119 6.30341 17.3484 6.32233 17.2881C6.34126 17.2278 6.37286 17.1718 6.41345 17.1233Z"
                            fill="#667085"
                          />
                          <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M12.4997 24.0574C18.6064 24.0574 23.5574 19.1064 23.5574 12.9997C23.5574 6.89297 18.6064 1.94201 12.4997 1.94201C6.39297 1.94201 1.44201 6.89297 1.44201 12.9997C1.44201 19.1064 6.39297 24.0574 12.4997 24.0574ZM12.4997 25.0189C19.1382 25.0189 24.5189 19.6382 24.5189 12.9997C24.5189 6.36124 19.1382 0.980469 12.4997 0.980469C5.86124 0.980469 0.480469 6.36124 0.480469 12.9997C0.480469 19.6382 5.86124 25.0189 12.4997 25.0189Z"
                            fill="#667085"
                          />
                        </svg>
                        <p className="text-base dark:text-gray-400 font-medium">Attitude Score:</p>
                      </div>
                      {englishProficiency(test?.attitude_score)}
                    </div>
                  </div>
                </div>
              )}

              {/* WeirdBehavior */}
              {WeirdBehavior && (
                <div className="flex flex-wrap  items-center w-full mt-5 p-3 bg-[#ffdddd] dark:bg-[#561a1c] dark:bg-opacity-25  dark:border-gray-700  bg-opacity-30  gap-4">
                  <div className="flex items-center gap-2">
                    <Icon icon="fluent:warning-16-filled" className="text-[#C72716] dark:text-[#e5a2a2]" width={25} />
                    <p className="text-[#C72716] dark:text-[#e5a2a2] text-sm ">Suspicious behavior detected:</p>
                  </div>

                  <div className="flex flex-wrap w-fit lg:gap-6  gap-4 text-sm items-center">
                    {test?.weirdBehavior?.tabSwitchedCount > 0 && (
                      <div className="flex flex-wrap items-center">
                        <h2 className="dark:text-gray-200 px-2">Tab Switches:</h2>
                        <div className="flex items-center">
                          <div className="text-[#C72716]">
                            {test?.weirdBehavior?.tabSwitchedCount}
                            <span className="text-[#000000] px-1 dark:text-white">times</span>
                          </div>
                          <Tooltip className="z-[100]" content=" The user switched tabs during the test, possibly to look up answers.">
                            <Icon icon="quill:info" className="text-[#1C274C] dark:text-gray-400" width={20} />
                          </Tooltip>
                        </div>
                      </div>
                    )}
                    {test?.weirdBehavior?.ipChangeCount > 0 && (
                      <div className="flex flex-wrap items-center g">
                        <h2 className="dark:text-gray-200 ">IP Changes:</h2>
                        <div className="flex items-center">
                          <div className="text-[#C72716] px-2">
                            {test?.weirdBehavior?.ipChangeCount} <span className="text-[#000000] px-1 dark:text-white">times</span>
                          </div>
                          <Tooltip className="z-[100] " content=" The IP address changed during the test, possibly using external resources.">
                            <Icon icon="quill:info" className="text-[#1C274C] dark:text-gray-400" width={20} />
                          </Tooltip>
                        </div>
                      </div>
                    )}
                    {test?.weirdBehavior?.openContextMenuCount > 0 && (
                      <div className="flex flex-wrap items-center gap-1">
                        <h2 className="dark:text-gray-200 ">Context Menu:</h2>
                        <div className="flex items-center">
                          <div className="text-[#C72716] px-2">
                            {test?.weirdBehavior?.openContextMenuCount} <span className="text-[#000000] dark:text-white px-1 ">times</span>
                          </div>
                          <Tooltip className="z-[100]" content="The context menu was opened, suggesting attempts to find answers.">
                            <Icon icon="quill:info" className="text-[#1C274C] dark:text-gray-400" width={20} />
                          </Tooltip>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </>
          )
        ) : (
          statusIsNotSubmitted()
        )}
      </Card>

      {/* Need Subscription */}
      {needSubscription && <SubscribeDialog onClose={() => setNeedSubscription(false)} />}
    </>
  );
};
