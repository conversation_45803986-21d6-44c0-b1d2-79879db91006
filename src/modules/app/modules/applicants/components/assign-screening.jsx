// React
import { useState, useEffect } from 'react';

// UI
import { Api, useNotify, Select, Drawer, Form, useForm, useValidate, NoDataFound } from '/src';

// Components
import { TestCreatedSucessfully } from './test-created-sucessfully';
import { AssignScreeningListView } from './assign-screening-list-view';
import { TimeSettingsDialog } from './assign-time-settings';

/*
  questionsListData = [
    {
      _id: "123456789",
      title: "Question",
      isEditMode: Boolean,
    }
  ]
*/

export const AssignScreening = ({ setAssignTestVisibility, applicantDetails, setApplicantDetails, refreshMainTable = () => {} }) => {
  // State
  const [quizUrl, setQuizUrl] = useState('');
  const [isTestCreatedSucessfullyVisible, setTestCreatedSucessfullyVisibilty] = useState(false);
  const [quizzesListData, setQuizzesListData] = useState([]);
  const [screeningData, setScreeningData] = useState();
  const [questionsListData, setQuestionsListData] = useState();
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(() => {
    const result = new Date(startDate);
    result.setDate(result.getDate() + 1);
    return result;
  });
  const [extraTime, setExtraTime] = useState(0);
  const [isTimeSettingsVisible, setTimeSettingsVisible] = useState();
  const [loading, setLoading] = useState();
  const type = 'screening';

  // Hooks
  const { notify } = useNotify();
  const { form, setFieldValue, setFormValue, resetForm } = useForm({ screening: null });
  const { isRequired } = useValidate();

  const handleGetPhoneScreeningTests = async () => {
    try {
      const response = await Api.get('quizzes/list/phoneScreening');
      const { count, items } = response.data;
      setQuizzesListData(items);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    } finally {
    }
  };

  const onClose = () => {
    setAssignTestVisibility(false);
    setApplicantDetails(false);
  };

  const handleUpdate = async (id) => {
    try {
      setLoading(true);
      let payload = {
        questionsStatus: {
          deleted: [],
          created: [],
          updated: questionsListData
            ?.filter((question) => question?._id === id)
            ?.map((question) => ({ title: question?.pendingTitle, id: question?._id })),
        },
        duration: screeningData?.duration,
      };
      await Api.put(`quizzes/single/phoneScreening/${screeningData._id}`, payload);
      notify('Questions saved successfully!');
    } catch (error) {
      notify.error(error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!startDate) {
      notify.error('Please select start date');
    } else if (!endDate) {
      notify.error('Please select end date');
    } else {
      try {
        setLoading(true);
        let payload = {
          quizId: screeningData?._id,
          dueDate: endDate,
          startDate: startDate,
        };
        if (extraTime >= 1) payload.exceededTime = extraTime;

        // @TODO:Handle multi applicant assignment
        if (applicantDetails?._id) {
          payload.applicantId = [applicantDetails._id];
        }

        const response = await Api.post('submissions/single', payload);
        setQuizUrl(response?.data.quizUrl);
        setTestCreatedSucessfullyVisibilty(true);
      } catch (error) {
        notify.error(error?.response?.data?.message);
      } finally {
        setLoading(false);
      }
    }
  };

  const noDataFound = {
    customIcon: 'screening',
    message: 'No screening selected yet',
  };

  useEffect(() => {
    handleGetPhoneScreeningTests();
  }, []);

  /* Initial value of select component when open drawer with applicantDetails */
  useEffect(() => {
    if (applicantDetails) {
      setFieldValue('screening')(quizzesListData?.find((test) => test?.difficulty === applicantDetails?.seniorityLevel)?._id);
    }
  }, [quizzesListData]);

  return (
    <Drawer onClose={onClose}>
      <Drawer.SingleView>
        <Drawer.Header
          headerLabel={applicantDetails?.email ? 'Assign Screening' : 'Generate Screening Link'}
          headerSubLabel={applicantDetails?.email}
          onClose={onClose}
          className="border-b border-[#E5E7EB] pb-2"
        />

        <Drawer.Body.DatePicker
          startDate={startDate}
          dueDate={endDate}
          extraTime={extraTime}
          setExtraTime={setExtraTime}
          setTimeSettingsVisible={setTimeSettingsVisible}
          loading={loading}
          type={type}
        />

        <Form className="flex flex-col xsmd:flex-row gap-4 xsmd:items-center w-full">
          <p className="font-medium text-[#3C3D3E] dark:text-white">
            Select a Screening <span className="text-red-600 dark:text-red-800"> *</span>
          </p>
          <div className="sm:min-w-[250px]">
            <Select
              name="screening"
              lookup={quizzesListData?.map((test) => ({ value: test?._id, label: test?.title }))}
              value={form.screening}
              onChange={setFieldValue('screening')}
              dropIcon={true}
              validators={[isRequired()]}
            />
          </div>
        </Form>

        {form?.screening ? (
          <AssignScreeningListView
            screeningData={screeningData}
            setScreeningData={setScreeningData}
            questionsListData={questionsListData}
            setQuestionsListData={setQuestionsListData}
            applicantDetails={applicantDetails}
            setApplicantDetails={setApplicantDetails}
            handleUpdate={handleUpdate}
            handleSubmit={handleSubmit}
            difficulty={quizzesListData?.find((test) => test?._id === form?.screening)?.difficulty}
            onClose={onClose}
            handleLoading={loading}
          />
        ) : (
          <NoDataFound noDataFound={noDataFound} width="70" height="70" textMessageSize="text-[20px]" margin="mx-auto" />
        )}
      </Drawer.SingleView>

      {/* Test Ceated Sucessfully Visible */}
      {isTestCreatedSucessfullyVisible && (
        <TestCreatedSucessfully
          assignment={applicantDetails?.email ? true : false}
          defaultType="Screening"
          quizUrl={quizUrl}
          onClose={() => {
            applicantDetails?.email && refreshMainTable();
            onClose();
          }}
        />
      )}

      {/* Time Setting */}
      {isTimeSettingsVisible && (
        <TimeSettingsDialog
          onClose={() => setTimeSettingsVisible(false)}
          startDate={startDate}
          setStartDate={setStartDate}
          dueDate={endDate}
          setDueDate={setEndDate}
          type={type}
        />
      )}
    </Drawer>
  );
};
