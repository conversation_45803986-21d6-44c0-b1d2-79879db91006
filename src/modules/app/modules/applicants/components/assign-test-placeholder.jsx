export const AssignTestPlaceHolder = () => (
  <div>
    <div className="flex flex-col">
      <div className={` max-w p-6 rounded-lg shadow-sm  mt-2 h-10 animate-pulse flex justify-between items-center`}>
        <div className="h-5 bg-gray-300 rounded-full flex items-center justify-center dark:bg-gray-600 w-40"></div>
      </div>

      <div className={` max-w p-6 rounded-lg shadow-sm   h-10 animate-pulse flex justify-between items-center`}>
        <div className="h-4 bg-gray-200 rounded-full flex items-center justify-center dark:bg-gray-600 w-96"></div>
      </div>
    </div>
    <div className="flex w-full  ml-2  align-middle items-center gap-1 animate-pulse ">
      <div className="h-9  bg-gray-200 rounded-md mt-3  ml-1 dark:bg-gray-600 w-[70rem] mb-2.5"></div>
      <div className={` max-w p-6 rounded-lg shadow-sm  mt-2 h-10 animate-pulse flex justify-between items-center`}>
        <div className="h-10 bg-gray-200 rounded-md flex items-center align-middle justify-center dark:bg-gray-600 w-28"></div>
      </div>
    </div>

    <div className="flex w-full align-middle items-center  gap-9 animate-pulse">
      <div className="flex align-middle flex-row items-center gap-3 animate-pulse">
        <div className="h-6  bg-gray-200 rounded-md mt-3  ml-1 dark:bg-gray-600 w-36 mb-2.5"></div>
        <div className="h-10  bg-gray-200 rounded-md mt-3  ml-1 dark:bg-gray-600 w-72 mb-2.5"></div>
      </div>

      <div className="flex align-middle flex-row items-center gap-3 animate-pulse">
        <div className="h-6  bg-gray-200 rounded-md mt-3  ml-1 dark:bg-gray-600 w-36 mb-2.5"></div>
        <div className="h-10  bg-gray-200 rounded-md mt-3  ml-1 dark:bg-gray-600 w-72 mb-2.5"></div>
      </div>
    </div>

    <div className="relative p-4 space-y-4 mt-2  border-gray-200 divide-y divide-gray-200 rounded-xl shadow animate-pulse dark:divide-gray-700 md:p-6 dark:border-gray-700">
      <div className="flex items-center pb-4 dark:border-gray-500 border rounded-md p-3 w-full flex-col justify-between pt-4">
        <div className="w-full">
          <div className="flex w-full justify-between">
            <div className="flex gap-5 items-center align-middle">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-8 mb-2.5"></div>
              <div className="h-3  bg-gray-300 rounded-full dark:bg-gray-600 w-60 mb-3"></div>
            </div>
            <div>
              <div className="h-10 bg-gray-200 mt-3 rounded-2xl dark:bg-gray-600 w-40"></div>
            </div>
          </div>
        </div>
        <div className="flex items-start  mt-4 w-full ">
          <div className="flex  items-start gap-6 ">
            <div className="flex gap-2 align-middle items-center">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-28"></div>
            </div>

            <div className="flex gap-2 align-middle items-center">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-28"></div>
            </div>

            <div className="flex gap-2 align-middle items-center">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-28"></div>
            </div>

            <div className="flex gap-2 align-middle items-center">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-28"></div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center pb-4 dark:border-gray-500  rounded-md border p-3 w-full flex-col justify-between pt-4">
        <div className="w-full">
          <div className="flex w-full justify-between">
            <div className="flex gap-5 items-center align-middle">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-8 mb-2.5"></div>
              <div className="h-3  bg-gray-300 rounded-full dark:bg-gray-600 w-60 mb-3"></div>
            </div>
            <div>
              <div className="h-10 bg-gray-200 mt-3 rounded-2xl dark:bg-gray-600 w-40"></div>
            </div>
          </div>
        </div>
        <div className="flex items-start  mt-4 w-full ">
          <div className="flex  items-start gap-6 ">
            <div className="flex gap-2 align-middle items-center">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-28"></div>
            </div>

            <div className="flex gap-2 align-middle items-center">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-28"></div>
            </div>

            <div className="flex gap-2 align-middle items-center">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-28"></div>
            </div>

            <div className="flex gap-2 align-middle items-center">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-28"></div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center pb-4 dark:border-gray-500  rounded-md border p-3 w-full flex-col justify-between pt-4">
        <div className="w-full">
          <div className="flex w-full justify-between">
            <div className="flex gap-5 items-center align-middle">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-8 mb-2.5"></div>
              <div className="h-3  bg-gray-300 rounded-full dark:bg-gray-600 w-60 mb-3"></div>
            </div>
            <div>
              <div className="h-10 bg-gray-200 mt-3 rounded-2xl dark:bg-gray-600 w-40"></div>
            </div>
          </div>
        </div>
        <div className="flex items-start  mt-4 w-full ">
          <div className="flex  items-start gap-6 ">
            <div className="flex gap-2 align-middle items-center">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-28"></div>
            </div>

            <div className="flex gap-2 align-middle items-center">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-28"></div>
            </div>

            <div className="flex gap-2 align-middle items-center">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-28"></div>
            </div>

            <div className="flex gap-2 align-middle items-center">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-28"></div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center  pb-4  dark:border-gray-500  rounded-md border p-3 w-full flex-col justify-between pt-4">
        <div className="w-full">
          <div className="flex w-full justify-between">
            <div className="flex gap-5 items-center align-middle">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-8 mb-2.5"></div>
              <div className="h-3  bg-gray-300 rounded-full dark:bg-gray-600 w-60 mb-3"></div>
            </div>
            <div>
              <div className="h-10 bg-gray-200 mt-3 rounded-2xl dark:bg-gray-600 w-40"></div>
            </div>
          </div>
        </div>
        <div className="flex items-start  mt-4 w-full ">
          <div className="flex  items-start gap-6 ">
            <div className="flex gap-2 align-middle items-center">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-28"></div>
            </div>

            <div className="flex gap-2 align-middle items-center">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-28"></div>
            </div>

            <div className="flex gap-2 align-middle items-center">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-28"></div>
            </div>

            <div className="flex gap-2 align-middle items-center">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-28"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div className="flex justify-between mt-2 gap-4 p-2 animate-pulse">
      <div className="h-11 bg-gray-300 rounded-full dark:bg-gray-600 w-1/3"></div>
      <div className="h-11 bg-gray-300 rounded-full dark:bg-gray-600 w-2/3"></div>
    </div>
  </div>
);
