// React
import { useState } from 'react';

// UI
import { Icon, EnumText, CustomIcon } from '/src';

// Flowbite
import { Label, Radio } from 'flowbite-react';

// React icons
import { FaCheckCircle, FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { FaUserGraduate, FaUser, FaStar, FaMedal } from 'react-icons/fa';

export const AssignTestListData = ({ singleQuiz, expandedAll, setExpandedAll, setSubmissionId }) => {
  const [expandSection, setExpandSection] = useState({});

  // Methods
  const handleIconColors = (difficulty) => {
    let difficultyIcon;
    let difficultyColor;
    let iconSize = 'text-[15px]';
    switch (difficulty) {
      case 1:
        difficultyIcon = <FaUserGraduate className={`${iconSize} text-teal-700`} />; // Intern
        difficultyColor = ' text-teal-700 ';
        break;
      // Star Icon fresh level
      case 2:
        difficultyIcon = <FaUser className={`${iconSize} text-sky-800`} />; // Fresh
        difficultyColor = 'text-sky-800 ';
        break;
      // Medal Star junior
      case 3:
        difficultyIcon = <FaStar className={`${iconSize} text-amber-700`} />; // Junior
        difficultyColor = ' text-amber-700 ';
        break;
      // betetr medal star midlevel
      case 4:
        difficultyIcon = <FaMedal className={`${iconSize} text-orange-700`} />; // Mid-level
        difficultyColor = 'text-orange-700';
        break;

      // Tropy icon for senior with star
      case 5:
        difficultyIcon = <Icon icon="solar:crown-star-bold" width={18} className={`${iconSize} text-red-800`} />; // Senior
        difficultyColor = 'text-red-800';
        break;
      default:
        difficultyIcon = null;
    }
    return <div>{difficultyIcon}</div>;
  };

  const handleTopicsCoveredCount = (subCategory) => {
    let topicsCoveredCount = 0;
    subCategory.map((singleSubCategory) => (topicsCoveredCount += +singleSubCategory.topics?.length));
    return `${topicsCoveredCount} ${topicsCoveredCount > 1 ? ' Topics' : 'Topic'} covered`;
  };

  const handleSingleTopicCoveredCount = (topics) => {
    let singleTopicCoveredCount = 0;
    topics.map((singleTopic) => (singleTopicCoveredCount += +singleTopic.count));
    return `${singleTopicCoveredCount} ${singleTopicCoveredCount > 1 ? 'Questions' : 'Question'}`;
  };

  return (
    <div className="border rounded-lg p-3 mr-1" key={singleQuiz?._id}>
      <div className="flex gap-2">
        <Radio id={singleQuiz?._id} name="assignSelectedTestId" onClick={() => setSubmissionId(singleQuiz?._id)} className="cursor-pointer mt-1" />
        <div className="flex gap-2 justify-between grow">
          <div className="flex flex-col gap-3">
            <Label htmlFor={singleQuiz?._id} className="flex items-center gap-2 text-[15px] font-medium capitalize text-[#101828] dark:text-white">
              {singleQuiz?.subCategoryName?.join(' & ')}
            </Label>
            <div className="flex flex-col lg:flex-row lg:items-center gap-3 text-sm font-medium text-gray-500 dark:text-gray-300">
              <div className="flex flex-col sm:flex-row gap-2 lg:gap-3">
                {/* Difficulty */}
                <div className="flex gap-2 items-center">
                  <div>{handleIconColors(singleQuiz?.difficulty)}</div>
                  <EnumText name="QuizDifficulty" value={singleQuiz?.difficulty} /> Level
                </div>
                <span className="hidden sm:block text-gray-400 dark:text-gray-300">•</span>
                {/* Duration */}
                <div className="flex gap-2 items-center">
                  <span>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 22" fill="currentColor" className="size-4">
                      <path
                        fillRule="evenodd"
                        d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 6a.75.75 0 0 0-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 0 0 0-1.5h-3.75V6Z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </span>
                  <span className="text-gray-500 dark:text-gray-300">{singleQuiz?.duration} mins</span>
                </div>
              </div>
              <span className="hidden lg:block text-gray-400 dark:text-gray-300">•</span>
              <div className="flex flex-col sm:flex-row gap-2 lg:gap-3">
                {/* Questions */}
                <div className="flex gap-2 items-center">
                  <span>
                    <CustomIcon definedIcon='questions' className='text-[#8A43F9]' width="18" height="19"/>
                  </span>
                  <span className="text-gray-500 dark:text-gray-300">
                    {singleQuiz?.numOfQuestions} {singleQuiz?.numOfQuestions > 1 ? ' Questions' : 'Question'}
                  </span>
                </div>
                <span className="hidden sm:block text-gray-400 dark:text-gray-300">•</span>
                {/* Topics */}
                <div className="flex gap-2 items-center">
                  <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M4.08301 0.112305C3.08845 0.112305 2.13462 0.507393 1.43136 1.21065C0.728096 1.91392 0.333008 2.86774 0.333008 3.8623V12.1956H0.362174C0.462387 12.8899 0.809469 13.5248 1.3398 13.984C1.87014 14.4432 2.54819 14.6958 3.24967 14.6956H12.833C13.054 14.6956 13.266 14.6078 13.4223 14.4516C13.5785 14.2953 13.6663 14.0833 13.6663 13.8623V1.3623C13.6663 1.03078 13.5346 0.712842 13.3002 0.478421C13.0658 0.244001 12.7479 0.112305 12.4163 0.112305H4.08301ZM3.24967 10.1123H12.4163V13.4456H3.24967C2.80765 13.4456 2.38372 13.27 2.07116 12.9575C1.7586 12.6449 1.58301 12.221 1.58301 11.779C1.58301 11.3369 1.7586 10.913 2.07116 10.6005C2.38372 10.2879 2.80765 10.1123 3.24967 10.1123ZM9.93301 4.31314C10.0526 4.19821 10.1216 4.04049 10.1249 3.87468C10.1282 3.70887 10.0654 3.54854 9.95051 3.42897C9.83558 3.3094 9.67786 3.24039 9.51205 3.2371C9.34624 3.23382 9.18591 3.29654 9.06634 3.41147L6.89634 5.49564L5.76551 4.4123C5.70687 4.35275 5.63689 4.30556 5.55969 4.27352C5.4825 4.24149 5.39966 4.22526 5.31609 4.2258C5.23251 4.22635 5.14989 4.24365 5.07312 4.27668C4.99635 4.30971 4.92698 4.3578 4.86912 4.41812C4.81126 4.47843 4.76609 4.54973 4.73627 4.62781C4.70645 4.70588 4.6926 4.78915 4.69552 4.87268C4.69845 4.9562 4.7181 5.03829 4.75331 5.11409C4.78852 5.18989 4.83857 5.25786 4.90051 5.31397L6.46301 6.81397C6.57937 6.92551 6.73432 6.98779 6.89551 6.98779C7.05669 6.98779 7.21165 6.92551 7.32801 6.81397L9.93301 4.31314Z"
                      fill="#6B7280"
                    />
                  </svg>
                  <span className="text-gray-500 dark:text-gray-300">{handleTopicsCoveredCount(singleQuiz.subCategories)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Button expand all */}
          <div
            onClick={() => setExpandedAll((prev) => ({ ...prev, [singleQuiz._id]: !prev[singleQuiz._id] }))}
            className="flex justify-between items-center self-start sm:self-center border border-[#1C1B1B40] dark:border-gray-500 text-[#101828] dark:text-white px-2 py-2 rounded-lg text-sm font-medium cursor-pointer"
          >
            <Icon className="sm:mr-2 h-5 w-5" icon={expandedAll[singleQuiz._id] ? 'iconamoon:eye-off' : 'ph:eye'} width="18" />
            <span className="hidden sm:block text-nowrap">{expandedAll[singleQuiz._id] ? 'Hide Details' : 'Show Details'}</span>
          </div>
        </div>
      </div>

      <div
        className={`${
          expandedAll[singleQuiz._id] ? 'block' : 'hidden'
        } bg-white dark:bg-gray-800 rounded-xl py-2  mt-2 border-1 border border-gray-200 dark:border-none`}
      >
        {/* Sub category breakdown */}
        <div className="space-y-2">
          {singleQuiz?.subCategories?.map((subCategory, subIndex) => (
            <div
              key={subCategory?.subCategoryId}
              className={`py-2 ${subIndex === singleQuiz?.subCategories?.length - 1 ? '' : 'border-b border-gray-200 dark:border-gray-700'}`}
            >
              {/* Subcategory Header */}
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center px-4 gap-3">
                <div className="flex flex-col sm:flex-row text-gray-700 dark:text-white">
                  <span className="text-sm font-medium">{subCategory?.subCategoryName}</span>
                  <span className="text-sm font-normal text-gray-500 dark:text-gray-400 sm:mx-2">
                    ( {handleSingleTopicCoveredCount(subCategory?.topics)} )
                  </span>
                </div>
                <div
                  onClick={() => {
                    setExpandSection((prev) => ({ ...prev, [subIndex]: !prev[subIndex] }));
                  }}
                  className="cursor-pointer text-gray-500 dark:text-gray-300 text-opacity-90 hover:underline focus:outline-none text-[14px] font-medium flex items-center"
                >
                  {expandSection[subIndex] ? 'Hide Topics' : 'Show Topics'}
                  {expandSection[subIndex] ? <FaChevronUp className="mx-2" /> : <FaChevronDown className="mx-2" />}
                </div>
              </div>

              {/* Subcategory Topics */}
              {expandSection[subIndex] && (
                <div className="mt-3 space-y-1 px-4">
                  {subCategory?.topics?.map((topic) => (
                    <div key={topic?.topicId} className="flex gap-2 items-center ">
                      <FaCheckCircle className="text-gray-500 dark:text-gray-600 text-[15px]" />
                      <p className="flex items-center font-medium text-[15px] text-gray-500 dark:text-gray-300">{topic?.topicName}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
