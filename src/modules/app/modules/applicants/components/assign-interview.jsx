// React
import { useState, useRef, useEffect } from 'react';

// Core
import { Form, Radio, useValidate, MultiSelect, Icon, Button, useForm, useNotify, Select, Api, TextInput, Enums, Drawer, Regex } from '/src';

// Flowbite
import { Label } from 'flowbite-react';

// Components
import { TimeSettingsDialog } from './assign-time-settings';
import { TestCreatedSucessfully } from './test-created-sucessfully';

export const AssignIntreview = ({ onClose, onCreate, applicantDetails, refreshMainTable = () => {} }) => {
  // Reference
  const subCategoryRef = useRef(null);

  // Hooks
  const { notify } = useNotify();
  const { isRequired, isNumber, isValidateMaxAndMinNumber } = useValidate();

  // State
  const [isTestCreatedSucessfullyVisible, setTestCreatedSucessfullyVisibilty] = useState(false);
  const [loading, setLoading] = useState(false);
  const [interviewQuiz, setInterviewQuiz] = useState(false);
  const [startDate, setStartDate] = useState(new Date());
  const [dueDate, setDueDate] = useState(() => {
    const result = new Date(startDate);
    result.setDate(result.getDate() + 1);
    return result;
  });
  const [extraTime, setExtraTime] = useState(false);
  const [isTimeSettingsVisible, setTimeSettingsVisible] = useState();
  const type = 'interview';

  // Form
  const { form, setFieldValue } = useForm({
    technology: '',
    numberOfQuestions: '',
    yearsOfExperience: '',
    estimationTime: '',
    type: 1, // Interview type
    skips: '',
    applicantId: '',
    notes: '',
    dueDate: '',
    startDate: '',
    category: applicantDetails?.track ? applicantDetails.track : null,
    subCategory: [],
    modelType: 'gpt-4.1-mini',
    avatarName: Enums?.AiAvatarModel[0].value,
    avatarLang: '',
  });

  const handleCreate = async (e) => {
    if (form.numberOfQuestions > 30) {
      notify.error("Max questions can't exceed 30");
    } else if (Number(form.skips) >= Number(form.numberOfQuestions)) {
      notify.error("Max skips can't exceed or equal to number of questions");
    } else if (!form?.startDate) {
      notify.error('Please select start date');
    } else if (!form?.dueDate) {
      notify.error('Please select due date');
    } else {
      try {
        setLoading(true);
        const { applicantId, ...payload } = form;
        if (payload.notes == '') delete payload.notes;
        // @TODO:Handle multi applicant assignment
        if (form.applicantId) payload.applicantId = [form.applicantId];
        
        // Include recordInterview flag
        payload.recordInterview = !!form.recordInterview;
        
        const result = await Api.post('ai-interview/single', payload);
        setInterviewQuiz(result.data.quizUrl);
        setTestCreatedSucessfullyVisibilty(true);
        onCreate();
      } catch (error) {
        notify.error(error.response.data.message);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleCopyLink = () => {
    navigator.clipboard.writeText(interviewQuiz);
    notify('Link copied');
  };

  useEffect(() => {
    if (applicantDetails) {
      setFieldValue('applicantId')(applicantDetails?._id);
      setFieldValue('category')(applicantDetails?.track);
    }
  }, []);

  useEffect(() => {
    setFieldValue('startDate')(startDate);
    setFieldValue('dueDate')(dueDate);
  }, [startDate, dueDate]);

  return (
    <Drawer split onClose={onClose} className="justify-between">
      <Drawer.SingleView>
        <Drawer.Header
          headerLabel={applicantDetails?.email ? 'Assign Interview' : 'Generate Interview Link'}
          headerSubLabel={applicantDetails?.email}
          onClose={onClose}
          className="border-b border-[#E5E7EB] pb-2"
        />

        <Drawer.Body.DatePicker
          startDate={startDate}
          dueDate={dueDate}
          extraTime={extraTime}
          setExtraTime={setExtraTime}
          setTimeSettingsVisible={setTimeSettingsVisible}
          loading={loading}
          type={type}
        />

        {/* Creation Form */}
        <div className="h-full overflow-auto">
          <Form className="grid sm:grid-cols-2 gap-4 my-2">
            <Select
              label="Interview Type"
              name="type"
              value={form.type}
              disabled={loading}
              onChange={setFieldValue('type')}
              lookup="$InterviewType"
              dropIcon
              requiredLabel
              labelTooltip={
                <div className="space-y-2">
                  <p>Interactive Interview: Live interview experience with real-time questions.</p>
                  <p>Ready Questions: Static set of questions for self-paced completion.</p>
                </div>
              }
            />

            <Select
              label="Interview Model"
              name="model"
              value={form.modelType}
              onChange={setFieldValue('modelType', String)}
              lookup="$InterviewModel"
              dropIcon={true}
            />

            {!applicantDetails?.track && (
              <Select
                label="Category"
                name="category"
                value={form.category}
                disabled={loading}
                onChange={(newCategory) => {
                  subCategoryRef.current?.blur();
                  setFieldValue('category')(newCategory);
                  setFieldValue('subCategory')(null);
                }}
                lookup="category"
                optionValueKey="_id"
                optionLabelKey="name"
                dropIcon
                requiredLabel
                creationOptions={{
                  url: 'lookups/category/single',
                  fieldName: 'name',
                  validation: Regex.categorySubcategoryTopic,
                }}
              />
            )}

            <MultiSelect
              key={form.category}
              label="Subcategory"
              requiredLabel
              name="subCategory"
              placeholder="Search for subcategory"
              value={Array.isArray(form.subCategory) ? form.subCategory : []}
              onChange={(newSubCategory) => setFieldValue('subCategory')(newSubCategory)}
              disabled={!form.category || loading}
              disabledMessage="Please select category first"
              lookup="subcategory"
              params={{ categoryId: form.category }}
              creationOptions={{
                url: 'lookups/subCategory/single',
                fieldName: 'name',
                validation: Regex.categorySubcategoryTopic,
              }}
              optionValueKey="_id"
              optionLabelKey="name"
              dropIcon
            />

            <Select
              disabled={loading}
              name="difficulty"
              label="Difficulty"
              lookup="$QuizDifficulty"
              value={form.difficulty}
              onChange={setFieldValue('difficulty')}
              dropIcon
              requiredLabel
            />

            <TextInput
              disabled={loading}
              name="numberOfQuestions"
              label="Number of Questions"
              placeholder="Number of questions"
              value={form.numberOfQuestions}
              onChange={setFieldValue('numberOfQuestions')}
              validators={[isNumber(), isRequired()]}
              requiredLabel
            />

            <TextInput
              disabled={loading}
              name="estimationTime"
              label="Estimation Time"
              placeholder="Estimation time"
              labelTooltip="Expected time for the interview in minutes."
              value={form.estimationTime}
              onChange={setFieldValue('estimationTime')}
              validators={[isNumber(), isRequired(), isValidateMaxAndMinNumber('min', 10), isValidateMaxAndMinNumber('max', 240)]}
              requiredLabel
              type="number"
              min={10}
            />

            <TextInput
              disabled={loading}
              name="skips"
              label="Max Skips"
              labelTooltip="Maximum skips allowed without affecting the score."
              placeholder="Max Skips"
              value={form.skips}
              onChange={setFieldValue('skips')}
              validators={[isNumber(), isRequired()]}
              requiredLabel
            />

            <TextInput
              label="Notes"
              name="notes"
              placeholder="e.g., Focus on advanced JavaScript topics"
              labelTooltip="Provide specific details or instructions for the interview."
              value={form.notes}
              onChange={setFieldValue('notes')}
              disabled={loading}
            />

            <Select
              name="avatarLang"
              label="Language"
              lookup={[
                { value: 'English', label: 'English' },
                { value: 'Arabic', label: 'Arabic' },
                { value: 'Turkish', label: 'Turkish' },
              ]}
              value={form.avatarLang}
              onChange={setFieldValue('avatarLang')}
              dropIcon={true}
              validators={[isRequired()]}
            />

            <div className="space-y-2">
              <Label className="text-inputLabel dark:text-inputDarkLabel">Avatar Model</Label>
              <div className="grid grid-cols-2 space-y-2 ml-1">
                {Enums?.AiAvatarModel?.map((model) => (
                  <div className="flex items-center">
                    <Radio
                      key={model?.value}
                      name={model?.value}
                      selectionValue={model?.value}
                      value={form.avatarName}
                      onChange={setFieldValue('avatarName')}
                      lookup="$AiAvatarModel"
                      className="cursor-pointer"
                    />
                    <img
                      src={`/images/models/${model?.iconPath}`}
                      alt="Icon"
                      className="size-10 rounded-full cursor-pointer"
                      onClick={() => setFieldValue('avatarName')(model?.value)}
                    />
                    <Label htmlFor={model.value} className="ml-2 text-inputLabel dark:text-inputDarkLabel capitalize cursor-pointer">
                      {model?.value}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </Form>
        </div>

        <Drawer.Footer>
          <div className="grid grid-cols-6 w-full gap-4 items-end">
            <Button className="col-span-2" tertiary label="Cancel" onClick={onClose} />
            <Button
              loading={loading}
              disabled={
                loading ||
                !form.type ||
                !form.category ||
                !form.subCategory ||
                !form.difficulty ||
                !form.numberOfQuestions ||
                !form.estimationTime ||
                !form.skips ||
                !form.avatarLang
              }
              className="col-span-4"
              onClick={handleCreate}
              label={applicantDetails?.email ? 'Assign Interview' : 'Generate Link'}
            />
          </div>
        </Drawer.Footer>
      </Drawer.SingleView>

      {/* Interview Ceated Sucessfully */}
      {isTestCreatedSucessfullyVisible && (
        <TestCreatedSucessfully
          assignment={applicantDetails?.email ? true : false}
          defaultType="Interview"
          quizUrl={interviewQuiz}
          onClose={() => {
            applicantDetails?.email && refreshMainTable();
            onClose();
          }}
        />
      )}

      {isTimeSettingsVisible && (
        <TimeSettingsDialog
          onClose={() => setTimeSettingsVisible(false)}
          startDate={startDate}
          setStartDate={setStartDate}
          dueDate={dueDate}
          setDueDate={setDueDate}
          type={type}
        />
      )}
    </Drawer>
  );
};
