// React
import { useContext, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

// UI
import { Icon, useNotify, Api, EnumText, CustomIcon, Button, SubscribeDialog } from '/src';

// Context
import { AppContext } from '/src/components/provider';

// Components
import { AssignScreening } from '../components/assign-screening';
import { AssignTest } from './assign-test';
import { AssignIntreview } from './assign-interview';
import { ApplicantDataSkeleton } from './applicant-data-skeleton';
import { ApplicantsSingleDialog } from './single-dialog';
import { PerformanceSummaryDialog } from './performance-summary';

// Flowbite
import { Tooltip, Dropdown } from 'flowbite-react';

export const ApplicantData = () => {
  // User Data
  const { userData } = useContext(AppContext);
  const isSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));

  // Hooks
  const { id } = useParams();
  const { notify } = useNotify();

  // State
  const [applicantDetails, setApplicantDetails] = useState({});
  const [isAssignTestVisible, setAssignTestVisibility] = useState(false);
  const [isScreeningVisible, setIsScreeningVisible] = useState(false);
  const [isAssignInterviewTestVisible, setAssignInterviewTestVisible] = useState(false);
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [isPerformanceSummaryDialogVisible, setIsPerformanceSummaryDialogVisible] = useState(false);
  const [needSubscription, setNeedSubscription] = useState(false);

  const actions = [
    {
      label: 'Assign',
      dropdownlist: [
        {
          label: 'Screening',
          customIcon: 'screening',
          onClick: () => {
            if (isSuperAdmin || userData?.features?.assignScreening > 0) {
              setIsScreeningVisible(true);
            } else {
              setNeedSubscription(true);
            }
          },
        },
        {
          label: 'Test',
          customIcon: 'tests',
          onClick: () => {
            if (isSuperAdmin || userData?.features?.assignTest > 0) {
              setAssignTestVisibility(true);
            } else {
              setNeedSubscription(true);
            }
          },
        },
        {
          label: 'Interview',
          customIcon: 'interview',
          element: (
            <span
              className={`w-24 text-xs px-2 py-1 rounded-full shadow-md font-bold tracking-wide bg-magic-gradient bg-[length:200%]  text-white overflow-hidden`}
            >
              AI Magic ✨
            </span>
          ),
          onClick: () => {
            if (isSuperAdmin || userData?.features?.assignInterview > 0) {
              setAssignInterviewTestVisible(true);
            } else {
              setNeedSubscription(true);
            }
          },
        },
      ],
    },
  ];

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get(`applicants/assessments/count/${id}`);
      setApplicantDetails(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  const handleJoinDate = (date) => {
    const [mon, day, year] = new Date(date).toDateString().slice(4).split(' ');
    return `${day} ${mon}, ${year}`;
  };

  const renderPhoneAndEmail = () => {
    const applicantData = [
      {
        startIcon: 'mynaui:envelope',
        endIcon: 'si:copy-line',
        text: applicantDetails?.applicant?.email,
        notify: 'Email',
      },
      {
        startIcon: 'ph:phone-light',
        endIcon: 'si:copy-line',
        text: applicantDetails?.applicant?.mobileNumber,
        notify: 'Phone',
      },
    ];
    return (
      <div className="flex pt-3 px-0 gap-4 text-linaHalfGray dark:text-linaDarkHalfGray justify-center sm:justify-start font-semibold flex-wrap sm:flex-nowrap">
        {applicantData?.map((singleData) => (
          <div
            key={singleData?.startIcon}
            className="flex items-center gap-2 sm:border border-[#EAECF0] rounded-lg px-1 py-1 max-w-full overflow-hidden"
          >
            <Icon className="text-[#798296] dark:text-purple-400" width={22} icon={singleData?.startIcon} />
            <p className={`text-[#333] dark:text-gray-300 font-normal truncate ${singleData?.notify === 'Phone' && 'text-[17px] font-semibold'}`}>
              {singleData?.text}
            </p>
            <Icon
              className={`dark:text-purple-400 text-[#4f5561] cursor-pointer ${singleData?.notify === 'Phone' && 'text-[#798296] '} `}
              width={singleData?.notify === 'Phone' ? 21 : 20}
              icon={singleData?.endIcon}
              onClick={() => {
                navigator.clipboard.writeText(singleData?.text);
                notify(`${singleData?.notify} copied`);
              }}
            />
          </div>
        ))}
      </div>
    );
  };

  useEffect(() => {
    if (id) {
      handleGet();
    }
  }, []);

  useEffect(() => {
    if (isAssignTestVisible || isScreeningVisible) {
      document.querySelector('html').classList.add('overflow-x-hidden');
    } else {
      document.querySelector('html').classList.remove('overflow-x-hidden');
    }
  }, [isAssignTestVisible, isScreeningVisible]);

  if (Object.keys(applicantDetails).length === 0) {
    return <ApplicantDataSkeleton />;
  }

  return (
    <>
      <div className=" bg-opacity-30 border-b pb-3 dark:bg-darkBackgroundCard rounded-lg dark:border-gray-700">
        {/* Applicant information */}
        <div className="flex flex-col sm:flex-row w-full align-middle px-3 gap-4 py-4  ">
          <div className="flex flex-col lg:flex-row justify-between w-full items-start ">
            <div className="space-y-3 w-full ">
              <div className="flex flex-wrap gap-5 xsmd:justify-between  ">
                {/* name and seniorty level */}
                <div className="flex sm:flex-row sm:items-center  gap-5 mx-auto xsmd:mx-0 flex-col w-full">
                  {/* avatar image */}
                  <div className="xsmd:min-w-32 mx-auto">
                    <img
                      src={applicantDetails?.applicant?.gender === 2 ? '/images/avatar-female.svg' : '/images/avatar-male.svg'}
                      className="w-full "
                      alt="Avatar"
                    />
                  </div>
                  {/* Applicant Name,track,button popUp */}
                  <div className="flex flex-col sm:flex-row justify-between w-full gap-3 ">
                    <div className="flex flex-col items-center sm:items-start  space-y-1 ">
                      <h2 className="text-2xl capitalize w-full sm:max-w-xl sm:text-start text-center  truncate  whitespace-nowrap font-bold dark:text-white">
                        {applicantDetails?.applicant?.name}
                      </h2>
                      <p className="text-lg font-normal capitalize pt-1 text-[#667085] dark:text-linaDarkHalfGray">
                        <EnumText name="QuizDifficulty" value={applicantDetails?.applicant?.seniorityLevel} />{' '}
                        {applicantDetails?.applicant?.trackName}
                      </p>
                      <p className="block sm:hidden self-center text-halfGray pb-4 sm:py-3 py-0 italic dark:text-darkHalfGray sm:ml-4">
                        Joined: {handleJoinDate(applicantDetails?.applicant?.createdAt)}
                      </p>

                      {/* performance summary  */}
                      <div
                        className={`w-fit text-nowrap flex items-center p-2 gap-2 ${
                          applicantDetails?.totalWeirdBeauvoir + applicantDetails?.totalMissed + applicantDetails?.totalOverDue > 0
                            ? 'bg-[#FEF3F2] text-[#C72716]'
                            : 'bg-[#FAFFF5] text-[#0F766E]'
                        }  rounded-lg hover:cursor-pointer`}
                        onClick={() => setIsPerformanceSummaryDialogVisible(true)}
                      >
                        {applicantDetails?.totalWeirdBeauvoir + applicantDetails?.totalMissed + applicantDetails?.totalOverDue > 0 && (
                          <CustomIcon definedIcon="info" width="14" height="14" />
                        )}
                        <p className="text-sm font-normal ">Performance Summary</p>

                        <CustomIcon
                          definedIcon="arrowRight"
                          stroke={
                            applicantDetails?.totalWeirdBeauvoir + applicantDetails?.totalMissed + applicantDetails?.totalOverDue > 0
                              ? '#C72716'
                              : '#0F766E'
                          }
                          width="14"
                          height="14"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-center sm:justify-start gap-3">
                        {actions.map((action) => {
                          return (
                            <div className="relative  " key={action?.label || action?.icon}>
                              <Dropdown
                                label=""
                                dismissOnClick={false}
                                renderTrigger={() => <Button tertiary customIcon="assign" label={action.label} />}
                              >
                                {action.dropdownlist.map(({ label, color, icon, element, customIcon, ...subButton }, index) => (
                                  <div className="min-w-56" key={label || icon}>
                                    <Dropdown.Item onClick={subButton.onClick}>
                                      <div className="flex gap-2 cursor-pointer w-fit" {...subButton}>
                                        {icon && <Icon className="cursor-pointer" width={20} style={{ color: color }} icon={icon} />}{' '}
                                        {customIcon && <CustomIcon definedIcon={customIcon} className="cursor-pointer" width="19" height="19" />}
                                        <p className="text-nowrap text-black dark:text-white">{label}</p>
                                        <p>{element}</p>
                                      </div>
                                    </Dropdown.Item>
                                    {action.dropdownlist.length - 1 > index && <hr className="dark:border-gray-500" />}
                                  </div>
                                ))}
                              </Dropdown>
                            </div>
                          );
                        })}
                        <Button customIcon="edit" label="Edit Profile" onClick={() => setCreateDialogVisibility(true)} tertiary />
                      </div>
                      <p className="hidden sm:block text-center text-halfGray pt-6 italic dark:text-darkHalfGray ">
                        Joined: {handleJoinDate(applicantDetails?.applicant?.createdAt)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {renderPhoneAndEmail()}
            </div>
          </div>
        </div>

        {isAssignTestVisible && (
          <AssignTest
            isAssignTestVisible={isAssignTestVisible}
            setAssignTestVisibility={setAssignTestVisibility}
            applicantDetails={applicantDetails?.applicant}
            setApplicantDetails={() => {}}
          />
        )}

        {isScreeningVisible && (
          <AssignScreening
            isAssignTestVisible={isScreeningVisible}
            setAssignTestVisibility={setIsScreeningVisible}
            applicantDetails={applicantDetails?.applicant}
            setApplicantDetails={() => {}}
          />
        )}
        {isAssignInterviewTestVisible && (
          <AssignIntreview
            applicantDetails={applicantDetails?.applicant}
            onClose={() => {
              setAssignInterviewTestVisible(false);
            }}
          />
        )}
        {isPerformanceSummaryDialogVisible && (
          <PerformanceSummaryDialog
            applicantDetails={applicantDetails}
            onClose={() => {
              setIsPerformanceSummaryDialogVisible(false);
            }}
          />
        )}
      </div>

      {/* Need Subscription */}
      {needSubscription && <SubscribeDialog onClose={() => setNeedSubscription(false)} />}

      {/* Create new applicant */}
      {isCreateDialogVisible && <ApplicantsSingleDialog onCreate={handleGet} onClose={() => setCreateDialogVisibility(false)} id={id} />}
    </>
  );
};
