// Hooks
import { useDarkMode } from '/src/hooks/dark-mode';

// Core
import { Button, Icon } from '/src';

export const StudioPage = () => {
  // Hooks
  const { switchDarkMode, isDark } = useDarkMode();

  return (
    <div className="space-y-4 text-red-700 dark:text-cyan-400">
      <p>Studio for testing purpose only</p>

      <Icon icon="emojione:turtle" width={40} className="!justify-start" />

      <Button label="Dark theme" icon={isDark ? 'ic:outline-light-mode' : 'ic:outline-dark-mode'} onClick={switchDarkMode} />
    </div>
  );
};
