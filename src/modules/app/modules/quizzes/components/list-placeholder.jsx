export const ListPlaceholder = () => (
  <div className="relative border border-gray-200 divide-gray-200 shadow animate-pulse dark:divide-gray-700 dark:border-gray-700 rounded-lg">
    <div className="hidden lg:block py-4">
      <div className="grid grid-cols-12 py-4 px-4">
        <div className="col-span-4 h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-32"></div>
        <div className="col-span-3 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
        <div className="col-span-2 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
        <div className="col-span-2 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
        <div className="col-span-1 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
      </div>
      <div className="grid grid-cols-12 py-4 px-4">
        <div className="col-span-4 h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-32"></div>
        <div className="col-span-3 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
        <div className="col-span-2 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
        <div className="col-span-2 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
        <div className="col-span-1 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
      </div>
      <div className="grid grid-cols-12 py-4 px-4">
        <div className="col-span-4 h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-32"></div>
        <div className="col-span-3 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
        <div className="col-span-2 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
        <div className="col-span-2 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
        <div className="col-span-1 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
      </div>
      <div className="grid grid-cols-12 py-4 px-4">
        <div className="col-span-4 h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-32"></div>
        <div className="col-span-3 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
        <div className="col-span-2 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
        <div className="col-span-2 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
        <div className="col-span-1 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
      </div>
      <div className="grid grid-cols-12 py-4 px-4">
        <div className="col-span-4 h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-32"></div>
        <div className="col-span-3 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
        <div className="col-span-2 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
        <div className="col-span-2 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
        <div className="col-span-1 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
      </div>
    </div>

    <div className="hidden sm:block lg:hidden p-6">
      <div className="grid grid-cols-1 gap-10">
        <div className="grid grid-cols-5 gap-6 ">
          <div className="col-span-1 h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
          <div className="col-span-4 h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-12"></div>
        </div>
        <div className="grid grid-cols-5 gap-6 ">
          <div className="col-span-1 h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
          <div className="col-span-4 h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-12"></div>
        </div>
        <div className="grid grid-cols-5 gap-6 ">
          <div className="col-span-1 h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
          <div className="col-span-4 h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-12"></div>
        </div>
        <div className="grid grid-cols-5 gap-6 ">
          <div className="col-span-1 h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
          <div className="col-span-4 h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-12"></div>
        </div>
      </div>
    </div>

    <div className="sm:hidden p-6">
      <div className="flex gap-3">
        <div className="w-[20px] h-[20px] bg-gray-300 dark:bg-gray-600 rounded-full "></div>
        <div className="grid grid-cols-1 gap-2">
          <div className="col-span-1 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-20"></div>
          <div className="col-span-1 h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
        </div>
      </div>
    </div>
  </div>
);
