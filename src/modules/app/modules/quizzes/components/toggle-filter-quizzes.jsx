import React, { useState } from 'react';
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import { Icon } from '/src';

export const ToggleFilter = ({ filters }) => {
  const [open, setOpen] = useState(false);

  const renderFilterOptionsManual = (filter) => {
    return (
      <DropdownMenu.Sub>
        <DropdownMenu.SubTrigger className="flex cursor-default rounded-lg items-center justify-between w-full px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-[#212129]">
          <span>{filter.label}</span>

          <div className="flex gap-2 items-center">
            <div className="bg-[#ece2fd] dark:bg-[#1f1a2e] size-6 flex justify-center items-center text-primaryPurple text-xs rounded-full">
              {filter.options.length}
            </div>
            <Icon icon="mdi:chevron-right" className="ml-2" width="18" />
          </div>
        </DropdownMenu.SubTrigger>
        <DropdownMenu.SubContent
          className="bg-white  border border-[#e4e4e7] shadow-sm rounded-lg py-3 w-[200px] space-y-2  dark:border-[#27272a] dark:bg-darkGrayBackground"
          side="right"
          align="start"
          sideOffset={8}
        >
          <div className="text-gray-700 dark:text-white border-b border-b-gray-200 dark:border-b-[#27272a] text-sm font-bold pb-1 px-3">
            {filter.label}
          </div>
          <div className="max-h-52 custom-scroll overflow-y-auto flex flex-col gap-2 px-3 pb-1 me-1.5">
            {filter.options.length > 0 ? (
              filter.options.map((option, index) => (
                <div key={index} className="flex items-center justify-between">
                  <label className="flex items-center space-x-2  w-full cursor-pointer">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500 cursor-pointer"
                      checked={option.value}
                      onChange={() => option.onChange(!option.value)}
                    />
                    <span
                      className={`${
                        option.value ? 'text-purple-600 dark:text-purple-400' : 'text-gray-900 dark:text-gray-200'
                      } flex text-sm font-medium items-center`}
                    >
                      {option.label}
                    </span>
                  </label>
                </div>
              ))
            ) : (
              <div className="text-sm font-bold text-gray-500">No options</div>
            )}
          </div>
        </DropdownMenu.SubContent>
      </DropdownMenu.Sub>
    );
  };

  return (
    <DropdownMenu.Root open={open} onOpenChange={setOpen}>
      <DropdownMenu.Trigger asChild>
        <button className="inline-flex items-center justify-center h-10 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
          <Icon icon="pajamas:filter" width="22" />
          {/* <span className="ml-2">Filters</span> */}
        </button>
      </DropdownMenu.Trigger>

      <DropdownMenu.Content
        className="min-w-[250px] max-w-[300px] bg-white border border-[#e4e4e7]  shadow-sm rounded-lg p-3 dark:border-[#27272a] dark:bg-darkGrayBackground z-50"
        sideOffset={5}
      >
        <div className="text-gray-700 dark:text-white border-b border-b-gray-200 dark:border-b-[#27272a] px-4 pb-2  ">Filters</div>
        <div className="mt-1.5">
          {filters.map((filter, index) => (
            <div key={index}>{renderFilterOptionsManual(filter)}</div>
          ))}
        </div>

        <div className="flex justify-end gap-2 mt-2 border-t dark:text-white border-gray-200 dark:border-[#27272a]">
          <button
            className="px-4 py-2 pb-1 text-sm font-medium underline"
            onClick={() => filters.forEach((filter) => filter.options.forEach((option) => option.reset()))}
          >
            Reset
          </button>
        </div>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};
