// React
import { useEffect, useState } from 'react';

// UI
import { useNotify, Api, useScreenSize, Drawer, <PERSON><PERSON>, ToggleFilter, useConfirmDialog, Icon, NoDataFound, NoDataMatches } from '/src';

// React icons
import { HiOutlineAdjustmentsHorizontal } from 'react-icons/hi2';

export const QuizSelectionAutomate = ({
  setAutomateSelection,
  form,
  setFieldValue,
  handleGetQuestionData,
  lookups,
  selectedQuestionIds,
  setSelectedQuestionIds,
  questionDatabaseOfMainTest,
}) => {
  // State
  const [loading, setLoading] = useState(false);
  const [generatedQuestionsIds, setGeneratedQuestionsIds] = useState([]);
  const [showFilter, setShowFilter] = useState(false);
  const [numberOfTotalQuestionsCanAdd, setNumberOfTotalQuestionsCanAdd] = useState();
  // Search
  const [search, setSearch] = useState(null);
  // Question List
  const [questionDatabase, setQuestionDatabase] = useState([]);
  const [questionsList, setQuestionsList] = useState([]);
  // Edit mode map
  const [isAnyQuestionHasEditMode, setAnyQuestionHasEditMode] = useState({});

  // separate loading states for different actions
  const [questionGenerateLoading, setQuestionGenerateLoading] = useState(false);

  // Calculate the current questions to display based on pagination
  const [currentPage, setCurrentPage] = useState({ page: 1 }); // Page
  const questionsPerPage = 10; // Size
  const indexOfLastQuestion = currentPage?.page * questionsPerPage;
  const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;
  const currentQuestions = questionsList.slice(indexOfFirstQuestion, indexOfLastQuestion);
  const totalPages = Math.max(Math.ceil(questionsList.length / questionsPerPage), 1);

  // Showing Text in Pagination as table
  const showingText = `${questionsList.length ? currentPage?.page * questionsPerPage - questionsPerPage + 1 : questionsList.length} - ${
    currentPage?.page * questionsPerPage > questionsList.length ? questionsList.length : currentPage?.page * questionsPerPage
  }`;

  // Hooks
  const { notify } = useNotify();
  const screen = useScreenSize();

  const onClose = () => {
    setFieldValue('questionsDifficulty')([]);
    setFieldValue('subCategoryFiltration')([]);
    setFieldValue('numOfQuestions')(null);
    setAutomateSelection(false);
    setSelectedQuestionIds([]);
  };

  // Methods
  const handleGetQuestionGenerateData = async (id) => {
    try {
      setQuestionGenerateLoading(true);
      const response = await Api.get(`questions/single/${id}`);
      setQuestionDatabase((prev) => [...prev, response.data]);
      setSelectedQuestionIds((prev) => [...prev, response.data._id]);
    } catch (error) {
      notify.error(error.response.data.message);
    } finally {
      setQuestionGenerateLoading(false);
    }
  };

  const onSubmit = async () => {
    try {
      setQuestionGenerateLoading(true);

      if (selectedQuestionIds.length) {
        await Promise.all(selectedQuestionIds.map((question) => handleGetQuestionData(question)));
      }
      setFieldValue('questionIds')([...form.questionIds, ...selectedQuestionIds]);
      setSelectedQuestionIds([]);
      setQuestionGenerateLoading(false);
      onClose();
    } catch (error) {
      throw error;
    }
  };

  const noDataFound = {
    customIcon: 'questions',
    messageHeader: 'No questions have been added yet',
  };

  const handleGenerateAutomateSubmit = async () => {
    if (!form.questionsDifficulty) {
      return notify.error('Please select a questions difficulty.');
    }
    try {
      setQuestionGenerateLoading(true);
      const response = await Api.post('questions/generate', {
        numOfQuestions: form.numOfQuestions,
        category: form.category,
        subCategory: form.subCategoryFiltration.length > 0 ? form.subCategoryFiltration : form.subCategory,
        questionsDifficulty: form.questionsDifficulty,
        exclude: [...questionDatabase.map((question) => question._id), ...form.questionIds],
      });
      response.data.map((question) => handleGetQuestionGenerateData(question._id));
      setGeneratedQuestionsIds((prev) => [...prev, ...response.data.map((question) => question._id)]);
      const result = response.data.map((item) => item._id);
      if (result.length > 0) {
        notify('Questions added successfully!');
      }
      if (result.length === 0 && form.questionIds.length > 0) {
        notify.error('There is no more questions for this category');
      } else if (result.length === 0) {
        notify.error('There is no question for this category');
      }
      setFieldValue('questionsDifficulty')([]);
      setFieldValue('subCategoryFiltration')([]);
      setFieldValue('numOfQuestions')(0);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    } finally {
      setQuestionGenerateLoading(false);
    }
  };

  const drawerFilter = (
    <div className="py-3 border-t mx-3">
      <div className="pt-4">
        <Drawer.FilterSection
          label="Subcategory"
          optional
          form={form}
          showSingleClear={true}
          handleSingleClear={() => setFieldValue('subCategoryFiltration')([])}
          lookups={lookups}
          setFieldValue={setFieldValue}
          handleGetQuestionGenerateData={handleGetQuestionGenerateData}
          setGeneratedQuestionsIds={setGeneratedQuestionsIds}
          propertyKeyObject="subCategoryFiltration"
        />

        <hr className="my-4 border-gray-900 border-opacity-5 dark:border-gray-700" />

        <Drawer.FilterSection
          label="Difficulty"
          form={form}
          lookups={lookups}
          setFieldValue={setFieldValue}
          showSingleClear={true}
          handleSingleClear={() => setFieldValue('questionsDifficulty')([])}
          handleGetQuestionGenerateData={handleGetQuestionGenerateData}
          setGeneratedQuestionsIds={setGeneratedQuestionsIds}
          propertyKeyObject="questionsDifficulty"
        />
      </div>
    </div>
  );

  /* Get total question exists of selected subcategories */
  const handleTotalQuestions = async () => {
    try {
      const response = await Api.get('questions/total', {
        subCategoryIds: form?.subCategoryFiltration.length > 0 ? form?.subCategoryFiltration : form?.subCategory,
      });
      setNumberOfTotalQuestionsCanAdd(response?.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    } finally {
    }
  };

  const handleAvailableQuestions = () => {
    if (form.subCategoryFiltration.length > 0) {
      return (
        numberOfTotalQuestionsCanAdd -
        questionDatabaseOfMainTest?.filter((question) => form.subCategoryFiltration.includes(question.subCategory)).length -
        questionDatabase?.filter((question) => form.subCategoryFiltration.includes(question.subCategory)).length
      );
    } else {
      return (
        numberOfTotalQuestionsCanAdd -
        questionDatabaseOfMainTest?.filter((question) => form.subCategory.includes(question.subCategory)).length -
        questionDatabase?.filter((question) => form.subCategory.includes(question.subCategory)).length
      );
    }
  };

  const renderMessage = () => {
    const availableQuestions = handleAvailableQuestions();
    if (availableQuestions < 100) {
      if (!form.numOfQuestions) {
        return {};
      } else if (form.numOfQuestions <= availableQuestions) {
        return {};
      } else if (form.numOfQuestions > availableQuestions) {
        return {
          text:
            availableQuestions === 0
              ? 'No more available questions'
              : `Only ${availableQuestions} questions are avaliable in this category, please adjust your number.`,
          isButtonDisabled: true,
          color: 'text-[#C72716]',
          icon: (
            <svg width="12" height="11" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M6 8.27222V8.27778M6 2.72222V6.61111M11 5.5C11 8.26144 8.76144 10.5 6 10.5C3.23858 10.5 1 8.26144 1 5.5C1 2.73858 3.23858 0.5 6 0.5C8.76144 0.5 11 2.73858 11 5.5Z"
                stroke="#C72716"
                strokeWidth="0.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
        };
      }
    } else {
      if (!form.numOfQuestions) {
        return {
          text: 'You can add up to 100 questions at a time.',
          isButtonDisabled: true,
          color: 'text-[#667085]',
          icon: null,
        };
      } else if (form.numOfQuestions <= 100) {
        return {
          text: 'You can add up to 100 questions at a time.',
          isButtonDisabled: false,
          color: 'text-[#479E64]',
          icon: (
            <svg width="10" height="8" viewBox="0 0 10 8" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 4.30555L3.46154 6.75L9 1.25" stroke="#479E64" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          ),
        };
      } else if (form.numOfQuestions > 100) {
        return {
          text: 'You can add up to 100 questions at a time.',
          isButtonDisabled: true,
          color: 'text-[#C72716]',
          icon: (
            <svg width="12" height="11" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M6 8.27222V8.27778M6 2.72222V6.61111M11 5.5C11 8.26144 8.76144 10.5 6 10.5C3.23858 10.5 1 8.26144 1 5.5C1 2.73858 3.23858 0.5 6 0.5C8.76144 0.5 11 2.73858 11 5.5Z"
                stroke="#C72716"
                strokeWidth="0.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
        };
      } else if (form.numOfQuestions > questionDatabaseOfMainTest?.length) {
        return {
          text: `You’ve selected ${questionDatabase.length} questions. You can add up to ${availableQuestions} more..`,
          isButtonDisabled: true,
          color: 'text-[#C72716]',
          icon: (
            <svg width="12" height="11" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M6 8.27222V8.27778M6 2.72222V6.61111M11 5.5C11 8.26144 8.76144 10.5 6 10.5C3.23858 10.5 1 8.26144 1 5.5C1 2.73858 3.23858 0.5 6 0.5C8.76144 0.5 11 2.73858 11 5.5Z"
                stroke="#C72716"
                strokeWidth="0.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
        };
      }
    }

    return {
      text: '',
      isButtonDisabled: false,
      color: '',
      icon: null,
    };
  };

  useEffect(() => {
    handleTotalQuestions();
  }, [form?.subCategoryFiltration]);

  useEffect(() => {
    setQuestionsList([...questionDatabase]);
    setSearch('');
  }, [questionDatabase]);

  useEffect(() => {
    setQuestionsList(questionDatabase.filter((question) => question.title.toLowerCase().includes(search.toLowerCase())));
    setCurrentPage((prev) => ({ ...prev, page: 1 }));
  }, [search]);

  return (
    <Drawer split onClose={onClose}>
      <Drawer.SplitView>
        {(showFilter || screen.gt.sm()) && (
          <Drawer.SplitView.SplitLeftSide>
            <Drawer.HeaderSection
              headerLabel="Adjust Questions"
              icon={<HiOutlineAdjustmentsHorizontal className="text-xl" />}
              onReset={() => {
                setFieldValue('questionsDifficulty')([]);
                setFieldValue('subCategoryFiltration')([]);
              }}
              onClose={screen.lt.md() ? () => setShowFilter(false) : null}
            />

            <div className="pt-4">
              <Drawer.FilterSection
                label="Filter By Subcategory"
                optional
                form={form}
                lookups={lookups}
                setFieldValue={setFieldValue}
                handleGetQuestionGenerateData={handleGetQuestionGenerateData}
                setGeneratedQuestionsIds={setGeneratedQuestionsIds}
                propertyKeyObject="subCategoryFiltration"
              />

              <hr className="my-4 border-gray-900 border-opacity-5 dark:border-gray-700" />

              <Drawer.FilterSection
                label="Filter By Difficulty Level"
                form={form}
                lookups={lookups}
                setFieldValue={setFieldValue}
                handleGetQuestionGenerateData={handleGetQuestionGenerateData}
                setGeneratedQuestionsIds={setGeneratedQuestionsIds}
                propertyKeyObject="questionsDifficulty"
              />
            </div>
          </Drawer.SplitView.SplitLeftSide>
        )}

        <Drawer.SplitView.SplitRightSide>
          <Drawer.HeaderSection headerLabel="Auto Question Selection" selectedQuestionsCount={selectedQuestionIds.length} onClose={onClose} />

          <div className="mt-2 text-[#808080] text-[13px] font-normal">
            <span className="font-medium">{handleAvailableQuestions()}</span> Available Questions based on your filter
          </div>

          <Drawer.GenerateQuestions
            form={form}
            setFieldValue={setFieldValue}
            setGeneratedQuestionsIds={setGeneratedQuestionsIds}
            onSubmit={handleGenerateAutomateSubmit}
            numberOfTotalQuestionsCanAdd={numberOfTotalQuestionsCanAdd}
            isButtonDisabled={renderMessage().isButtonDisabled}
          />

          <div className={`${renderMessage().color} text-[13px] font-normal flex align-middle items-center gap-2 `}>
            {renderMessage().icon}
            {renderMessage().text}
          </div>

          <div className="flex items-center gap-3">
            <Drawer.Search value={search} onInput={(e) => setSearch(e.target.value)} className="flex w-full" />

            {!screen.gt.sm() && (
              <ToggleFilter
                drawerFilter={{ element: drawerFilter, filterCountNumber: 0 }}
                drawerClearAll={() => {
                  setFieldValue('questionsDifficulty')([]);
                  setFieldValue('subCategoryFiltration')([]);
                }}
                resultsFound={handleAvailableQuestions()}
                drawerInsideDrawer
              />
            )}
          </div>

          <div className="overflow-y-auto h-full">
            {currentQuestions.length ? (
              <Drawer.Body>
                <div className="space-y-1">
                  {currentQuestions.map((row, index) => (
                    <Drawer.QuestionOfTest
                      key={row?._id}
                      index={index}
                      row={row}
                      mainQuestionsListForm={form}
                      currentPage={currentPage?.page}
                      questionsPerPage={questionsPerPage}
                      selectedQuestionIds={selectedQuestionIds}
                      setSelectedQuestionIds={setSelectedQuestionIds}
                      handleGetQuestionGenerateData={handleGetQuestionGenerateData}
                      setQuestionDatabase={setQuestionDatabase}
                      generatedQuestionsIds={generatedQuestionsIds}
                      setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
                    />
                  ))}
                </div>
              </Drawer.Body>
            ) : (
              <div className="flex justify-center items-center h-full">
                <div className=" w-2/4 space-y-2">
                  {/* No data created || No results found */}
                  {questionDatabase.length > 0 ? (
                    <NoDataMatches message="No results found." />
                  ) : (
                    <NoDataFound noDataFound={noDataFound} width="70" height="70" />
                  )}
                </div>
              </div>
            )}

            {currentQuestions.length > 0 && (
              <Drawer.Footer
                isPaginationActive={questionsList.length > 0}
                paginationData={{
                  showingText: showingText,
                  count: questionsList.length,
                  size: questionsPerPage,
                  onPageChange: setCurrentPage,
                  currentPage: currentPage?.page,
                  pagesCount: totalPages,
                }}
              />
            )}
          </div>

          {currentQuestions.length > 0 && (
            <Drawer.Footer>
              <Drawer.Footer.Button label="Cancel" disabled={questionGenerateLoading} tertiary onClick={onClose} />
              <Drawer.Footer.Button
                loading={questionGenerateLoading}
                label="Done"
                disabled={
                  questionGenerateLoading ||
                  !selectedQuestionIds.length ||
                  Object.keys(isAnyQuestionHasEditMode)?.find((question) => isAnyQuestionHasEditMode[question])
                }
                onClick={onSubmit}
                mainButton
              />
            </Drawer.Footer>
          )}
        </Drawer.SplitView.SplitRightSide>
      </Drawer.SplitView>
    </Drawer>
  );
};
