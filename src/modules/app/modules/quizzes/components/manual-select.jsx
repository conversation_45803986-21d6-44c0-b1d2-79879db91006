import { SelectQuestions } from '../components/select-quizzes';
import { Dialog, Form, Button, useNotify, Api } from '/src';
import { QuestionsListItem } from '../components/questions-list-item';
import { useState } from 'react';

export const ManualSelect = ({ onClose, questions, handleSearch, form, setFormValue, handleGetQuestionData }) => {
  const { notify } = useNotify();
  const [questionManualSelectDatabase, setQuestionManualSelectDatabase] = useState([]);
  const [questionManualSelectIds, setQuestionManualSelectIds] = useState([]);

  const handleGetQuestionDataManualSelect = async (id) => {
    try {
      const response = await Api.get(`questions/single/${id}`);
      setQuestionManualSelectDatabase((prev) => [...prev, response.data]);
      setQuestionManualSelectIds((prev) => [...prev, id]);
    } catch (error) {
      notify.error(error);
    }
  };

  const onRemove = (id) => {
    setQuestionManualSelectDatabase((prev) => prev.filter((target) => id !== target._id));
    setQuestionManualSelectIds((prev) => prev.filter((target) => id !== target));
  };

  const onSubmit = () => {
    setFormValue({
      ...form,
      questionIds: questionManualSelectIds,
    });
    handleSelectingQuestion(questionManualSelectIds);
    onClose();
  };

  const handleSelectingQuestion = (questionIds) => {
    setFormValue({
      ...form,
      questionIds: [...form.questionIds, ...questionIds],
    });
    questionIds.map((question) => handleGetQuestionData(question));
  };

  return (
    <Dialog show popup size="3xl" modalHeader="Manual Select Questions" onClose={onClose} overflowVisible={true}>
      <div className="min-h-64">
        <Form className="h-full" onSubmit={onSubmit}>
          <div>
            <p className="dark:text-white mt-1 mb-3">Select Question</p>
            <SelectQuestions
              filterOnly
              name="questionIds"
              lookup={questions}
              optionLabelKey={'title'}
              optionValueKey="_id"
              onSearch={(value) => handleSearch(value, questionManualSelectIds)}
              multiSelect={true}
              value={form.category === 0 ? null : form.questionIds}
              onChange={(questionId) => handleGetQuestionDataManualSelect(questionId)}
              disabled={form.category === 0}
            />

            <div className="mt-5 lg:border lg:border-gray-200 lg:dark:border-gray-600 rounded-lg text-sm font-medium text-gray-900 dark:text-white">
              <div className="hidden lg:grid grid-cols-12 p-4 bg-white border border-gray-200 rounded-t-lg dark:bg-gray-700 dark:border-gray-600">
                <p className="col-span-4">Questions Name</p>
                <p className="col-span-2">Subcategory</p>
                <p className="col-span-3">Topic</p>
                <p className="col-span-2">Difficulty</p>
                <p className="col-span-1">Actions</p>
              </div>
              <div className="max-h-[30vh] overflow-y-auto">
                {questionManualSelectDatabase.length ? (
                  questionManualSelectDatabase.map((data, index) => (
                    <QuestionsListItem key={index} data={data} showEditIcon={false} onRemove={onRemove} />
                  ))
                ) : (
                  <div className="text-gray-500 dark:text-gray-400 p-4 hover:dark:bg-gray-700 my-4">
                    <p className="flex justify-center items-center dark:text-white">No selected questions yet</p>
                  </div>
                )}
              </div>
            </div>
          </div>
          <Button className="flex w-full items-end mt-14" type="submit" label={questionManualSelectIds.length ? 'Select' : 'Cancel'} />
        </Form>
      </div>
    </Dialog>
  );
};
