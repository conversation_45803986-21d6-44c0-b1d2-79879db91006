import React from 'react';
import { useScreenSize } from '/src';

export const ManualSelectPlaceholder = () => {
  const screen = useScreenSize();

  return (
    <div>
      <div className="flex flex-col sm:flex-row justify-between">
        <div className={` max-w p-6 rounded-lg shadow-sm mb-1 mt-2 h-10 animate-pulse flex justify-between items-center`}>
          <div className="h-5 bg-gray-300 rounded-full flex items-center justify-center dark:bg-gray-600 w-40"></div>
        </div>
      </div>
      <div className="relative p-4 space-y-4 border border-gray-200 divide-y divide-gray-200 rounded-xl shadow animate-pulse dark:divide-gray-700 md:p-6 dark:border-gray-700">
        <div className="flex items-center justify-between pt-1">
          <div className='w-[100%]'>
            <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-[80%] mb-2.5"></div>
            <div className="w-[90%] h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            <div className="flex items-center justify-between pt-4">
              <div className="flex flex-wrap justify-between items-center align-middle gap-4">
                <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-32 mb-2.5"></div>
                <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-32 mb-2.5"></div>
                <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-20 mb-2.5"></div>
                <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-32 mb-2.5"></div>
              </div>

              {/* <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div> */}
            </div>

            <div className="flex items-center justify-between pt-3">
              <div className="flex flex-wrap justify-between items-center align-middle gap-4">
                <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-20 mb-2.5"></div>
                <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-32 mb-2.5"></div>
                <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-32 mb-2.5"></div>
                <div className="h-6  bg-gray-300 rounded-full dark:bg-gray-600 w-32 mb-2.5"></div>
              </div>

              {/* <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div> */}
            </div>
            <div className="h-9 bg-gray-300 rounded-full mt-3 dark:bg-gray-600 w-11/12 mb-2.5"></div>
          </div>
        </div>
        <div className="flex items-center justify-between pt-2">
          <div>
            <div className="w-40 h-5 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          {/* <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div> */}
        </div>
        <div className="flex items-center  justify-between pt-4">
          <div className="">
            <div className="flex gap-5 items-center align-middle">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-8 mb-2.5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-[100%] mb-3"></div>
            </div>
            <div className="flex flex-wrap justify-between items-center text-center mx-6 gap-9">
              <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-40"></div>
              <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-40"></div>
              <div className="h-6 hidden sm:block bg-gray-300 rounded-full dark:bg-gray-600 w-40"></div>
            </div>
          </div>
          {/* <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div> */}
        </div>

        <div className="flex items-center  justify-between pt-4">
          <div className="">
            <div className="flex gap-5 items-center align-middle">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-8 mb-2.5"></div>
              <div className="h-3  bg-gray-300 rounded-full dark:bg-gray-600 w-[100%] mb-3"></div>
            </div>
            <div className="flex flex-wrap justify-between items-center text-center mx-6 gap-9">
              <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-40"></div>
              <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-40"></div>
              <div className="h-6 hidden sm:block bg-gray-300 rounded-full dark:bg-gray-600 w-40"></div>
            </div>
          </div>
          {/* <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div> */}
        </div>

        <div className="flex items-center  justify-between pt-4">
          <div className="">
            <div className="flex gap-5 items-center align-middle">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-8 mb-2.5"></div>
              <div className="h-3  bg-gray-300 rounded-full dark:bg-gray-600 w-[100%] mb-3"></div>
            </div>
            <div className="flex flex-wrap justify-between items-center text-center mx-6 gap-9">
              <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-40"></div>
              <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-40"></div>
              <div className="h-6 hidden sm:block bg-gray-300 rounded-full dark:bg-gray-600 w-40"></div>
            </div>
          </div>
          {/* <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div> */}
        </div>

        <div className="flex items-center  justify-between pt-4">
          <div className="">
            <div className="flex gap-5 items-center align-middle">
              <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-8 mb-2.5"></div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-[100%] mb-3"></div>
            </div>
            <div className="flex flex-wrap justify-between items-center text-center mx-6 gap-9">
              <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-40"></div>
              <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-40"></div>
              <div className="h-6 hidden sm:block bg-gray-300 rounded-full dark:bg-gray-600 w-40"></div>
            </div>
          </div>
          {/* <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div> */}
        </div>

        {/* <div className="flex gap-4 p-4">
      <Button label="Cancel" className="w-1/3" outline onClick={onClose} />
      <Button label="Done" className="w-2/3" onClick={onSubmitActions} />
    </div> */}

        <span className="sr-only">Loading...</span>
      </div>
      <div className="flex justify-between mt-2 gap-4 p-2">
        <div className="h-11 bg-gray-300 rounded-full dark:bg-gray-600 w-1/3"></div>
        <div className="h-11 bg-gray-300 rounded-full dark:bg-gray-600 w-2/3"></div>
      </div>
    </div>
  );
};
