// React
import { useContext, useState, useEffect } from 'react';

// UI
import { useF<PERSON><PERSON><PERSON><PERSON>, Drawer, useScreenSize, Button, ToggleFilter, NoDataMatches, Form, MultiSelect, useForm } from '/src';

// Components
import { NoDataFound } from '../../../../../../components/no-data-found-placeholder';

// React icons
import { HiOutlineAdjustmentsHorizontal } from 'react-icons/hi2';

// Context
import { AppContext } from '/src/components/provider';
import { ManualSelectPlaceholder } from './placeholder';

// Flowbite
import { Spinner } from 'flowbite-react';

export const QuizSelectionManual = ({
  setManualSelection,
  selectedQuestionIds,
  setSelectedQuestionIds,
  form,
  setFormValue,
  setFieldValue,
  handleGetQuestionData,
  questionDatabaseOfMainTest,
}) => {
  // States
  const [showFilter, setShowFilter] = useState(false);
  // Edit mode map
  const [isAnyQuestionHasEditMode, setAnyQuestionHasEditMode] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [submitLoading, setSubmitloading] = useState(false);

  // Static data
  const filterLabelTitle = {
    subcategory: { label: 'Subcategory', optional: true, handleSingleClear: setFieldValue('subCategoryFiltration', []) },
    QuestionDifficulty: { label: 'Difficulty ', optional: false, handleSingleClear: setFieldValue('questionsDifficulty', []) },
  };

  const noDataFound = {
    customIcon: 'questions',
    messageHeader: ' No questions have been added yet',
  };

  // User data
  const { userData } = useContext(AppContext);

  // Hooks
  const screen = useScreenSize();
  const { ready, loading, setLoading, list, count, search, pagination, filters, refresh, exclude } = useFetchList('questions/list', {
    search: '',
    pagination: {
      page: 1,
      size: 10,
    },
    filters: {
      ...(userData.trackId
        ? {}
        : {
            category: {
              label: 'Category',
              lookup: 'category',
            },
          }),
      subCategory: {
        label: 'Sub Category',
        lookup: 'subcategory',
        parentLookup: { key: 'category', fieldName: 'categoryId', fieldValue: userData.trackId ? userData.trackId : form?.category },
      },
      difficulty: {
        label: 'Difficulty',
        enum: 'QuestionDifficulty',
      },
    },
    exclude: [...form.questionIds, ...questionDatabaseOfMainTest.map((question) => question._id)],
  });

  // Pagination
  const { page, size } = pagination;
  const pagesCount = Math.max(Math.ceil(count / size), 1);
  const showingText = `${count ? page * size - size + 1 : count} - ${page * size > count ? count : page * size}`;
  const isPaginationActive = !!pagination.update;

  const onClose = () => {
    setManualSelection(false);
    setSelectedQuestionIds([]);
  };

  const onSubmit = async () => {
    try {
      setSubmitloading(true);
      await Promise.all(selectedQuestionIds.map((question) => handleGetQuestionData(question)));
      setFormValue({
        ...form,
        questionIds: [...form.questionIds, ...selectedQuestionIds],
      });
      setSelectedQuestionIds([]);
      setSubmitloading(false);
      onClose();
    } catch (error) {
      throw error;
    }
  };

  const drawerFilter = (
    <div className="py-3 border-t mx-3">
      {filters.map((filter, index) => {
        return (
          filter.key !== 'category' &&
          filter.options.length > 0 && (
            <div key={filter.label}>
              <Drawer.FilterSection
                label={filterLabelTitle?.[filter.key]?.label}
                optional={filterLabelTitle?.[filter.key]?.optional}
                form={form}
                showSingleClear={true}
                handleSingleClear={() => filter.options.forEach((option) => option.resetSingle(filter?.originalKey))}
                filter={filter}
                propertyKeyObject={filter.key}
              />
              {index < filters.length - 1 && <hr className="my-4 border-gray-900 border-opacity-5 dark:border-gray-700" />}
            </div>
          )
        );
      })}
    </div>
  );
  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  /* FIXME: This is the worest case to pre select the choosen filters using the fetchList */
  /* TODO: pre defined the choosen subcategories to be selected without purple color */
  useEffect(() => {
    /* To calculate the number of pre defined subcategories */
    // let counter = 0;
    filters.map((filter) => {
      /* Select category with the selected track or category */
      filter.key === 'category' &&
        filter.options.map((option) => form.category.includes(option.name.split('-')[1]) && !option.value && option.onChange(true));
      /* Select subcategories with the pre defined subcategories */
      // filter.key === 'subcategory' && filter.options.map((option) => option.value && ++counter);
    });
    // if (counter === 0) {
    //   filters.map(
    //     (filter) =>
    //       filter.key === 'subcategory' &&
    //       filter.options.filter((option) => form.subCategory.includes(option.name.split('-')[1]) && !option.value && option.onChange(true))
    //   );
    // }
  }, [filters]);

  return (
    <Drawer split onClose={onClose}>
      {ready ? (
        <Drawer.SplitView>
          {screen.gt.sm() && (
            <Drawer.SplitView.SplitLeftSide>
              <Drawer.HeaderSection
                headerLabel="Filters"
                icon={<HiOutlineAdjustmentsHorizontal className="text-xl" />}
                onReset={() => filters.forEach((filter) => filter.options.forEach((option) => option.reset()))}
                onClose={screen.lt.md() ? () => setShowFilter(false) : null}
                resultsFound={count}
              />

              <div className="pt-4">
                {filters.map((filter, index) => {
                  return (
                    filter.key !== 'category' &&
                    filter.options.length > 0 && (
                      <div key={filter.label}>
                        <Drawer.FilterSection
                          label={filterLabelTitle?.[filter.key]?.label}
                          optional={filterLabelTitle?.[filter.key]?.optional}
                          form={form}
                          filter={filter}
                          showSingleClear={true}
                          handleSingleClear={() => filter.options.forEach((option) => option.resetSingle(filter?.originalKey))}
                          propertyKeyObject={filter.key}
                        />
                        {index < filters.length - 1 && <hr className="my-4 border-gray-900 border-opacity-5 dark:border-gray-700" />}
                      </div>
                    )
                  );
                })}
              </div>

              {/* Loading Indicator */}
              {submitLoading && (
                <div className="absolute left-0 right-0 bottom-0 top-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80 z-50">
                  <Spinner size="lg" color="purple" />
                </div>
              )}
            </Drawer.SplitView.SplitLeftSide>
          )}

          <Drawer.SplitView.SplitRightSide>
            <Drawer.HeaderSection headerLabel="Manual Selection Questions" selectedQuestionsCount={selectedQuestionIds.length} onClose={onClose} />

            <div className="flex items-center gap-3">
              <Drawer.Search onInput={(e) => search.update(e.target.value)} className="flex w-full" />

              {!screen.gt.sm() && (
                <ToggleFilter
                  filters={filters}
                  drawerFilter={{ element: drawerFilter, filterCountNumber: 0 }}
                  drawerClearAll={() => filters.forEach((filter) => filter.options.forEach((option) => option.reset()))}
                  resultsFound={count}
                  drawerInsideDrawer
                  /* 
                    FIXME: WARNING! This property is considered as an Exceptional Case (Only in  Manual Selection & Drawer Filter).
                    This prop should not be used in ToggleFilter anywhere else. 
                  */
                  tempException={true}
                  /* END WARNING */
                />
              )}
            </div>
            <div className="overflow-y-auto h-full">
              {list.length ? (
                <Drawer.Body>
                  <div className="space-y-1">
                    {list.map((row, index) => (
                      <Drawer.QuestionOfTest
                        key={row?._id}
                        index={index}
                        row={row}
                        mainQuestionsListForm={form}
                        currentPage={pagination.page}
                        questionsPerPage={pagination.size}
                        selectedQuestionIds={selectedQuestionIds}
                        setSelectedQuestionIds={setSelectedQuestionIds}
                        refresh={refresh}
                        setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
                      />
                    ))}
                  </div>
                </Drawer.Body>
              ) : (
                <div className="flex justify-center items-center h-full">
                  <div className=" w-2/4 space-y-2">
                    {/* No data created || No results found */}
                    {backupList.length > 0 ? (
                      <NoDataMatches message="No results found." />
                    ) : (
                      <NoDataFound noDataFound={noDataFound} width="70" height="70" />
                    )}
                  </div>
                </div>
              )}

              {list.length > 0 && (
                <Drawer.Footer
                  isPaginationActive={isPaginationActive}
                  paginationData={{
                    showingText: showingText,
                    count: count,
                    size: size,
                    onPageChange: pagination.update,
                    currentPage: page,
                    pagesCount: pagesCount,
                  }}
                />
              )}
            </div>

            {list.length > 0 && (
              <Drawer.Footer>
                <Drawer.Footer.Button label="Cancel" tertiary onClick={onClose} />
                <Drawer.Footer.Button
                  label="Done"
                  loading={submitLoading}
                  disabled={
                    !selectedQuestionIds.length || Object.keys(isAnyQuestionHasEditMode)?.find((question) => isAnyQuestionHasEditMode[question])
                  }
                  onClick={onSubmit}
                  mainButton
                />
              </Drawer.Footer>
            )}
          </Drawer.SplitView.SplitRightSide>
        </Drawer.SplitView>
      ) : (
        <ManualSelectPlaceholder />
      )}
    </Drawer>
  );
};
