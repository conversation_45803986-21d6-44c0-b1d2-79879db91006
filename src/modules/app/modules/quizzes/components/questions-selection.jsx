// UI
import { Drawer } from '/src';

// Components
import { NoDataFound } from '../../../../../components/no-data-found-placeholder';

export const QuestionsSelection = ({
  currentQuestions,
  form,
  currentPage,
  questionsPerPage,
  setFieldValue,
  questionDatabase,
  setQuestionDatabase,
  setAnyQuestionHasEditMode,
}) => {
  const noDataFound = {
    imageSrc: '/images/NotFoundData.png',
    messageHeader: 'No questions have been added yet.',
  };

  return (
    <div className="my-2 overflow-y-auto">
      {currentQuestions.length ? (
        currentQuestions.map((row, index) => (
          <div className="px-2 py-1">
            <Drawer.QuestionOfTest
              key={row?._id}
              row={row}
              index={index}
              mainQuestionsListForm={form}
              mainSetFieldValueForm={setFieldValue}
              currentPage={currentPage}
              questionsPerPage={questionsPerPage}
              questionDatabase={questionDatabase}
              setQuestionDatabase={setQuestionDatabase}
              canRemoveQuestion
              setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
            />
          </div>
        ))
      ) : (
        <div className="ml-auto mr-auto w-2/4 space-y-2">
          <NoDataFound noDataFound={noDataFound} />
        </div>
      )}
    </div>
  );
};
