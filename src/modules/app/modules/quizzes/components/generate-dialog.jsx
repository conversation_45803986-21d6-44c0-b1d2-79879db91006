// React
import React, { useEffect, useRef, useState } from 'react';

// Components
import { Dialog, Form, MultiSelect, TextInput, Select, Button, useForm, useNotify, Api, useValidate } from '/src';

export const SubmissionsGenerateDialog = ({ onClose, category, questionIds, setQuestions, setUpdateVal, handleGetQuestionData }) => {
  //State
  const subCategoryRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const { notify } = useNotify();
  const { isRequired, isSelected } = useValidate();

  // Form
  const { form, setFieldValue } = useForm({
    numOfQuestions: 10,
    subCategory: [],
    questionsDifficulty: 0,
    // topic: null,
  });

  // Methods
  const handleSubmit = async () => {
    try {
      if (category === 0) {
        notify.error('Please select a category.');
      } else if (form.subCategory < 1) {
        notify.error('There is no question subcategory chosen');
      } else if (form.questionsDifficulty < 1) {
        notify.error('There is no question difficulty chosen');
      } else {
        setLoading(true);
        const response = await Api.post('questions/generate', {
          numOfQuestions: form.numOfQuestions,
          category: category,
          subCategory: form.subCategory,
          questionsDifficulty: form.questionsDifficulty,
          exclude: questionIds,
        });
        response.data.map((question) => handleGetQuestionData(question._id));
        const result = response.data.map((item) => item._id);
        setQuestions(result);
        if (result.length === 0 && questionIds.length > 0) {
          notify.error('There is no more questions for this category');
        } else if (result.length === 0) {
          notify.error('There is no question for this category');
        } else {
          setUpdateVal(true);
          onClose();
        }
      }
    } catch (error) {
      notify.error(error.response);
    } finally {
      setLoading(false);
    }
  };

  // On Mount
  useEffect(() => {
    setUpdateVal(false);
  }, []);

  return (
    <Dialog show popup size="lg" modalHeader="Generate Questions " onClose={onClose} overflowVisible={true}>
      <Form onSubmit={handleSubmit}>
        {/* Generate Random Questions */}
        <MultiSelect
          key={category}
          ref={subCategoryRef}
          label="Question Subcategory"
          name="SubCategory"
          value={form.subCategory}
          onChange={(newSubCategory) => {
            setFieldValue('subCategory')(newSubCategory);
          }}
          disabled={!category}
          lookup="subcategory"
          params={{ categoryId: category }}
          optionValueKey="_id"
          optionLabelKey="name"
          validators={[isSelected()]}
        />
        <div className="grid grid-cols-2 gap-2 space-y-2 mb-6">
          {/* <div className="col-span-2">
            <Select
              name="topic"
              label="Topic"
              lookup=""
              value={!form.subCategory}
              onChange={setFieldValue('topic')}
              dropIcon={true}
              validators={[isRequired()]}
              disabled={!form.subCategory.length}
            />
          </div> */}
          <Select
            name="questionsDifficulty"
            label="Difficulty"
            lookup="$QuestionDifficulty"
            value={form.questionsDifficulty}
            onChange={setFieldValue('questionsDifficulty', Number)}
            dropIcon={true}
            validators={[isRequired()]}
          />
          <TextInput
            name="numOfQuestions"
            label="Question Count"
            placeholder="Enter number"
            type="number"
            value={form.numOfQuestions}
            onChange={setFieldValue('numOfQuestions', Number)}
            min={1}
            max={100}
          />
        </div>
        {/* Actions */}
        <Button type="submit" label="Generate" className="w-full" loading={loading} disabled={loading} />
      </Form>
    </Dialog>
  );
};
