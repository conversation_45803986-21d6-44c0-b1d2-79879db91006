import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';

import { QuizzesMainLayout } from '../layouts/main';

import { QuizzesListPage } from '../pages/list';
import { QuizzesSinglePage } from '../pages/single';

export default [
  {
    path: 'tests',
    element: <QuizzesMainLayout />,
    // loader() {
    //   return {
    //     label: 'Tests',
    //   };
    // },
    children: [
      // Default
      {
        path: '',
        element: <Navigate to="/app/tests/list/setup" />,
      },

      // Routes
      {
        path: '',
        element: <Outlet />,
        children: [
          {
            path: 'list/setup',
            element: <QuizzesListPage />,
          },
          {
            path: 'create',
            element: <QuizzesSinglePage />,
            loader() {
              return {
                label: 'Create',
                title: 'Create Test',
                subtitle: 'Create tests using the form below.',
              };
            },
          },
          {
            path: 'edit/:id',
            element: <QuizzesSinglePage />,
            loader() {
              return {
                label: 'Edit',
                title: 'Edit Test',
                subtitle: "Modify the test as needed. Once satisfied with the changes, click 'Update' to save",
              };
            },
          },
          {
            path: 'view/:id',
            element: <QuizzesSinglePage />,
            loader() {
              return {
                label: 'View',
                title: 'View Test',
                subtitle: "Review the test details below. To make changes, click 'Edit Test' to proceed.",
              };
            },
          },
        ],
        loader() {
          return {
            label: 'Test Managament',
            icon: 'fluent:quiz-new-20-regular',
            title: 'Test Management',
            subtitle: 'View and manage all available tests.',
          };
        },
      },
    ],
  },
];
