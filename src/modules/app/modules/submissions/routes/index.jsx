import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { SubmissionsMainLayout } from '../layouts/main';
import { SubmissionsListPage } from '../pages/list';
import { SubmissionsSinglePage } from '../pages/single';
import { SubmissionsBankPage } from '../pages/bank';
import { PDFViewerComponent } from '../components/pdf-viewer';

export default [
  {
    path: 'tests',
    element: <SubmissionsMainLayout />,
    loader() {
      return {
        label: 'Tests',
      };
    },
    children: [
      // Routes
      {
        path: 'result',
        element: <Outlet />,
        loader() {
          return {
            label: 'Result',
            icon: 'ri:mail-send-line',
            title: 'Test Results',
            subtitle: 'Track assigned test status and review applicant scores',
            // infoIcon: 'solar:info-circle-outline',
            // infoText: "View and manage all assigned tests, see applicants' progress, and review their scores.",
          };
        },
        children: [
          {
            path: '',
            element: <SubmissionsListPage />,
          },
          {
            path: 'view/:id',
            element: <SubmissionsSinglePage />,
            loader() {
              return {
                label: 'Progress',
                title: 'Test Progress',
              };
            },
          },
        ],
      },

      {
        path: 'list/prepared',
        element: <SubmissionsBankPage />,
        loader() {
          return {
            label: 'List',
            icon: 'ri:mail-send-line',
            title: 'Test Library',
            subtitle: 'Explore and assign programming tests across various categories to assess applicant.',
            // infoIcon: 'solar:info-circle-outline',
            // infoText: 'Access a list of predefined templates that you can use directly and assign to applicants.',
          };
        },
      },
      {
        path: 'create',
        element: <SubmissionsSinglePage />,
        loader() {
          return {
            label: 'Create new',
            icon: 'ri:mail-send-line',
            title: 'Create Test',
            subtitle: 'Create test so you can assign him test',
          };
        },
      },
      {
        path: 'pdf/:id',
        element: <PDFViewerComponent />,
      },
    ],
  },
];
