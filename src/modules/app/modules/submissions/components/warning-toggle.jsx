import React, { useState } from 'react';
import { Icon } from '/src';
import { Tooltip } from 'flowbite-react';

const WarningComponent = ({ weirdBehaviorData }) => {
  const [isVisible, setIsVisible] = useState(false);

  const handleToggle = () => {
    setIsVisible(!isVisible);
  };

  // Check if there's any data to display
  const hasData = weirdBehaviorData?.ipChangeCount > 0 || weirdBehaviorData?.tabSwitchedCount > 0 || weirdBehaviorData?.openContextMenuCount > 0;

  return (
    <div className="w-full">
      {hasData && (
        <>
          {/* warning icon and clickable label */}
          <div className="flex items-center gap-4 p-3 cursor-pointer bg-[#fff5f5] dark:bg-[#1f2933] rounded-lg shadow-sm" onClick={handleToggle}>
            <Icon width="28" icon="ph:warning-duotone" className="text-[#e63946] dark:text-red-700" />
            <div className="flex-1">
              <p className="text-[15px] md:text-base font-semibold text-[#e63946] dark:text-red-700">
                Suspicious Behavior Detected. Click for Details.
              </p>
            </div>
            <Icon width="20" icon={isVisible ? 'carbon:chevron-up' : 'carbon:chevron-down'} className="text-[#e63946] dark:text-red-700" />
          </div>

          {/* Behavior Details */}
          {isVisible && (
            <div className=" text-[15px] md:text-base mt-3 text-gray-80 bg-[#fff5f5] dark:bg-[#1f2933] p-4 rounded-lg shadow-md border border-[#f5c6cb] dark:border-gray-600">
              <p className="font-medium text-[#333333]  dark:text-gray-400 mb-3">Observed Behaviors:</p>

              {/* Horizontal view for behaviors */}
              <div className="flex flex-wrap gap-2">
                {weirdBehaviorData?.ipChangeCount > 0 && (
                  <div className="flex items-center gap-2">
                    <Tooltip
                      className="z-[100] "
                      content="IP Address Change: The IP address changed during the test, possibly using external resources."
                    >
                      <Icon icon={'ion:information-circle-sharp'} className="text-purple-500 dark:text-purple-400" width={20} />
                    </Tooltip>
                    <span className="font-semibold text-gray-800 dark:text-gray-200">IP Changes:</span>
                    <span className="font-normal ml-1 text-gray-600 dark:text-gray-400">
                      {weirdBehaviorData.ipChangeCount} {weirdBehaviorData.ipChangeCount === 1 ? 'time' : 'times'}
                    </span>
                  </div>
                )}

                {weirdBehaviorData?.tabSwitchedCount > 0 && (
                  <div className="flex items-center gap-2">
                    <Tooltip className="z-[100]" content="Tab Switching: The user switched tabs during the test, possibly to look up answers.">
                      <Icon icon={'ion:information-circle-sharp'} className="text-purple-500 dark:text-purple-400" width={20} />
                    </Tooltip>
                    <span className="font-semibold text-gray-600 dark:text-gray-200">Tab Switches:</span>
                    <span className="font-normal ml-1 text-[#e63946] dark:text-red-700">
                      <span className="font-semibold"> {weirdBehaviorData.tabSwitchedCount}</span>{' '}
                      {weirdBehaviorData.tabSwitchedCount === 1 ? 'time' : 'times'}
                    </span>
                  </div>
                )}

                {weirdBehaviorData?.openContextMenuCount > 0 && (
                  <div className="flex items-center gap-2">
                    <Tooltip className="z-[100]" content="Context Menu: The context menu was opened, suggesting attempts to find answers.">
                      <Icon icon={'ion:information-circle-sharp'} className="text-purple-500 dark:text-purple-400" width={20} />
                    </Tooltip>
                    <span className="font-semibold text-gray-600 dark:text-gray-200">Context Menu:</span>
                    <span className="font-normal ml-1 text-[#e63946] dark:text-red-700">
                      <span className="font-semibold"> {weirdBehaviorData.openContextMenuCount} </span>{' '}
                      {weirdBehaviorData.openContextMenuCount === 1 ? 'time' : 'times'}
                    </span>
                  </div>
                )}
              </div>

              {/* Additional message */}
              <div className="mt-4 flex items-center gap-2">
                <p className="text-base text-[#6b6b6b]  dark:text-gray-400">
                  These behaviors suggest possible attempts to <span className="font-medium">cheat</span>, further investigation may be needed.
                </p>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default WarningComponent;
