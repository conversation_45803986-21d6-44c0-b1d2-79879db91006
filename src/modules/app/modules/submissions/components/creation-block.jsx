import React, { useContext, useEffect, useState } from 'react';
import { AppContext } from '/src/components/provider';
import { Dialog, TextInput, useForm, Form, useValidate, Button, useNotify, Api, useConfirmDialog, Icon } from '/src';

// Components
import { CategoryCardData } from './card/category-card-data';

export const SubmissionsCreationBlockDialog = ({
  refresh,
  blockDetails,
  setBlockDetails,
  loading,
  setCreateBlockVisibility,
  setCreateTestDialogVisibility,
  handleEditBlockTest,
}) => {
  // UI Hooks
  const { notify } = useNotify();
  const { isRequired, isNotSpaces } = useValidate();

  // Context
  const app = useContext(AppContext);

  // State
  const { showConfirm, hideConfirm } = useConfirmDialog();
  const [testDetails, setTestDetails] = useState();

  // Form
  const { form, setFieldValue } = useForm({
    title: '',
  });

  const handleGet = async () => {
    try {
      const response = await Api.get(`/templates/template/list/${blockDetails.blockIdDetails}`);
      setTestDetails(response.data);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  const handleInsert = async () => {
    try {
      await Api.post(`/templates/block`, {
        title: form.title,
      });
      refresh(true);
      setCreateBlockVisibility(false);
      notify('Category created successfully!');
    } catch (error) {
      notify.error(error.response.data.message);
    } finally {
      setBlockDetails(null);
    }
  };

  const handleUpdate = async () => {
    try {
      await Api.put(`/templates/block/${blockDetails.blockIdDetails}`, {
        title: form.title,
      });
      refresh(true);
      setCreateBlockVisibility(false);
      notify('Category updated successfully!');
    } catch (error) {
      notify.error(error.response.data.message);
    } finally {
      setBlockDetails(null);
    }
  };

  const ConfirmTextBlock = () => {
    return (
      <div>
        <div className="flex mx-auto p-4 mb-7 bg-[#ddd1f8] w-24 h-24 rounded-full">
          <div className="flex mx-auto mb-7 bg-[#cab6f5] w-16 h-16 justify-center rounded-full">
            <Icon icon="hugeicons:archive-02" className="text-red-600" width="40" />
          </div>
        </div>
        <p>Once confirmed, This Category will be archived permanently!</p>
      </div>
    );
  };
  const handleDeleteBlock = async () => {
    setCreateBlockVisibility(false);
    // Use onClose function in provider
    app.hideDialogConfirm({ onClose: () => setCreateBlockVisibility(true) });
    showConfirm(ConfirmTextBlock(), {
      async onConfirm() {
        try {
          await Api.delete(`/templates/block/${blockDetails.blockIdDetails}`);
        } catch (error) {
          notify.error(error.response.data.message);
        } finally {
          refresh(true);
          hideConfirm();
          setBlockDetails(null);
        }
      },
    });
  };

  const ConfirmTextBlockTest = () => {
    return (
      <div>
        <div className="flex mx-auto p-4 mb-7 bg-[#ddd1f8] w-24 h-24 rounded-full">
          <div className="flex mx-auto mb-7 bg-[#cab6f5] w-16 h-16 justify-center rounded-full">
            <Icon icon="hugeicons:archive-02" className="text-red-600" width="40" />
          </div>
        </div>
        <p>Once confirmed, This Test will be archived permanently!</p>
      </div>
    );
  };
  const handleDeleteBlockTest = async (blockId, testId) => {
    setCreateBlockVisibility(false);
    // Use onClose function in provider
    app.hideDialogConfirm({ onClose: () => setCreateBlockVisibility(true) });
    showConfirm(ConfirmTextBlockTest(), {
      async onConfirm() {
        try {
          await Api.delete(`/templates/template/single/${testId}`, {
            blockId: blockId,
          });
        } catch (error) {
          notify.error(error);
        } finally {
          refresh(true);
          hideConfirm();
          setCreateBlockVisibility(true);
        }
      },
    });
  };

  const onClose = () => {
    setCreateBlockVisibility(false);
    setBlockDetails(null);
  };

  useEffect(() => {
    if (blockDetails?.blockIdDetails) {
      handleGet();
      setFieldValue('title')(blockDetails.titleDetails);
    }
  }, []);

  return (
    <Dialog
      show
      popup
      size="lg"
      modalHeader={blockDetails?.blockIdDetails ? 'Update Template' : 'Create New Template'}
      className={'text-center'}
      onClose={onClose}
      overflowVisible={true}
    >
      <Form className="space-y-4" onSubmit={blockDetails?.blockIdDetails ? handleUpdate : handleInsert}>
        <TextInput
          name="title"
          label="Template Title"
          placeholder="Enter name"
          value={form.title}
          onChange={setFieldValue('title')}
          validators={[isRequired(), isNotSpaces()]}
          validatorsScroll={true}
        />

        {blockDetails?.blockIdDetails &&
          testDetails?.map((singleTest) => {
            return (
              <CategoryCardData
                key={singleTest._id}
                test={singleTest}
                blockDetails={blockDetails}
                isEditMode={true}
                setCreateBlockVisibility={setCreateBlockVisibility}
                setCreateTestDialogVisibility={setCreateTestDialogVisibility}
                handleEditBlockTest={handleEditBlockTest}
                handleDeleteBlockTest={handleDeleteBlockTest}
              />
            );
          })}

        {/* Buttons */}
        <div className="flex gap-2">
          {blockDetails?.blockIdDetails && <Button outline danger label="Delete" loading={loading} disabled={loading} onClick={handleDeleteBlock} />}
          <Button
            type="submit"
            label={blockDetails?.blockIdDetails ? 'Update' : 'Create Template'}
            className="w-full"
            loading={loading}
            disabled={loading}
          />
        </div>
      </Form>
    </Dialog>
  );
};
