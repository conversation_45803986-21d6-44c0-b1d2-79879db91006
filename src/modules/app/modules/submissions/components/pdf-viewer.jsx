// React
import { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';

// UI
import { useNotify, Api } from '/src';

// Components
import { ReportCard } from './report-card';

/* Be aware of:
    - Type     : submissions | interviews   | screening
    - URL      : submissions | ai-interview | 
    - Response : submissions | interviews   | 
      * subcategoryScore | subcategoryScore | 
      * submission       | interviewObj     | 
      * applicant        | applicant        | 
*/

export const PDFViewerComponent = () => {
  const { notify } = useNotify();
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const type = searchParams.get('type');
  const [details, setDetails] = useState(null);

  const handleGet = async () => {
    try {
      const response = await Api.get(
        `${type === 'submissions' ? 'submissions' : type === 'interviews' ? 'ai-interview' : type === 'screening' ? 'screening' : '—'}/pdf/${id}`
      );
      setDetails(response.data);
    } catch (error) {
      notify.error(error.response?.data?.message);
    }
  };

  useEffect(() => {
    if (id) {
      handleGet();
    }
  }, []);

  return details && <ReportCard details={details} propertyKeyObject={type} />;
};
