// UI
import { Card, Icon } from '/src';

export const ShowInterviewProgressBlocks = ({ statistics }) => {
  const customIconWavyAnswered = (
    <svg width="35" height="35" viewBox="0 0 35 35" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M32.7969 13.5625C32.2031 12.9531 31.5938 12.3125 31.3594 11.7656C31.1505 11.2783 31.1402 10.58 31.1294 9.84769C31.128 9.75824 31.1267 9.66827 31.125 9.57812C31.1094 8.0625 31.0781 6.32812 29.875 5.125C28.6719 3.92188 26.9375 3.89062 25.4219 3.875L25.4181 3.87493C24.5912 3.85933 23.7492 3.84344 23.2344 3.64062C22.7188 3.4375 22.0469 2.79688 21.4375 2.20312C20.3594 1.17188 19.125 0 17.5 0C15.875 0 14.6406 1.17188 13.5625 2.20312C12.9531 2.79688 12.3125 3.40625 11.7656 3.64062C11.2783 3.84949 10.58 3.85981 9.84769 3.87065L9.84761 3.87065C9.75818 3.87197 9.66825 3.8733 9.57812 3.875C8.0625 3.89062 6.32812 3.92188 5.125 5.125C3.92188 6.32812 3.89062 8.0625 3.875 9.57812L3.87493 9.5819C3.85933 10.4088 3.84344 11.2508 3.64062 11.7656C3.4375 12.2812 2.79688 12.9531 2.20312 13.5625C1.17188 14.6406 0 15.875 0 17.5C0 19.125 1.17188 20.3594 2.20312 21.4375C2.79688 22.0469 3.40625 22.6875 3.64062 23.2344C3.84949 23.7217 3.85981 24.42 3.87065 25.1523L3.87065 25.1524C3.87197 25.2418 3.8733 25.3318 3.875 25.4219C3.89062 26.9375 3.92188 28.6719 5.125 29.875C6.32812 31.0781 8.0625 31.1094 9.57812 31.125L9.5819 31.1251H9.58192C10.4088 31.1407 11.2508 31.1566 11.7656 31.3594C12.2812 31.5625 12.9531 32.2031 13.5625 32.7969C14.6406 33.8281 15.875 35 17.5 35C19.125 35 20.3594 33.8281 21.4375 32.7969C22.0469 32.2031 22.6875 31.5938 23.2344 31.3594C23.7217 31.1505 24.42 31.1402 25.1523 31.1294C25.2418 31.128 25.3317 31.1267 25.4219 31.125C26.9375 31.1094 28.6719 31.0781 29.875 29.875C31.0781 28.6719 31.1094 26.9375 31.125 25.4219L31.1251 25.4181V25.4181C31.1407 24.5912 31.1566 23.7492 31.3594 23.2344C31.5625 22.7188 32.2031 22.0469 32.7969 21.4375C33.8281 20.3594 35 19.125 35 17.5C35 15.875 33.8281 14.6406 32.7969 13.5625ZM15.0074 22.0686L12.6316 24.3463V22.3908C12.6316 22.0822 12.3813 21.8314 12.0723 21.8314H9.85703C9.8 21.8314 9.74648 21.8104 9.70547 21.7768L9.68984 21.7611C9.64648 21.7174 9.61875 21.6568 9.61875 21.5936V11.5572C9.61875 11.4947 9.64766 11.435 9.69102 11.3912C9.73477 11.3479 9.79453 11.3189 9.85703 11.3189H22.575C22.6379 11.3189 22.6984 11.3471 22.7418 11.3908C22.7848 11.4338 22.8125 11.4932 22.8125 11.5572V13.2166C23.1883 13.2424 23.5625 13.2979 23.9313 13.3807V11.5572C23.9313 11.1842 23.7766 10.8443 23.5324 10.6002C23.2867 10.3541 22.9453 10.2002 22.575 10.2002H9.85703C9.48633 10.2002 9.1457 10.3557 8.90039 10.6006C8.65547 10.8459 8.5 11.1865 8.5 11.5572V21.5936C8.5 21.9646 8.65352 22.3057 8.89922 22.5518L8.94219 22.5904C9.18359 22.8123 9.50508 22.9506 9.85703 22.9506H11.5129V25.6549C11.5129 25.9639 11.7633 26.2143 12.0723 26.2143C12.1514 26.2143 12.2296 26.1976 12.3018 26.1652C12.374 26.1328 12.4385 26.0855 12.491 26.0264L15.3551 23.2799C15.2047 22.885 15.0887 22.4799 15.0074 22.0686ZM28.5 20.6244C28.5 19.0393 27.8934 17.4521 26.6812 16.2354L26.6211 16.1799C25.4164 15.0061 23.8535 14.4178 22.293 14.4178C20.7078 14.4178 19.1207 15.024 17.9039 16.2365C16.6926 17.4475 16.0863 19.0365 16.0863 20.6244C16.0863 22.21 16.6926 23.7967 17.9043 25.0131C19.1156 26.2244 20.7043 26.8311 22.293 26.8311C23.8785 26.8311 25.466 26.2244 26.6824 25.0127L26.7375 24.9525C27.9117 23.7479 28.5 22.185 28.5 20.6244ZM21.4156 22.0861H23.132L23.3773 22.8947H24.9637L23.1262 17.9912H21.468L19.6227 22.8947H21.1691L21.4156 22.0861ZM22.2762 19.2627L22.8113 21.0252H21.7383L22.2762 19.2627ZM18.5957 16.9271C20.6371 14.885 23.9484 14.885 25.9906 16.9271C28.0328 18.9689 28.0328 22.2799 25.9906 24.3221C23.9484 26.3639 20.6371 26.3639 18.5957 24.3221C16.5531 22.2799 16.5531 18.9689 18.5957 16.9271ZM11.7793 14.6701C11.7793 14.9787 12.0297 15.2295 12.3387 15.2295H17.1891C17.6505 14.7926 18.166 14.4166 18.723 14.1107H12.3387C12.0297 14.1107 11.7793 14.3611 11.7793 14.6701ZM11.7793 17.9799C11.7793 18.2889 12.0297 18.5393 12.3387 18.5393H15.1641C15.275 18.1576 15.418 17.7842 15.5914 17.4205H12.3387C12.0297 17.4205 11.7793 17.6713 11.7793 17.9799Z"
        fill="#D07EAA"
      />
    </svg>
  );
  const customIconWavyUnAnswered = (
    <svg width="35" height="35" viewBox="0 0 35 35" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M32.7969 13.5625C32.2031 12.9531 31.5938 12.3125 31.3594 11.7656C31.1505 11.2783 31.1402 10.58 31.1294 9.84769C31.128 9.75824 31.1267 9.66827 31.125 9.57812C31.1094 8.0625 31.0781 6.32812 29.875 5.125C28.6719 3.92188 26.9375 3.89062 25.4219 3.875L25.4181 3.87493C24.5912 3.85933 23.7492 3.84344 23.2344 3.64062C22.7188 3.4375 22.0469 2.79688 21.4375 2.20312C20.3594 1.17188 19.125 0 17.5 0C15.875 0 14.6406 1.17188 13.5625 2.20312C12.9531 2.79688 12.3125 3.40625 11.7656 3.64062C11.2783 3.84949 10.58 3.85981 9.84769 3.87065L9.84761 3.87065C9.75818 3.87197 9.66825 3.8733 9.57812 3.875C8.0625 3.89062 6.32812 3.92188 5.125 5.125C3.92188 6.32812 3.89062 8.0625 3.875 9.57812L3.87493 9.5819C3.85933 10.4088 3.84344 11.2508 3.64062 11.7656C3.4375 12.2812 2.79688 12.9531 2.20312 13.5625C1.17188 14.6406 0 15.875 0 17.5C0 19.125 1.17188 20.3594 2.20312 21.4375C2.79688 22.0469 3.40625 22.6875 3.64062 23.2344C3.84949 23.7217 3.85981 24.42 3.87065 25.1523L3.87065 25.1524C3.87197 25.2418 3.8733 25.3318 3.875 25.4219C3.89062 26.9375 3.92188 28.6719 5.125 29.875C6.32812 31.0781 8.0625 31.1094 9.57812 31.125L9.5819 31.1251H9.58192C10.4088 31.1407 11.2508 31.1566 11.7656 31.3594C12.2812 31.5625 12.9531 32.2031 13.5625 32.7969C14.6406 33.8281 15.875 35 17.5 35C19.125 35 20.3594 33.8281 21.4375 32.7969C22.0469 32.2031 22.6875 31.5938 23.2344 31.3594C23.7217 31.1505 24.42 31.1402 25.1523 31.1294C25.2418 31.128 25.3317 31.1267 25.4219 31.125C26.9375 31.1094 28.6719 31.0781 29.875 29.875C31.0781 28.6719 31.1094 26.9375 31.125 25.4219L31.1251 25.4181V25.4181C31.1407 24.5912 31.1566 23.7492 31.3594 23.2344C31.5625 22.7188 32.2031 22.0469 32.7969 21.4375C33.8281 20.3594 35 19.125 35 17.5C35 15.875 33.8281 14.6406 32.7969 13.5625ZM15.0074 22.0686L12.6316 24.3463V22.3908C12.6316 22.0822 12.3812 21.8314 12.0723 21.8314H9.85703C9.8 21.8314 9.74648 21.8104 9.70547 21.7768L9.68984 21.7611C9.64648 21.7174 9.61875 21.6568 9.61875 21.5936V11.5572C9.61875 11.4947 9.64766 11.435 9.69102 11.3912C9.73477 11.3479 9.79453 11.3189 9.85703 11.3189H22.575C22.6379 11.3189 22.6984 11.3471 22.7418 11.3908C22.7848 11.4338 22.8125 11.4932 22.8125 11.5572V13.2166C23.1883 13.2424 23.5625 13.2979 23.9312 13.3807V11.5572C23.9312 11.1842 23.7766 10.8443 23.5324 10.6002C23.2867 10.3541 22.9453 10.2002 22.575 10.2002H9.85703C9.48633 10.2002 9.1457 10.3557 8.90039 10.6006C8.65547 10.8459 8.5 11.1865 8.5 11.5572V21.5936C8.5 21.9646 8.65352 22.3057 8.89922 22.5518L8.94219 22.5904C9.18359 22.8123 9.50508 22.9506 9.85703 22.9506H11.5129V25.6549C11.5129 25.9639 11.7633 26.2143 12.0723 26.2143C12.1514 26.2143 12.2296 26.1976 12.3018 26.1652C12.374 26.1328 12.4385 26.0855 12.491 26.0264L15.3551 23.2799C15.2047 22.885 15.0887 22.4799 15.0074 22.0686ZM28.5 20.6244C28.5 19.0393 27.8934 17.4521 26.6812 16.2354L26.6211 16.1799C25.4164 15.0061 23.8535 14.4178 22.293 14.4178C20.7078 14.4178 19.1207 15.024 17.9039 16.2365C16.6926 17.4475 16.0863 19.0365 16.0863 20.6244C16.0863 22.21 16.6926 23.7967 17.9043 25.0131C19.1156 26.2244 20.7043 26.8311 22.293 26.8311C23.8785 26.8311 25.466 26.2244 26.6824 25.0127L26.7375 24.9525C27.9117 23.7479 28.5 22.185 28.5 20.6244ZM21.4156 22.0861H23.132L23.3773 22.8947H24.9637L23.1262 17.9912H21.468L19.6227 22.8947H21.1691L21.4156 22.0861ZM22.2762 19.2627L22.8113 21.0252H21.7383L22.2762 19.2627ZM18.5957 16.9271C20.6371 14.885 23.9484 14.885 25.9906 16.9271C28.0328 18.9689 28.0328 22.2799 25.9906 24.3221C23.9484 26.3639 20.6371 26.3639 18.5957 24.3221C16.5531 22.2799 16.5531 18.9689 18.5957 16.9271ZM11.7793 14.6701C11.7793 14.9787 12.0297 15.2295 12.3387 15.2295H17.1891C17.6505 14.7926 18.166 14.4166 18.723 14.1107H12.3387C12.0297 14.1107 11.7793 14.3611 11.7793 14.6701ZM11.7793 17.9799C11.7793 18.2889 12.0297 18.5393 12.3387 18.5393H15.1641C15.275 18.1576 15.418 17.7842 15.5914 17.4205H12.3387C12.0297 17.4205 11.7793 17.6713 11.7793 17.9799Z"
        fill="#94989F"
      />
    </svg>
  );
  const customIconWavyWrong = (
    <svg width="35" height="35" viewBox="0 0 35 35" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M32.7969 13.5625C32.2031 12.9531 31.5938 12.3125 31.3594 11.7656C31.2847 11.5918 31.2354 11.3906 31.2026 11.1709C31.1432 10.7754 31.1364 10.3193 31.1294 9.8492C31.1281 9.75925 31.1267 9.66878 31.125 9.57812C31.1094 8.0625 31.0781 6.32812 29.875 5.125C28.6719 3.92188 26.9375 3.89062 25.4219 3.875H25.4175C24.5908 3.85938 23.749 3.84326 23.2344 3.64062C22.7188 3.4375 22.0469 2.79688 21.4375 2.20312C20.3594 1.17188 19.125 0 17.5 0C15.875 0 14.6406 1.17188 13.5625 2.20312C12.9531 2.79688 12.3125 3.40625 11.7656 3.64062C11.2806 3.84864 10.5865 3.85928 9.85782 3.87045L9.84766 3.87061L9.57812 3.875C8.0625 3.89062 6.32812 3.92188 5.125 5.125C3.92188 6.32812 3.89062 8.0625 3.875 9.57812V9.58252C3.85938 10.4092 3.84326 11.251 3.64062 11.7656C3.4375 12.2812 2.79688 12.9531 2.20312 13.5625C1.17188 14.6406 0 15.875 0 17.5C0 19.125 1.17188 20.3594 2.20312 21.4375C2.79688 22.0469 3.40625 22.6875 3.64062 23.2344C3.84949 23.7217 3.85981 24.42 3.87065 25.1523C3.87197 25.2418 3.8733 25.3317 3.875 25.4219C3.89062 26.9375 3.92188 28.6719 5.125 29.875C6.32812 31.0781 8.0625 31.1094 9.57812 31.125L9.5819 31.1251C10.4088 31.1407 11.2508 31.1566 11.7656 31.3594C12.2812 31.5625 12.9531 32.2031 13.5625 32.7969C14.6406 33.8281 15.875 35 17.5 35C19.125 35 20.3594 33.8281 21.4375 32.7969C22.0469 32.2031 22.6875 31.5938 23.2344 31.3594C23.7217 31.1505 24.42 31.1402 25.1523 31.1294C25.2418 31.128 25.3317 31.1267 25.4219 31.125C26.9375 31.1094 28.6719 31.0781 29.875 29.875C31.0781 28.6719 31.1094 26.9375 31.125 25.4219V25.4175C31.1406 24.5908 31.1567 23.749 31.3594 23.2344C31.5625 22.7188 32.2031 22.0469 32.7969 21.4375C33.8281 20.3594 35 19.125 35 17.5C35 15.875 33.8281 14.6406 32.7969 13.5625ZM12.6919 11.2778C12.2932 10.8957 11.6601 10.9092 11.278 11.3079C10.8959 11.7067 10.9094 12.3397 11.3081 12.7218L16.5547 17.7498L11.3081 22.7778C10.9094 23.16 10.8959 23.793 11.278 24.1917C11.6601 24.5905 12.2932 24.604 12.6919 24.2218L18 19.1349L23.3081 24.2218C23.7068 24.604 24.3399 24.5905 24.722 24.1917C25.1041 23.793 25.0906 23.16 24.6919 22.7778L19.4453 17.7498L24.6919 12.7218C25.0906 12.3397 25.1041 11.7067 24.722 11.3079C24.3399 10.9092 23.7068 10.8957 23.3081 11.2778L18 16.3648L12.6919 11.2778Z"
        fill="#F05252"
      />
    </svg>
  );
  const testBlocks = [
    { key: 'total', icon: 'ph:circle-wavy-question-fill', color: 'text-[#9061F9]', title: 'Total Questions' },
    { key: 'answered', svg: customIconWavyAnswered, color: 'text-[#D07EAA]', title: 'Questions Answered' },
    { key: 'unanswered', svg: customIconWavyUnAnswered, color: 'text-[#94989F]', title: 'Questions Unanswered' },
    { key: 'correct', icon: 'ph:circle-wavy-check-fill', color: 'text-[#52F062]', title: 'Correct Answers' },
    { key: 'wrong', svg: customIconWavyWrong, color: 'text-[#F05252]', title: 'Wrong Answers' },
    { key: 'skipped', icon: 'ph:circle-wavy-warning-fill', color: 'text-[#FEB825]', title: 'Skipped Questions' },
  ];

  const handleSubmissionTitle = (key) => {
    if (key === 'total') return statistics.totalQuestions;
    else if (key === 'answered') return statistics.answeredQuestions;
    else if (key === 'unanswered') return statistics.unAnsweredQuestions;
    else if (key === 'correct') return statistics.correctAnswers;
    else if (key === 'wrong') return statistics.wrongAnswers;
    else if (key === 'skipped') return statistics.skippedQuestions;
    else return '—';
  };

  return (
    <>
      {testBlocks.map((block, index) => (
        <Card key={block.key} className="space-y-3 relative">
          <div>
            {block.icon && <Icon width="40" className={`w-fit ${block.color}`} icon={block.icon} />}
            {block.svg && block.svg}
          </div>
          <p className="text-base text-[#374151] dark:text-white min-[1536px]:w-[105px] min-[1685px]:w-full">{block.title}</p>
          <p className="text-2xl dark:text-white">
            <span>{handleSubmissionTitle(block.key)}</span>{' '}
            {block.key === 'suspicious' && statistics.cheatingPercentage > 0 && <span>({statistics.cheatingPercentage}%)</span>}
          </p>
        </Card>
      ))}
    </>
  );
};
