import React, { useEffect, useState, useRef } from 'react';

// UI
import { Form, TextInput, Select, MultiSelect, Button, Checkbox, useForm, useNotify, Enums, Api, Regex, useValidate, Icon, Dialog } from '/src';

export const SubmissionsCreationTestDialog = ({
  blockDetails,
  setBlockDetails,
  onClose,
  refresh,
  setCreateBlockVisibility,
  setCreateTestDialogVisibility,
}) => {
  // Route Params
  const { isRequired, isSelected, isNotSpaces, minLength, maxLength } = useValidate();
  // State
  const { notify } = useNotify();
  const [loading, setLoading] = useState(false);
  const [quizUrl, setQuizUrl] = useState('');
  const [submissionId, setSubmissionId] = useState('');
  const [applicants, setApplicants] = useState([]);
  const [addIconApplicant, setAddIconApplicant] = useState(false);
  const [searchResult, setSearchResult] = useState(null);
  const [emailRegex, setEmailRegex] = useState(false);
  const subCategoryRef = useRef(null);

  // Form
  const { form, setFieldValue, setFormValue } = useForm({
    title: '',
    category: 0,
    subCategory: [],
    numOfQuestions: 10,
    duration: 50,
    difficulty: 0,
    applicantId: '',
    // willSendEmail: false,
  });

  // Methods
  const handleSearch =
    (endpoint, action, isCustomAdd = false) =>
    async (keyword) => {
      try {
        const result = await Api.get(endpoint, { keyword });
        action(result?.data);
        setEmailRegex(false);
        if (isCustomAdd) {
          if (!result?.data?.length && !!keyword) {
            setAddIconApplicant(true);
            setSearchResult(keyword);
          } else {
            setAddIconApplicant(false);
          }
        }
      } catch (error) {
        notify.error(error.response.data.message);
      }
    };

  const handleAddNewApplicant = async () => {
    const pattern = Regex.email;
    const value = document.getElementById('applicantId').value;
    if (!pattern.test(value)) {
      setEmailRegex(true);
      setAddIconApplicant(false);
    } else {
      try {
        const response = await Api.post('applicants/single/custom', {
          email: searchResult,
        });
        setFieldValue('applicantId')(response.data);
        setAddIconApplicant(false);
      } catch (error) {
        notify.error(error.response.data.message);
      }
    }
  };

  // Generate Test
  const handleGenerateSubmission = async () => {
    if (form.subCategory.length == 0) {
      notify.error('Please select 1 to 4 subCategory for your test questions.');
    } else {
      try {
        setLoading(true);
        const response = await Api.post('submissions/single', {
          customTest: true,
          title: form.title,
          category: form.category,
          subCategory: form.subCategory,
          numOfQuestions: form.numOfQuestions,
          duration: form.duration,
          difficulty: form.difficulty,
          // willSendEmail: form.willSendEmail,
          applicantId: form.applicantId,
        });
        setQuizUrl(response.data.quizUrl);
        setSubmissionId(response.data.submissionId);
      } catch (error) {
        notify.error(error.response.data.message);
      } finally {
        setLoading(false);
      }
    }
  };

  // Generate Block Test
  const handleGenerateBlockTest = async () => {
    if (form.subCategory.length == 0) {
      notify.error('Please select 1 to 4 subCategory for your test questions.');
    } else {
      try {
        setLoading(true);
        await Api.post('/templates/template/single', {
          blockId: blockDetails.blockIdDetails,
          category: form.category,
          numOfQuestions: form.numOfQuestions,
          duration: form.duration,
          subCategory: form.subCategory,
          difficulty: form.difficulty,
          title: form.subCategory.map((ele) => ele).join(),
        });
        refresh(true);
        setCreateTestDialogVisibility(false);
        setBlockDetails(null);
      } catch (error) {
        notify.error(error.response.data.message);
      } finally {
        setLoading(false);
      }
    }
  };

  // Edit Block Test
  const handleEditBlockTest = async () => {
    if (form.subCategory.length == 0) {
      notify.error('Please select 1 to 4 subCategory for your test questions.');
    } else {
      try {
        setLoading(true);
        await Api.put(`/templates/template/single/${blockDetails.testIdDetails.data._id}`, {
          blockId: blockDetails.blockIdDetails,
          _id: blockDetails.testIdDetails._id,
          category: form.category,
          subCategory: form.subCategory,
          numOfQuestions: form.numOfQuestions,
          duration: form.duration,
          difficulty: form.difficulty,
          title: form.subCategory.map((ele) => ele),
        });
        refresh(true);
        setCreateTestDialogVisibility(false);
        setCreateBlockVisibility(true);
      } catch (error) {
        notify.error(error.response.data.message);
      } finally {
        setLoading(false);
      }
    }
  };

  // On Submit
  const onSubmit = () => {
    if (blockDetails?.testIdDetails) {
      // Edit custom test-template without Applicant without Title
      handleEditBlockTest();
    } else if (blockDetails?.blockIdDetails) {
      // New custom test-template without Applicant without Title
      handleGenerateBlockTest();
    } else {
      // New custom submission with Applicant with Title
      handleGenerateSubmission();
    }
  };

  // On Mount
  useEffect(() => {
    if (blockDetails?.testIdDetails) {
      setFormValue(blockDetails.testIdDetails.data);
    }
    // setFieldValue('willSendEmail')(false);
  }, [form.applicantId]);

  return (
    <Dialog
      show
      popup
      size="lg"
      modalHeader={!quizUrl ? (blockDetails?.testIdDetails ? 'Update Test' : 'Create Test') : 'Test Created Successfully!'}
      className={!!quizUrl && 'text-center'}
      onClose={onClose}
      overflowVisible={true}
    >
      {/* Creation Form */}
      {!quizUrl && (
        <Form className="space-y-4" onSubmit={onSubmit}>
          {/* Title */}
          {!blockDetails?.blockIdDetails && (
            <TextInput
              name="title"
              label="Test Title"
              placeholder="Enter test title"
              value={form.title}
              onChange={setFieldValue('title')}
              validators={[isRequired(), isNotSpaces(), minLength(2), maxLength(100)]}
              validatorsScroll={true}
            />
          )}
          <Select
            label="Category"
            name="category"
            required
            value={form.category}
            onChange={(newCategory) => {
              subCategoryRef.current?.blur();
              setFieldValue('category')(newCategory);
              setFieldValue('subCategory')([]);
            }}
            lookup="category"
            creationOptions={{
              url: 'lookups/category/single',
              fieldName: 'name',
              validation: Regex.categorySubcategoryTopic,
            }}
            optionValueKey="_id"
            optionLabelKey="name"
            dropIcon={true}
            validators={[isRequired()]}
          />
          <MultiSelect
            key={form.category}
            ref={subCategoryRef}
            label="Subcategory"
            name="SubCategory"
            value={form.subCategory}
            onChange={(newSubCategory) => {
              setFieldValue('subCategory')(newSubCategory);
            }}
            disabled={!form.category}
            lookup="subcategory"
            creationOptions={{
              // it will use the params prop as default paload
              url: 'lookups/subCategory/single',
              fieldName: 'name',
              validation: Regex.categorySubcategoryTopic,
            }}
            params={{ categoryId: form.category }}
            optionValueKey="_id"
            optionLabelKey="name"
            validators={form.category ? [isSelected()] : []}
          />
          <div className="grid grid-cols-2 gap-2">
            <TextInput
              name="numOfQuestions"
              label="Question Count"
              placeholder="Enter number"
              type="number"
              value={form.numOfQuestions}
              onChange={setFieldValue('numOfQuestions', Number)}
              min={10}
              max={100}
            />
            <TextInput
              name="duration"
              label="Duration"
              placeholder="Enter duration..."
              type="number"
              value={form.duration <= 1 ? 1 : form.duration}
              onChange={setFieldValue('duration', Number)}
              min={20}
              max={120}
            />
          </div>
          <Select
            name="difficulty"
            label="Difficulty"
            lookup="$QuizDifficulty"
            value={form.difficulty}
            onChange={setFieldValue('difficulty', Number)}
            dropIcon={true}
            validators={[isRequired()]}
          />
          {!blockDetails?.blockIdDetails && (
            <>
              <div className="relative w-full">
                <Select
                  name="applicantId"
                  label="Applicant"
                  placeholder="Find applicants by email..."
                  value={form.applicantId}
                  onChange={setFieldValue('applicantId')}
                  // FIXME: (isCustomAdd = true)
                  onSearch={handleSearch('applicants/search', setApplicants, true)}
                  creationOptions={{
                    url: 'applicants/single/custom',
                    fieldName: 'email',
                    validation: Regex.email,
                  }}
                  disabled={loading}
                  lookup={applicants}
                  optionValueKey="_id"
                  optionLabelKey="email"
                  isCustomValue={true}
                  customAddIconApplicant={emailRegex}
                />
                {/* {addIconApplicant && (
                  <div className="absolute right-2 top-[42px] cursor-pointer bg-white dark:bg-[#374151]" onClick={handleAddNewApplicant}>
                    <Icon width={22} className="text-green-500" icon="material-symbols:add" />
                  </div>
                )} */}
              </div>

              {/* <TextInput
                name="applicantLimit"
                label="Limit Num Of Applicants"
                type="number"
                placeholder="Enter limit num"
                value={form.applicantLimit}
                onChange={setFieldValue('applicantLimit')}
                disabled={form.applicantId}
                min={0}
              /> */}

              {/* <Checkbox
                name="willSendEmail"
                label="Send link via applicant email"
                value={form.willSendEmail}
                onChange={setFieldValue('willSendEmail')}
                disabled={form.applicantId.length === 0 || loading}
                preventSendingMail={form.applicantId.length === 0 || loading}
              /> */}
            </>
          )}

          {/* Actions */}
          <Button
            type="submit"
            label={blockDetails?.testIdDetails ? 'Update Test' : 'Create Test'}
            className="w-full"
            loading={loading}
            disabled={loading || emailRegex}
          />
        </Form>
      )}

      {/* After Creation */}
      {quizUrl && (
        <div>
          <Icon icon="fluent-emoji:clapping-hands" width="120" />
          <hr className="h-px my-4 bg-gray-200 border-0 dark:bg-gray-700" />
          <div className="space-y-4">
            {/* {form.applicantId && (
              <Button className="w-full" label="View Progress" icon="pajamas:progress" to={`/app/tests/result/view/${submissionId}`} />
            )} */}

            <Button
              className="w-full"
              outline
              label="Copy Link"
              icon="material-symbols:content-copy-outline-rounded"
              onClick={() => {
                navigator.clipboard.writeText(quizUrl);
                notify('Link copied');
              }}
            />
          </div>
        </div>
      )}
    </Dialog>
  );
};
