import React, { useState } from 'react';

// Components
import { Dialog, Form, Select, Button, useForm, useNotify, Api } from '/src';

export const SubmissionsLockDialog = ({ onClose, onFinish }) => {
  // Hooks
  const { notify } = useNotify();

  // Form
  const { form, setFieldValue, resetForm } = useForm({
    quizId: '',
  });

  // State
  const [loading, setLoading] = useState(false);
  const [quizzes, setQuizzes] = useState([]);

  // Methods
  const handleSearch = async (keyword) => {
    try {
      const result = await Api.get('quizzes/search', { keyword });

      setQuizzes(result?.data);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };
  const handleSubmit = async () => {
    try {
      setLoading(true);

      await Api.put('submissions/bulk-lock', form);

      // Reset form
      resetForm();

      // Update Parent
      onFinish();
      onClose();

      // Notify
      notify('Test locked successfully!');
    } catch (error) {
      notify.error(error.response.data.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog show popup size="md" modalHeader={'Lock Bulk Test'} onClose={onClose} overflowVisible={true}>
      <Form className="space-y-4" onSubmit={handleSubmit}>
        <Select
          name="quizId"
          label="Test"
          placeholder="Find tests..."
          value={form.quizId}
          onChange={setFieldValue('quizId')}
          onSearch={handleSearch}
          disabled={loading}
          lookup={quizzes.map(({ title, _id }) => ({
            label: title,
            value: _id,
          }))}
        />

        <div className="pt-2 space-y-4">
          <Button
            type="submit"
            label="Lock All"
            icon="material-symbols:lock-outline"
            className="w-full"
            loading={loading}
            disabled={loading || !form.quizId}
          />
        </div>
      </Form>
    </Dialog>
  );
};
