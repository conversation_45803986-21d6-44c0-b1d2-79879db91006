import React, { useState, useRef, useEffect, useContext } from 'react';

import {
  Dialog,
  Form,
  Radio,
  useValidate,
  Textarea,
  Checkbox,
  MultiSelect,
  Icon,
  Button,
  useForm,
  useNotify,
  Select,
  Api,
  Regex,
  TextInput,
  Enums,
} from '/src';
import { DatePicker, DateRangePicker } from 'rsuite';

export const AiIntreviewDialog = ({ onClose, onCreate, applicantDetails, refreshMainTable = () => {} }) => {
  // Hooks
  const { notify } = useNotify();
  const [applicants, setApplicants] = useState([]);
  const [loading, setLoading] = useState(false);
  const [emailRegex, setEmailRegex] = useState(false);
  const [addIconApplicant, setAddIconApplicant] = useState(false);
  const [interviewQuiz, setInterviewQuiz] = useState(false);
  const [searchResult, setSearchResult] = useState(null);
  const [startDate, setStartDate] = useState();
  const [dueDate, setDueDate] = useState();

  const { isRequired, isNumber, isValidateMaxAndMinNumber } = useValidate();

  // State
  const subCategoryRef = useRef(null);
  const { beforeToday } = DateRangePicker;

  // Form
  const { form, setFieldValue } = useForm({
    technology: '',
    numberOfQuestions: '',
    yearsOfExperience: '',
    estimationTime: '',
    type: 1, // this type for interview
    skips: '',
    applicantId: '',
    notes: '',
    // willSendEmail: false,
    dueDate: '',
    startDate: '',
    category: applicantDetails?.track ? applicantDetails.track : null,
    subCategory: [],
  });

  const handleInsert = async (e) => {
    if (form.numberOfQuestions > 30) {
      notify.error("Max questions can't exceed 30");
    } else if (Number(form.skips) >= Number(form.numberOfQuestions)) {
      notify.error("Max skips can't exceed or equal to number of questions");
    } else if (!form?.startDate) {
      notify.error('Please select start date');
    } else if (!form?.dueDate) {
      notify.error('Please select due date');
    } else {
      try {
        setLoading(true);
        const { applicantId, ...payload } = form;
        if (payload.notes == '') delete payload.notes;
        // @TODO:Handle multi applicant assignment
        if (form.applicantId) payload.applicantId = [form.applicantId];
        const result = await Api.post('ai-interview/single', payload);
        setInterviewQuiz(result.data.quizUrl);
        onCreate();
      } catch (error) {
        notify.error(error.response.data.message);
      } finally {
        setLoading(false);
      }
    }
  };

  // RANDOM CREATION:- No need for applicant select input
  // const handleSearch =
  //   (endpoint, action, isCustomAdd = false) =>
  //   async (keyword) => {
  //     try {
  //       const result = await Api.get(endpoint, { keyword });
  //       action(result?.data);
  //       if (isCustomAdd) {
  //         if (!result?.data?.length && !!keyword) {
  //           setAddIconApplicant(true);
  //           setSearchResult(keyword);
  //         } else {
  //           setAddIconApplicant(false);
  //         }
  //       }
  //     } catch (error) {
  //       notify.error(error.response.data.message);
  //     }
  //   };

  const handleAddNewApplicant = async () => {
    const pattern = Regex.email;
    const value = document.getElementById('applicantId').value;
    if (!pattern.test(value)) {
      setEmailRegex(true);
      setAddIconApplicant(false);
    } else {
      const requestData = {
        email: searchResult,
      };
      const response = await Api.post('applicants/single/custom', requestData);
      setFieldValue('applicantId')(response.data._id);
      setAddIconApplicant(false);
    }
  };

  const handleCopyLink = () => {
    navigator.clipboard.writeText(interviewQuiz);
    notify('Link copied');
  };

  const subModalHeader = () => {
    return applicantDetails?.email && <p className="text-base font-semibold text-[#5C5C5C] dark:text-white">{applicantDetails?.email}</p>;
  };

  // useEffect(() => {
  //   setFieldValue('willSendEmail')(false);
  // }, [form.applicantId]);

  useEffect(() => {
    if (applicantDetails) {
      setFieldValue('applicantId')(applicantDetails?._id);
      setFieldValue('category')(applicantDetails?.track);
    }
  }, []);

  useEffect(() => {
    setFieldValue('startDate')(startDate);
    setFieldValue('dueDate')(dueDate);
  }, [startDate, dueDate]);

  return (
    <Dialog
      size="lg"
      show
      popup
      modalHeader={applicantDetails?.email ? 'Assign Interview to: ' : 'Generate Interview Link'}
      subModalHeader={subModalHeader()}
      onClose={() => {
        interviewQuiz && applicantDetails?.email && refreshMainTable();
        onClose();
      }}
      overflowVisible={true}
    >
      {/* Creation Form */}
      {!interviewQuiz && (
        <Form onSubmit={handleInsert}>
          <div className="space-y-4">
            <div className="space-y-3">
              <h3 className="font-medium text-sm text-inputLabel dark:text-inputDarkLabel">
                Interview Type <span className="text-red-600 dark:text-red-800">*</span>
              </h3>
              <div className="flex flex-wrap xsmd:gap-[70px]  gap-4 items-center container mx-auto  ">
                {Enums.InterviewType.map((type) => (
                  <div key={type.value} className="flex  items-center gap-2">
                    <Radio
                      name="type"
                      selectionValue={type.value}
                      value={form.type}
                      onChange={() => setFieldValue('type')(type.value)}
                      required
                      className="cursor-pointer"
                      labelTooltip={
                        type.key === 'Interactive'
                          ? 'Live interview experience with real-time questions.'
                          : 'Static set of questions for self-paced completion.'
                      }
                      // NB: These styles because tooltip is inside dialog
                      labelTooltipStyles="min-w-60 sm:min-w-72"
                      label={type.label}
                    />
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="font-medium text-sm text-inputLabel dark:text-inputDarkLabel">
                Due Date
                <span className="text-red-600 dark:text-red-800">*</span>
              </h3>

              <div className="flex flex-col sm:flex-row justify-between items-center gap-2 sm:gap-4">
                <DatePicker
                  format="dd/MM/yyyy hh:mm aa"
                  placeholder="Enter a start date"
                  className="w-full bg-gray-50 rounded-full"
                  value={startDate}
                  onChange={(value) => setStartDate(value)}
                  showMeridiem
                  placement="autoVerticalStart"
                  disabled={loading}
                  shouldDisableDate={beforeToday()}
                />

                <DatePicker
                  format="dd/MM/yyyy hh:mm aa"
                  placeholder="Enter an end date"
                  className="w-full bg-gray-50 rounded-full"
                  value={dueDate}
                  onChange={(value) => setDueDate(value)}
                  showMeridiem
                  placement="autoVerticalStart"
                  disabled={loading}
                  shouldDisableDate={beforeToday()}
                />
              </div>
            </div>

            {!applicantDetails?.track && (
              <Select
                label="Category"
                name="category"
                value={form.category}
                disabled={loading}
                onChange={(newCategory) => {
                  subCategoryRef.current?.blur();
                  setFieldValue('category')(newCategory);
                  setFieldValue('subCategory')(null);
                }}
                lookup="category"
                optionValueKey="_id"
                optionLabelKey="name"
                dropIcon
                requiredLabel
                creationOptions={{
                  url: 'lookups/category/single',
                  fieldName: 'name',
                  validation: Regex.categorySubcategoryTopic,
                }}
              />
            )}

            <MultiSelect
              key={form.category}
              label="Subcategory"
              requiredLabel
              name="subCategory"
              placeholder="Search for subcategory"
              value={Array.isArray(form.subCategory) ? form.subCategory : []}
              onChange={(newSubCategory) => setFieldValue('subCategory')(newSubCategory)}
              disabled={!form.category || loading}
              disabledMessage="Please select category first"
              lookup="subcategory"
              params={{ categoryId: form.category }}
              creationOptions={{
                url: 'lookups/subCategory/single',
                fieldName: 'name',
                validation: Regex.categorySubcategoryTopic,
              }}
              optionValueKey="_id"
              optionLabelKey="name"
              dropIcon
            />

            <div className="grid sm:grid-cols-2 gap-4 space-y-4 sm:space-y-0">
              <Select
                disabled={loading}
                name="difficulty"
                label="Difficulty"
                lookup="$QuizDifficulty"
                value={form.difficulty}
                onChange={setFieldValue('difficulty')}
                dropIcon
                requiredLabel
              />

              <TextInput
                disabled={loading}
                name="numberOfQuestions"
                label="Number of Questions"
                placeholder="Number of questions"
                value={form.numberOfQuestions}
                onChange={setFieldValue('numberOfQuestions')}
                validators={[isNumber(), isRequired()]}
                requiredLabel
              />
            </div>

            <div className="grid sm:grid-cols-2 gap-4 space-y-4 sm:space-y-0">
              <TextInput
                disabled={loading}
                name="estimationTime"
                label="Estimation Time"
                placeholder="Estimation time"
                labelTooltip="Expected time for the interview in minutes."
                value={form.estimationTime}
                onChange={setFieldValue('estimationTime')}
                validators={[isNumber(), isRequired(), isValidateMaxAndMinNumber('min', 10), isValidateMaxAndMinNumber('max', 240)]}
                requiredLabel
                type="number"
                min={10}
              />
              <TextInput
                disabled={loading}
                name="skips"
                label="Max Skips"
                labelTooltip="Maximum skips allowed without affecting the score."
                placeholder="Max Skips"
                value={form.skips}
                onChange={setFieldValue('skips')}
                validators={[isNumber(), isRequired()]}
                requiredLabel
              />
            </div>

            <TextInput
              label="Notes"
              name="notes"
              placeholder="e.g., Focus on advanced JavaScript topics"
              labelTooltip="Provide specific details or instructions for the interview."
              value={form.notes}
              onChange={setFieldValue('notes')}
              disabled={loading}
            />
          </div>

          <Button
            loading={loading}
            disabled={loading}
            className="w-full mt-5"
            type="submit"
            label={applicantDetails?.email ? 'Assign Interview' : 'Generate Link'}
          />
        </Form>
      )}

      {/* ai interview dialog */}
      {interviewQuiz && (
        <div className="py-5  pt-0">
          <div>
            <div className="flex justify-center items-center mx-auto mb-5 !mt-0 text-gray-300 dark:text-gray-200">
              <img src="/images/Vector.svg" alt="done mark" />
            </div>

            <div className="text-center">
              <h2 className="text-center dark:text-white font-medium text-xl ">
                AI interview {applicantDetails?.email ? 'Assigned' : 'Generated'} Successfully!
              </h2>
              <div className=" w-72 text-center mx-auto mt-2">
                <p className="text-center font-normal text-base   dark:text-white text-[#626262]">
                  Send the link below for quick and easy access to the applicant
                </p>
              </div>
            </div>
            <div className="mt-5 py-1">
              <div className="grid w-full max-w-80 mx-auto text-center">
                <div className="relative flex ">
                  <input
                    value={interviewQuiz}
                    type="text"
                    className="col-span-6 block w-full rounded-lg border border-gray-300 bg-gray-50 px-2.5 py-4 text-base text-[#313437] pr-12 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-400 dark:placeholder:text-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                  />
                  <Icon
                    icon="ooui:copy-ltr"
                    onClick={() => handleCopyLink()}
                    className="dark:text-white cursor-pointer text-gray-400  text-2xl absolute right-3 top-4"
                  />
                </div>
              </div>
              {/* 
            <Button
              className="w-full"
              outline
              label="Copy Link"
              icon="material-symbols:content-copy-outline-rounded"
              onClick={() => handleCopyLink()}
            /> */}
            </div>
          </div>
        </div>
      )}
    </Dialog>
  );
};
