import React from 'react';
import { EnumText, useScreenSize, Icon } from '/src';
import { Tooltip } from 'flowbite-react';
import { FaUserGraduate, FaUser, FaStar, FaMedal, FaTrophy } from 'react-icons/fa';

export const CategoryCardData = ({ test, creationSubmissionsDialog, back }) => {
  const text = test.title ?? 'Quiz';
  const screen = useScreenSize();

  // Define icon and color for difficulty levels
  let difficultyIcon = null;
  let difficultyColor = 'font-semibold';

  let iconSize = 'text-xs';
  switch (test.difficulty) {
    case 1:
      difficultyIcon = <FaUserGraduate className={`${iconSize} ${difficultyColor}`} />; // Intern
      break;

    // Star Icon fresh level
    case 2:
      difficultyIcon = <FaUser className={`${iconSize} ${difficultyColor}`} />; // Fresh
      break;
    // Medal Star junior
    case 3:
      difficultyIcon = <FaStar className={`${iconSize} ${difficultyColor}`} />; // Junior
      break;
    case 4:
      difficultyIcon = <FaMedal className={`${iconSize} ${difficultyColor}`} />; // Mid-level
      break;

    // Tropy icon for senior with star
    case 5:
      difficultyIcon = <Icon icon="solar:crown-star-bold" className={`${iconSize}  ${difficultyColor}`} />; // Senior
      // difficultyColor = 'text-red-800';
      break;
    default:
      difficultyIcon = null;
  }
  return (
    <div className="relative flex flex-col p-2 bg-white dark:bg-darkBackgroundCard space-y-1">
      <div onClick={() => creationSubmissionsDialog(test, back)} className="cursor-pointer">
        <h3 className="text-base font-semibold text-darkBlueText dark:text-grayTextOnDarkMood hover:underline truncate">{text}</h3>
        {screen.gt.lg() && (
          <Tooltip content={text} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 w-fit text-sm">
            <div className="w-full h-full absolute left-0 top-0"></div>
          </Tooltip>
        )}
      </div>
      <div className="flex items-center text-[#798296] dark:text-[#838398] text-sm  gap-2 mb-2">
        <span>
          <span className="font-semibold"> {test.numOfQuestions} </span> {test.numOfQuestions > 1 ? 'Questions' : 'Question'}
        </span>
        <span className="text-gray-400 dark:text-gray-300">•</span> {/* Separator */}
        <span>
          <span className="font-semibold"> {test.duration} </span>min
        </span>
        <span className="text-gray-400 dark:text-gray-300">•</span> {/* Separator */}
        <div className={`flex items-center text-sm gap-1`}>
          {difficultyIcon}
          <EnumText name={'QuizDifficulty'} value={test.difficulty} />
        </div>
      </div>
      <div className="pt-1">
        <hr className="border-gray-200 dark:border-gray-700" />
      </div>
    </div>
  );
};
