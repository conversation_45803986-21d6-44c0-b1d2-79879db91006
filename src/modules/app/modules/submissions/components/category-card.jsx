import React from 'react';
import { Card, Icon, useScreenSize } from '/src';
import { CategoryCardData } from './card/category-card-data';
import { FaGreater<PERSON>han, FaLessThan } from 'react-icons/fa';

// Flowbite
import { Tooltip } from 'flowbite-react';

export const CategoryCard = ({ subCategoryName, quizzes, blockIndex, subBlockIndex, showMoreInfo, creationSubmissionsDialog }) => {
  const screen = useScreenSize();

  return (
    <Card className="dark:text-white min-h-[320px] overflow-hidden !p-0 rounded-lg relative">
      <div className="relative h-full pb-8">
        {/* Subcategory Name */}
        <div className="py-4 relative  border-gray-100 border-b-2 dark:border-gray-700 rounded-t-lg ">
          <h2 className="text-lg font-medium px-4 break-all text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-purple-800 mx-auto text-center">
            {subCategoryName}
          </h2>
          {screen.gt.lg() && (
            <Tooltip
              content={subCategoryName}
              placement="bottom"
              arrow={false}
              className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 w-fit text-xs"
            >
              <div className="w-full h-full absolute left-0 top-0"></div>
            </Tooltip>
          )}

          {/* SVG Gradient Definition */}
          {/* <svg width="0" height="0">
            <defs>
              <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#ec4899" />
                <stop offset="50%" stopColor="#a855f7" />
                <stop offset="100%" stopColor="#ec4899" />
              </linearGradient>
            </defs>
          </svg> */}
        </div>

        <div className="px-2">
          {quizzes.length > 0 ? (
            quizzes
              .slice(0, 3)
              .map((quiz, quizIndex) => (
                <CategoryCardData test={quiz} key={quiz._id || quizIndex} creationSubmissionsDialog={creationSubmissionsDialog} />
              ))
          ) : (
            <p className="text-gray-500">No test available</p>
          )}
        </div>

        {quizzes.length > 3 && (
          <div className="absolute left-0 bottom-4">
            <button
              className="hover:underline pt-4 pl-4 text-base text-darkBlueText dark:text-gray-400"
              onClick={() => showMoreInfo(blockIndex, subBlockIndex)}
            >
              View All
            </button>
          </div>
        )}
      </div>
    </Card>
  );
};
