import React from 'react';

import { Dialog } from '/src';

import { WeiredBehavior } from './weired-behavior';

export const WeirdBehaviorSingleDialog = ({ onClose, onCreate, id, stage }) => {
  return (
    <Dialog size="lg" show popup modalHeader={'Weird Behavior Analysis'} onClose={onClose}>
      <div className="dark:text-white">{stage?.weirdBehavior?.length ? <WeiredBehavior stage={stage} /> : <p>No Data</p>}</div>
    </Dialog>
  );
};
