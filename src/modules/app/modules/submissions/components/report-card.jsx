// UI
import { EnumText } from '/src';

// React PDF
import { PDFViewer, Document, Page, View, StyleSheet, Text, Image as PDFImage, Font, Svg, G, Path, Rect } from '@react-pdf/renderer';

// Fonts weight
import Thin from './font/Inter-Thin.otf';
import Extralight from './font/Inter-ExtraLight.otf';
import Light from './font/Inter-Light.otf';
import Inter from './font/Inter-Regular.otf';
import Medium from './font/Inter-Medium.otf';
import Semibold from './font/Inter-SemiBold.otf';
import Bold from './font/Inter-Bold.otf';
import Extrabold from './font/Inter-ExtraBold.otf';
import Black from './font/Inter-Black.otf';
// Fonts style
import Italic from './font/Inter-Italic.otf';

const headerIcon = {
  submissions: (
    <Svg width="30" height="30" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <Path
        d="M30.833 3.33331H4.16634C3.7061 3.33331 3.33301 3.70641 3.33301 4.16665V35.8333C3.33301 36.2935 3.7061 36.6666 4.16634 36.6666H30.833C31.2932 36.6666 31.6663 36.2935 31.6663 35.8333V4.16665C31.6663 3.70641 31.2932 3.33331 30.833 3.33331Z"
        fill="#E6E6E6"
      />
      <Path
        d="M23.3163 3.33331C22.9598 15.5773 18.4421 27.3344 10.508 36.6666H4.16634C3.94553 36.666 3.73395 36.578 3.57781 36.4218C3.42168 36.2657 3.33367 36.0541 3.33301 35.8333V4.16665C3.33367 3.94584 3.42168 3.73426 3.57781 3.57812C3.73395 3.42198 3.94553 3.33397 4.16634 3.33331H23.3163Z"
        fill="#F2F2F2"
      />
      <Path
        d="M31.6663 4.16665V8.33331H3.33301V4.16665C3.33367 3.94584 3.42168 3.73426 3.57781 3.57812C3.73395 3.42198 3.94553 3.33397 4.16634 3.33331H30.833C31.0538 3.33397 31.2654 3.42198 31.4215 3.57812C31.5777 3.73426 31.6657 3.94584 31.6663 4.16665Z"
        fill="#1A1A1A"
      />
      <Path
        d="M23.3163 3.33331C23.2663 5.01665 23.1386 6.68331 22.933 8.33331H3.33301V4.16665C3.33367 3.94584 3.42168 3.73426 3.57781 3.57812C3.73395 3.42198 3.94553 3.33397 4.16634 3.33331H23.3163Z"
        fill="#333333"
      />
      <Path
        d="M8.33333 6.66667C8.79357 6.66667 9.16667 6.29357 9.16667 5.83333C9.16667 5.3731 8.79357 5 8.33333 5C7.8731 5 7.5 5.3731 7.5 5.83333C7.5 6.29357 7.8731 6.66667 8.33333 6.66667Z"
        fill="#FF5543"
      />
      <Path
        d="M11.6663 6.66667C12.1266 6.66667 12.4997 6.29357 12.4997 5.83333C12.4997 5.3731 12.1266 5 11.6663 5C11.2061 5 10.833 5.3731 10.833 5.83333C10.833 6.29357 11.2061 6.66667 11.6663 6.66667Z"
        fill="#FFC239"
      />
      <Path
        d="M15.0003 6.66667C15.4606 6.66667 15.8337 6.29357 15.8337 5.83333C15.8337 5.3731 15.4606 5 15.0003 5C14.5401 5 14.167 5.3731 14.167 5.83333C14.167 6.29357 14.5401 6.66667 15.0003 6.66667Z"
        fill="#34D143"
      />
      <Path
        d="M36.6664 16.6667C36.6653 17.9924 36.1382 19.2635 35.2007 20.201C34.2633 21.1384 32.9922 21.6655 31.6664 21.6667C30.775 21.6692 29.8995 21.4302 29.1331 20.975C28.1869 20.4193 27.4499 19.5677 27.0357 18.5515C26.6216 17.5354 26.5533 16.4112 26.8414 15.3524C27.1296 14.2936 27.7582 13.3591 28.6302 12.6929C29.5022 12.0268 30.5691 11.6662 31.6664 11.6666C32.4139 11.6643 33.1521 11.8325 33.8248 12.1583C34.6753 12.5654 35.3935 13.2046 35.8963 14.0023C36.3991 14.8001 36.6661 15.7237 36.6664 16.6667Z"
        fill="#FFC239"
      />
      <Path
        d="M33.8498 12.8167C33.8495 14.471 33.4134 16.0961 32.5854 17.5283C31.7574 18.9605 30.5666 20.1493 29.1331 20.975C28.1869 20.4193 27.4499 19.5677 27.0357 18.5515C26.6216 17.5354 26.5533 16.4112 26.8414 15.3524C27.1296 14.2936 27.7582 13.3591 28.6302 12.6929C29.5022 12.0268 30.5691 11.6662 31.6664 11.6666C32.4139 11.6643 33.1521 11.8325 33.8248 12.1583C33.8414 12.375 33.8498 12.5917 33.8498 12.8167Z"
        fill="#FFE266"
      />
      <Path
        d="M33.333 17.5H31.6663C31.4453 17.5 31.2334 17.4122 31.0771 17.2559C30.9208 17.0997 30.833 16.8877 30.833 16.6667V15C30.833 14.779 30.9208 14.567 31.0771 14.4108C31.2334 14.2545 31.4453 14.1667 31.6663 14.1667C31.8874 14.1667 32.0993 14.2545 32.2556 14.4108C32.4119 14.567 32.4997 14.779 32.4997 15V15.8334H33.333C33.554 15.8334 33.766 15.9212 33.9223 16.0774C34.0785 16.2337 34.1663 16.4457 34.1663 16.6667C34.1663 16.8877 34.0785 17.0997 33.9223 17.2559C33.766 17.4122 33.554 17.5 33.333 17.5Z"
        fill="#333333"
      />
      <Path
        d="M10.8337 11.6667H7.50033C7.04009 11.6667 6.66699 12.0398 6.66699 12.5V15.8334C6.66699 16.2936 7.04009 16.6667 7.50033 16.6667H10.8337C11.2939 16.6667 11.667 16.2936 11.667 15.8334V12.5C11.667 12.0398 11.2939 11.6667 10.8337 11.6667Z"
        fill="#02BFE2"
      />
      <Path
        d="M10.0253 11.6667C9.80903 13.5623 8.95032 15.3268 7.59199 16.6667H7.50033C7.27951 16.666 7.06794 16.578 6.9118 16.4219C6.75566 16.2657 6.66765 16.0542 6.66699 15.8334V12.5C6.66765 12.2792 6.75566 12.0676 6.9118 11.9115C7.06794 11.7554 7.27951 11.6673 7.50033 11.6667H10.0253Z"
        fill="#4FD7F7"
      />
      <Path
        d="M25.0003 13.3334H15.0003C14.7793 13.3334 14.5673 13.2456 14.4111 13.0893C14.2548 12.933 14.167 12.721 14.167 12.5C14.167 12.279 14.2548 12.067 14.4111 11.9108C14.5673 11.7545 14.7793 11.6667 15.0003 11.6667H25.0003C25.2213 11.6667 25.4333 11.7545 25.5896 11.9108C25.7459 12.067 25.8337 12.279 25.8337 12.5C25.8337 12.721 25.7459 12.933 25.5896 13.0893C25.4333 13.2456 25.2213 13.3334 25.0003 13.3334ZM23.3337 16.6667H15.0003C14.7793 16.6667 14.5673 16.5789 14.4111 16.4226C14.2548 16.2663 14.167 16.0544 14.167 15.8334C14.167 15.6123 14.2548 15.4004 14.4111 15.2441C14.5673 15.0878 14.7793 15 15.0003 15H23.3337C23.5547 15 23.7666 15.0878 23.9229 15.2441C24.0792 15.4004 24.167 15.6123 24.167 15.8334C24.167 16.0544 24.0792 16.2663 23.9229 16.4226C23.7666 16.5789 23.5547 16.6667 23.3337 16.6667Z"
        fill="#333333"
      />
      <Path
        d="M10.8337 20H7.50033C7.04009 20 6.66699 20.3731 6.66699 20.8333V24.1667C6.66699 24.6269 7.04009 25 7.50033 25H10.8337C11.2939 25 11.667 24.6269 11.667 24.1667V20.8333C11.667 20.3731 11.2939 20 10.8337 20Z"
        fill="#02BFE2"
      />
      <Path
        d="M10.0253 20C9.80903 21.8957 8.95032 23.6601 7.59199 25H7.50033C7.27951 24.9993 7.06794 24.9113 6.9118 24.7552C6.75566 24.5991 6.66765 24.3875 6.66699 24.1667V20.8333C6.66765 20.6125 6.75566 20.4009 6.9118 20.2448C7.06794 20.0887 7.27951 20.0007 7.50033 20H10.0253Z"
        fill="#4FD7F7"
      />
      <Path
        d="M25.0003 21.6667H15.0003C14.7793 21.6667 14.5673 21.5789 14.4111 21.4226C14.2548 21.2663 14.167 21.0543 14.167 20.8333C14.167 20.6123 14.2548 20.4004 14.4111 20.2441C14.5673 20.0878 14.7793 20 15.0003 20H25.0003C25.2213 20 25.4333 20.0878 25.5896 20.2441C25.7459 20.4004 25.8337 20.6123 25.8337 20.8333C25.8337 21.0543 25.7459 21.2663 25.5896 21.4226C25.4333 21.5789 25.2213 21.6667 25.0003 21.6667ZM25.0003 25H15.0003C14.7793 25 14.5673 24.9122 14.4111 24.7559C14.2548 24.5996 14.167 24.3877 14.167 24.1667C14.167 23.9457 14.2548 23.7337 14.4111 23.5774C14.5673 23.4211 14.7793 23.3333 15.0003 23.3333H25.0003C25.2213 23.3333 25.4333 23.4211 25.5896 23.5774C25.7459 23.7337 25.8337 23.9457 25.8337 24.1667C25.8337 24.3877 25.7459 24.5996 25.5896 24.7559C25.4333 24.9122 25.2213 25 25.0003 25Z"
        fill="#333333"
      />
      <Path
        d="M10.8337 28.3333H7.50033C7.04009 28.3333 6.66699 28.7064 6.66699 29.1666V32.5C6.66699 32.9602 7.04009 33.3333 7.50033 33.3333H10.8337C11.2939 33.3333 11.667 32.9602 11.667 32.5V29.1666C11.667 28.7064 11.2939 28.3333 10.8337 28.3333Z"
        fill="#02BFE2"
      />
      <Path
        d="M10.0253 28.3333C9.80903 30.229 8.95032 31.9934 7.59199 33.3333H7.50033C7.27951 33.3327 7.06794 33.2446 6.9118 33.0885C6.75566 32.9324 6.66765 32.7208 6.66699 32.5V29.1666C6.66765 28.9458 6.75566 28.7343 6.9118 28.5781C7.06794 28.422 7.27951 28.334 7.50033 28.3333H10.0253Z"
        fill="#4FD7F7"
      />
      <Path
        d="M25.0003 30H15.0003C14.7793 30 14.5673 29.9122 14.4111 29.7559C14.2548 29.5996 14.167 29.3877 14.167 29.1666C14.167 28.9456 14.2548 28.7337 14.4111 28.5774C14.5673 28.4211 14.7793 28.3333 15.0003 28.3333H25.0003C25.2213 28.3333 25.4333 28.4211 25.5896 28.5774C25.7459 28.7337 25.8337 28.9456 25.8337 29.1666C25.8337 29.3877 25.7459 29.5996 25.5896 29.7559C25.4333 29.9122 25.2213 30 25.0003 30ZM25.0003 33.3333H15.0003C14.7793 33.3333 14.5673 33.2455 14.4111 33.0892C14.2548 32.933 14.167 32.721 14.167 32.5C14.167 32.279 14.2548 32.067 14.4111 31.9107C14.5673 31.7544 14.7793 31.6666 15.0003 31.6666H25.0003C25.2213 31.6666 25.4333 31.7544 25.5896 31.9107C25.7459 32.067 25.8337 32.279 25.8337 32.5C25.8337 32.721 25.7459 32.933 25.5896 33.0892C25.4333 33.2455 25.2213 33.3333 25.0003 33.3333Z"
        fill="#333333"
      />
      <Path
        d="M30.8333 37.5H4.16667C3.72477 37.4996 3.30111 37.3238 2.98864 37.0114C2.67618 36.6989 2.50044 36.2752 2.5 35.8333V4.16667C2.50044 3.72477 2.67618 3.30111 2.98864 2.98864C3.30111 2.67618 3.72477 2.50044 4.16667 2.5H30.8333C31.2752 2.50044 31.6989 2.67618 32.0114 2.98864C32.3238 3.30111 32.4996 3.72477 32.5 4.16667V11.6667C32.5 11.8877 32.4122 12.0996 32.2559 12.2559C32.0996 12.4122 31.8877 12.5 31.6667 12.5C31.4457 12.5 31.2337 12.4122 31.0774 12.2559C30.9211 12.0996 30.8333 11.8877 30.8333 11.6667V4.16667H4.16667V35.8333H30.8333V21.6667C30.8333 21.4457 30.9211 21.2337 31.0774 21.0774C31.2337 20.9211 31.4457 20.8333 31.6667 20.8333C31.8877 20.8333 32.0996 20.9211 32.2559 21.0774C32.4122 21.2337 32.5 21.4457 32.5 21.6667V35.8333C32.4996 36.2752 32.3238 36.6989 32.0114 37.0114C31.6989 37.3238 31.2752 37.4996 30.8333 37.5Z"
        fill="black"
      />
      <Path
        d="M31.6667 9.16667H3.33333C3.11232 9.16667 2.90036 9.07887 2.74408 8.92259C2.5878 8.76631 2.5 8.55435 2.5 8.33333C2.5 8.11232 2.5878 7.90036 2.74408 7.74408C2.90036 7.5878 3.11232 7.5 3.33333 7.5H31.6667C31.8877 7.5 32.0996 7.5878 32.2559 7.74408C32.4122 7.90036 32.5 8.11232 32.5 8.33333C32.5 8.55435 32.4122 8.76631 32.2559 8.92259C32.0996 9.07887 31.8877 9.16667 31.6667 9.16667Z"
        fill="black"
      />
      <Path
        d="M8.33333 6.66667C8.79357 6.66667 9.16667 6.29357 9.16667 5.83333C9.16667 5.3731 8.79357 5 8.33333 5C7.8731 5 7.5 5.3731 7.5 5.83333C7.5 6.29357 7.8731 6.66667 8.33333 6.66667Z"
        fill="black"
      />
      <Path
        d="M11.6663 6.66667C12.1266 6.66667 12.4997 6.29357 12.4997 5.83333C12.4997 5.3731 12.1266 5 11.6663 5C11.2061 5 10.833 5.3731 10.833 5.83333C10.833 6.29357 11.2061 6.66667 11.6663 6.66667Z"
        fill="black"
      />
      <Path
        d="M15.0003 6.66667C15.4606 6.66667 15.8337 6.29357 15.8337 5.83333C15.8337 5.3731 15.4606 5 15.0003 5C14.5401 5 14.167 5.3731 14.167 5.83333C14.167 6.29357 14.5401 6.66667 15.0003 6.66667Z"
        fill="black"
      />
      <Path
        d="M31.6663 22.5C30.5126 22.5 29.3848 22.1579 28.4255 21.5169C27.4662 20.8759 26.7186 19.9649 26.277 18.899C25.8355 17.8331 25.72 16.6602 25.9451 15.5286C26.1702 14.3971 26.7257 13.3577 27.5416 12.5419C28.3574 11.7261 29.3968 11.1705 30.5283 10.9454C31.6599 10.7203 32.8328 10.8358 33.8987 11.2774C34.9646 11.7189 35.8756 12.4665 36.5166 13.4258C37.1576 14.3851 37.4997 15.5129 37.4997 16.6666C37.4979 18.2132 36.8828 19.6959 35.7892 20.7895C34.6956 21.8831 33.2129 22.4982 31.6663 22.5ZM31.6663 12.5C30.8423 12.5 30.0367 12.7444 29.3515 13.2022C28.6663 13.66 28.1322 14.3108 27.8168 15.0721C27.5015 15.8335 27.419 16.6713 27.5797 17.4795C27.7405 18.2878 28.1373 19.0302 28.7201 19.6129C29.3028 20.1956 30.0452 20.5925 30.8535 20.7533C31.6617 20.914 32.4995 20.8315 33.2609 20.5161C34.0222 20.2008 34.673 19.6667 35.1308 18.9815C35.5886 18.2963 35.833 17.4907 35.833 16.6666C35.8317 15.562 35.3923 14.5029 34.6112 13.7218C33.83 12.9407 32.771 12.5013 31.6663 12.5Z"
        fill="black"
      />
      <Path
        d="M33.333 17.5H31.6663C31.4453 17.5 31.2334 17.4122 31.0771 17.2559C30.9208 17.0996 30.833 16.8877 30.833 16.6666V15C30.833 14.779 30.9208 14.567 31.0771 14.4107C31.2334 14.2544 31.4453 14.1666 31.6663 14.1666C31.8874 14.1666 32.0993 14.2544 32.2556 14.4107C32.4119 14.567 32.4997 14.779 32.4997 15V15.8333H33.333C33.554 15.8333 33.766 15.9211 33.9223 16.0774C34.0785 16.2337 34.1663 16.4456 34.1663 16.6666C34.1663 16.8877 34.0785 17.0996 33.9223 17.2559C33.766 17.4122 33.554 17.5 33.333 17.5ZM10.833 17.5H7.49967C7.05778 17.4995 6.63412 17.3238 6.32165 17.0113C6.00919 16.6989 5.83345 16.2752 5.83301 15.8333V12.5C5.83345 12.0581 6.00919 11.6344 6.32165 11.322C6.63412 11.0095 7.05778 10.8338 7.49967 10.8333H10.833C11.2749 10.8338 11.6986 11.0095 12.011 11.322C12.3235 11.6344 12.4992 12.0581 12.4997 12.5V15.8333C12.4992 16.2752 12.3235 16.6989 12.011 17.0113C11.6986 17.3238 11.2749 17.4995 10.833 17.5ZM7.49967 12.5V15.8333H10.8347L10.833 12.5H7.49967ZM24.9997 13.3333H14.9997C14.7787 13.3333 14.5667 13.2455 14.4104 13.0892C14.2541 12.933 14.1663 12.721 14.1663 12.5C14.1663 12.279 14.2541 12.067 14.4104 11.9107C14.5667 11.7544 14.7787 11.6666 14.9997 11.6666H24.9997C25.2207 11.6666 25.4327 11.7544 25.5889 11.9107C25.7452 12.067 25.833 12.279 25.833 12.5C25.833 12.721 25.7452 12.933 25.5889 13.0892C25.4327 13.2455 25.2207 13.3333 24.9997 13.3333ZM23.333 16.6666H14.9997C14.7787 16.6666 14.5667 16.5788 14.4104 16.4226C14.2541 16.2663 14.1663 16.0543 14.1663 15.8333C14.1663 15.6123 14.2541 15.4003 14.4104 15.2441C14.5667 15.0878 14.7787 15 14.9997 15H23.333C23.554 15 23.766 15.0878 23.9223 15.2441C24.0785 15.4003 24.1663 15.6123 24.1663 15.8333C24.1663 16.0543 24.0785 16.2663 23.9223 16.4226C23.766 16.5788 23.554 16.6666 23.333 16.6666ZM10.833 25.8333H7.49967C7.05778 25.8329 6.63412 25.6571 6.32165 25.3447C6.00919 25.0322 5.83345 24.6085 5.83301 24.1666V20.8333C5.83345 20.3914 6.00919 19.9678 6.32165 19.6553C6.63412 19.3428 7.05778 19.1671 7.49967 19.1666H10.833C11.2749 19.1671 11.6986 19.3428 12.011 19.6553C12.3235 19.9678 12.4992 20.3914 12.4997 20.8333V24.1666C12.4992 24.6085 12.3235 25.0322 12.011 25.3447C11.6986 25.6571 11.2749 25.8329 10.833 25.8333ZM7.49967 20.8333V24.1666H10.8347L10.833 20.8333H7.49967ZM24.9997 21.6666H14.9997C14.7787 21.6666 14.5667 21.5788 14.4104 21.4226C14.2541 21.2663 14.1663 21.0543 14.1663 20.8333C14.1663 20.6123 14.2541 20.4003 14.4104 20.2441C14.5667 20.0878 14.7787 20 14.9997 20H24.9997C25.2207 20 25.4327 20.0878 25.5889 20.2441C25.7452 20.4003 25.833 20.6123 25.833 20.8333C25.833 21.0543 25.7452 21.2663 25.5889 21.4226C25.4327 21.5788 25.2207 21.6666 24.9997 21.6666ZM24.9997 25H14.9997C14.7787 25 14.5667 24.9122 14.4104 24.7559C14.2541 24.5996 14.1663 24.3877 14.1663 24.1666C14.1663 23.9456 14.2541 23.7337 14.4104 23.5774C14.5667 23.4211 14.7787 23.3333 14.9997 23.3333H24.9997C25.2207 23.3333 25.4327 23.4211 25.5889 23.5774C25.7452 23.7337 25.833 23.9456 25.833 24.1666C25.833 24.3877 25.7452 24.5996 25.5889 24.7559C25.4327 24.9122 25.2207 25 24.9997 25ZM10.833 34.1666H7.49967C7.05778 34.1662 6.63412 33.9905 6.32165 33.678C6.00919 33.3655 5.83345 32.9419 5.83301 32.5V29.1666C5.83345 28.7248 6.00919 28.3011 6.32165 27.9886C6.63412 27.6762 7.05778 27.5004 7.49967 27.5H10.833C11.2749 27.5004 11.6986 27.6762 12.011 27.9886C12.3235 28.3011 12.4992 28.7248 12.4997 29.1666V32.5C12.4992 32.9419 12.3235 33.3655 12.011 33.678C11.6986 33.9905 11.2749 34.1662 10.833 34.1666ZM7.49967 29.1666V32.5H10.8347L10.833 29.1666H7.49967ZM24.9997 30H14.9997C14.7787 30 14.5667 29.9122 14.4104 29.7559C14.2541 29.5996 14.1663 29.3877 14.1663 29.1666C14.1663 28.9456 14.2541 28.7337 14.4104 28.5774C14.5667 28.4211 14.7787 28.3333 14.9997 28.3333H24.9997C25.2207 28.3333 25.4327 28.4211 25.5889 28.5774C25.7452 28.7337 25.833 28.9456 25.833 29.1666C25.833 29.3877 25.7452 29.5996 25.5889 29.7559C25.4327 29.9122 25.2207 30 24.9997 30ZM24.9997 33.3333H14.9997C14.7787 33.3333 14.5667 33.2455 14.4104 33.0892C14.2541 32.933 14.1663 32.721 14.1663 32.5C14.1663 32.279 14.2541 32.067 14.4104 31.9107C14.5667 31.7544 14.7787 31.6666 14.9997 31.6666H24.9997C25.2207 31.6666 25.4327 31.7544 25.5889 31.9107C25.7452 32.067 25.833 32.279 25.833 32.5C25.833 32.721 25.7452 32.933 25.5889 33.0892C25.4327 33.2455 25.2207 33.3333 24.9997 33.3333Z"
        fill="black"
      />
    </Svg>
  ),
  interviews: (
    <Svg width="40" height="30" viewBox="0 10 46 30" fill="none" xmlns="http://www.w3.org/2000/svg">
      <Path
        d="M23.5824 22.5547C23.6421 22.4402 23.6742 22.3112 23.6758 22.1797V8.89844H42.4568V21.4219H26.6946C26.6035 21.4216 26.5133 21.4402 26.4286 21.4766L25.8752 21.7188L25.7746 21.7656L23.3955 22.8516L23.5824 22.5547ZM39.0427 13.0625C39.0427 12.8553 38.967 12.6566 38.8322 12.5101C38.6974 12.3636 38.5146 12.2813 38.3239 12.2813H28.0314C27.9255 12.2524 27.8148 12.2502 27.7079 12.2747C27.601 12.2993 27.5007 12.3501 27.4146 12.4231C27.3284 12.4962 27.2587 12.5896 27.2108 12.6964C27.1629 12.8031 27.138 12.9203 27.138 13.0391C27.138 13.1578 27.1629 13.275 27.2108 13.3818C27.2587 13.4885 27.3284 13.5819 27.4146 13.655C27.5007 13.7281 27.601 13.7788 27.7079 13.8034C27.8148 13.828 27.9255 13.8257 28.0314 13.7969H38.3527C38.5311 13.7895 38.7005 13.7102 38.8281 13.5744C38.9556 13.4387 39.0321 13.2562 39.0427 13.0625ZM39.0427 16.9688C39.0427 16.7615 38.967 16.5628 38.8322 16.4163C38.6974 16.2698 38.5146 16.1875 38.3239 16.1875H28.0314C27.876 16.2298 27.7381 16.3274 27.6395 16.4647C27.541 16.602 27.4874 16.7712 27.4874 16.9453C27.4874 17.1195 27.541 17.2886 27.6395 17.4259C27.7381 17.5632 27.876 17.6608 28.0314 17.7031H38.3527C38.5276 17.6963 38.6943 17.6203 38.8213 17.4894C38.9483 17.3585 39.0271 17.1817 39.0427 16.9922V16.9688Z"
        fill="#F7AB3E"
      />
      <Path
        d="M20.7864 34.375C20.7826 34.6947 20.8369 35.0121 20.9462 35.309C21.0554 35.6059 21.2176 35.8764 21.4232 36.105C21.6289 36.3336 21.8741 36.5159 22.1447 36.6413C22.4152 36.7667 22.706 36.8329 23.0001 36.8359H26.5939C27.1855 36.8257 27.7492 36.5608 28.1615 36.0995C28.5737 35.6382 28.8009 35.0181 28.7932 34.375V33.1406C28.7915 32.5036 28.5603 31.8926 28.1492 31.4384C27.7382 30.9843 27.1799 30.7232 26.5939 30.7109H23.0001C22.4073 30.7109 21.8387 30.9669 21.4195 31.4226C21.0003 31.8782 20.7648 32.4962 20.7648 33.1406L20.7864 34.375ZM17.0345 39.4531L20.9014 39.3906C21.0568 39.3483 21.1947 39.2507 21.2933 39.1134C21.3919 38.9761 21.4454 38.807 21.4454 38.6328C21.4454 38.4587 21.3919 38.2895 21.2933 38.1522C21.1947 38.0149 21.0568 37.9173 20.9014 37.875L17.0345 37.9453C16.8822 37.9903 16.7478 38.0885 16.652 38.2247C16.5562 38.361 16.5042 38.5277 16.5042 38.6992C16.5042 38.8707 16.5562 39.0375 16.652 39.1737C16.7478 39.31 16.8822 39.4082 17.0345 39.4531ZM14.9142 36.8359C15.2102 36.836 15.5032 36.7721 15.7763 36.648C16.0494 36.524 16.2971 36.3422 16.505 36.1133C16.713 35.8844 16.877 35.6128 16.9877 35.3144C17.0983 35.016 17.1533 34.6967 17.1495 34.375V33.1406C17.1476 32.4969 16.9115 31.8801 16.4927 31.4249C16.0739 30.9696 15.5064 30.713 14.9142 30.7109H11.3779C10.7851 30.7109 10.2165 30.9669 9.79733 31.4226C9.37813 31.8782 9.14262 32.4962 9.14262 33.1406V34.375C9.14262 35.0194 9.37813 35.6374 9.79733 36.0931C10.2165 36.5487 10.7851 36.8047 11.3779 36.8047L14.9142 36.8359ZM31.7689 41.1172H6.167V32.4063C6.16605 31.1071 6.40077 29.8205 6.85772 28.6201C7.31467 27.4196 7.98489 26.3289 8.83003 25.4102C9.67518 24.4916 10.6787 23.7631 11.7831 23.2664C12.8875 22.7697 14.0712 22.5146 15.2664 22.5156H21.9939L21.1745 24.0781C21.1054 24.2158 21.0759 24.3728 21.09 24.5289C21.1041 24.6849 21.1611 24.8327 21.2536 24.9531C21.3489 25.0732 21.477 25.157 21.6193 25.1921C21.7616 25.2273 21.9105 25.212 22.0442 25.1484L26.1123 23.2891C27.7766 24.034 29.2014 25.2968 30.2077 26.9188C31.2141 28.5409 31.7572 30.4499 31.7689 32.4063V41.1172Z"
        fill="#D8D8D8"
      />
      <Path
        d="M27.3984 33.1406V34.375C27.4032 34.4951 27.3862 34.615 27.3483 34.7279C27.3104 34.8408 27.2525 34.9445 27.1777 35.033C27.1029 35.1215 27.0128 35.1932 26.9126 35.2438C26.8123 35.2945 26.7038 35.3231 26.5934 35.3281H22.9996C22.8876 35.3251 22.7772 35.298 22.6749 35.2483C22.5726 35.1987 22.4804 35.1275 22.4035 35.0389C22.3267 34.9502 22.2667 34.8459 22.2272 34.732C22.1876 34.6181 22.1692 34.4967 22.173 34.375V33.1406C22.1748 32.9015 22.2621 32.6725 22.4163 32.5019C22.5705 32.3314 22.7797 32.2326 22.9996 32.2266H26.5934C26.8101 32.2366 27.0149 32.3374 27.1649 32.5077C27.3149 32.6781 27.3986 32.9048 27.3984 33.1406ZM15.7546 33.1406V34.375C15.7556 34.4957 15.7345 34.6154 15.6927 34.7272C15.6508 34.839 15.589 34.9407 15.5109 35.0264C15.4327 35.1121 15.3397 35.1801 15.2372 35.2266C15.1347 35.273 15.0247 35.2969 14.9137 35.2969H11.3774C11.1525 35.2969 10.9368 35.1997 10.7777 35.0269C10.6187 34.854 10.5293 34.6195 10.5293 34.375V33.1406C10.5312 32.8975 10.6214 32.665 10.7802 32.4938C10.9391 32.3226 11.1537 32.2266 11.3774 32.2266H14.9137C15.0241 32.2266 15.1335 32.2502 15.2355 32.2961C15.3375 32.3421 15.4302 32.4094 15.5083 32.4943C15.5864 32.5792 15.6483 32.6799 15.6906 32.7908C15.7329 32.9017 15.7546 33.0206 15.7546 33.1406Z"
        fill="#9BECFF"
      />
      <Path
        d="M4.31274 30.1562H4.9668C4.82916 30.8969 4.75934 31.6506 4.75836 32.4063V35.9375C4.40915 35.8522 4.09709 35.6401 3.87366 35.3362C3.65023 35.0323 3.52877 34.6547 3.5293 34.2656V30.1562H4.31274Z"
        fill="#D8D8D8"
      />
      <Path
        d="M43.125 7.37501H23C22.8094 7.37501 22.6266 7.45732 22.4918 7.60383C22.357 7.75034 22.2812 7.94906 22.2812 8.15626V21.0078H15.2662C13.0933 21.0176 10.9762 21.757 9.20457 23.1247C7.43296 24.4923 6.09337 26.4216 5.36906 28.6484H5.03125L5.10312 22.3984C5.37747 22.2218 5.58999 21.951 5.70723 21.6286C5.82447 21.3063 5.83978 20.9507 5.75075 20.6179C5.66171 20.2851 5.47339 19.9939 5.21542 19.7901C4.95745 19.5864 4.64447 19.4816 4.32575 19.4924C4.00702 19.5032 3.70063 19.6288 3.4548 19.8496C3.20897 20.0704 3.03766 20.3737 2.96781 20.7119C2.89797 21.0501 2.93357 21.404 3.069 21.7178C3.20444 22.0316 3.43202 22.2875 3.71594 22.4453L3.64406 28.6953H2.875C2.68438 28.6953 2.50156 28.7776 2.36677 28.9241C2.23198 29.0707 2.15625 29.2694 2.15625 29.4766V34.3516C2.17007 35.131 2.44239 35.8787 2.9231 36.4572C3.40381 37.0357 4.06056 37.406 4.7725 37.5V41.875C4.7725 42.0822 4.84823 42.2809 4.98302 42.4274C5.11781 42.5739 5.30063 42.6563 5.49125 42.6563H32.4659C32.6566 42.6563 32.8394 42.5739 32.9742 42.4274C33.109 42.2809 33.1847 42.0822 33.1847 41.875V32.4063C33.1805 30.5331 32.7537 28.6897 31.9417 27.0375C31.1297 25.3853 29.9573 23.9749 28.5272 22.9297H43.125C43.3156 22.9297 43.4984 22.8474 43.6332 22.7009C43.768 22.5544 43.8438 22.3556 43.8438 22.1484V8.08594C43.8276 7.89116 43.7447 7.71006 43.6117 7.57843C43.4786 7.4468 43.3049 7.37421 43.125 7.37501ZM4.37 20.9531C4.38716 20.9531 4.40361 20.9605 4.41574 20.9737C4.42787 20.9869 4.43469 21.0048 4.43469 21.0234C4.43469 21.1016 4.30531 21.1016 4.30531 21.0234C4.30531 21.0048 4.31213 20.9869 4.32426 20.9737C4.33639 20.9605 4.35284 20.9531 4.37 20.9531ZM3.54344 34.2813V30.1563H4.98094C4.84093 30.8967 4.7687 31.6504 4.76531 32.4063V35.9375C4.42043 35.85 4.11254 35.6393 3.89099 35.3389C3.66944 35.0386 3.54705 34.6662 3.54344 34.2813ZM31.7688 32.4063V41.1172H6.16687V32.4063C6.16593 31.1071 6.40065 29.8205 6.8576 28.6201C7.31455 27.4196 7.98477 26.3289 8.82991 25.4102C9.67506 24.4916 10.6785 23.7631 11.783 23.2664C12.8874 22.7697 14.071 22.5146 15.2662 22.5156H21.9937L21.1744 24.0781C21.1052 24.2158 21.0758 24.3728 21.0899 24.5289C21.104 24.6849 21.161 24.8328 21.2534 24.9531C21.3487 25.0732 21.4769 25.157 21.6192 25.1921C21.7615 25.2273 21.9104 25.212 22.0441 25.1484L26.1194 23.2891C27.7818 24.0361 29.2045 25.2996 30.2094 26.9213C31.2143 28.5431 31.7567 30.451 31.7688 32.4063ZM42.4566 21.4219H26.6944C26.6033 21.4216 26.5131 21.4402 26.4284 21.4766L25.8391 21.75L23.4456 22.8438L23.6038 22.5469C23.6635 22.4324 23.6956 22.3034 23.6972 22.1719V8.89063H42.4566V21.4219Z"
        fill="black"
      />
      <Path
        d="M28.0312 13.8203H38.3524C38.5078 13.778 38.6458 13.6804 38.7443 13.5431C38.8429 13.4058 38.8964 13.2367 38.8964 13.0625C38.8964 12.8883 38.8429 12.7192 38.7443 12.5819C38.6458 12.4446 38.5078 12.347 38.3524 12.3047H28.0312C27.9252 12.2758 27.8145 12.2736 27.7076 12.2982C27.6008 12.3228 27.5004 12.3735 27.4143 12.4466C27.3281 12.5196 27.2585 12.6131 27.2105 12.7198C27.1626 12.8265 27.1377 12.9437 27.1377 13.0625C27.1377 13.1813 27.1626 13.2985 27.2105 13.4052C27.2585 13.5119 27.3281 13.6054 27.4143 13.6784C27.5004 13.7515 27.6008 13.8022 27.7076 13.8268C27.8145 13.8514 27.9252 13.8492 28.0312 13.8203ZM28.0312 17.75H38.3524C38.5078 17.7077 38.6458 17.6101 38.7443 17.4728C38.8429 17.3355 38.8964 17.1663 38.8964 16.9922C38.8964 16.818 38.8429 16.6489 38.7443 16.5116C38.6458 16.3743 38.5078 16.2767 38.3524 16.2344H28.0312C27.8758 16.2767 27.7378 16.3743 27.6393 16.5116C27.5407 16.6489 27.4872 16.818 27.4872 16.9922C27.4872 17.1663 27.5407 17.3355 27.6393 17.4728C27.7378 17.6101 27.8758 17.7077 28.0312 17.75Z"
        fill="black"
      />
    </Svg>
  ),
};

export const ReportCard = ({ details, propertyKeyObject }) => {
  Font.register({
    family: 'Inter',
    fonts: [
      { src: Thin, fontWeight: 'thin' }, // 100
      { src: Extralight, fontWeight: 'extralight' }, // 200
      { src: Light, fontWeight: 'light' }, // 300
      { src: Inter, fontWeight: 'normal' }, // 400
      { src: Medium, fontWeight: 'medium' }, // 500
      { src: Semibold, fontWeight: 'semibold' }, // 600
      { src: Bold, fontWeight: 'bold' }, // 700
      { src: Extrabold, fontWeight: 'extrabold' }, // 800
      { src: Black, fontWeight: 'black' }, // 900
      { src: Italic, fontWeight: 'normal', fontStyle: 'italic' },
    ],
  });

  const styles = StyleSheet.create({
    breakDownText: {
      fontSize: 10,
      color: '#6B7280',
      fontWeight: 500,
    },
    breakDownContainer: {
      display: 'flex',
      flexDirection: 'row',
      gap: 5,
      alignItems: 'center',
    },
  });

  const SubCategoryChart = ({ percentage }) => {
    const remainingPercentage = 100 - percentage;

    return (
      <View style={{ width: '100%', display: 'flex', flexDirection: 'row', gap: 7 }}>
        {percentage > 0 && (
          <View
            style={{
              width: `${percentage}%]`,
              backgroundColor: `${percentage > 80 ? '#4AA264' : percentage >= 50 ? '#DDB856' : '#DC7E83'}`,
              height: 7,
              borderRadius: 1,
            }}
          />
        )}

        {remainingPercentage > 0 && <View style={{ width: `${remainingPercentage}%]`, backgroundColor: '#D9D9D9', height: 7, borderRadius: 1 }} />}
      </View>
    );
  };

  const HalfFilledCircleBorder = ({ percentage }) => {
    const radius = 80;
    const strokeWidth = 15;
    const circumference = Math.PI * radius * 2;
    const dashLength = (percentage / 100) * circumference;
    const gapLength = circumference - dashLength;
    const startingPoint = { x: 0, y: -radius };
    const circlePath = `
    M ${startingPoint.x}, ${startingPoint.y}
    a ${radius},${radius} 0 1,1 0,${2 * radius}
    a ${radius},${radius} 0 1,1 0,-${2 * radius}
  `;

    return (
      <Svg width="100" height="100" viewBox="-100 -100 200 200">
        <G>
          <Path d={circlePath} fill="none" stroke="#d3d3d3" strokeWidth={strokeWidth} strokeDasharray={`${circumference}`} strokeLinecap="round" />
          {percentage > 0 && (
            <Path
              d={circlePath}
              fill="none"
              stroke={handleGrade(percentage)?.chartCircle}
              strokeWidth={strokeWidth}
              strokeDasharray={`${dashLength},${gapLength}`}
              strokeLinecap="round"
            />
          )}
        </G>
      </Svg>
    );
  };

  const handlePadding = () => {
    if (details[propertyKeyObject]?.score === 100) {
      return 0;
    } else if (details[propertyKeyObject]?.score >= 0 && details[propertyKeyObject]?.score < 10) {
      return 10;
    } else {
      return 5;
    }
  };

  const handleGrade = (score) => {
    // Copy these colors from tailwind
    if (score > 80) {
      // Excellent
      return {
        chartCircle: '#22C55E',
        chartTextColor: '#11905D',
        chartBackground: '#EBFFF0',
        chartBorder: '#E1FFE6',
        text: 'Excellent',
      };
    } else if (score >= 50) {
      // Good
      return {
        chartCircle: '#FBBF24',
        chartTextColor: '#C48D01',
        chartBackground: '#FFF2D0',
        chartBorder: '#FEF3C7',
        text: 'Good',
      };
    } else if (score < 50) {
      // Poor
      return {
        chartCircle: '#EF4444',
        chartTextColor: '#D92D20',
        chartBackground: '#FEE2E2',
        chartBorder: '#FECACA',
        text: 'Poor',
      };
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '—';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'medium',
      timeStyle: 'short',
    }).format(date);
  };

  const applicantDetailsBlocksTestResults = () => {
    const subtitleMinExceeded = `${
      details.submission?.quiz?.duration < details[propertyKeyObject]?.timeTaken
        ? `${details.submission?.quiz?.duration - details[propertyKeyObject]?.timeTaken} mins Exceeded`
        : ''
    }`;
    const testResultsData = [
      {
        title: 'Time Taken',
        number: `${details[propertyKeyObject]?.timeTaken} min`,
        subtitle: subtitleMinExceeded,
        subtitleTextColor: '#D92D20',
        subtitleBgColor: '#FEF3F2',
      },
      {
        title: 'Answered Questions',
        number: `${details[propertyKeyObject]?.questionsSummary?.totalAnswers}/${details[propertyKeyObject]?.questionsSummary?.totalQuestions}`,
        subtitle: propertyKeyObject === 'interviews' && `${details.interviews.questionsSummary.skippedQuestions} skips`,
        subtitleTextColor: '#D97920',
        subtitleBgColor: '#FFF9EA',
      },
      {
        title: 'Average Response Time',
        number: `${Math.floor(details.averageResponseTime)} mins / Question`,
        // <EnumText name={'QuizDifficulty'} value={details[propertyKeyObject]?.quiz.difficulty />
      },
      {
        title: 'Total Score',
        number: `${details[propertyKeyObject]?.score}%`,
      },
    ];

    return (
      <View
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-around',
          alignItems: 'flex-end',
          gap: 10,
          paddingTop: 18,
        }}
      >
        {testResultsData?.map((singleBlock, index) => (
          <>
            <View
              key={singleBlock?.title}
              style={{
                width: '25%',
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                paddingTop: 8,
                paddingBottom: 3,
              }}
            >
              <Text style={{ fontSize: 10, fontWeight: 'medium', color: '#798296' }}>{singleBlock?.title}</Text>
              <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', gap: 6 }}>
                <Text style={{ fontSize: 10, color: '#000' }}>{singleBlock?.number}</Text>
                {singleBlock?.subtitle && (
                  <Text
                    style={{
                      fontSize: 7,
                      fontWeight: 'medium',
                      color: singleBlock?.subtitleTextColor,
                      backgroundColor: singleBlock?.subtitleBgColor,
                      paddingTop: 2,
                      paddingBottom: 2,
                      paddingRight: 4,
                      paddingLeft: 4,
                      borderRadius: 15,
                    }}
                  >
                    {singleBlock?.subtitle}
                  </Text>
                )}
              </View>
            </View>
            {index < testResultsData?.length - 1 && <View style={{ width: 1, height: '100%', backgroundColor: '#EAECF0' }} />}
          </>
        ))}
      </View>
    );
  };

  const countAllTopicsCoverd = () => {
    let counter = 0;
    details?.subcategoryScore?.map((subCategory) => (counter += subCategory?.topics?.length));
    return counter;
  };

  return (
    <PDFViewer style={{ width: '100%', height: '100vh' }}>
      <Document>
        <Page size="A4" style={{ backgroundColor: '#fff', padding: 15, position: 'relative', fontFamily: 'Inter' }}>
          {/* Background, First block */}
          <PDFImage
            src="/images/pdf-header.png"
            style={{
              position: 'absolute',
              right: 0,
              left: 0,
              top: 0,
            }}
          />
          {/* Main header */}
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}
          >
            {/* Test icon svg */}
            <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 5 }}>
              {headerIcon?.[propertyKeyObject]}
              <Text style={{ fontSize: 17, fontWeight: 500, color: '#1B063D' }}>
                {propertyKeyObject === 'submissions' ? 'Test' : 'Interview'} Result overview
              </Text>
            </View>
            <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 11 }}>
              <PDFImage style={{ height: 22 }} src={'/images/Thepass-1.png'} />
            </View>
          </View>

          {/* Sub main header */}
          <View style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', paddingTop: 10, flexDirection: 'column' }}>
            <View style={{ display: 'flex', alignItems: 'center', gap: 15, flexDirection: 'row' }}>
              <Text style={{ fontWeight: 'medium', fontSize: 12, color: '#1B063D' }}>
                {details[propertyKeyObject].title || details[propertyKeyObject].quiz.title}
              </Text>
              {details[propertyKeyObject]?.score >= 50 && (
                <Text
                  style={{
                    backgroundColor: '#EBFFF0',
                    fontSize: 10,
                    fontWeight: 'medium',
                    color: '#11905D',
                    paddingHorizontal: 8,
                    paddingVertical: 5,
                    borderRadius: 10,
                  }}
                >
                  Passed
                </Text>
              )}
            </View>
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                fontSize: 12,
                color: '#6B7280',
                paddingTop: 8,
              }}
            >
              <Text style={{ fontStyle: 'italic', fontWeight: 'normal', fontSize: 9 }}>Test created by</Text>
              <Text style={{ fontStyle: 'normal', fontWeight: 'medium', fontSize: 9, paddingLeft: 5 }}>
                {details[propertyKeyObject]?.author?.fullName}.{' '}
              </Text>
              <Text style={{ fontStyle: 'italic', fontWeight: 'normal', fontSize: 9 }}>Submitted</Text>
              <Text style={{ fontStyle: 'normal', fontWeight: 'medium', fontSize: 9, paddingLeft: 5 }}>
                {formatDate(details[propertyKeyObject]?.submittedAt)}
              </Text>
            </View>
          </View>

          {/* Applicant details */}
          <View
            style={{
              paddingLeft: 10,
              paddingRight: 10,
              paddingTop: 16,
              paddingBottom: 16,
              borderRadius: 6,
              borderWidth: 1,
              borderColor: '#E8E8E8',
              marginBottom: 15,
              marginTop: 15,
            }}
          >
            {/* Applicant avatar, name, email, number */}
            <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', width: '100%' }}>
              <View style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', flexDirection: 'row', gap: 12 }}>
                {/* Avatar */}
                <PDFImage
                  src={details?.applicant?.gender === 2 ? '/images/avatar-female-img.png' : '/images/avatar-male-img.png'}
                  style={{ width: '42px', height: '42px', borderRadius: '50%' }}
                />

                {/* Name */}
                <View style={{ display: 'flex', flexDirection: 'column', gap: 5 }}>
                  <Text style={{ fontSize: 14, color: '#101828', fontWeight: 'medium' }}>{details?.applicant?.name}</Text>
                  <Text style={{ fontSize: 10, fontWeight: 'medium', color: '#667085' }}>
                    {<EnumText name={'QuizDifficulty'} value={details.applicant?.seniorityLevel} />} {details.applicant?.trackName}{' '}
                    {/* @TODO: uncomment when endpoint is return its value */}
                    {/* React JS Developer */}
                    {/* {details?.applicant?.seniorityLevel} */}
                    {/* <EnumText name={'seniorityLevel'} value={details?.applicant?.seniorityLevel} /> */}
                  </Text>
                </View>
              </View>

              <View
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  flexWrap: 'nowrap',
                  height: '100%',
                  gap: 8,
                }}
              >
                {/* Email */}
                <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 3 }}>
                  <Svg width="12" height="9" viewBox="0 0 14 12" fill="white" xmlns="http://www.w3.org/2000/svg">
                    <Path
                      d="M13 2.5V9.5C13 9.89782 12.8541 10.2794 12.5945 10.5607C12.3348 10.842 11.9826 11 11.6154 11H2.38462C2.01739 11 1.66521 10.842 1.40554 10.5607C1.14588 10.2794 1 9.89782 1 9.5V2.5M13 2.5C13 2.10218 12.8541 1.72064 12.5945 1.43934C12.3348 1.15804 11.9826 1 11.6154 1H2.38462C2.01739 1 1.66521 1.15804 1.40554 1.43934C1.14588 1.72064 1 2.10218 1 2.5M13 2.5V2.662C13 2.9181 12.9395 3.16994 12.8243 3.39353C12.709 3.61712 12.5428 3.80502 12.3415 3.93933L7.72615 7.016C7.50782 7.16169 7.25643 7.23883 7 7.23883C6.74357 7.23883 6.49218 7.16169 6.27385 7.016L1.65846 3.94C1.45718 3.80569 1.291 3.61779 1.17573 3.3942C1.06047 3.1706 0.999974 2.91876 1 2.66267V2.5"
                      stroke="#798296"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </Svg>
                  <Text style={{ fontSize: 9, fontWeight: 'medium', color: '#333' }}>{details?.applicant?.email}</Text>
                </View>

                {/* Number */}
                <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 3 }}>
                  <Svg width="12" height="11" viewBox="0 0 13 14" fill="white" xmlns="http://www.w3.org/2000/svg">
                    <Path
                      d="M0.5 3.76923C0.5 8.86708 4.63292 13 9.73077 13H11.1154C11.4826 13 11.8348 12.8541 12.0945 12.5945C12.3541 12.3348 12.5 11.9826 12.5 11.6154V10.7711C12.5 10.4535 12.284 10.1766 11.9757 10.0997L9.25385 9.41908C8.98308 9.35138 8.69877 9.45292 8.532 9.67569L7.93508 10.4714C7.76154 10.7028 7.46185 10.8049 7.19046 10.7052C6.183 10.3348 5.26809 9.7499 4.5091 8.9909C3.7501 8.23191 3.16515 7.317 2.79477 6.30954C2.69508 6.03815 2.79723 5.73846 3.02862 5.56492L3.82431 4.968C4.04769 4.80123 4.14862 4.51631 4.08092 4.24615L3.40031 1.52431C3.36285 1.37458 3.27644 1.24166 3.1548 1.14667C3.03316 1.05167 2.88326 1.00005 2.72892 1H1.88462C1.51739 1 1.16521 1.14588 0.905544 1.40554C0.645879 1.66521 0.5 2.01739 0.5 2.38462V3.76923Z"
                      stroke="#798296"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </Svg>
                  <Text style={{ fontSize: 9, fontWeight: 'medium', color: '#333' }}>{details?.applicant?.mobileNumber}</Text>
                </View>
              </View>
            </View>

            {/* Test results */}
            {applicantDetailsBlocksTestResults()}
          </View>

          {/* Report Summary */}
          {/* <View
            style={{
              backgroundColor: '#C3CCD71A',
              opacity: 10,
              display: 'flex',
              paddingLeft: 15,
              paddingRight: 15,
              paddingTop: 16,
              paddingBottom: 16,
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              borderRadius: 6,
              borderWidth: 1,
              borderColor: '#E8E8E8',
              marginBottom: 15,
            }}
          >
            <View style={{ display: 'flex', flexDirection: 'column', gap: 10 }}>
              <Text style={{ fontWeight: 'medium', fontSize: '13px' }}>Report Summary</Text>
              <Text style={{ fontWeight: 'normal', fontSize: '11px', color: '#667085' }}>
                Lina performed exceptionally well in HTML and CSS with perfect scores, showcasing strong fundamentals. JavaScript and Vue.js need
                improvement, particularly in DOM manipulation and Vue.js directives.
              </Text>
              <Text style={{ fontWeight: 'normal', fontSize: '11px', color: '#667085' }}>
                Additionally, she changed her IP address and switched tabs multiple times during the assessment, which may indicate potential
                cheating. Focusing on these areas will enhance her overall frontend skills.
              </Text>
            </View>
          </View> */}

          {/* Test Score Breakdown */}
          <Text style={{ fontWeight: 'medium', fontSize: 12, color: '#667085', paddingBottom: 10 }}>Test Score Breakdown:</Text>
          <View
            style={{
              display: 'flex',
              marginTop: 5,
              flexDirection: 'column',
              alignItems: 'center',
              borderRadius: 6,
              borderWidth: 1,
              borderColor: '#E8E8E8',
              gap: 10,
            }}
          >
            <View
              style={{
                borderBottom: '0.5px',
                borderBottomColor: '#EAECF0',
                width: '100%',
                paddingLeft: 10,
                paddingRight: 10,
                paddingTop: 10,
                paddingBottom: 3,
              }}
            >
              <Text style={{ fontWeight: 500, fontSize: 13 }}>{details[propertyKeyObject].title || details[propertyKeyObject].quiz.title}</Text>
              <View style={{ display: 'flex', flexDirection: 'row', marginTop: 10, marginBottom: 5, gap: 20 }}>
                <View style={styles.breakDownContainer}>
                  <Svg width="12" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <Path
                      d="M7.83317 0.51582C7.94325 0.49707 8.05689 0.49707 8.16698 0.51582L15.2692 1.76582C15.667 1.83457 15.9546 2.14395 15.9546 2.5002C15.9546 2.85645 15.667 3.16582 15.2692 3.23457L12.5455 3.71582V5.5002C12.5455 7.70957 10.5107 9.5002 8.00007 9.5002C5.48942 9.5002 3.45462 7.70957 3.45462 5.5002V3.71582L1.75007 3.41582V5.4502L2.3076 7.9002C2.33956 8.04707 2.29695 8.2002 2.19041 8.31582C2.08388 8.43145 1.92053 8.5002 1.75007 8.5002H0.613708C0.443253 8.5002 0.283452 8.43457 0.173367 8.31582C0.0632818 8.19707 0.0206682 8.04707 0.0561795 7.9002L0.613708 5.4502V3.20645C0.27635 3.10332 0.0455261 2.82207 0.0455261 2.5002C0.0455261 2.14395 0.333168 1.83457 0.730895 1.76582L7.83317 0.51582ZM4.01925 10.7408C4.39212 10.6346 4.7934 10.7533 5.06328 11.0064L7.58459 13.3658C7.80831 13.5752 8.18828 13.5752 8.412 13.3658L10.9333 11.0064C11.2032 10.7533 11.6045 10.6346 11.9773 10.7408C14.2856 11.3939 15.9546 13.2939 15.9546 15.5408C15.9546 16.0721 15.4646 16.5002 14.8644 16.5002H1.13573C0.535583 16.5002 0.0455261 16.0689 0.0455261 15.5408C0.0455261 13.2939 1.71456 11.3939 4.01925 10.7408Z"
                      fill="#6B7280"
                    />
                  </Svg>

                  <Text style={styles.breakDownText}>
                    {<EnumText name="QuizDifficulty" value={details[propertyKeyObject]?.quiz?.difficulty || details[propertyKeyObject].difficulty} />}{' '}
                    Level
                  </Text>
                </View>
                <View style={styles.breakDownContainer}>
                  <Svg width="12" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <Path
                      d="M8.33333 16.8333C3.73083 16.8333 0 13.1025 0 8.5C0 3.8975 3.73083 0.166667 8.33333 0.166667C12.9358 0.166667 16.6667 3.8975 16.6667 8.5C16.6667 13.1025 12.9358 16.8333 8.33333 16.8333ZM9.16667 8.5V4.33333H7.5V10.1667H12.5V8.5H9.16667Z"
                      fill="#6B7280"
                    />
                  </Svg>

                  <Text style={styles.breakDownText}>{details[propertyKeyObject]?.quiz?.duration || details[propertyKeyObject]?.duration} Mins</Text>
                </View>
                <View style={styles.breakDownContainer}>
                  <Svg width="12" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <Path
                      d="M17.3337 5.62548C17.2227 4.78401 16.7784 3.88319 16.0314 3.13598C15.2844 2.38896 14.3837 1.94484 13.5424 1.83384C13.1616 1.78364 12.6533 1.57322 12.3487 1.33948C11.675 0.822815 10.7239 0.5 9.6672 0.5C8.61051 0.5 7.65946 0.822815 6.98595 1.33948C6.6813 1.57322 6.17295 1.78364 5.79218 1.83384C4.9507 1.94484 4.05025 2.38896 3.30322 3.13598C2.55619 3.88319 2.11182 4.78404 2.00086 5.62548C1.95085 6.00624 1.74023 6.51415 1.50669 6.81883C0.989813 7.49233 0.666992 8.44336 0.666992 9.5C0.666992 10.5567 0.989813 11.5077 1.50649 12.1812C1.74023 12.4859 1.95066 12.994 2.00067 13.3746C2.11166 14.216 2.55599 15.1169 3.30302 15.8641C4.05005 16.611 4.9507 17.0552 5.79198 17.1662C6.17276 17.2164 6.6811 17.4268 6.98575 17.6606C7.65927 18.1772 8.61031 18.5 9.66701 18.5C10.7237 18.5 11.6748 18.1772 12.3483 17.6606C12.6529 17.4268 13.1613 17.2164 13.542 17.1662C14.3835 17.0552 15.284 16.611 16.031 15.8641C16.778 15.1169 17.2224 14.216 17.3333 13.3746C17.3834 12.9938 17.594 12.4859 17.8275 12.1812C18.3442 11.5077 18.667 10.5567 18.667 9.5C18.667 8.44336 18.3442 7.4923 17.8275 6.81883C17.5942 6.51415 17.3838 6.00624 17.3337 5.62548ZM10.7446 14.6039C10.7446 14.8241 10.5661 15.0024 10.3461 15.0024H9.05506C8.8351 15.0024 8.65654 14.8241 8.65654 14.6039V13.313C8.65654 13.0928 8.83506 12.9145 9.05506 12.9145H10.3461C10.5661 12.9145 10.7446 13.0928 10.7446 13.313V14.6039ZM13.1858 8.43359C12.9255 8.84486 12.3696 9.40457 11.5175 10.1129C11.0766 10.4795 10.8028 10.7743 10.6964 10.9972C10.6167 11.1644 10.5692 11.4304 10.5549 11.7952C10.5463 12.0153 10.373 12.1935 10.1528 12.1935H9.05487C8.83487 12.1935 8.65436 12.0819 8.65215 11.9445C8.65016 11.817 8.64896 11.734 8.64896 11.6956C8.64896 11.0813 8.75061 10.576 8.95361 10.1797C9.15688 9.78335 9.56295 9.33763 10.1723 8.84206C10.7817 8.34672 11.1456 8.02212 11.2644 7.8687C11.4478 7.62601 11.5394 7.35842 11.5394 7.06611C11.5394 6.65982 11.3774 6.31193 11.0528 6.022C10.7282 5.7321 10.2912 5.58722 9.74131 5.58722C9.36349 5.58722 9.03016 5.66393 8.74042 5.81734C8.54594 5.92036 8.27137 6.15448 8.13886 6.33023C7.99101 6.5263 7.87126 6.76221 7.7796 7.03798C7.71006 7.24681 7.49843 7.40061 7.27986 7.37353L6.15384 7.23385C5.93524 7.20673 5.76649 7.00611 5.80553 6.7895C5.93505 6.07217 6.2971 5.4509 6.8915 4.92586C7.59249 4.30658 8.51267 3.99695 9.65203 3.99695C10.851 3.99695 11.8044 4.31035 12.5128 4.93703C13.2212 5.56371 13.5755 6.29318 13.5755 7.12546C13.5759 7.58655 13.4458 8.02251 13.1858 8.43359Z"
                      fill="#6B7280"
                    />
                  </Svg>

                  <Text style={styles.breakDownText}>{details[propertyKeyObject]?.questionsSummary?.totalQuestions} Questions</Text>
                </View>
                <View style={styles.breakDownContainer}>
                  <Svg width="12" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <Path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M4.92969 0.0625C3.75486 0.0625 2.62815 0.489195 1.79743 1.24872C0.966698 2.00824 0.5 3.03837 0.5 4.1125V13.1125H0.534453C0.652829 13.8623 1.06282 14.548 1.68928 15.0439C2.31573 15.5398 3.11668 15.8127 3.94531 15.8125H15.2656C15.5267 15.8125 15.7771 15.7177 15.9617 15.5489C16.1463 15.3801 16.25 15.1512 16.25 14.9125V1.4125C16.25 1.05446 16.0944 0.71108 15.8175 0.457906C15.5406 0.204732 15.165 0.0625 14.7734 0.0625H4.92969ZM3.94531 10.8625H14.7734V14.4625H3.94531C3.42317 14.4625 2.92241 14.2729 2.5532 13.9353C2.18398 13.5977 1.97656 13.1399 1.97656 12.6625C1.97656 12.1851 2.18398 11.7273 2.5532 11.3897C2.92241 11.0521 3.42317 10.8625 3.94531 10.8625ZM11.84 4.5994C11.9812 4.47528 12.0628 4.30494 12.0666 4.12586C12.0705 3.94679 11.9964 3.77363 11.8607 3.6445C11.7249 3.51537 11.5386 3.44083 11.3427 3.43728C11.1469 3.43374 10.9575 3.50148 10.8162 3.6256L8.25294 5.8765L6.91714 4.7065C6.84788 4.64218 6.76521 4.59121 6.67402 4.55662C6.58284 4.52202 6.48499 4.50449 6.38626 4.50508C6.28754 4.50567 6.18995 4.52435 6.09926 4.56002C6.00857 4.5957 5.92663 4.64764 5.85828 4.71278C5.78994 4.77791 5.73658 4.85492 5.70135 4.93924C5.66613 5.02357 5.64976 5.11349 5.65322 5.2037C5.65668 5.29391 5.67989 5.38257 5.72148 5.46443C5.76307 5.54629 5.8222 5.6197 5.89536 5.6803L7.74106 7.3003C7.87851 7.42076 8.06155 7.48802 8.25195 7.48802C8.44235 7.48802 8.62539 7.42076 8.76284 7.3003L11.84 4.5994Z"
                      fill="#6B7280"
                    />
                  </Svg>

                  <Text style={styles.breakDownText}>{countAllTopicsCoverd()} Topics Covered</Text>
                </View>
              </View>
            </View>

            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: 20,
                justifyContent: 'center',
                alignItems: 'center',
                paddingLeft: 15,
                paddingRight: 15,
                paddingBottom: 16,
              }}
            >
              <View style={{ alignSelf: 'center', width: '18%' }}>
                <View style={{ alignSelf: 'center' }}>
                  <HalfFilledCircleBorder percentage={details[propertyKeyObject]?.score} />
                </View>
                <View
                  style={{
                    position: 'absolute',
                    top: '80px',
                    left: '74px',
                    transform: 'translate(-50%, -50%)',
                    fontWeight: 'medium',
                  }}
                >
                  <Text style={{ fontSize: 20, paddingLeft: `${handlePadding()}` }}>{details[propertyKeyObject]?.score}%</Text>
                  <Text style={{ color: '#808080', fontSize: 7, paddingLeft: 5 }}>Total Score</Text>
                </View>
                <View
                  style={{
                    paddingTop: 7,
                    paddingBottom: 7,
                    paddingLeft: 15,
                    paddingRight: 15,
                    width: '90%',
                    fontSize: 10,
                    textAlign: 'center',
                    border: '1px',
                    borderColor: handleGrade(details[propertyKeyObject]?.score)?.chartBorder,
                    borderRadius: 8,
                    position: 'absolute',
                    transform: 'translate(-50%, 0)',
                    bottom: 0,
                    left: 54,
                    backgroundColor: handleGrade(details[propertyKeyObject]?.score)?.chartBackground,
                    fontWeight: 'medium',
                  }}
                >
                  <Text
                    style={{
                      color: handleGrade(details[propertyKeyObject]?.score)?.chartTextColor,
                      fontWeight: 'medium',
                    }}
                  >
                    {handleGrade(details[propertyKeyObject]?.score)?.text}
                  </Text>
                </View>
              </View>

              <View style={{ width: '80%' }}>
                <View style={{ display: 'flex' }}>
                  {details?.subcategoryScore?.map((category, index) => (
                    <View
                      key={index}
                      style={{ gap: 6, marginTop: 10, width: '100%', borderRadius: 6, display: 'flex', flexDirection: 'row', alignItems: 'center' }}
                    >
                      <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
                        <View>
                          <Text style={{ color: '#1B063D', fontSize: 11, fontWeight: 'medium' }}>{category?.subCategory}</Text>
                        </View>
                        <View style={{ display: 'flex', flexDirection: 'row' }}>
                          <View
                            style={{
                              flexGrow: 1,
                              display: 'flex',
                              flexDirection: 'row',
                              paddingRight: 10,
                              marginTop: 5,
                              backgroundColor: 'white',
                            }}
                          >
                            <SubCategoryChart percentage={category?.score} />
                          </View>
                          <Text style={{ fontSize: 12, color: '#000000', fontWeight: 'medium', width: 50 }}>{category?.score}%</Text>
                        </View>
                      </View>
                    </View>
                  ))}
                </View>
              </View>
            </View>
          </View>

          {/* Categories covered Breakdown */}
          <Text style={{ fontSize: 12, fontWeight: 'medium', color: '#667085', marginTop: 15, marginBottom: 15 }}>Categories covered breakdown:</Text>
          {details?.subcategoryScore?.map((category, index) => (
            <View
              key={index}
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: 10,
                width: '100%',
                borderRadius: 6,
                borderWidth: 1,
                borderColor: '#E8E8E8',
                marginBottom: 15,
              }}
            >
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 6,
                  borderBottom: '1px',
                  borderBottomColor: '#E8E8E8',
                  paddingLeft: 15,
                  paddingRight: 15,
                  paddingTop: 8,
                  paddingBottom: 5,
                }}
              >
                <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 15 }}>
                  <Text style={{ fontSize: 13, color: '#101828', paddingTop: 4, paddingBottom: 4, fontWeight: 500 }}>{category.subCategory}</Text>
                  <View
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      minWidth: 50,
                      fontSize: 9,
                      color: handleGrade(category?.score)?.chartTextColor,
                      backgroundColor: handleGrade(category?.score)?.chartBackground,
                      fontWeight: 'medium',
                      paddingHorizontal: 2,
                      paddingVertical: 2,
                      borderRadius: 6,
                    }}
                  >
                    <Text>{category.grade}</Text>
                  </View>
                </View>

                <View style={{ display: 'flex', flexDirection: 'row', gap: 20 }}>
                  {category?.difficulty?.length > 0 && (
                    <View style={{ color: '#6B7280', display: 'flex', flexDirection: 'row', gap: 5, alignItems: 'center' }}>
                      <Svg width="12" height="18" viewBox="0 0 19 20" fill="white" xmlns="http://www.w3.org/2000/svg">
                        <Path
                          d="M4.8125 12.3437V14.6875M7.9375 10V14.6875M11.0625 7.65625V14.6875M14.1875 5.3125V14.6875M3.25 18.5937H15.75C16.3716 18.5937 16.9677 18.3468 17.4073 17.9073C17.8468 17.4677 18.0937 16.8716 18.0937 16.25V3.75C18.0937 3.1284 17.8468 2.53226 17.4073 2.09272C16.9677 1.65318 16.3716 1.40625 15.75 1.40625H3.25C2.6284 1.40625 2.03226 1.65318 1.59272 2.09272C1.15318 2.53226 0.90625 3.1284 0.90625 3.75V16.25C0.90625 16.8716 1.15318 17.4677 1.59272 17.9073C2.03226 18.3468 2.6284 18.5937 3.25 18.5937Z"
                          stroke="#667085"
                          stroke-width="1.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </Svg>
                      <Text style={{ fontSize: 11, fontWeight: 'medium', color: '#6B7280' }}>
                        {category?.difficulty
                          .sort((a, b) => a - b)
                          .map((level, index) => <EnumText key={index} name={'QuestionDifficulty'} value={level} />)
                          .reduce((prev, curr) => [prev, ' - ', curr])}
                      </Text>
                    </View>
                  )}

                  <View style={{ color: '#6B7280', display: 'flex', flexDirection: 'row', gap: 5, alignItems: 'center' }}>
                    <Svg width="13" height="19" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <Path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M4.92969 0.5625C3.75486 0.5625 2.62815 0.989195 1.79743 1.74872C0.966698 2.50824 0.5 3.53837 0.5 4.6125V13.6125H0.534453C0.652829 14.3623 1.06282 15.048 1.68928 15.5439C2.31573 16.0398 3.11668 16.3127 3.94531 16.3125H15.2656C15.5267 16.3125 15.7771 16.2177 15.9617 16.0489C16.1463 15.8801 16.25 15.6512 16.25 15.4125V1.9125C16.25 1.55446 16.0944 1.21108 15.8175 0.957906C15.5406 0.704732 15.165 0.5625 14.7734 0.5625H4.92969ZM3.94531 11.3625H14.7734V14.9625H3.94531C3.42317 14.9625 2.92241 14.7729 2.5532 14.4353C2.18398 14.0977 1.97656 13.6399 1.97656 13.1625C1.97656 12.6851 2.18398 12.2273 2.5532 11.8897C2.92241 11.5521 3.42317 11.3625 3.94531 11.3625ZM11.84 5.0994C11.9812 4.97528 12.0628 4.80494 12.0666 4.62586C12.0705 4.44679 11.9964 4.27363 11.8607 4.1445C11.7249 4.01537 11.5386 3.94083 11.3427 3.93728C11.1469 3.93374 10.9575 4.00148 10.8162 4.1256L8.25294 6.3765L6.91714 5.2065C6.84788 5.14218 6.76521 5.09121 6.67402 5.05662C6.58284 5.02202 6.48499 5.00449 6.38626 5.00508C6.28754 5.00567 6.18995 5.02435 6.09926 5.06002C6.00857 5.0957 5.92663 5.14764 5.85828 5.21278C5.78994 5.27791 5.73658 5.35492 5.70135 5.43924C5.66613 5.52357 5.64976 5.61349 5.65322 5.7037C5.65668 5.79391 5.67989 5.88257 5.72148 5.96443C5.76307 6.04629 5.8222 6.1197 5.89536 6.1803L7.74106 7.8003C7.87851 7.92076 8.06155 7.98802 8.25195 7.98802C8.44235 7.98802 8.62539 7.92076 8.76284 7.8003L11.84 5.0994Z"
                        fill="#6B7280"
                      />
                    </Svg>

                    <Text style={{ fontSize: 12, fontWeight: 'medium', color: '#6B7280' }}>
                      {category?.topics?.length} Topic{category?.topics?.length > 1 && 's'} Covered
                    </Text>
                  </View>
                </View>
              </View>

              {/* Topics Covered Breakdown: */}
              <View style={{ paddingLeft: 12, marginBottom: 10 }}>
                <Text style={{ fontSize: 11, fontWeight: 'medium', marginBottom: 10, color: '#667085' }}>Topics Covered Breakdown:</Text>

                {category?.topics?.map((topic, index) => (
                  <View
                    key={index}
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      borderRadius: 6,
                      alignItems: 'center',
                    }}
                  >
                    <View
                      style={{
                        alignItems: 'start',
                        display: 'flex',
                        flexDirection: 'column',
                      }}
                    >
                      <View>
                        <Text style={{ color: '#1B063D', fontSize: 11, fontWeight: 'medium' }}>{topic?.topic}</Text>
                      </View>
                      <View style={{ display: 'flex', flexDirection: 'row' }}>
                        <View
                          style={{
                            flexGrow: 1,
                            display: 'flex',
                            flexDirection: 'row',
                            paddingRight: 10,
                            backgroundColor: 'white',
                            marginTop: 5,
                          }}
                        >
                          <SubCategoryChart percentage={topic?.score} />
                        </View>
                        <Text style={{ alignSelf: 'center', fontSize: 12, color: '#000000', fontWeight: 'medium', paddingLeft: 5, width: 60 }}>
                          {topic?.score}%
                        </Text>
                      </View>
                    </View>
                  </View>
                ))}
              </View>
            </View>
          ))}

          {/* Page number */}
          {/* <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', position: 'absolute', bottom: 14, left: 25 }}>
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
                gap: 5,
                alignSelf: 'center',
              }}
            >
              <PDFImage style={{ alignSelf: 'center', width: 29 }} src={'/public/images/logo.png'} />
              <Text style={{ alignSelf: 'center', color: '#000', fontSize: 17, fontWeight: 'bold' }}>TechPass</Text>
            </View>
            <Text
              style={{ alignSelf: 'center', marginLeft: 395, fontSize: 12, fontWeight: 'medium' }}
              render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`}
              fixed
            />
          </View> */}
        </Page>
      </Document>
    </PDFViewer>
  );
};
