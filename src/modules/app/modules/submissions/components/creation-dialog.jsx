// React
import React, { useContext, useEffect, useState } from 'react';
import { FaCheckCircle, FaChevronDown, FaChevronUp } from 'react-icons/fa';

// Components
import { Dialog, Form, Drawer, useValidate, Select, Button, Checkbox, useForm, useNotify, Icon, Api, Regex, EnumText, CustomIcon } from '/src';
import { AppContext } from '/src/components/provider';
import { FaUserGraduate, FaUser, FaStar, FaMedal, FaTrophy } from 'react-icons/fa';
import { TimeSettingsDialog } from 'src/modules/app/modules/applicants/components/assign-time-settings.jsx';

// Flowbite
import { Spinner } from 'flowbite-react';
// import { Clipboard } from 'flowbite-react';

export const SubmissionsCreationDialog = ({ onClose, testId, back, backButton }) => {
  // Constants
  const FORM_DEFAUT_VALUE = {
    applicantId: '',
    quizId: '',
    // willSendEmail: false,
  };

  // Hooks
  const { notify } = useNotify();

  // Form
  const { form, setFieldValue, resetForm } = useForm(FORM_DEFAUT_VALUE);

  const { isRequired } = useValidate();

  // State
  const [loading, setLoading] = useState(false);
  const [quizUrl, setQuizUrl] = useState('');
  const [submissionId, setSubmissionId] = useState('');
  const [applicants, setApplicants] = useState([]);
  const [quizzes, setQuizzes] = useState([]);
  const [singleQuiz, setSingleQuiz] = useState(null);
  const [searchResult, setSearchResult] = useState(null);
  const [emailRegex, setEmailRegex] = useState(false);
  const [expandedSections, setExpandedSections] = useState({});
  const [expandedAll, setExpandedAll] = useState(false);
  const [startDate, setStartDate] = useState(new Date());
  const [dueDate, setDueDate] = useState(() => {
    const result = new Date(startDate);
    result.setDate(result.getDate() + 1);
    return result;
  });
  const [extraTime, setExtraTime] = useState(false);
  const [isTimeSettingsVisible, setTimeSettingsVisible] = useState(false);
  const type = 'test';

  // User Data
  const { userData } = useContext(AppContext);
  // Methods
  const handleSearch =
    (endpoint, action, isCustomAdd = false) =>
    async (keyword) => {
      try {
        const result = await Api.get(endpoint, { keyword });
        action(result?.data);
        setEmailRegex(false);
        if (isCustomAdd) {
          if (!result?.data?.length && !!keyword) {
            // setAddIconApplicant(true);
            setSearchResult(keyword);
          } else {
            // setAddIconApplicant(false);
          }
        }
      } catch (error) {
        notify.error(error.response.data.message);
      }
    };

  const handleSubmit = async () => {
    try {
      setLoading(true);

      let { applicantId, ...payload } = form;
      payload = {
        ...payload,
        otherTest: true,
        dueDate: dueDate,
        startDate: startDate,
      };
      if (extraTime >= 1) payload.exceededTime = extraTime;

      if (form.applicantId) payload.applicantId = [form.applicantId];

      const response = await Api.post('submissions/single', payload);

      // Reset form
      resetForm();

      // Set Quiz URL
      setQuizUrl(response.data.quizUrl);
      setSubmissionId(response.data.submissionId);
      // Update Parent
    } catch (error) {
      notify.error(error.response.data.message);
    } finally {
      setLoading(false);
    }
  };

  const handelGet = async () => {
    try {
      // Add Submissions Details
      const response = await Api.get(`quizzes/single/custom/${testId}`);
      setSingleQuiz(response.data);
      setFieldValue('quizId')(testId);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  const handleCopyLink = () => {
    navigator.clipboard.writeText(quizUrl);
    notify('Link copied');
  };

  const generateDescription = () => {
    const totalCategories = singleQuiz.subcategoryDetails.length;
    const totalTopics = singleQuiz.subcategoryDetails.reduce((sum, sub) => sum + sub.topics.length, 0);

    // let difficultyText = '';
    // switch (details.difficulty) {
    //   case 1:
    //     difficultyText = 'suitable for interns';
    //     break;
    //   case 2:
    //     difficultyText = 'ideal for fresh graduates';
    //     break;
    //   case 3:
    //     difficultyText = 'great for junior-level candidates';
    //     break;
    //   case 4:
    //     difficultyText = 'targeted for mid-level professionals';
    //     break;
    //   case 5:
    //     difficultyText = 'challenging for senior professionals';
    //     break;
    //   default:
    //     difficultyText = '';
    // }

    return (
      <>
        This test covers{' '}
        <span className="font-semibold">
          {' '}
          {totalCategories} {totalCategories > 1 ? 'categories' : 'category'}{' '}
        </span>{' '}
        and includes{' '}
        <span className="font-semibold">
          {' '}
          {totalTopics} {totalTopics > 1 ? 'topics' : 'topic'}{' '}
        </span>
      </>
    );
  };

  // Check on quiz diffuculty
  let difficultyIcon;
  let difficultyColor;
  let iconSize = 'text-[15px]';
  if (singleQuiz) {
    switch (singleQuiz.difficulty) {
      case 1:
        difficultyIcon = <FaUserGraduate className={`${iconSize} text-teal-700`} />; // Intern
        difficultyColor = ' text-teal-700 ';
        break;
      // Star Icon fresh level
      case 2:
        difficultyIcon = <FaUser className={`${iconSize} text-sky-800`} />; // Fresh
        difficultyColor = 'text-sky-800 ';
        break;
      // Medal Star junior
      case 3:
        difficultyIcon = <FaStar className={`${iconSize} text-amber-700`} />; // Junior
        difficultyColor = ' text-amber-700 ';
        break;
      // betetr medal star midlevel
      case 4:
        difficultyIcon = <FaMedal className={`${iconSize} text-orange-700`} />; // Mid-level
        difficultyColor = 'text-orange-700';
        break;
      // Tropy icon for senior with star
      case 5:
        difficultyIcon = <Icon icon="solar:crown-star-bold" width={18} className={`${iconSize} text-red-800`} />; // Senior
        difficultyColor = 'text-red-800';
        break;
      default:
        difficultyIcon = null;
    }
  }

  const handleCountTopics = (tests) => {
    let topicsCount = 0;
    tests?.map((test) => (topicsCount += test?.topics?.length));
    return `${topicsCount} ${topicsCount > 1 ? 'Topics' : 'Topic'}`;
  };

  // On Mount
  useEffect(() => {
    if (testId) {
      handelGet();
    }
  }, [testId]);

  // useEffect(() => {
  //   setFieldValue('willSendEmail')(false);
  // }, [form.applicantId]);

  // Sync individual sections' state with the "expand all/collapse all" action
  useEffect(() => {
    const newState = {};
    singleQuiz?.subcategoryDetails.forEach((_, index) => {
      newState[index] = expandedAll; // Set all sections to expanded or collapsed based on expandedAll prop
    });
    setExpandedSections(newState);
  }, [expandedAll, singleQuiz?.subcategoryDetails]);

  const modalHeader = () => {
    // TODO: Remove case for select test
    if (!quizUrl && !singleQuiz) {
      return 'Select Test';
    } else if (!quizUrl && singleQuiz) {
      return 'Assign Test';
    } else {
      return '';
      // Test Created Successfully!
    }
  };

  return (
    <>
      <Drawer onClose={onClose}>
        <Drawer.SingleView>
          <Drawer.Header headerLabel="Assign Test" onClose={onClose} className="border-b border-[#E5E7EB] pb-2" />
          <Drawer.Body className="space-y-3 overflow-auto">
            {singleQuiz && testId ? (
              <div className="p-1 xssm:p-3 sm:p-4 space-y-2 sm:space-y-3 bg-[#F9FAFB] dark:bg-darkGrayBackground border border-[#F2F2F2] rounded-md">
                {/* Difficuluty */}
                <span className={`flex gap-1 items-center text-xs sm:text-sm ${difficultyColor}`}>
                  <span>{difficultyIcon}</span>
                  <span>
                    <EnumText name="QuizDifficulty" value={singleQuiz.difficulty} /> Level
                  </span>
                </span>

                {/* Page title (Test Name all) */}
                <p className="text-sm sm:text-base font-medium capitalize dark:text-white">{singleQuiz.title}</p>

                {/* Meta Data */}
                <div className="flex flex-wrap items-center gap-1 xssm:gap-4 text-gray-500 dark:text-gray-300 text-xs sm:text-sm font-medium">
                  {/* Duration */}
                  <div className="flex gap-1 xssm:gap-2 items-center">
                    <CustomIcon definedIcon="clock" className="text-[#D07EAA]" />
                    <span className="text-gray-500 dark:text-gray-300">{singleQuiz.duration} mins</span>
                  </div>

                  {/* Questions */}
                  <div className="flex gap-1 xssm:gap-2 items-center">
                    <CustomIcon definedIcon="questionsCircle" className="text-[#D07EAA]" />
                    <span className="text-gray-500 dark:text-gray-300">
                      {singleQuiz.numOfQuestions} {singleQuiz.numOfQuestions > 1 ? 'Questions' : 'Question'}
                    </span>
                  </div>

                  {/* Topics */}
                  <div className="flex gap-1 xssm:gap-2 items-center">
                    <CustomIcon definedIcon="book" className="text-[#D07EAA]" />
                    <span className="text-gray-500 dark:text-gray-300">{handleCountTopics(singleQuiz?.subcategoryDetails)}</span>
                  </div>
                </div>

                {/* Start of What's Inside ? */}
                {/* <div className="flex flex-col gap-2">
                  <div className="flex justify-between items-center flex-wrap ">
                    <p className="text-lg font-semibold text-gray-900 dark:text-white  ">What's Inside?</p>

                    <div
                      className="hover:underline focus:outline-none text-primaryPurple text-opacity-90 font-medium text-base cursor-pointer"
                      onClick={() => setExpandedAll(!expandedAll)}
                    >
                      {expandedAll ? 'Collapse all sections' : 'Expand all sections'}
                    </div>
                  </div>
                  <p className="text-[#808080] dark:text-gray-400 text-[15px] pb-1"> {generateDescription()}</p>
                </div> */}

                <div className="!hidden w-full bg-white dark:bg-gray-800 rounded-xl py-2  mt-2 border-1 border border-gray-200 dark:border-none">
                  {/* Sub category breakdown */}
                  <div className="space-y-2">
                    {singleQuiz.subcategoryDetails.map((sub, subIndex) => (
                      <div
                        key={subIndex}
                        className={`py-2 ${
                          subIndex === singleQuiz.subcategoryDetails.length - 1 ? '' : 'border-b border-gray-200 dark:border-gray-700'
                        }`}
                      >
                        {/* Subcategory Header */}
                        <div className="flex justify-between items-center px-4 flex-wrap">
                          <div className="text-base font-medium text-gray-700 dark:text-white">
                            {sub.subCategoryName}
                            <span className="text-sm font-normal text-gray-500 dark:text-gray-400 mx-2">
                              {' '}
                              ( {sub.count}
                              {sub.count > 1 ? ' Questions' : ' Question'} )
                            </span>
                          </div>
                          <div
                            onClick={(e) => {
                              setExpandedSections((prev) => ({ ...prev, [subIndex]: !prev[subIndex] }));
                            }}
                            className=" cursor-pointer text-gray-700 dark:text-gray-300 text-opacity-90  hover:underline focus:outline-none text-[13px] font-medium flex items-center"
                          >
                            {expandedSections[subIndex] ? 'Hide Topics' : 'Show Topics'}
                            {expandedSections[subIndex] ? <FaChevronUp className="mx-2 " /> : <FaChevronDown className="mx-2 " />}
                          </div>
                        </div>

                        {/* Subcategory Topics */}
                        {expandedSections[subIndex] && (
                          <div className="mt-3 space-y-1 px-4">
                            {sub.topics.map((topic, index) => (
                              <div key={index} className="flex gap-2 items-center ">
                                <FaCheckCircle className="text-gray-500 dark:text-gray-600 text-[15px]" />
                                <p className="flex items-center font-medium text-[15px] text-gray-500 dark:text-gray-300">{topic}</p>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
                {/* End of What's Inside ? */}
              </div>
            ) : (
              <Spinner />
            )}

            <Drawer.Body.DatePicker
              startDate={startDate}
              dueDate={dueDate}
              extraTime={extraTime}
              setExtraTime={setExtraTime}
              setTimeSettingsVisible={setTimeSettingsVisible}
              loading={loading}
              type={type}
            />

            <Form className="space-y-4">
              {!testId && (
                <Select
                  name="quizId"
                  label="Test"
                  placeholder="Find tests.."
                  value={form.quizId}
                  onChange={setFieldValue('quizId')}
                  onSearch={handleSearch('quizzes/search', setQuizzes)}
                  disabled={loading}
                  lookup={quizzes.map(({ title, _id }) => ({
                    label: title,
                    value: _id,
                  }))}
                  validators={[isRequired()]}
                />
              )}
              <div className="relative">
                <Select
                  name="applicantId"
                  label="Applicant"
                  placeholder={userData ? 'Find applicants by email...' : 'Enter applicant...'}
                  value={form.applicantId}
                  onChange={setFieldValue('applicantId')}
                  // @FIXME: (isCustomAdd = true)
                  onSearch={handleSearch('applicants/search', setApplicants, true)}
                  // creationOptions={{
                  //   url: 'applicants/single/custom',
                  //   fieldName: 'email',
                  //   validation: Regex.email,
                  // }}
                  disabled={loading}
                  lookup={userData ? applicants : []}
                  optionValueKey="_id"
                  optionLabelKey="email"
                  isCustomValue={true}
                  // customAddIconApplicant={emailRegex}
                />
                {/* {addIconApplicant && (
              <div className="absolute right-2 top-[42px] cursor-pointer bg-white dark:bg-[#374151]" onClick={handleAddNewApplicant}>
                <Icon width={22} className="text-green-500" icon="material-symbols:add" />
              </div>
            )} */}
              </div>
              {/* <TextInput
            name='applicantLimit'
            label='Limit Num Of Applicants'
            type='number'
            placeholder='Enter limit num'
            value={form.applicantLimit}
            onChange={setFieldValue('applicantLimit')}
            disabled={form.applicantId}
            min={0}
          /> */}

              <div className="pt-2 space-y-4">
                {/* <Checkbox
              name="willSendEmail"
              label="Send link via applicant email"
              value={form.willSendEmail}
              onChange={setFieldValue('willSendEmail')}
              disabled={form.applicantId.length === 0 || loading}
              preventSendingMail={form.applicantId.length === 0 || loading}
            /> */}
              </div>
            </Form>
          </Drawer.Body>

          <div className="flex gap-2">
            {back && <Button type="button" label="Back" outline disabled={loading} onClick={backButton} />}
            <Button
              type="button"
              onClick={handleSubmit}
              label="Assign"
              className="w-full"
              loading={loading}
              disabled={loading || !form.applicantId}
            />
          </div>
        </Drawer.SingleView>
      </Drawer>

      {isTimeSettingsVisible && (
        <TimeSettingsDialog
          onClose={() => setTimeSettingsVisible(false)}
          startDate={startDate}
          setStartDate={setStartDate}
          dueDate={dueDate}
          setDueDate={setDueDate}
          type={type}
        />
      )}

      {!!quizUrl && (
        <Dialog show popup size="lg" modalHeader={modalHeader()} className={!!quizUrl && 'text-center'} onClose={onClose} overflowVisible={true}>
          {userData ? (
            // image true
            <div className="py-5  pt-0">
              <div>
                <div className="flex justify-center items-center mx-auto mb-5 !mt-0  text-gray-400 dark:text-gray-200">
                  <img src="/images/Vector.svg" alt="done mark" />
                </div>
                {/* <hr className="h-px my-2 bg-gray-200 border-0 dark:bg-gray-700" /> */}
                <div className="text-center">
                  <h2 className="text-center dark:text-white font-medium text-xl ">Test Assigned Successfully!</h2>
                  <div className=" w-72 text-center mx-auto mt-2">
                    <p className="text-center font-normal text-base   dark:text-white text-[#626262]">
                      Send the link below for quick and easy access to the applicant
                    </p>
                  </div>
                </div>
                <div className="mt-5 py-1">
                  <div className="grid w-full max-w-80 mx-auto text-center">
                    <div className="relative flex ">
                      <input
                        value={quizUrl}
                        type="text"
                        className="col-span-6 block w-full rounded-lg border border-gray-300 bg-gray-50 px-2.5 py-4 text-sm text-[#313437] pr-12 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-400 dark:placeholder:text-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                      />
                      <Icon
                        icon={'clarity:copy-line'}
                        onClick={() => handleCopyLink()}
                        className="dark:text-white cursor-pointer text-gray-400  text-2xl absolute right-3 top-1/4"
                        width={22}
                        hight={22}
                      />
                      {/* <Clipboard.WithIconText valueToCopy="npm install flowbite-react" /> */}
                    </div>
                  </div>

                  {/* <Button
                className="w-full"
                outline
                label="Copy Link"
                icon="material-symbols:content-copy-outline-rounded"
                onClick={() => handleCopyLink()}
              /> */}
                  {/* {form.applicantId && (
                <Button className="w-9/12 mx-auto mt-8" label="View Progress" icon="pajamas:progress" to={`/app/tests/result/view/${submissionId}`} />
              )} */}
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <span className="text-gray-900 dark:text-white">Please log in to copy test link</span>
            </div>
          )}
        </Dialog>
      )}
    </>
  );
};
