import React, { useState, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../../../../../services/axios';
import { FaUserGraduate, FaUser, FaStar, FaMedal, FaTrophy } from 'react-icons/fa';

// Components
import { Table, Api, useNotify, useFetchList, useConfirmDialog, Icon, EnumText, useScreenSize } from '/src';
// import { SubmissionsLockDialog } from '../components/lock-dialog';
import { AppContext } from '/src/components/provider';
import { SubmissionsCreationDialog } from '../components/creation-dialog';
import { SubmissionsCreationTestDialog } from '../components/creation-test-dialog';
import { AiIntreviewDialog } from '../components/ai-dialog';

// Flowbite
import { Tooltip } from 'flowbite-react';

export const SubmissionsListPage = () => {
  const { userData } = useContext(AppContext);

  const isPermitted = userData?.roles.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const navigate = useNavigate();
  const ORIGIN = window.location.origin;

  // State
  const screen = useScreenSize();
  // const [isLockDialogVisible, setLockDialogVisibility] = useState(false);
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [isCreateTestDialogVisible, setCreateTestDialogVisibility] = useState(false);
  const [aiDialog, setAiDialog] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);
  const [lockUnlockAll, setLockUnlockAll] = useState(false);
  const [showMoreMap, setShowMoreMap] = useState({});

  // UI Hooks
  const { notify } = useNotify();
  const { showConfirm, hideConfirm } = useConfirmDialog();

  // List Hook
  const filterList = {
    status: {
      label: 'Status',
      enum: 'SubmissionStatus',
    },
    category: {
      label: 'Category',
      lookup: 'category',
    },
    subCategory: {
      label: 'Sub Category',
      lookup: 'subcategory',
      parentLookup: { key: 'category', fieldName: 'categoryId' },
    },
    difficulty: {
      label: 'Difficulty',
      enum: 'QuizDifficulty',
    },
    // locked: {
    //   label: 'Is Locked?',
    //   enum: 'YesNo',
    // },
    // scope: {
    //   label: 'Scope',
    //   enum: 'Scope',
    // },
  };
  const {
    // Load States
    ready,
    loading,
    setLoading,
    // List
    list,
    count,
    refresh,
    // Data Manipulation
    search,
    pagination,
    filters,
  } = useFetchList('submissions/list', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: filterList,
  });

  // Methods
  const switchHandler = async (submissionId, status) => {
    try {
      setLoading(true);
      await Api.put(`submissions/lock/${submissionId}?status=${status}`);
      refresh();
    } catch (error) {
      notify.error(error.response.data.message);
    } finally {
      setLoading(false);
    }
  };

  // Archive Submission
  const ConfirmText = (value) => {
    return (
      <div>
        <div className="flex mx-auto p-4 mb-7 bg-[#ddd1f8] w-24 h-24 rounded-full">
          <div className="flex mx-auto mb-7 bg-[#cab6f5] w-16 h-16 justify-center rounded-full">
            <Icon icon="hugeicons:archive-02" className="text-[#9061F9]" width="40" />
          </div>
        </div>
        {value ? (
          <p>
            Once confirmed, {value} test{value > 1 && 's'} will be archived permanently!
          </p>
        ) : (
          <p>Once confirmed, This test will be archived permanently!</p>
        )}
      </div>
    );
  };
  const handleArchive = async (row) => {
    showConfirm(ConfirmText(), {
      async onConfirm() {
        try {
          await Api.delete(`submissions/single/${row._id}`);
          refresh(true);
          notify('Test archived successfully!');
        } catch (error) {
          notify.error(error.response.data.message);
        } finally {
          hideConfirm();
        }
      },
    });
  };

  // Delete all selected ids
  const handleArchiveSelectedIds = async () => {
    if (selectedIds.length) {
      showConfirm(ConfirmText(selectedIds.length), {
        async onConfirm() {
          try {
            setLoading(true);
            await Api.delete('submissions/multi', { ids: selectedIds });
            setSelectedIds([]);
            refresh(true);
            notify('Tests deleted successfully!');
          } catch (error) {
            notify.error(error.response.data.message);
          } finally {
            hideConfirm();
            setLoading(false);
          }
        },
      });
    }
  };

  // Lock all selected ids
  const handleLockSelectedIds = async (value) => {
    try {
      setLoading(true);
      await Api.put('submissions/lock/multi', { ids: selectedIds, status: value });
      setLockUnlockAll(value);
      refresh(true);
    } catch (error) {
      notify.error(error.response.data.message);
    } finally {
      setLoading(false);
    }
  };

  // Download Excel all selected ids
  const handleDownloadExcelSelectedIds = async () => {
    if (selectedIds.length) {
      try {
        setLoading(true);
        const response = await api.get(`submissions/report`, {
          responseType: 'blob',
          params: { ids: selectedIds },
        });
        const url = window.URL.createObjectURL(
          new Blob([response.data], {
            type: response.headers['content-type'],
          })
        );
        const a = document.createElement('a');
        a.href = url;
        a.download = 'data.xlsx';
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
        refresh(true);
      } catch (error) {
        notify.error(error.response.data.message);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <>
      {/* Table */}
      <Table
        ready={ready}
        loading={loading}
        title="All Results"
        searchPlaceholder={screen.customScreen ? 'Search by name or applicant' : 'Name or applicant'}
        rows={list}
        count={count}
        search={search}
        pagination={pagination}
        filters={filters}
        slots={{
          quiz: (_, row) => {
            const element = row.title;
            return (
              <div className="flex gap-x-1 relative">
                <div className={`break-words overflow-auto whitespace-normal text-clip`}>
                  <div
                    className={`text-gray-800 font-medium capitalize dark:text-grayTextOnDarkMood lg:truncate ${
                      !showMoreMap[row._id] && 'truncate sm:overflow-visible sm:whitespace-normal'
                    }`}
                  >
                    {element}
                  </div>
                </div>
                {screen.gt.md() && (
                  <Tooltip content={element} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                    <div className="w-full h-full absolute left-0 top-0"></div>
                  </Tooltip>
                )}
              </div>
            );
          },
          applicant: (_, row) => {
            const element = (
              <>
                <div className="lg:truncate">{row.applicantName || '—'}</div>
                <div className="lg:truncate">{row.applicantEmail}</div>
              </>
            );
            return (
              <div className="flex gap-x-1 relative">
                <div className={`break-words overflow-auto whitespace-normal text-clip`}>
                  <div className="text-gray-800 font-medium dark:text-grayTextOnDarkMood lg:truncate">{element}</div>
                </div>
                {screen.gt.md() && (
                  <Tooltip content={element} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                    <div className="w-full h-full absolute left-0 top-0"></div>
                  </Tooltip>
                )}
              </div>
            );
          },
          score: (_, row) => {
            const result = row.score;
            const weirdCheating = row.cheatingPercentage;
            const element = <div>Just a heads up, this applicant showed some unusual behavior. Please take a look at the details for more info.</div>;
            const weird = () => {
              if (row.status === 3 && weirdCheating) {
                return (
                  <Tooltip content={element}>
                    <Icon icon="f7:exclamationmark-circle" className="text-[#FF4C4C]" width={20} />
                  </Tooltip>
                );
              }
            };

            if (result < 50) {
              return (
                <div className="flex gap-2 text-chartPoorCircle text-sm">
                  {result}% {weird()}
                </div>
              );
            } else if (result <= 80) {
              return (
                <div className="flex gap-2 text-chartGoodCircle text-sm">
                  {result}% {weird()}
                </div>
              );
            } else if (result > 80) {
              return (
                <div className="flex gap-2 text-chartExcellentCircle text-sm">
                  {result}% {weird()}
                </div>
              );
            } else {
              return '—';
            }
          },
          status: (_, row) => {
            const status = row.status;
            const statusResult = <EnumText name={'SubmissionStatus'} value={status} />;
            const statusColor = () => {
              switch (status) {
                case 1:
                  return 'text-statusColorNotStartedText bg-statusColorNotStartedBackground';
                case 2:
                  return 'text-statusColorInProgressText bg-statusColorInProgressBackground';
                case 3:
                  return 'text-statusColorSubmittedText bg-statusColorSubmittedBackground';
                default:
                  return '';
              }
            };
            return (
              <div>
                <div className={`w-fit px-3 py-2 rounded-full text-xs font-normal flex gap-1 ${statusColor()}`}>
                  <Icon icon="material-symbols:circle" width={8} />
                  {statusResult}
                </div>
              </div>
            );
          },
          difficulty: (_, row) => {
            let difficultyIcon;
            let difficultyColor;
            let iconSize = 'text-sm';

            switch (row.difficulty) {
              case 1:
                difficultyIcon = <FaUserGraduate className={`${iconSize} text-teal-700`} />; // Intern
                difficultyColor = ' text-teal-700 ';
                break;

              // Star Icon fresh level
              case 2:
                difficultyIcon = <FaUser className={`${iconSize} text-sky-800`} />; // Fresh
                difficultyColor = 'text-sky-800 ';
                break;
              // Medal Star junior
              case 3:
                difficultyIcon = <FaStar className={`${iconSize} text-amber-700`} />; // Junior
                difficultyColor = ' text-amber-700 ';
                break;
              // betetr medal star midlevel
              case 4:
                difficultyIcon = <FaMedal className={`${iconSize} text-orange-700`} />; // Mid-level
                difficultyColor = 'text-orange-700';
                break;

              // Tropy icon for senior with star
              case 5:
                difficultyIcon = <Icon icon="solar:crown-star-bold" width={18} className={`${iconSize} text-red-800`} />; // Senior
                difficultyColor = 'text-red-800';
                break;
              default:
                difficultyIcon = null;
            }
            return (
              <span className={`inline-flex items-center  py-1 text-sm font-medium rounded-full capitalize ${difficultyColor}`}>
                <span className="mr-1 flex items-center justify-center">{difficultyIcon}</span>
                <EnumText name={'QuizDifficulty'} value={row.difficulty} />
              </span>
            );
          },
          authorName: (_, row) => {
            const date = new Date(row.createdAt);
            const element = (
              <>
                <div className="lg:truncate font-normal">{row.authorName || '—'}</div>
                <div className="font-normal">{date.toDateString().slice(4)}</div>
              </>
            );
            return (
              <div className="flex gap-x-1 relative">
                <div className={`break-words overflow-auto whitespace-normal text-clip`}>
                  <div className="text-gray-400 font-normal capitalize dark:text-gray-400 lg:truncate">{element}</div>
                </div>
                {screen.gt.md() && (
                  <Tooltip content={element} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                    <div className="w-full h-full absolute left-0 top-0"></div>
                  </Tooltip>
                )}
              </div>
            );
          },
        }}
        columns={[
          {
            key: 'quiz',
            label: 'Test',
            width: '15%',
            tooltip: true,
          },
          {
            key: 'applicant',
            label: 'Applicant',
            width: '25px',
            tooltip: true,
          },
          { key: 'difficulty', label: 'Difficulty', width: '13%' },
          { key: 'score', label: 'Score', width: '10%' },
          {
            key: 'status',
            label: 'Status',
            width: '13%',
          },
          { key: 'authorName', label: 'Creation', width: '12%' },
          {
            key: 'locked',
            label: 'Actions',
            width: '12%',
            buttons(_, row) {
              return [
                ...(row.status !== 3
                  ? [
                      {
                        label: 'Copy Link',
                        icon: 'material-symbols:content-copy-outline',
                        color: 'text-black dark:text-white',
                        isCopied: row.status,
                        onClick() {
                          navigator.clipboard.writeText(`${ORIGIN}/test/${row._id}`);
                          notify('Link copied');
                        },
                      },
                    ]
                  : []),
                {
                  label: 'Show Progress',
                  icon: 'iconamoon:eye',
                  color: 'text-black dark:text-white',
                  path: `/app/tests/result/view/${row._id}`,
                  // @TODO: Delete when done testing
                  // onClick() {
                  //   navigate(`/app/tests/result/view/${row._id}`);
                  // },
                },
                // @TODO: Add when needed
                // {
                //   label: 'Archive',
                //   color: '#000000',
                //   icon: 'hugeicons:archive-02',
                //   onClick() {
                //     handleArchive(row);
                //   },
                // },
              ];
            },
            locked() {
              return [
                {
                  label: {
                    unlock: 'Unlock Test',
                    lock: 'Lock Test',
                  },
                  color: 'text-black dark:text-white',
                  switchHandler,
                },
              ];
            },
            report(_, row) {
              return [
                {
                  label: 'Download',
                  icon: 'mingcute:file-download-line',
                  color: 'text-black dark:text-white',
                  onClick() {
                    window.open(`/app/tests/pdf/${row._id}?type=submissions`, '_blank', 'noopener,noreferrer');
                  },
                },
              ];
            },
          },
        ]}
        // multiSelectedRow={{
        //   selectedIds: selectedIds,
        //   setSelectedIds: setSelectedIds,
        //   handleArchiveSelectedIds: handleArchiveSelectedIds,
        //   lockUnlockAll: lockUnlockAll,
        //   handleLockSelectedIds: handleLockSelectedIds,
        //   handleDownloadExcelSelectedIds: handleDownloadExcelSelectedIds,
        // }}
        noDataFound={{
          icon: 'healthicons:i-exam-multiple-choice-outline',
          message: 'No tests evaluated yet.',
        }}
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
      />

      {/* Creation Dialog */}
      {isCreateDialogVisible && <SubmissionsCreationDialog onClose={() => setCreateDialogVisibility(false)} />}

      {/* Creation New Test Dialog */}
      {isCreateTestDialogVisible && <SubmissionsCreationTestDialog onClose={() => setCreateTestDialogVisibility(false)} />}

      {/* Ai Interview */}
      {aiDialog && <AiIntreviewDialog onClose={() => setAiDialog(false)} />}

      {/* Lock Dialog */}
      {/* {isLockDialogVisible && <SubmissionsLockDialog onClose={() => setLockDialogVisibility(false)} onFinish={refresh} />} */}
    </>
  );
};
