import React, { useEffect, useState, useContext } from 'react';

// UI
import { Card, useNotify, Api, Icon, useFetchList, SubscribeCard, ToggleFilter } from '/src';
// Components
import { Jumbotron, TabsButtons } from '/src';
import { ShowFullInfo } from '../components/show-full-info';
import { SubmissionsCreationBlockDialog } from '../components/creation-block';
import { SubmissionsCreationDialog } from '../components/creation-dialog';
import { SubmissionsCreationTestDialog } from '../components/creation-test-dialog';
import { CategoryCard } from '../components/category-card';
import { TestNotExist } from '../components/test-not-exist';
import { CardPlaceholder } from '../components/card/card-placeholder';
import { AppContext } from '/src/components/provider';
import { AiIntreviewDialog } from '../components/ai-dialog';

export const SubmissionsBankPage = () => {
  // UI Hooks
  const { notify } = useNotify();

  // State
  const [isShowFullInfo, setShowFullInfo] = useState(false);
  const [isCreateBlockVisible, setCreateBlockVisibility] = useState(false);
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [isCreateTestDialogVisible, setCreateTestDialogVisibility] = useState(false);
  const [isTestNotExist, setTestNotExist] = useState(false);
  const [blockIndex, setBlockIndex] = useState(0);
  const [subBlockIndex, setBlockSubIndex] = useState(0);
  const [testId, setTestId] = useState(null);
  const [back, setBack] = useState(false);
  const [showInfo, setShowInfo] = useState(false);
  const [blockDetails, setBlockDetails] = useState({
    titleDetails: '',
    blockIdDetails: '',
    testIdDetails: '',
  });
  const [showSpecialAccessButton, setShowSpecialAccessButton] = useState(false);
  const [showSubscribe, setShow1Subscribe] = useState(false);
  const [aiDialog, setAiDialog] = useState(false);

  // User Data
  const { userData } = useContext(AppContext);
  // const isPermittedSuperAdmin = userData?.roles?.some((role) => ['super-admin', 'admin'].includes(role));

  // Methods
  const { ready, loading, count, list, refresh, search, pagination, filters } = useFetchList('/blocks/list', {
    search: '',
    filters: {
      category: {
        label: 'Category',
        lookup: 'category',
      },
      subCategory: {
        label: 'Sub Category',
        lookup: 'subcategory',
        parentLookup: { key: 'category', fieldName: 'categoryId' },
      },
      difficulty: {
        label: 'Difficulty',
        enum: 'QuizDifficulty',
      },
    },
  });

  const handlePost = async (test) => {
    try {
      setTestId(test._id);
      setCreateDialogVisibility(true);
    } catch (error) {
      notify.error(error.response.data.message);
      setTestNotExist(true);
    }
  };

  const showMoreInfo = (blockIndex, subBlockIndex) => {
    setBlockIndex(blockIndex);
    setBlockSubIndex(subBlockIndex);
    setShowFullInfo(true);
  };

  const closeSubmissionsCreationDialog = () => {
    setCreateDialogVisibility(false);
    setTestId(null);
  };

  const closeSubmissionsCreationTestDialog = () => {
    if (blockDetails?.testIdDetails) {
      setCreateTestDialogVisibility(false);
      setCreateBlockVisibility(true);
    } else {
      setBlockDetails(null);
      setCreateTestDialogVisibility(false);
    }
  };

  const creationSubmissionsDialog = (test, back) => {
    setShowFullInfo(false);
    setBack(back);
    handlePost(test);
  };

  const backButton = () => {
    setShowFullInfo(true);
    setCreateDialogVisibility(false);
  };

  const handleEditBlock = (singleBlock) => {
    setBlockDetails({
      blockIdDetails: singleBlock._id,
      titleDetails: singleBlock.title,
    });
    setCreateBlockVisibility(true);
  };

  const handleAddBlockTests = (singleBlock) => {
    setBlockDetails({ ...blockDetails, blockIdDetails: singleBlock._id });
    setCreateTestDialogVisibility(true);
  };

  const handleEditBlockTest = async (blockId, testId) => {
    setCreateBlockVisibility(false);
    try {
      const response = await Api.get(`/templates/template/single/${testId}`, {
        blockId: blockId,
      });
      setBlockDetails({ ...blockDetails, testIdDetails: response });
    } catch (error) {
      notify.error(error);
    }
    setCreateTestDialogVisibility(true);
  };

  // On Mount
  useEffect(() => {
    const hasAccess = userData?.roles.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
    setShowSpecialAccessButton(hasAccess);
  }, []);

  // Handle Ready
  // if (!ready) {
  //   return <CardPlaceholder />;
  // }

  return (
    <>
      <div>
        <div className="space-y-4">
          {/* Header Only In Large Screen */}
          <Jumbotron />
          {/* <div className="sm:flex gap-3 items-center justify-between space-y-2">
          <TabsButtons />
        </div> */}
        </div>

        <div className="flex justify-end gap-4 my-4">
          {/* Search bar */}
          <div className="flex flex-row items-center space-x-3 space-y-0 justify-between shadow-sm">
            <div className="relative w-full mb-4 sm:mb-0">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Icon icon="carbon:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search tests..."
                className="bg-gray-white border border-gray-200 text-gray-800 text-[13.5px] rounded-lg  block w-full lg:w-[270px] pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white focus:ring-0 focus:border-gray-300"
                value={search.value}
                onInput={(e) => search.update(e.target.value)}
              />
            </div>
          </div>

          <ToggleFilter filters={filters} />
        </div>

        {!ready && <CardPlaceholder />}

        {/* Blocks List */}
        {list.length > 0 && (
          <div className="grid grid-cols-1 grid-rows-[320px] sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4 mt-2">
            {list
              .filter((singleBlock) => singleBlock.quiz && singleBlock.quiz.length > 0)
              .map((singleBlock, blockIndex) => {
                if (!singleBlock || !singleBlock.quizzes) return null;
                return (
                  <CategoryCard
                    key={singleBlock.subCategory}
                    subCategoryName={singleBlock.subCategoryName}
                    index={subBlockIndex}
                    quizzes={singleBlock.quiz}
                    blockIndex={blockIndex}
                    subBlockIndex={subBlockIndex}
                    showMoreInfo={showMoreInfo}
                    creationSubmissionsDialog={creationSubmissionsDialog}
                    refresh={refresh}
                    setCreateBlockVisibility={setCreateBlockVisibility}
                    setCreateTestDialogVisibility={setCreateTestDialogVisibility}
                  />
                );
              })}
          </div>
        )}

        {/* Information Lamp Icon */}
        <div className="fixed bottom-[10%] right-8 w-full cursor-pointer z-10">
          {showInfo && (
            <div className="text-[#374151] absolute top-[-155px] right-0 rounded-lg bg-[#F2ECFF] dark:bg-[#d0bcfd] p-4">
              <p className="mb-2 font-medium">Applicant Category</p>
              <div className="text-xs flex flex-col gap-1">
                <p>Intern: 0 years of experience.</p>
                <p>Fresh: 0 -1 years of experience.</p>
                <p>Junior : 1-3 years of experience.</p>
                <p>Mid-Level : 3-5 years of experience.</p>
                <p>Senior : 5+ years of experience.</p>
              </div>
              <div className="absolute top-full right-[14px] w-0 h-0 border-[10px] border-solid border-transparent border-t-[#F2ECFF] dark:border-t-[#d0bcfd] border-r-[10px] "></div>
            </div>
          )}
          <div
            onClick={() => setShowInfo(!showInfo)}
            className="w-[50px] h-[50px] rounded-[50%] absolute top-[50%] right-0 bg-[#F2ECFF] dark:bg-[#d0bcfd]"
          >
            <Icon
              icon={showInfo ? 'iconamoon:close-fill' : 'heroicons-outline:light-bulb'}
              width="30"
              className="text-center mt-[22%] text-primaryPurple"
            />
          </div>
        </div>

        {/* Card Full View */}
        {isShowFullInfo && (
          <ShowFullInfo
            singleDetails={list[blockIndex]}
            creationSubmissionsDialog={creationSubmissionsDialog}
            onClose={() => setShowFullInfo(false)}
          />
        )}

        {/* Creation Block */}
        {isCreateBlockVisible && (
          <SubmissionsCreationBlockDialog
            refresh={refresh}
            blockDetails={blockDetails}
            setBlockDetails={setBlockDetails}
            loading={loading}
            setCreateBlockVisibility={setCreateBlockVisibility}
            setCreateTestDialogVisibility={setCreateTestDialogVisibility}
            handleEditBlockTest={handleEditBlockTest}
          />
        )}

        {/* Creation Dialog */}
        {isCreateDialogVisible && (
          <SubmissionsCreationDialog testId={testId} back={back} backButton={backButton} onClose={closeSubmissionsCreationDialog} />
        )}

        {/* Creation New Test Dialog */}
        {isCreateTestDialogVisible && (
          <SubmissionsCreationTestDialog
            blockDetails={blockDetails}
            setBlockDetails={setBlockDetails}
            onClose={closeSubmissionsCreationTestDialog}
            setCreateBlockVisibility={setCreateBlockVisibility}
            setCreateTestDialogVisibility={setCreateTestDialogVisibility}
            refresh={refresh}
          />
        )}

        {/* Ai Interview */}
        {aiDialog && <AiIntreviewDialog onClose={() => setAiDialog(false)} />}

        {/* Test Not Available */}
        {isTestNotExist && <TestNotExist onClose={() => setTestNotExist(false)} />}

        {/* Subscription */}
        {showSubscribe && <SubscribeCard onClose={() => setShow1Subscribe(false)} />}
      </div>

      {/* Creation Block */}
      {/* {isCreateBlockVisible && (
        <SubmissionsCreationBlockDialog
          refresh={refresh}
          blockDetails={blockDetails}
          setBlockDetails={setBlockDetails}
          loading={loading}
          setCreateBlockVisibility={setCreateBlockVisibility}
          setCreateTestDialogVisibility={setCreateTestDialogVisibility}
          handleEditBlockTest={handleEditBlockTest}
        />
      )} */}
    </>
  );
};
