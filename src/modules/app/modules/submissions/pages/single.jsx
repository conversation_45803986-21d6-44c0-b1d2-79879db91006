import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { api } from '../../../../../services/axios';

// Components
import { Jumbotron, useScreenSize } from '/src';

// UI
import { Card, EnumText, useNotify, Api, Icon } from '/src';
import { Tooltip } from 'flowbite-react';
import { Result } from '../components/result-chart';
import { WeirdBehaviorSingleDialog } from '../components/single-dialog';
import { ProgressCardPlaceholder } from '../components/card/progress-card-placeholder';
import { ShowProgressBlocks } from '../components/show-progress-blocks';
import { TestAnswersCardPlaceholder } from '../components/card/test-answers-placeholder';
import WarningComponent from '../components/warning-toggle';
export const SubmissionsSinglePage = () => {
  // Route Params
  const { id } = useParams();

  // UI Hooks
  const { notify } = useNotify();
  const screen = useScreenSize();

  // State
  const [details, setDetails] = useState(null);
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [submissionStages, setSubmissionStages] = useState([]);
  const [isMaximized, setIsMaximize] = useState(false);
  const [weirdBehaviorData, setWeirdBehavior] = useState();

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get(`submissions/single/${id}`);
      setDetails(response.data);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  const handleGetWeirdBehavior = async () => {
    try {
      const response = await Api.get(`submissions/behavior/${id}`);
      setWeirdBehavior(response.data);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  const handleGetSubmissionStages = async () => {
    try {
      const response = await Api.get(`submissions/stages?submissionId=${id}&page=${1}&size=${20}`);
      setSubmissionStages(response.data.stages);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  const handleToggleCard = async () => {
    setIsMaximize(!isMaximized);
    handleGetSubmissionStages();
  };

  const downloadDocument = async () => {
    try {
      const response = await api.get(`submissions/stages/report/${id}`, {
        responseType: 'blob',
      });
      const url = window.URL.createObjectURL(
        new Blob([response.data], {
          type: response.headers['content-type'],
        })
      );
      const a = document.createElement('a');
      a.href = url;
      a.download = 'test-report.xlsx';
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  // On Mount
  useEffect(() => {
    handleGet();
  }, []);

  useEffect(() => {
    if (details && details.submission.cheatingPercentage !== 0) {
      handleGetWeirdBehavior();
    }
  }, [details]);

  if (!details) {
    return <ProgressCardPlaceholder />;
  }

  // Function for values in small screen table
  const Section = ({ title, value }) => {
    return (
      <>
        <div className="sm:flex">
          <div className="h-full p-2 flex items-center self-center font-medium text-gray-700 dark:text-gray-400 w-44">
            <p className="break-words text-sm max-w-32">{title}</p>
          </div>
          <div className="p-2 self-center break-all font-medium text-gray-900 dark:text-white">{value || '—'}</div>
        </div>
      </>
    );
  };

  const convertToHoursAndMinutes = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return { hours, remainingMinutes };
  };

  const { hours, remainingMinutes } = convertToHoursAndMinutes(details.submission.quiz.duration);

  const handleResult = () => {
    if (details.submission.notStarted) return 'Not Started';
    else if (details.submission.score > 80) return 'Excellent';
    else if (details.submission.score >= 50) return 'Good';
    else if (details.submission.score < 50) return 'Poor';
    else return 'Completed';
  };
  const handleColors = () => {
    if (details.submission.notStarted) return { label: 'bg-[#EDEEF0] text-[#6B7280]' };
    else if (details.submission.score > 80)
      return {
        circle: 'text-chartExcellentCircle',
        label: 'text-chartExcellentTextColor bg-chartExcellentBackground border-chartExcellentTextColor',
      };
    else if (details.submission.score >= 50)
      return { circle: 'text-chartGoodCircle', label: 'text-chartGoodTextColor bg-chartGoodBackground border-chartGoodTextColor' };
    else if (details.submission.score < 50)
      return { circle: 'text-chartPoorCircle', label: 'text-chartPoorTextColor bg-chartPoorBackground border-chartPoorTextColor' };
  };

  const formatDate = (dateString) => {
    if (!dateString) return '—';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'medium',
      timeStyle: 'short',
    }).format(date);
  };

  return (
    <div className="space-y-8">
      <WarningComponent weirdBehaviorData={weirdBehaviorData} />

      {/* <!-- Test Meta Section --> */}
      <div className="bg-[#f1ecfc] bg-opacity-30 dark:bg-darkBackgroundCard p-4 rounded-lg shadow-lg">
        <div className="flex gap-2 items-center text-center pb-2 mb-4 border-b">
          <div className="text-gray-900  dark:text-gray-100 text-xl font-semibold dark:border-gray-500 mb-2 ">
            {details?.submission?.subCategoryName}
          </div>
          <span className="bg-purple-100 dark:bg-gray-700 text-indigo-500 dark:text-indigo-400 mb-3 text-center  px-4 py-2 rounded-full text-xs font-medium">
            {details.submission.categoryName}
          </span>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
          {/* Date & Time */}
          <div className="flex items-center  gap-2">
            <Icon icon="mdi:calendar-month-outline" className="text-indigo-500 dark:text-indigo-400" width="24" />
            <div>
              <span className="text-gray-600 dark:text-gray-500 text-sm">Created At:</span>
              <div className="text-gray-700 dark:text-grayTextOnDarkMood text-base font-medium"> {formatDate(details?.submission?.createdAt)}</div>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Icon icon="ph:chart-bar" className="text-indigo-500 dark:text-indigo-400" width="24" />
            <div>
              <span className="text-gray-600 dark:text-gray-500 text-sm ">Difficulty:</span>
              <div className=" text-gray-700 dark:text-grayTextOnDarkMood text-base font-medium">
                <EnumText name={'QuizDifficulty'} value={details.submission.quiz.difficulty || '—'} />
              </div>
            </div>
          </div>

          {/* Duration */}
          <div className="flex items-center gap-3">
            <Icon icon="mdi:clock-time-four-outline" className="text-indigo-500 dark:text-indigo-400" width="24" />
            <div>
              <span className="text-gray-600 dark:text-gray-500 text-sm ">Duration</span>
              <div className="text-gray-700 font-medium dark:text-grayTextOnDarkMood text-base">{details?.submission?.quiz.duration || '—'} min </div>
            </div>
          </div>

          {/* Number of Questions */}
          <div className="flex items-center gap-3">
            <Icon icon="ph:circle-wavy-question-fill" className="text-purple-500 dark:text-purple-400" width="25" />
            <div>
              <span className="text-gray-600 dark:text-gray-500 text-sm ">Number of Questions</span>
              <div className="text-gray-700 font-medium dark:text-grayTextOnDarkMood text-base">{details?.submission?.questions?.length || '—'}</div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-darkBackgroundCard p-6 rounded-lg shadow-xl mt-6">
        <div className="flex flex-col lg:flex-row gap-6 md:border-none">
          {/* Test Progress */}
          <div className="flex-3 grow bg-white dark:bg-darkBackgroundCard dark:border-none">
            <div className="space-y-6">
              <div className="flex flex-col md:flex-row md:items-start space-y-6 md:space-y-0 md:space-x-8">
                <div className="rounded-lg  flex-1 space-y-6">
                  {/* Applicant Information */}
                  <div className="space-y-4">
                    {/* Name, Email, and Phone */}
                    <div className="space-y-4">
                      <div className="flex gap-2 items-start">
                        <div className="flex flex-col md:flex-row items-start space-y-4 md:space-y-0 md:space-x-4 w-full ">
                          <div className="flex sm:flex-row sm:items-center space-x-4">
                            <Icon icon="mdi:account-circle" className="text-purple-500 dark:text-purple-400 " width="50" />
                            <div className="flex-1 space-y-1">
                              <p className="text-gray-800 dark:text-white text-xl font-bold">{details?.applicant?.name || '—'}</p>
                              <p className="text-gray-900 dark:text-gray-300 text-base text-ellipsis break-all">{details.applicant?.email || '—'}</p>
                              <p className="text-gray-900 dark:text-gray-300 text-base">{details.applicant?.mobileNumber || '—'}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Test Accessed and Time Taken */}
                    <div className="space-y-4">
                      <div className="flex items-center gap-3 text-md">
                        <Icon icon="mdi:clock-outline" className="text-purple-500 dark:text-purple-400" width="22" />
                        <span className="text-gray-600 dark:text-gray-500 ">
                          Started At:{' '}
                          <span className="font-medium text-gray-700 dark:text-grayTextOnDarkMood">{formatDate(details?.submission?.startedAt)}</span>
                        </span>
                      </div>
                      <div className="flex items-center gap-3 text-md">
                        <Icon icon="mdi:timer-sand" className="text-purple-500 dark:text-purple-400" width="22" />
                        <span className="text-gray-600  dark:text-gray-500">
                          Time Taken:{' '}
                          <span className="font-medium text-gray-700 dark:text-grayTextOnDarkMood"> {details.submission.timeTaken || '—'}</span>
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Question Breakdown */}
                  <div className="space-y-2 px-2">
                    <div className="flex flex-col md:flex-row justify-between">
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Score Breakdown</h2>
                      {/* Breakdown Labels */}
                      <div className="flex flex-col sm:flex-row justify-end md:justify-between items-start sm:items-center text-sm mt-2 gap-2 sm:gap-8 md:gap-2 lg:gap-8">
                        <div className="flex items-center gap-1">
                          <span className="w-2 h-2 inline-block bg-green-500"></span>
                          <span className="text-gray-900 dark:text-gray-100 ">Correct answers:</span>
                          <span className="text-gray-700 dark:text-gray-400 font-medium">
                            {details.submission.questionsSummary.correctAnswers}
                            {/* To be added when need percentage  */}
                            {/* <span className="ml-1 text-gray-900 font-medium dark:text-gray-100">
                              {(
                                (details.submission.questionsSummary.correctAnswers / details.submission.questionsSummary.totalQuestions) *
                                100
                              ).toFixed(1)}
                              %
                            </span> */}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <span className="w-2 h-2 inline-block bg-red-500"></span>
                          <span className="text-gray-900 dark:text-gray-100 ">Wrong answers:</span>
                          <span className="text-gray-700 dark:text-gray-400 font-medium">
                            {details.submission.questionsSummary.wrongAnswers}
                            {/* To be added when need percentage  */}
                            {/* <span className="ml-1 text-gray-900 font-medium dark:text-gray-100">
                              {(
                                (details.submission.questionsSummary.wrongAnswers / details.submission.questionsSummary.totalQuestions) *
                                100
                              ).toFixed(1)}
                              %
                            </span> */}
                          </span>
                        </div>
                        {details.submission.questionsSummary.unAnsweredQuestions > 0 && (
                          <div className="flex items-center gap-1">
                            <span className="w-2 h-2 inline-block bg-gray-400"></span>
                            <span className="text-gray-900 dark:text-gray-100 ">Unanswered:</span>
                            <span className="text-gray-700 dark:text-gray-400 font-medium">
                              {' '}
                              {details.submission.questionsSummary.unAnsweredQuestions}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Breakdown Bar */}
                    <div className="mt-2 h-4 bg-gray-200 flex">
                      <div
                        className="bg-green-500 h-full"
                        style={{
                          width: `${
                            (details.submission.questionsSummary.correctAnswers / details.submission.questionsSummary.totalQuestions) * 100
                          }%`,
                        }}
                      ></div>

                      {/* TODO : Decrease the contrast of colors consider using pastel colors (decease opacity) */}
                      {/* TODO : Add Skipped Question for interview.*/}
                      <div
                        className="bg-red-500 h-full"
                        style={{
                          width: `${(details.submission.questionsSummary.wrongAnswers / details.submission.questionsSummary.totalQuestions) * 100}%`,
                          marginLeft: '2px', // Small space between sections
                        }}
                      ></div>
                      {details.submission.questionsSummary.unAnsweredQuestions > 0 && (
                        <div
                          className="bg-gray-400 h-full"
                          style={{
                            width: `${
                              (details.submission.questionsSummary.unAnsweredQuestions / details.submission.questionsSummary.totalQuestions) * 100
                            }%`,
                          }}
                        ></div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Chart Progress Section */}
          <div className="font-medium dark:text-white space-y-4 self-center grow flex-1">
            <div className="flex flex-col items-center justify-center gap-4">
              {/* Test Charts */}
              <div className="relative self-center sm:col-span-2 mx-auto">
                <Result testResult={details.submission.score} />
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <p className={`text-4xl ${handleColors().circle} text-center`}>{details.submission.score}%</p>
                  <p className="text-center text-[#5C5C5C] text-md font-normal text-nowrap">Total Score</p>
                </div>

                <div
                  className={`w-full text-center p-1 text-base font-medium rounded-xl absolute left-1/2 -translate-x-1/2 bottom-0 py-2 border 
                    ${handleColors().label}`}
                >
                  {handleResult()}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-darkBackgroundCard p-6 rounded-lg shadow-lg mt-6 flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0 md:space-x-4">
        <div className="flex space-x-3">
          <p className="text-gray-800 dark:text-gray-200 text-lg">
            <span>
              You can download the <span className="text-primaryPurple font-medium">Test Answers</span>, primarily for{' '}
              <span className="text-primaryPurple font-medium">Technical Evaluation</span>.{' '}
            </span>
            <span className="font-medium">Click the button to download the full report.</span>
          </p>
        </div>
        <div>
          <Button onClick={downloadDocument} label="Download report" />
        </div>
      </div>
    </div>
  );
};
