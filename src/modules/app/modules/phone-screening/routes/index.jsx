import { Navigate, Outlet } from 'react-router-dom';
import { PhoneScreeningLayout } from '../layouts/main';
import { PhoneScreeningListPage } from '../pages/list';
import { SinglePhoneScreeningPage } from '../pages/single';

export default [
  {
    path: 'screening',
    element: <PhoneScreeningLayout />,
    // loader() {
    //   return {
    //     label: 'Screening',
    //   };
    // },
    children: [
      {
        path: '',
        element: <Navigate to="/app/screening/list" />,
      },
      {
        path: '',
        element: <Outlet />,
        loader() {
          return {
            label: 'Screening Managament',
            icon: '',
            title: 'Screening Management',
            subtitle: 'Here you can manage all screenings to match applicants with the right positions based on their skills. ',
          };
        },
        children: [
          {
            path: 'list',
            element: <PhoneScreeningListPage />,
          },
          {
            path: 'create',
            element: <SinglePhoneScreeningPage />,
            loader() {
              return {
                label: 'Create',
                icon: '',
                title: 'Create Screening',
                subtitle: 'Create tests using the form below.',
              };
            },
          },

          {
            path: 'view/:id',
            element: <SinglePhoneScreeningPage />,
            loader() {
              return {
                label: 'View',
                icon: '',
                title: 'View Screening',
                subtitle: "Review the screening details below. To make changes, click 'Edit Screening' to proceed.",
              };
            },
          },

          {
            path: 'edit/:id',
            element: <SinglePhoneScreeningPage />,
            loader() {
              return {
                label: 'Edit',
                icon: '',
                title: 'Edit Screening',
                subtitle: "Modify the screening as needed. Once satisfied with the changes, click 'Update' to save",
              };
            },
          },
        ],
      },
    ],
  },
];
