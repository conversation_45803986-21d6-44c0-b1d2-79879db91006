import { Dialog, Icon, useNotify } from '/src';

export const TestCreatedSucessfully = ({ quizUrl, onClose }) => {
  // Hooks
  const { notify } = useNotify();

  // Methods
  const handleCopyLink = () => {
    navigator.clipboard.writeText(quizUrl);
    notify('Link copied');
  };

  return (
    <Dialog show popup size="lg" className="text-center" onClose={onClose}>
      <div className="py-5  pt-0">
        <div>
          <div className="flex justify-center items-center mx-auto mb-5 !mt-0  text-gray-400 dark:text-gray-200">
            <img src="/images/Vector.svg" alt="done mark" />
          </div>
          <div className="text-center">
            <h2 className="text-center dark:text-white font-medium text-xl ">Screening Generated Successfully!</h2>
            <div className="w-full text-center mx-auto mt-2">
              <p className="text-center font-normal text-base   dark:text-white text-[#626262]">
                Send the link below for quick and easy access to the applicant
              </p>
            </div>
          </div>
          <div className="mt-5 py-1">
            <div className="grid w-full max-w-80 mx-auto text-center">
              <div className="relative flex ">
                <input
                  value={quizUrl}
                  type="text"
                  className="col-span-6 block w-full rounded-lg border border-gray-300 bg-gray-50 px-2.5 py-4 text-sm text-[#313437] pr-12 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-400 dark:placeholder:text-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                />
                <Icon
                  icon={'ooui:copy-ltr'}
                  onClick={() => handleCopyLink()}
                  className="dark:text-white cursor-pointer text-gray-400  text-2xl absolute right-3 top-1/4"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  );
};
