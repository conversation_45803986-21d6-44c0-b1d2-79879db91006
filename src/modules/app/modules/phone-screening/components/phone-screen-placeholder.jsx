export const PhoneScreenManagementPlaceHolder = () => (
  <div className="w-full relative">
    <div className="flex   justify-between flex-wrap xssm:flex-nowrap gap-4 items-center w-full p-2  m-2">
      <div className="w-full  space-y-4">
        <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse w-96 mb-2"></div>
        <div className="h-4 bg-gray-200 mt-3 rounded-full dark:bg-gray-600 w-11/12 mb-2 "></div>
      </div>
      <div className="w-full flex justify-start items-center space-x-4 animate-pulse sm:justify-end">
        <div className="h-12 bg-gray-300 rounded-full dark:bg-gray-600 w-52 mb-2"></div>
      </div>
    </div>

    <div className="flex  justify-between flex-wrap sm:flex-nowrap gap-4 items-center w-full p-2  m-2">
      <div className="w-full flex text-center items-center  gap-2 ">
        <div className="h-4 bg-gray-200  rounded-full dark:bg-gray-600 w-52"></div>
        <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse w-11 "></div>
      </div>
      <div className="w-full flex justify-start items-center space-x-4 animate-pulse sm:justify-end">
        <div className="h-10 bg-gray-200 rounded-xl dark:bg-gray-600  sm:w-72 w-3/4 mb-2"></div>
        <div className="h-10 bg-gray-200 rounded-lg dark:bg-gray-600 sm:w-20 w-1/4 mb-2"></div>
      </div>
    </div>

    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-6">
      <div className="space-y-6 border   text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700">
        <div className="w-44 text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <div className="flex  flex-wrap items-center   gap-4">
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
        </div>
        <div>
          <div className="w-full h-4  mb-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <div className="flex gap-6">
          <div className="h-9 bg-gray-300 rounded-lg dark:bg-gray-600 w-1/5 mb-2"></div>
          <div className="h-9 bg-gray-300 rounded-lg dark:bg-gray-600 w-1/3 mb-2"></div>
        </div>
      </div>

      <div className="space-y-6 border   text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700">
        <div className="w-44 text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <div className="flex  flex-wrap items-center   gap-4">
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
        </div>
        <div>
          <div className="w-full h-4  mb-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <div className="flex gap-6">
          <div className="h-9 bg-gray-300 rounded-lg dark:bg-gray-600 w-1/5 mb-2"></div>
          <div className="h-9 bg-gray-300 rounded-lg dark:bg-gray-600 w-1/3 mb-2"></div>
        </div>
      </div>

      <div className="space-y-6 border   text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700">
        <div className="w-44 text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <div className="flex  flex-wrap items-center   gap-4">
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
        </div>
        <div>
          <div className="w-full h-4  mb-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <div className="flex gap-6">
          <div className="h-9 bg-gray-300 rounded-lg dark:bg-gray-600 w-1/5 mb-2"></div>
          <div className="h-9 bg-gray-300 rounded-lg dark:bg-gray-600 w-1/3 mb-2"></div>
        </div>
      </div>
      <div className="space-y-6 border   text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700">
        <div className="w-44 text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <div className="flex  flex-wrap items-center   gap-4">
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
        </div>
        <div>
          <div className="w-full h-4  mb-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <div className="flex gap-6">
          <div className="h-9 bg-gray-300 rounded-lg dark:bg-gray-600 w-1/5 mb-2"></div>
          <div className="h-9 bg-gray-300 rounded-lg dark:bg-gray-600 w-1/3 mb-2"></div>
        </div>
      </div>

      <div className="space-y-6 border   text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700">
        <div className="w-44 text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <div className="flex  flex-wrap items-center   gap-4">
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
        </div>
        <div>
          <div className="w-full h-4  mb-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <div className="flex gap-6">
          <div className="h-9 bg-gray-300 rounded-lg dark:bg-gray-600 w-1/5 mb-2"></div>
          <div className="h-9 bg-gray-300 rounded-lg dark:bg-gray-600 w-1/3 mb-2"></div>
        </div>
      </div>

      <div className="space-y-6 border   text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700">
        <div className="w-44 text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <div className="flex  flex-wrap items-center   gap-4">
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
          <div className="flex  gap-1">
            <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-6 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-600 w-28 mb-2"></div>
          </div>
        </div>
        <div>
          <div className="w-full h-4  mb-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <div className="flex gap-6">
          <div className="h-9 bg-gray-300 rounded-lg dark:bg-gray-600 w-1/5 mb-2"></div>
          <div className="h-9 bg-gray-300 rounded-lg dark:bg-gray-600 w-1/3 mb-2"></div>
        </div>
      </div>

      {/* <div className="space-y-9 border   text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
        <div className="w-44 text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <div className="flex gap-4">
          <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-32 mb-2"></div>
          <div className="w-32 h-5 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>

        <div>
          <div className="w-full h-4  mb-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          <div className="w-full h-4  mb-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-5/6 mb-2 "></div>
          <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-2/6 "></div>
        </div>

        <div className="flex gap-6">
          <div className="h-7 bg-gray-300 rounded-full dark:bg-gray-600 w-2/6 mb-2"></div>
          <div className="h-7 bg-gray-300 rounded-full dark:bg-gray-600 w-2/6 mb-2"></div>
        </div>
      </div> */}
    </div>
  </div>
);
