export const UpdateScreeningPlaceholder = () => (
  <div>
    <div className="my-3 rounded-lg shadow-sm h-10 animate-pulse items-center">
      <div className="h-5 bg-gray-200 rounded-full flex items-center justify-center mb-3 dark:bg-gray-600 w-40"></div>
      <div className="h-4 bg-gray-200 rounded-full flex items-center justify-center dark:bg-gray-600 w-full"></div>
    </div>
    <div className="flex items-center my-9 border rounded-md py-12 px-3 w-full flex-col justify-between pt-4">
      <div className="w-full">
        <div className="flex w-full justify-between">
          <div className="flex gap-5 items-start align-middle">
            <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-8 mb-2.5"></div>
            <div className="h-3  bg-gray-300 rounded-full dark:bg-gray-600 w-60 mb-3"></div>
          </div>
          <div className="flex px-3 gap-3 align-middle items-center">
            <div className="h-4  bg-gray-200 rounded-full dark:bg-gray-600 w-32 mb-3"></div>
            <div className="h-8 bg-gray-200 rounded-full dark:bg-gray-600 w-24 mb-2.5"></div>
          </div>
        </div>
      </div>
    </div>
    <div className="flex items-center border rounded-md p-3 w-full flex-col justify-between pt-4">
      <div className="w-full">
        <div className="flex w-full justify-between">
          <div className="flex gap-5 items-center align-middle">
            <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-8 mb-2.5"></div>
            <div className="h-3  bg-gray-300 rounded-full dark:bg-gray-600 w-60 mb-3"></div>
          </div>
        </div>
      </div>

      <div className="flex justify-between mb-3  w-full py-5 rounded-lg border px-3 mt-2 gap-6 ">
        <div className="flex gap-5 items-center">
          <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-600 w-10"></div>
          <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-96"></div>
        </div>
        <div className="flex  gap-3 items-center justify-around w-18 px-6 ">
          <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-600 w-10"></div>
          <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-600 w-10"></div>
        </div>
      </div>
      <div className="flex justify-between mb-3  w-full py-5 rounded-lg border px-3 mt-2 gap-6 ">
        <div className="flex gap-5 items-center">
          <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-600 w-10"></div>
          <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-96"></div>
        </div>
        <div className="flex  gap-3 items-center justify-around w-18 px-6 ">
          <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-600 w-10"></div>
          <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-600 w-10"></div>
        </div>
      </div>

      <div className="flex justify-between mb-3  w-full py-5 rounded-lg border px-3 mt-2 gap-6 ">
        <div className="flex gap-5 items-center">
          <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-600 w-10"></div>
          <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-96"></div>
        </div>
        <div className="flex  gap-3 items-center justify-around w-18 px-6 ">
          <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-600 w-10"></div>
          <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-600 w-10"></div>
        </div>
      </div>

      <div className="flex justify-between mb-3  w-full py-5 rounded-lg border px-3 mt-2 gap-6 ">
        <div className="flex gap-5 items-center">
          <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-600 w-10"></div>
          <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-96"></div>
        </div>
        <div className="flex  gap-3 items-center justify-around w-18 px-6 ">
          <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-600 w-10"></div>
          <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-600 w-10"></div>
        </div>
      </div>

      <div className="flex justify-between mb-3  w-full py-5 rounded-lg border px-3 mt-2 gap-6 ">
        <div className="flex gap-5 items-center">
          <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-600 w-10"></div>
          <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-600 w-96"></div>
        </div>
        <div className="flex  gap-3 items-center justify-around w-18 px-6 ">
          <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-600 w-10"></div>
          <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-600 w-10"></div>
        </div>
      </div>
    </div>
    <div className="flex items-start   mt-2 gap-4 p-2">
      <div className="h-11 bg-gray-200 rounded-full dark:bg-gray-600 w-56 ml-auto"></div>
      <div className="h-11 bg-gray-200 rounded-full dark:bg-gray-600 w-56"></div>
    </div>
  </div>
);
