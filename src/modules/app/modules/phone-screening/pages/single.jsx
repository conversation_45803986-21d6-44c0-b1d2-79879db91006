// React
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

// Flowbite
import { TextInput as FlowbiteTextInput, Pagination } from 'flowbite-react';

// UI
import {
  Icon,
  useConfirmDialog,
  Api,
  useNotify,
  Button,
  Card,
  Jumbotron,
  useForm,
  Form,
  useValidate,
  TextInput,
  Select,
  Textarea,
  CustomIcon,
  useScreenSize,
  StaticData,
} from '/src';

// Components
import { UpdateScreeningPlaceholder } from '../components/update-screening-placeholder';
import { QuestionsListSection } from '../components/question-list-section-single';

/*
  questionsListFilterdData = {
    _id: "",
    title: "",
    deleted: [], 
    created: [],
    updated: [],
*/

export const SinglePhoneScreeningPage = () => {
  // Constant
  const exceededLimitNumber = 30;
  const exceededLimitMessage = "Total questions can't exceed " + exceededLimitNumber;

  // State
  const [questionsListFilterdData, setQuestionsListFilterdData] = useState(null);
  const [addQuestionFieldsDropdown, setAddQuestionFieldsDropdown] = useState({ showDropdown: false, inputValueDropdown: '' });
  const [loading, setLoading] = useState(false);
  const [isViewOnly, setViewOnly] = useState(false);

  // Pagination
  const count = questionsListFilterdData?.length;
  const [page, setPage] = useState(1);
  const size = 20;
  const pagesCount = Math.ceil(count / size);
  const showingText = `${count ? page * size - size + 1 : count} - ${page * size > count ? count : page * size}`;

  // Hooks
  const { showConfirm, hideConfirm } = useConfirmDialog();
  const { id } = useParams();
  const { notify } = useNotify();
  const navigate = useNavigate();
  const { isRequired, isValidateMaxAndMinNumber } = useValidate();
  const screen = useScreenSize();

  // Form
  const { form, setFieldValue, setFormValue, resetForm } = useForm({
    _id: '',
    title: '',
    difficulty: 0,
    internPhase: 0,
    duration: 10,
    description: '',
  });

  const handleGetQuizzData = async () => {
    try {
      setLoading(true);
      const response = await Api.get(`quizzes/single/phoneScreening/${id}`);
      setFormValue(response.data[0]);
      setQuestionsListFilterdData(response.data[0].questions.map((question, index) => ({ ...question, index: index + 1 })));
    } catch (error) {
      notify.error(error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  };

  const ConfirmText = () => {
    return (
      <div>
        <div className="flex mx-auto p-4 mb-7 bg-[#ddd1f8] w-24 h-24 rounded-full">
          <div className="flex mx-auto mb-7 bg-[#cab6f5] w-16 h-16 justify-center rounded-full">
            <Icon icon="uil:question" className="text-[#9061F9]" width="40" />
          </div>
        </div>
        <p>Once confirmed, This screening will be {id ? 'updated permanently!' : 'created!'}</p>
      </div>
    );
  };

  const handleSubmitButton = async () => {
    if (!form?.difficulty) {
      notify.error('Choose difficulty');
    } else {
      showConfirm(ConfirmText(), {
        onConfirm() {
          handleSubmit();
          hideConfirm();
        },
      });
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      let payload = {
        questionsStatus: {
          deleted: questionsListFilterdData.filter((question) => question.deleted && !question.created).map((question) => question._id),
          created: questionsListFilterdData
            .filter((question) => !question.deleted && question.created)
            .map((question) => ({ title: question.title })),
          updated: questionsListFilterdData
            .filter((question) => !question.deleted && !question.created && question.updated)
            .map((question) => ({ title: question.title, id: question._id })),
        },
        title: form?.title,
        duration: form?.duration,
        description: form?.description,
        difficulty: form?.difficulty,
      };
      if (form.difficulty === 1) {
        payload.internPhase = form.internPhase;
      }
      await Api.put(`quizzes/single/phoneScreening/${id}`, payload);
      notify('Screening updated successfull!');
      navigate('/app/screening');
    } catch (error) {
      notify.error(error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTestSCreening = async () => {
    showConfirm(ConfirmText(), {
      async onConfirm() {
        try {
          setLoading(true);
          let payload = {
            title: form?.title,
            difficulty: form?.difficulty,
            duration: form?.duration,
            description: form?.description,
            questionIds: questionsListFilterdData?.map((question) => question.title),
          };
          if (form.difficulty === 1) {
            payload.internPhase = form.internPhase;
          }
          await Api.post('quizzes/single/phoneScreening', payload);
          notify('Screening created successfull!');
          navigate('/app/screening');
        } catch (error) {
          notify.error(error?.response?.data?.message);
        } finally {
          setLoading(false);
          hideConfirm();
        }
      },
    });
  };

  const handleAddQuestionFields = () => {
    setQuestionsListFilterdData((prev) => [
      ...(prev || []),
      ...Array(addQuestionFieldsDropdown.inputValueDropdown)
        .fill(null)
        .map((_, index) => ({ title: '', _id: Math.random(), created: true, index: (count || 0) + index + 1 })),
    ]);
    setAddQuestionFieldsDropdown((prev) => ({ ...prev, inputValueDropdown: '', showDropdown: false }));
  };

  useEffect(() => {
    if (id) {
      setQuestionsListFilterdData(null);
      handleGetQuizzData();
    }
    setViewOnly(window.location.href.includes('view'));
  }, [window.location.href]);

  if (id && !questionsListFilterdData) {
    return <UpdateScreeningPlaceholder />;
  }

  const JumbotronModuleInfo = {
    moduleName: 'screening',
    routeName: 'screening',
  };

  return (
    <>
      <Form className="flex flex-col text-[#111827] dark:text-grayTextOnDarkMood" onSubmit={id ? handleSubmitButton : handleCreateTestSCreening}>
        <Jumbotron header type={id && 'update'} isShowViewButtons={JumbotronModuleInfo} />

        <Card className="space-y-4 !pb-4 !px-0 !py-0 my-2">
          <div className="flex flex-col gap-3 sm:gap-0 sm:flex-row justify-between border-b px-4 pb-3 py-2 bg-[#F8FAFC] dark:bg-gray-700 dark:border-none rounded-t-md">
            <p className="text-[17px] font-medium text-[#111827] py-2 dark:text-white">Screening Details</p>
          </div>

          <div className={`sm:grid grid-cols-1 space-y-3 ${form.difficulty === 1 ? 'grid-cols-4' : ' sm:grid-cols-3'} items-start gap-4 px-4`}>
            <div className="col-span-1">
              <TextInput
                label="Title"
                name="title"
                placeholder="Enter title"
                value={form.title}
                onChange={setFieldValue('title')}
                validators={[isRequired()]}
                requiredLabel
                readOnly={isViewOnly}
              />
            </div>

            <div className={`grid gap-3 sm:!mt-0 ${form.difficulty === 1 ? 'col-span-3 sm:grid-cols-3 grid-cols-2' : 'col-span-2 grid-cols-2'}`}>
              <Select
                label="Level"
                name="difficulty"
                requiredLabel
                required
                value={form.difficulty}
                onChange={setFieldValue('difficulty', Number)}
                lookup="$QuizDifficulty"
                dropIcon={true}
                validators={[isRequired()]}
                placeholder="Search for difficulty level"
                readOnly={isViewOnly}
              />
              {form.difficulty === 1 && (
                <Select
                  name="internPhase"
                  label="Intern Phase"
                  value={form.internPhase}
                  onChange={setFieldValue('internPhase', Number)}
                  lookup="$InternPhase"
                  dropIcon={true}
                  validators={form.difficulty === 1 ? [isRequired()] : []}
                  readOnly={isViewOnly}
                />
              )}
              <TextInput
                name="duration"
                label="Duration"
                labelTooltip="Duration time in minutes."
                requiredLabel
                placeholder="Enter duration..."
                type="number"
                value={form.duration}
                validators={[isRequired(), isValidateMaxAndMinNumber('min', 10), isValidateMaxAndMinNumber('max', 120)]}
                onChange={setFieldValue('duration', Number)}
                readOnly={isViewOnly}
              />
            </div>

            <div className={`${form.difficulty === 1 ? 'col-span-4' : 'col-span-3'}`}>
              <Textarea
                label="Description"
                name="description"
                placeholder="Enter description..."
                type="textarea"
                rows="4"
                value={form.description}
                onChange={setFieldValue('description')}
                requiredLabel
                readOnly={isViewOnly}
                validators={[isRequired()]}
              />
            </div>
          </div>
        </Card>

        <Card className="space-y-2 !px-0 !py-0 my-2">
          <div className="flex flex-col gap-3 sm:gap-0 sm:flex-row justify-between border-b px-4 pb-3 py-2 bg-[#F8FAFC] dark:bg-gray-700 dark:border-none rounded-t-md">
            <p className="flex gap-1 text-[17px] font-medium text-[#111827] py-2 dark:text-white">
              <p>Questions{'  '}</p>
              {count > 0 && (
                <span className="ml-1 text-sm rounded-full px-2 py-1 items-center font-normal text-[#8A43F9] dark:text-white bg-[#ece2fb] dark:bg-[#6F3ED8]">
                  {questionsListFilterdData?.filter((question) => !question.deleted && question.title)?.length}
                </span>
              )}
            </p>
            {!isViewOnly && (
              <div className="relative">
                <Button
                  label="Add Question Fields"
                  tertiary
                  type="button"
                  onClick={() => setAddQuestionFieldsDropdown((prev) => ({ ...prev, showDropdown: !prev.showDropdown }))}
                  disabled={count >= exceededLimitNumber}
                  disabledMessage={<p className="text-nowrap">{exceededLimitMessage}</p>}
                />

                {addQuestionFieldsDropdown.showDropdown && (
                  <div className="p-4 mt-2 space-y-3 bg-white dark:bg-darkGrayBackground border dark:border-gray-700 rounded-md absolute z-20 top-full sm:right-0 w-60">
                    <p className="text-sm text-[#667085] dark:text-gray-200">
                      Number of Fields to Add <span className="text-red-600">*</span>
                    </p>
                    <div className="flex gap-2">
                      <FlowbiteTextInput
                        value={addQuestionFieldsDropdown.inputValueDropdown}
                        onChange={(e) => setAddQuestionFieldsDropdown((prev) => ({ ...prev, inputValueDropdown: +e.target.value }))}
                        placeholder="0"
                        className="w-24"
                        type="number"
                        min={0}
                        max={exceededLimitNumber}
                        autoFocus
                      />
                      <Button
                        label="Add"
                        type="button"
                        onClick={handleAddQuestionFields}
                        disabled={
                          addQuestionFieldsDropdown.inputValueDropdown <= 0 ||
                          addQuestionFieldsDropdown.inputValueDropdown + count > exceededLimitNumber
                        }
                        disabledMessage={
                          addQuestionFieldsDropdown.inputValueDropdown <= 0 ? (
                            'Enter number'
                          ) : addQuestionFieldsDropdown.inputValueDropdown + count >= exceededLimitNumber ? (
                            <p className="text-nowrap">{exceededLimitMessage}</p>
                          ) : undefined
                        }
                        className="!h-[42px]"
                        size="md"
                      />
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          <div>
            {count > 0 ? (
              questionsListFilterdData?.slice((page - 1) * size, page * size).map((singleData, index) => (
                <QuestionsListSection
                  key={singleData?._id || singleData?.created}
                  index={index}
                  pagination={{
                    page: page,
                    size: size,
                    count: count,
                  }}
                  singleData={singleData}
                  setQuestionsListFilterdData={setQuestionsListFilterdData}
                  isViewOnly={isViewOnly}
                />
              ))
            ) : (
              // Placeholder
              <div className="px-4 py-4 space-y-2">
                <div className="flex items-center gap-3 p-4 bg-[#F2F4F788] dark:bg-darkGrayBackground rounded-md">
                  <Icon icon="mdi:info" className="text-[#D1D5DB]" width={30} />
                  <p className="text-[#6B7280C7] dark:text-gray-200">You haven't added any questions yet</p>
                </div>

                <div className="p-4 flex items-center gap-3 flex-wrap lg:flex-nowrap">
                  <CustomIcon definedIcon={'lampGuidance'} width={40} height={40} />
                  {/* <p className="text-lg font-semibold dark:text-white">Guidance:</p> */}
                  <p className="text-[#667085] dark:text- dark:text-gray-200">
                    Easily create input fields for your screening questions in just a few steps.
                    <span className="font-medium"> Click 'Add Question', Enter the number of fields you need, </span>
                    and
                    <span className="font-medium"> Click 'Add'. </span>
                    Once the fields are added, you can immediately begin writing your questions in the newly created spaces.
                  </p>
                </div>
              </div>
            )}

            {count > 0 && (
              <nav
                // className="flex flex-row justify-between items-center space-y-0 p-4 rounded-b-2xl bg-white dark:bg-darkBackgroundCard sticky bottom-0 z-10"
                className="flex justify-center items-center px-4 my-1 sticky bottom-0 z-10"
              >
                {/* <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
                  Showing <span className="font-semibold text-gray-900 dark:text-white">{showingText}</span> of{' '}
                  <span className="font-semibold text-gray-900 dark:text-white">{count}</span>
                </span> */}

                {count > size && (
                  <Pagination
                    theme={StaticData.paginationTheme}
                    currentPage={page}
                    onPageChange={(value) => setPage(value)}
                    showIcons
                    totalPages={pagesCount}
                    layout={screen.gt.md() ? 'pagination' : 'navigation'}
                    previousLabel={<span className="hidden sm:block">Previous</span>}
                    nextLabel={<span className="hidden sm:block">Next</span>}
                  />
                )}
              </nav>
            )}
          </div>
        </Card>

        <div className="flex justify-end">
          <Jumbotron
            buttons
            type={!id && 'create'}
            isShowViewButtons={{
              ...JumbotronModuleInfo,
              disabled:
                !questionsListFilterdData ||
                questionsListFilterdData?.find((question) => !question?.title) ||
                questionsListFilterdData?.filter((question) => !question.deleted)?.length === 0,
              disabledMessage: !loading && <p className="text-nowrap">Fill the data below</p>,
            }}
          />
        </div>
      </Form>
    </>
  );
};
