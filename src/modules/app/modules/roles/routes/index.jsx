import React from 'react';
import { Navigate } from 'react-router-dom';

import { RolsMainLayout } from '../layouts/main';

import { RolsListPage } from '../pages/list';

export default [
  {
    path: 'roles',
    element: <RolsMainLayout />,
    loader() {
      return {
        label: 'Roles',
      };
    },
    children: [
      // Default
      {
        path: '',
        element: <Navigate to='/app/roles/list' />,
      },

      // Routes
      {
        path: 'list',
        element: <RolsListPage />,
        loader() {
          return {
            label: 'List',
            icon: 'vaadin:cog',
            title: 'Roles',
            subtitle: 'Here you can add, edit or remove roles',
          };
        },
      },
    ],
  },
];
