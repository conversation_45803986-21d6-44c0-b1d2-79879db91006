import React, { useState } from 'react';

// Flowbite
import { Spin<PERSON>, Pagination } from 'flowbite-react';

// UI
import { useFetchList, Button, Icon, StaticData } from '/src';
// Components
import { Jumbotron } from '/src';

// Components
import { CardData } from '../components/card-data';
import { CardPlaceholder } from '../components/card-placeholder';
import { RolesCreationDialog } from '../components/creation-dialog';

export const RolsListPage = () => {
  const { ready, loading, count, list, refresh, search, pagination } = useFetchList('roles/list', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
  });

  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [id, setId] = useState(null);
  const paginationLimit = 20;
  const { page, size } = pagination;

  // Computed
  const pagesCount = Math.max(Math.ceil(count / size), 1);
  const showingText = `${count ? page * size - size + 1 : count} - ${page * size > count ? count : page * size}`;
  const isPaginationActive = count > paginationLimit;

  // Handle Ready
  if (!ready) {
    return <CardPlaceholder />;
  }

  const closeCreateDialogVisibility = () => {
    setId(null);
    setCreateDialogVisibility(false);
  };

  // Render
  return (
    <div className="relative">
      <div>
        {/* Header Only In Large Screen */}
        <div className="hidden lg:flex gap-3 flex-row items-center justify-between space-y-2 space-x-4">
          <Jumbotron />
          <div className="flex gap-2">
            {/* Search bar */}
            <div className="flex flex-row items-center space-x-3 space-y-0 justify-between">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <Icon icon="mdi:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder={'Search by role name'}
                  className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg  block w-full lg:w-[270px] pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white focus:ring-0 focus:border-gray-300"
                  value={search.value}
                  onInput={(e) => search.update(e.target.value)}
                />
              </div>
            </div>

            {/* Buttons */}
            <Button type="button" gradientMonochrome="purple" onClick={() => setCreateDialogVisibility(true)}>
              <div className="mr-2 h-5 w-5">
                <Icon icon="mdi:add" width="22" />
              </div>
              <p>Create Role</p>
            </Button>
          </div>
        </div>

        {/* Header Only In Samll Screen */}
        <div className="lg:hidden space-y-3">
          <div className="sm:flex justify-between items-center space-y-3 sm:space-y-0">
            <Jumbotron />

            {/* Buttons */}
            <div className="order-2 flex space-y-0 items-center space-x-3">
              <Button type="button" gradientMonochrome="purple" onClick={() => setCreateDialogVisibility(true)}>
                <div className="sm:mr-2 h-5 w-5">
                  <Icon icon="mdi:add" width="22" />
                </div>
                <p className="hidden sm:block">Create Role</p>
              </Button>
            </div>
          </div>

          {/* Search bar */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Icon icon="mdi:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            </div>
            <input
              type="text"
              placeholder={'Search by role name'}
              className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg  block w-full pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white focus:ring-0"
              value={search.value}
              onInput={(e) => search.update(e.target.value)}
            />
          </div>
        </div>
      </div>
      <div className={`${list.length > 0 ? 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4  gap-4 mt-4' : 'mt-24'} `}>
        {list && list.length > 0 ? (
          list.map((data, index) => (
            <CardData key={index} addButtonPath="/app/roles/create" data={data} setCreateDialogVisibility={setCreateDialogVisibility} setId={setId} />
          ))
        ) : (
          <div className="dark:text-white text-lg font-medium text-center flex justify-center gap-3">
            {' '}
            <Icon icon="ic:round-do-not-disturb-alt" width="30" className="text-gray-300 dark:text-gray-500" />
            No data available
          </div>
        )}
      </div>

      {/* Pagination */}
      {isPaginationActive && (
        <nav className="flex flex-row justify-between items-center space-y-0 p-4" aria-label="Table navigation">
          <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
            Showing <span className="font-semibold text-gray-900 dark:text-white">{showingText}</span> of{' '}
            <span className="font-semibold text-gray-900 dark:text-white">{count}</span>
          </span>

          {count > paginationLimit && (
            <Pagination
              theme={StaticData.paginationTheme}
              currentPage={page}
              onPageChange={(page) => pagination.update({ page })}
              showIcons
              totalPages={pagesCount}
              // layout={screen.gt[breakpoint]() ? 'pagination' : 'navigation'}
              previousLabel={<span className="hidden sm:block">Previous</span>}
              nextLabel={<span className="hidden sm:block">Next</span>}
            />
          )}
        </nav>
      )}

      {/* Loading */}
      {loading && (
        <div className="absolute left-0 right-0 bottom-0 top-0 flex items-center justify-center bg-white/80 dark:dark:bg-gray-800/80">
          <Spinner size="lg" color="purple" />
        </div>
      )}

      {/* Creation Dialog */}
      {isCreateDialogVisible && (
        <RolesCreationDialog
          onClose={closeCreateDialogVisibility}
          id={id}
          closeCreateDialogVisibility={closeCreateDialogVisibility}
          onCreate={refresh}
        />
      )}
    </div>
  );
};
