import React from "react";

// UI
import { Icon } from "/src";

export const RoleListItem = ({ id, onRemove, isLastItem }) => {
  return (
    <li
      className={`${
        !isLastItem && "border-b"
      } w-full px-4 py-2 border-gray-200 rounded-t-lg dark:border-gray-600`}
    >
      <div className="flex justify-between w-full items-center">
        <div className="w-3/4">
          <p className="break-all">{id}</p>
        </div>

        <div className="flex gap-3">
          <Icon
            icon="octicon:trash-16"
            width={22}
            className="text-[#F05252] cursor-pointer"
            onClick={() => onRemove(id)}
          />
        </div>
      </div>
    </li>
  );
};
