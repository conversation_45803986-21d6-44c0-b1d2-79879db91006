import React from 'react';
import { Card, Icon } from '/src';

export const CardData = ({ data, setId, setCreateDialogVisibility }) => {
  const editRole = () => {
    setCreateDialogVisibility(true);
    setId(data._id);
  };

  return (
    <Card>
      <div className="flex justify-between mb-4">
        <div className="font-medium">
          <div className="text-grayDetail dark:text-white text-sm">Role</div>
          <div className="text-[#374151] dark:text-gray-400">{data.name}</div>
        </div>
        <div className="flex h-fit cursor-pointer border border-violet-600 rounded-lg p-1.5 " onClick={editRole}>
          <Icon className="text-[#7633E7]" icon="lucide:pencil" width={20} />
        </div>
      </div>

      <div className="font-medium mb-4">
        <div className="text-grayDetail dark:text-white text-sm">Users Count</div>
        <div className="text-[#374151] dark:text-gray-400">
          {data.numOfUsers ? data.numOfUsers : 0} User{data.numOfUsers > 1 && 's'}
        </div>
      </div>
      <div className="font-medium">
        <div className="text-grayDetail dark:text-white text-sm">Permissions</div>
        <div className="text-[#374151] dark:text-gray-400 mt-1">
          <ul className="ml-7 list-disc">
            {data.permissions.map((permission) => {
              return (
                <li key={permission} className="text-[15px]">
                  {permission}
                </li>
              );
            })}
          </ul>
        </div>
      </div>
    </Card>
  );
};
