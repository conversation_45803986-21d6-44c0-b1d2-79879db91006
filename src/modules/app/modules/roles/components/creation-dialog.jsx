import React, { useEffect, useState } from 'react';
import { RoleListItem } from './role-list-item';

// Components
import { Dialog, Form, Select, Button, useForm, useNotify, Api, TextInput, useValidate, Regex } from '/src';

export const RolesCreationDialog = ({ onClose, id, closeCreateDialogVisibility, onCreate }) => {
  // State
  const [child, setChild] = useState([]);

  // Hooks
  const { notify } = useNotify();
  const { isRequired, isNotSpaces, minLength, maxLength, validateRegex } = useValidate();

  // Form
  const { form, setFieldValue, setFormValue } = useForm({
    // Meta
    name: '',
    permissions: [],
  });

  // State
  const [loading, setLoading] = useState(false);

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get(`roles/single/${id}`);
      setFormValue(response.data);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };
  const handleInsert = async () => {
    try {
      await Api.post('roles/single', form);
      onCreate();
      notify('Role added successfully!');
      closeCreateDialogVisibility();
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };
  const handleUpdate = async () => {
    try {
      await Api.put(`/roles/single/${id}`, form);
      onCreate();
      notify('Role updated successfully!');
      closeCreateDialogVisibility();
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };
  const handleSearch = async (keyword) => {
    try {
      const result = await Api.get('roles/single/permissions/search', {
        keyword: keyword,
        exclude: form.permissions,
      });

      const transformedPermissions = result.data.map((permission) => ({ name: permission }));
      setChild(transformedPermissions);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };
  // On Mount
  useEffect(() => {
    if (id) {
      handleGet();
    }
  }, []);

  return (
    <Dialog show popup size="md" modalHeader={id ? 'Update Role' : 'Create Role'} onClose={onClose} overflowVisible={true}>
      {/* Creation Form */}
      <Form className="space-y-4" onSubmit={id ? handleUpdate : handleInsert}>
        <TextInput
          name="name"
          label="Role Name"
          value={form.name}
          placeholder="Enter role name"
          onChange={setFieldValue('name')}
          validators={[isRequired(), minLength(2), maxLength(50), isNotSpaces(), validateRegex(Regex.name)]}
        />
        <div className="col-span-1 md:col-span-3 space-y-4">
          <Select
            filterOnly
            name="permissions"
            label="Assign Permissions"
            placeholder="Select or search for permissions"
            onSearch={handleSearch}
            lookup={child}
            optionValueKey="name"
            optionLabelKey="name"
            multiSelect={true}
            onChange={(permissions) => (
              setFormValue({
                ...form,
                permissions: [...form.permissions, permissions],
              }),
              setChild([])
            )}
            value={form.permissions.length}
            validators={[isRequired()]}
          />
          <p className="text-sm font-medium text-gray-900 dark:text-white">Selected Permissions ({form.permissions.length})</p>
          <ul
            className={`${
              form.permissions.length > 0 && 'border border-gray-200 rounded-lg dark:bg-gray-700 dark:border-gray-600'
            } text-sm font-medium text-gray-900 bg-white dark:text-white`}
          >
            {form.permissions.map((id, index) => (
              <RoleListItem
                key={index}
                id={id}
                onRemove={(id) =>
                  setFormValue({
                    ...form,
                    permissions: form.permissions.filter((target) => id !== target),
                  })
                }
                isLastItem={index === form.permissions.length - 1}
              />
            ))}
          </ul>
        </div>

        <div className="pt-2 space-y-4">
          <Button type="submit" label={id ? 'Update' : 'Create'} className="w-full" loading={loading} disabled={loading} />
        </div>
      </Form>
    </Dialog>
  );
};
