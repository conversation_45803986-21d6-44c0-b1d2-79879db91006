import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';

import { QuestionsMainLayout } from '../layouts/main';

import { QuestionsListPage } from '../pages/list';
import { QuestionsSinglePage } from '../pages/single';

export default [
  {
    path: 'questions',
    element: <QuestionsMainLayout />,
    // loader() {
    //   return {
    //     label: 'Questions',
    //   };
    // },
    children: [
      // Default
      {
        path: '',
        element: <Navigate to="/app/questions/list" />,
      },

      // Routes
      {
        path: '',
        element: <Outlet />,
        loader() {
          return {
            label: 'Question Managament',
            icon: 'ph:circle-wavy-question-fill',
            title: 'Question Managament',
            subtitle: 'Track and manage all available questions.',
          };
        },
        children: [
          // Default
          {
            path: 'list',
            element: <QuestionsListPage />,
          },

          {
            path: 'create',
            element: <QuestionsSinglePage />,
            loader() {
              return {
                label: 'Create',
                title: 'Create Question',
                subtitle: 'Here you can create multiple questions using the form below.',
              };
            },
          },
          {
            path: 'edit/:id',
            element: <QuestionsSinglePage />,
            loader() {
              return {
                label: 'Edit',
                title: 'Edit Question',
                subtitle: "Modify the question details and answers as needed. Once satisfied with the changes, click 'Update' to save",
              };
            },
          },
          {
            path: 'view/:id',
            element: <QuestionsSinglePage />,
            loader() {
              return {
                label: 'View',
                title: 'View Question',
                subtitle: "Review the question details below. To make changes, click 'Edit Question' to proceed.",
              };
            },
          },
        ],
      },
    ],
  },
];
