// React
import { useEffect, useRef, useContext, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

// Context
import { AppContext } from '/src/components/provider';

// Flowbite
import { <PERSON><PERSON>, Toolt<PERSON> } from 'flowbite-react';

// Core
import {
  Form,
  Textarea,
  RichText,
  Card,
  Button,
  useForm,
  useNotify,
  Enums,
  Api,
  useValidate,
  useConfirmDialog,
  Select,
  Regex,
  Icon,
  Jumbotron,
} from '/src';

export const QuestionsSinglePage = () => {
  // Context
  const { isViewOnly, setViewOnly, userData } = useContext(AppContext);

  // Params
  const { id } = useParams();

  // Hooks
  const { notify } = useNotify();
  const navigate = useNavigate();
  const { isRequired, isNotSpaces } = useValidate();

  // Reference
  const subCategoryRef = useRef(null);
  const topicRef = useRef(null);

  // State
  const [exitAfterSubmission, setExitAfterSubmission] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { showConfirm, hideConfirm } = useConfirmDialog();

  // Form
  const initialFormState = {
    // Meta
    title: '',
    difficulty: null,
    notes: '',
    subCategory: 0,
    topic: null,
    category: userData.trackId ? userData.trackId : null,

    // Options
    options: [
      {
        id: 1,
        label: '',
      },
      {
        id: 2,
        label: '',
      },
    ],

    // Categorization
    type: 1,

    // Answers
    singleChoiceAnswer: 1,
    multiChoiceAnswer: {
      1: false,
      2: false,
      3: false,
      4: false,
    },
  };
  const { form, setFieldValue, setFormValue, resetForm } = useForm(initialFormState);

  const errorTriggerWords = ['difficulty', 'category', 'SubCategory', 'topic', 'title'];

  const handleMinTwoAnswers = () => {
    let counter = 0;
    Object.entries(form.multiChoiceAnswer).forEach(([key, value]) => {
      value && counter++;
    });
    if (form.type === Enums.QuestionType.Multichoice) {
      if (counter === 2) {
        return true;
      }
      return notify.error('Choose two answers');
    }
    return true;
  };

  const handleGet = async () => {
    try {
      const response = await Api.get(`questions/single/${id}`);
      setFormValue(response.data);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  const scrollToError = (error) => {
    errorTriggerWords.map((word) => {
      if (error.toLowerCase().includes(word.toLowerCase())) {
        document.getElementById(word).scrollIntoView({ behavior: 'smooth', block: 'end' });
      }
    });
  };

  const ConfirmText = (value) => {
    return (
      <div>
        <div className="flex mx-auto p-4 mb-7 bg-[#ddd1f8] w-24 h-24 rounded-full">
          <div className="flex mx-auto mb-7 bg-[#cab6f5] w-16 h-16 justify-center rounded-full">
            <Icon icon="uil:question" className="text-[#9061F9]" width="40" />
          </div>
        </div>
        {value ? (
          <p>
            Once confirmed, {value} question{value > 1 && 's'} will be {id ? 'updated permanently!' : 'created'}
          </p>
        ) : (
          <p>Once confirmed, This question will be {id ? 'updated permanently!' : 'created'} </p>
        )}
      </div>
    );
  };

  const handleCheckEssayQuestion = () => {
    if (form.type === Enums.QuestionType.Singlechoice) {
      if (form.options[0].label === '' || form.options[1].label === '') {
        notify.error("Answers field shouldn't be empty");
      } else {
        isEditMode() ? handleUpdate() : handleInsert();
      }
    } else if (form.type === Enums.QuestionType.Multichoice) {
      if (form.options[0].label === '' || form.options[1].label === '' || form.options[2].label === '') {
        notify.error("Answers field shouldn't be empty");
      } else {
        isEditMode() ? handleUpdate() : handleInsert();
      }
    } else if (form.type === Enums.QuestionType.Textarea) {
      isEditMode() ? handleUpdate() : handleInsert();
    }
  };

  const handleInsert = async () => {
    const canSubmit = form.type === Enums.QuestionType.Multichoice ? handleMinTwoAnswers() : true;

    if (canSubmit) {
      showConfirm(ConfirmText(), {
        async onConfirm() {
          try {
            setIsLoading(true);
            await Api.post('questions/single', form);
            notify('Question added successfully!');
            if (exitAfterSubmission) {
              navigate('/app/questions');
            } else {
              const { topic, difficulty, subCategory, category } = { ...form };
              setFormValue({ ...initialFormState, topic, difficulty, subCategory, category });
            }
          } catch (error) {
            scrollToError(error.response?.data.message[0]);
            notify.error(error.response?.data.message[0]);
          } finally {
            hideConfirm();
            setIsLoading(false);
          }
        },
      });
    }
  };

  const handleUpdate = async () => {
    const canSubmit = form.type === Enums.QuestionType.Multichoice ? handleMinTwoAnswers() : true;

    if (canSubmit) {
      if (form.title != '') {
        showConfirm(ConfirmText(), {
          async onConfirm() {
            try {
              await Api.put(`questions/single/${id}`, form);
              navigate('/app/questions');
              notify('Question updated successfully!');
            } catch (error) {
              notify.error(error.response.data.message);
            } finally {
              hideConfirm();
            }
          },
        });
      } else {
        document.getElementById('title').scrollIntoView({ behavior: 'smooth', block: 'end' });
        notify.error(`Content shouldn't be empty`);
      }
    }
  };

  const handleAddOption = () => {
    if (form.options.length >= 4) return;
    const newOption = {
      id: form.options.length + 1,
      label: '',
    };

    setFieldValue('options')([...form.options, newOption]);
  };

  const handleRemoveOption = (id) => {
    // Remove unwanted form options
    const updatedOptions = form.options.filter((option) => option.id !== id);
    setFieldValue('options')(updatedOptions);

    // Set removed form multiChoiceAnswer to false
    const updateMultiChoiceAnswer = { ...form.multiChoiceAnswer, [id]: false };
    setFieldValue('multiChoiceAnswer')(updateMultiChoiceAnswer);
  };

  const handleSelectCheckbox = (value, id) => {
    const numberOfSelectedAnswers = Object.entries(form.multiChoiceAnswer).filter(([key, value]) => value).length;
    if (numberOfSelectedAnswers >= 2 && value) {
      return notify.error('Only two answers are allowed');
    }
    const newObj = { ...form.multiChoiceAnswer, [id]: value };

    setFieldValue('multiChoiceAnswer')(newObj);
  };

  const handleGenerateAiQuestion = async () => {
    const payload = {
      category: [form?.category],
      subCategory: [form?.subCategory],
      numOfQuestions: 1,
      difficulty: form?.difficulty,
      type: form?.type,
      notes: form?.notes,
    };

    if (form?.type === 2) {
      payload.notes =
        'I want the answers to be 3 or 4 options, only 2 options of them are correct, Tell me the answers to be object keys is numbers of 1 or 2 or 3 or 4 and value is true or false, ' +
        form?.notes;
    }
    if (payload.notes === '') delete payload.notes;

    try {
      setIsLoading(true);
      const response = await Api.post(`ai-interview/generate/questions`, payload);
      setFieldValue('title')(response?.data?.title);
      if (response?.data?.type === 1) {
        setFieldValue('options')([
          { id: 1, label: 'True' },
          { id: 2, label: 'False' },
        ]);
        if (response?.data?.answer === 'True') {
          setFieldValue('singleChoiceAnswer')(1);
        } else if (response?.data?.answer === 'False') {
          setFieldValue('singleChoiceAnswer')(2);
        }
      } else if (response?.data?.type === 2) {
        setFieldValue('options')(response?.data?.options);
        setFieldValue('multiChoiceAnswer')(response?.data?.answer);
      } else if (response?.data?.type === 3) {
        // setFieldValue('options')(response?.data?.options);
      }
    } catch (error) {
      notify.error(error?.response?.data?.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNavigate = () => setExitAfterSubmission(true);

  // Getters
  const isEditMode = () => !!form._id;

  // On Mount
  useEffect(() => {
    if (id) {
      handleGet();
    }
  }, [window.location.href]);

  useEffect(() => {
    if (form.type === Enums.QuestionType.Multichoice && form.options.length <= 2) {
      setFieldValue('options')([...form.options, { id: 3, label: '' }]);
    }
  }, [form.type]);

  const JumbotronModuleInfo = {
    moduleName: 'question',
    routeName: 'questions',
  };

  // Render
  return (
    <>
      {/* <Form className="space-y-4" onSubmit={isEditMode() ? handleUpdate : handleInsert}> */}
      <Form className="space-y-4 relative" onSubmit={handleCheckEssayQuestion}>
        <div className="flex flex-wrap justify-between items-center gap-2">
          <div className="grow">
            <Jumbotron header type={isEditMode() && 'update'} isShowViewButtons={JumbotronModuleInfo} />
          </div>

          {!isEditMode() && (
            <Button
              label="Ai Creation"
              tertiary
              onClick={handleGenerateAiQuestion}
              disabled={
                form?.category === null ||
                form?.category?.length <= 0 ||
                form?.subCategory === null ||
                form?.subCategory?.length <= 0 ||
                !form?.topic ||
                !form?.difficulty ||
                !form.type
              }
            />
          )}
        </div>

        <div className="grid grid-cols-1 gap-4">
          <Card className="space-y-4 !pb-4 !px-0 !py-0 mt-4">
            <div className="flex items-center gap-[5px] border-b px-4 pb-3 py-4 bg-[#F8FAFC] dark:bg-gray-700 dark:border-none rounded-t-md">
              <p className="text-[17px] font-medium dark:text-white">Question Setup</p>
              <Tooltip className="z-[100]" content={'These options auto-fill for each new question to save your time. You can still edit them.'}>
                <Icon
                  icon={'solar:info-circle-outline'}
                  width="18"
                  className="text-[#667085] text-opacity-80 dark:text-gray-200 dark:bg-opacity-90 font-medium"
                />
              </Tooltip>
            </div>
            <div className="px-4 space-y-4">
              {/* Category Select */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 w-full">
                {!userData?.trackId && (
                  <Select
                    label="Category"
                    requiredLabel
                    name="category"
                    value={form.category}
                    onChange={(newCategory) => {
                      subCategoryRef.current?.blur();
                      topicRef.current?.blur();
                      setFieldValue('category')(newCategory);
                      setFieldValue('subCategory')(null);
                      setFieldValue('topic')(null);
                    }}
                    lookup="category"
                    optionValueKey="_id"
                    optionLabelKey="name"
                    dropIcon={true}
                    validators={[isRequired()]}
                    creationOptions={{
                      url: 'lookups/category/single',
                      fieldName: 'name',
                      validation: Regex.categorySubcategoryTopic,
                    }}
                    placeholder="Search for category ..."
                    readOnly={isViewOnly}
                  />
                )}

                {/* Subcategory Select */}
                <Select
                  key={form.category}
                  ref={subCategoryRef}
                  label="Subcategory"
                  requiredLabel
                  name="SubCategory"
                  value={form.subCategory}
                  onChange={(newSubCategory) => {
                    setFieldValue('subCategory')(newSubCategory);
                    setFieldValue('topic')(null);
                  }}
                  disabled={!form.category}
                  disabledMessage="Please select category first"
                  lookup="subcategory"
                  params={{ categoryId: form.category }}
                  creationOptions={{
                    url: 'lookups/subCategory/single',
                    fieldName: 'name',
                    validation: Regex.categorySubcategoryTopic,
                  }}
                  optionValueKey="_id"
                  optionLabelKey="name"
                  dropIcon={true}
                  validators={form.category ? [isRequired()] : []}
                  placeholder="Search for subcategory ..."
                  readOnly={isViewOnly}
                />

                {/* Topic Select */}
                <Select
                  key={form.subCategory}
                  ref={topicRef}
                  label="Topic"
                  requiredLabel
                  name="topic"
                  value={form.topic}
                  onChange={setFieldValue('topic')}
                  disabled={!form.subCategory}
                  disabledMessage="Please select subcategory first"
                  lookup="topic"
                  params={{ subcategoryId: form.subCategory }}
                  optionValueKey="_id"
                  optionLabelKey="name"
                  dropIcon={true}
                  validators={form.subCategory ? [isRequired()] : []}
                  creationOptions={{
                    url: 'lookups/topic/single',
                    fieldName: 'name',
                    validation: Regex.categorySubcategoryTopic,
                  }}
                  placeholder="Search for topic ..."
                  readOnly={isViewOnly}
                />

                {/* Difficulty Select */}
                <Select
                  name="difficulty"
                  requiredLabel
                  label="Difficulty"
                  lookup="$QuestionDifficulty"
                  value={form.difficulty}
                  onChange={setFieldValue('difficulty', Number)}
                  dropIcon={true}
                  validators={[isRequired()]}
                  validatorsScroll
                  placeholder="Search for difficulty ..."
                  readOnly={isViewOnly}
                />

                {/* Question Type Select */}
                <Select
                  label="Question Type"
                  requiredLabel
                  name="answerTypeSingle"
                  value={form.type}
                  onChange={setFieldValue('type', Number)}
                  lookup="$QuestionType"
                  dropIcon={true}
                  validators={[isRequired()]}
                  validatorsScroll
                  placeholder="Search for question type ..."
                  readOnly={isViewOnly}
                />
              </div>
              <Textarea label="Notes" value={form?.notes} onChange={setFieldValue('notes')} placeholder="Write your notes..." />
            </div>
          </Card>

          {/* Question Card */}
          <Card className=" space-y-2 !pb-4 !px-0 !py-0 relative">
            <div className="flex gap-2 flex-wrap justify-between items-center border-b py-3 px-4 bg-[#F8FAFC] dark:bg-gray-700 dark:border-none rounded-t-md">
              <div className="flex items-center">
                <p className="text-[17px] font-medium dark:text-white ">Question</p>
              </div>

              {/* Create & Add Another Button (only in create mode) */}
              {!id && (
                <Button tertiary type="submit" loading={isLoading} disabled={isLoading} onClick={() => setExitAfterSubmission(false)}>
                  Create & Add Another Question
                </Button>
              )}
            </div>

            {/* Question Content */}
            <div className=" pb-4 px-4">
              <RichText
                name="title"
                id="title"
                label={!!id ? 'Edit your question below' : 'Write your question below'}
                value={form.title}
                onChange={setFieldValue('title')}
                validators={[isRequired()]}
                validatorsScroll
                labelClassName="!text-[#798296] text-base !font-normal !py-2 "
                editorClassName="!rounded-b-lg"
                requiredLabel
                readOnly={isViewOnly}
              />
            </div>

            {/* Answers Section (only for Single Choice and Multiple Choice) */}
            {(form.type === Enums.QuestionType.Singlechoice || form.type === Enums.QuestionType.Multichoice) && (
              <div className="space-y-4 px-4">
                <div className="text-[#8C939F] text-sm font-medium">
                  {`${!!id ? 'Edit' : 'Write'} your answers below and select the correct one`}
                  <span className="text-red-600 dark:text-red-800"> *</span>
                </div>

                {/* Render answer options */}
                {form.options.map((option, index) => (
                  <div key={option.id} className="flex items-center w-full gap-2">
                    {/* Answer Textarea */}
                    <div className="flex-grow">
                      <Textarea
                        labelClassName="!text-[#798296] text-sm !font-medium !py-2 "
                        name={`answer${option.id}`}
                        className="w-full"
                        value={option.label}
                        onChange={setFieldValue(`options.${index}.label`)}
                        // validators={
                        //   form.type === Enums.QuestionType.Multichoice
                        //     ? index < 3
                        //       ? [isRequired(), isNotSpaces()]
                        //       : [isNotSpaces()]
                        //     : index < 2
                        //     ? [isRequired(), isNotSpaces()]
                        //     : [isNotSpaces()]
                        // }
                        requiredLabel={form.type === Enums.QuestionType.Multichoice ? index < 3 : index < 2}
                        maxHeight="150"
                        inputTypeProps={
                          form.type === Enums.QuestionType.Singlechoice
                            ? {
                                name: `answer${option.id}`,
                                selectionValue: option.id,
                                value: form.singleChoiceAnswer,
                                onChange: setFieldValue('singleChoiceAnswer', Number),
                                formType: form.type,
                              }
                            : form.type === Enums.QuestionType.Multichoice
                            ? {
                                name: `answer${option.id}`,
                                value: form.multiChoiceAnswer[option.id],
                                onChange: (e) => handleSelectCheckbox(e, option.id),
                                formType: form.type,
                              }
                            : null
                        }
                        readOnly={isViewOnly}
                      />
                    </div>
                    {/* Remove Option Button (appears for options beyond the initial two) */}
                    {!isViewOnly &&
                      (form.type === Enums.QuestionType.Multichoice ? form.options.length > 3 : form.options.length > 2) &&
                      index === form.options.length - 1 && (
                        <Icon
                          icon="solar:trash-bin-trash-linear"
                          className="cursor-pointer border border-[#D1D5DB] text-[#C72716] w-fit p-1 rounded-md"
                          width={20}
                          onClick={() => handleRemoveOption(option.id)}
                        />
                      )}
                  </div>
                ))}

                {/* Add button to add more options */}
                {/* here when adding options greater than 4 the button becomes invisible */}
                {!isViewOnly && form.options.length < 4 && (
                  <div
                    className="bg-newQuAnsBg w-full my-4 py-2 rounded-lg dark:bg-newQuAnsDarkBg dark:bg-opacity-80 hover:bg-newQuAnsHoverBg mx-auto cursor-pointer "
                    onClick={handleAddOption}
                  >
                    <button type="button" onClick={handleAddOption} className="flex items-center justify-center mx-auto">
                      <Icon icon="icon-park-solid:add-one" className="text-primaryPurple text-opacity-60" width="25" />
                      <span className="ml-2 text-newQuAnsText text-lg font-medium dark:text-newQuAnsDarkText">New Answer</span>
                    </button>
                  </div>
                )}
              </div>
            )}
          </Card>
        </div>

        {!isEditMode() && (
          <div className="flex justify-end">
            <Jumbotron
              buttons
              type={!isEditMode() && 'create'}
              isShowViewButtons={{
                ...JumbotronModuleInfo,
                customOnClick: handleNavigate,
              }}
            />
          </div>
        )}

        {/* Loading */}
        {isLoading && (
          <div className="absolute z-50 left-0 right-0 bottom-0 top-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80">
            <Spinner size="lg" color="purple" />
          </div>
        )}
      </Form>
    </>
  );
};
