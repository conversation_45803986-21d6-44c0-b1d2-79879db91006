// React
import { useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// Context
import { AppContext } from '/src/components/provider';

// React icons
import { LiaCircle } from 'react-icons/lia';
import { LuDiamond } from 'react-icons/lu';
import { PiCube } from 'react-icons/pi';
import { BiPolygon } from 'react-icons/bi';

// Core
import {
  Table,
  Api,
  useNotify,
  useFetchList,
  useConfirmDialog,
  Icon,
  EnumText,
  useScreenSize,
  SubscribeDialog,
  useForm,
  Form,
  MultiSelect,
  RadioGroup,
  Button,
  SidebarFilterPage,
  SidebarFilterDrawer,
} from '/src';

// Flowbite
import { Tooltip } from 'flowbite-react';

export const QuestionsListPage = () => {
  // State
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [needSubscription, setNeedSubscription] = useState(false);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);
  const [filterCountNumber, setFilterCountNumber] = useState(0);

  // Form
  const { form, setFieldValue, setFormValue, resetForm } = useForm({});

  // UI Hooks
  const navigate = useNavigate();
  const { notify } = useNotify();
  const { showConfirm, hideConfirm } = useConfirmDialog();
  const { userData, sidebarFilter, setSidebarFilter, sidebarSearch, setSidebarSearch } = useContext(AppContext);
  const isSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));
  const screen = useScreenSize();

  // User data
  const isPermitted = userData?.roles.some((role) => ['super-admin', 'admin', 'content-creator'].includes(role));
  const isPermittedAuthor = (rowAuthorName) => {
    return userData.name === rowAuthorName;
  };

  const initialFilters = {
    ...(userData.trackId
      ? {}
      : {
          category: {
            label: 'Category',
            lookup: 'category',
          },
        }),
    subCategory: {
      label: 'Sub Category',
      lookup: 'subcategory',
      parentLookup: { key: 'category', fieldName: 'categoryId', fieldValue: userData.trackId ? userData.trackId : null },
    },
    difficulty: {
      label: 'Difficulty',
      enum: 'QuestionDifficulty',
    },
  };
  const { ready, loading, setLoading, list, count, search, pagination, filters, setFilters, refresh } = useFetchList('questions/list', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: initialFilters,
  });

  const filterFeedData = Object.keys(initialFilters);

  const ConfirmText = (value) => {
    return (
      <div>
        <div className="flex mx-auto p-4 mb-7 bg-[#ddd1f8] w-24 h-24 rounded-full">
          <div className="flex mx-auto mb-7 bg-[#cab6f5] w-16 h-16 justify-center rounded-full">
            <Icon icon="hugeicons:archive-02" className="text-[#9061F9]" width="40" />
          </div>
        </div>
        {value ? (
          <p>
            Once confirmed, {value} question{value > 1 && 's'} will be archived permanently!
          </p>
        ) : (
          <p>Once confirmed, This question will be archived permanently!</p>
        )}
      </div>
    );
  };
  // Delete Question
  const handleArchive = async (row) => {
    showConfirm(ConfirmText(), {
      async onConfirm() {
        try {
          await Api.delete(`questions/single/${row._id}`);
          hideConfirm();
          refresh(true);
          notify('Question deleted successfully!');
        } catch (error) {
          hideConfirm();
          notify.error(error.response.data.message);
        }
      },
    });
  };

  // Delete all selected ids
  const handleArchiveSelectedIds = async () => {
    if (selectedIds.length) {
      showConfirm(ConfirmText(selectedIds.length), {
        async onConfirm() {
          try {
            setLoading(true);
            await Api.delete('questions/multi', { ids: selectedIds });
            setSelectedIds([]);
            refresh(true);
            notify('Questions deleted successfully!');
          } catch (error) {
            notify.error(error.response.data.message);
          } finally {
            hideConfirm();
            setLoading(false);
          }
        },
      });
    }
  };

  const clearFilter = () => {
    // resetForm();
    setFilters({});
  };

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
    // setSidebarSearch(search);
    // setSidebarFilter({ filterFeedData, setFilters });
  }, [list]);

  // Use these styles to make black overlay visible and not scrollable
  // Make the scroll in list pages only be smooth
  useEffect(() => {
    if (isShowDrawerFilter) {
      document.querySelector('html').classList.add('overflow-x-hidden');
    } else {
      document.querySelector('html').classList.remove('overflow-x-hidden');
    }
  }, [isShowDrawerFilter]);

  return (
    <>
      {/* {ready && (
        <div className="hidden 2xl:block w-[270px] h-[calc(100vh-100px)] fixed overflow-y-auto">
          <SidebarFilterPage
            filterData={{
              filterFeedData,
              setFilters,
            }}
          />
        </div>
      )} */}

      {/* <div className={`${ready && '2xl:pl-72'}`}> */}
      <Table
        ready={ready}
        loading={loading}
        title="Questions"
        searchPlaceholder={screen.customScreen ? 'Search by name or author' : 'Name or author'}
        addButtonLabel={isPermitted ? 'Create Question' : ''}
        addButtonPath={(isSuperAdmin || userData?.features?.questions) > 0 && '/app/questions/create'}
        onClickAdd={() => {
          if (isSuperAdmin || userData?.features?.questions > 0) {
          } else {
            setNeedSubscription(true);
          }
        }}
        rows={list}
        backupRows={backupList}
        count={count}
        search={search}
        // filters={filters}
        setFilters={setFilters}
        filterFeedData={filterFeedData}
        drawerFilter={{
          filterCountNumber: filterCountNumber,
          isShowDrawerFilter: isShowDrawerFilter,
          setShowDrawerFilter: setShowDrawerFilter,
        }}
        pagination={pagination}
        slots={{
          title: (_, row) => {
            const element = row.title;
            return (
              <div className="relative">
                <div
                  className={`text-[#101828] font-medium text-[14px]  capitalize dark:text-grayTextOnDarkMood lg:truncate ${
                    !showMoreMap[row._id] && 'truncate sm:overflow-visible sm:whitespace-normal'
                  }`}
                >
                  {element}
                </div>
                {screen.gt.md() && (
                  <Tooltip
                    content={<div className="whitespace-normal max-h-[300px] overflow-auto">{element}</div>}
                    placement="bottom-start"
                    arrow={false}
                    className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                  >
                    <div className="w-full h-full absolute left-0 top-0"></div>
                  </Tooltip>
                )}
              </div>
            );
          },
          categoryName: (_, row) => {
            const element = row.categoryName;
            return (
              <div className="flex gap-x-1 relative">
                <div className={`break-words overflow-auto whitespace-normal text-clip`}>
                  <div
                    className={`rounded-full py-1 text-[#535862] text-sm font-normal capitalize dark:text-gray-400 lg:truncate ${
                      !showMoreMap[row._id] && 'truncate sm:overflow-visible sm:whitespace-normal'
                    }`}
                  >
                    {element}
                  </div>
                </div>
                {screen.gt.md() && (
                  <Tooltip content={element} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                    <div className="w-full h-full absolute left-0 top-0"></div>
                  </Tooltip>
                )}
              </div>
            );
          },
          subCategoryName: (_, row) => {
            const element = row.subCategoryName;
            return (
              <div className="flex gap-x-1 relative">
                <div className={`break-words overflow-auto whitespace-normal text-clip`}>
                  <div
                    className={`rounded-full py-1 text-[#535862] text-[14px] font-normal capitalize dark:text-gray-400 lg:truncate ${
                      !showMoreMap[row._id] && 'truncate sm:overflow-visible sm:whitespace-normal'
                    }`}
                  >
                    {element}
                  </div>
                </div>
                {screen.gt.md() && (
                  <Tooltip content={element} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                    <div className="w-full h-full absolute left-0 top-0"></div>
                  </Tooltip>
                )}
              </div>
            );
          },
          difficulty: (_, row) => {
            // Define icon and color for difficulty levels
            let difficultyIcon;
            let difficultyColor;
            let iconSize = 'text-lg text-center';

            switch (row.difficulty) {
              case 1:
                difficultyIcon = <LiaCircle className={iconSize} />;
                difficultyColor = 'text-green-500 ';
                break;
              case 2:
                difficultyIcon = <LuDiamond className={iconSize} />;
                difficultyColor = 'text-yellow-500';
                break;
              case 3:
                difficultyIcon = <BiPolygon className={iconSize} />;
                difficultyColor = 'text-orange-600 dark:text-opacity-90';
                break;
              case 4:
                difficultyIcon = <PiCube className={iconSize} />;
                difficultyColor = 'text-red-700 dark:text-red-800';
                break;
              default:
                difficultyIcon = null;
            }
            return (
              <span className={`inline-flex items-center  py-1 text-sm font-medium rounded-full capitalize ${difficultyColor}`}>
                <span className="mr-1 flex items-center justify-center">{difficultyIcon}</span>
                <EnumText name={'QuestionDifficulty'} value={row.difficulty} />
              </span>
            );
          },
          authorName: (_, row) => {
            const element = row.authorName;
            return (
              <div className="flex gap-x-1 relative">
                <div className={`break-words overflow-auto whitespace-normal text-clip`}>
                  <div className="text-gray-400 font-normal capitalize dark:text-gray-400 lg:truncate">{element}</div>
                </div>
                {screen.gt.md() && (
                  <Tooltip content={element} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                    <div className="w-full h-full absolute left-0 top-0"></div>
                  </Tooltip>
                )}
              </div>
            );
          },
        }}
        columns={[
          {
            key: 'title',
            label: 'Question',
            width: '28%',
            primary: true,
          },
          {
            key: 'difficulty',
            label: 'Difficulty',
            width: '12%',
            // enum: 'QuestionDifficulty',
            primary: true,
          },
          ...(!userData.trackId
            ? [
                {
                  key: 'categoryName',
                  label: 'Category',
                  width: '10%',
                  primary: true,
                },
              ]
            : []),
          ...(userData.trackId
            ? [
                {
                  key: 'subCategoryName',
                  label: 'Sub Category',
                  width: '14%',
                  primary: true,
                },
              ]
            : []),

          ...(!userData.trackId
            ? [
                {
                  key: 'subCategoryName',
                  label: 'Sub Category',
                  width: '14%',
                },
              ]
            : []),

          // {
          //   key: 'authorName',
          //   label: 'Author Name',
          //   width: '12%',
          // },
          {
            key: 'actions',
            label: 'Actions',
            width: '8%',
            buttons(_, row) {
              return [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  path: `/app/questions/view/${row._id}`,
                },
                isPermittedAuthor(row.authorName) && {
                  label: 'Archive',
                  customIcon: 'archive',
                  iconWidth: '18',
                  color: 'text-black dark:text-white',
                  onClick() {
                    handleArchive(row);
                  },
                },
              ];
            },
          },
        ]}
        noDataFound={{
          customIcon: 'questions',
          message: 'No questions created yet',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        // multiSelectedRow={{
        //   selectedIds: selectedIds,
        //   setSelectedIds: setSelectedIds,
        //   handleArchiveSelectedIds: handleArchiveSelectedIds,
        // }}
      />
      {/* </div> */}

      {needSubscription && <SubscribeDialog onClose={() => setNeedSubscription(false)} />}

      {/* Filter Drawer */}
      {isShowDrawerFilter && (
        <SidebarFilterDrawer
          drawerFilter={{
            // element: drawerFilter,
            // count: count,
            drawerClearAll: clearFilter,
            // isShowDrawerFilter: isShowDrawerFilter,
            setShowDrawerFilter: setShowDrawerFilter,
            // isAnyFilterApplied: isAnyFilterApplied,
          }}
          filterData={{
            filterFeedData,
            setFilters,
          }}
        />
      )}
    </>
  );
};
