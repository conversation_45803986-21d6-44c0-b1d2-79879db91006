import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { api } from '../../../../../services/axios';

// Components
import { Jumbotron, ToggleFilter } from '/src';

// UI
import { Card, EnumText, useNotify, Api, Icon, Button } from '/src';
import { Dropdown, Accordion, Tooltip } from 'flowbite-react';
import { Result } from '../components/result-chart';
import { WeirdBehaviorSingleDialog } from '../components/single-dialog';
import { ProgressCardPlaceholder } from '../components/card/progress-card-placeholder';
import { ShowInterviewProgressBlocks } from '../components/show-interview-progress-block';

export const AiInterviewSinglePage = () => {
  // Route Params
  const { id } = useParams();

  // UI Hooks
  const { notify } = useNotify();

  // State
  const [details, setDetails] = useState(null);
  const [weirdBehaviorFilter, setWeirdBehaviorFilter] = useState(false);
  const [typicalBehaviorFilter, setTypicalBehaviorFilter] = useState(false);
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [handleGetStage, setHandleGetSatge] = useState(false);
  const [isMaximized, setIsMaximize] = useState(true);

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get(`ai-interview/single/${id}?weirdBehavior=${weirdBehaviorFilter}&typicalBehavior=${typicalBehaviorFilter}`);
      setDetails(response.data);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '—';

    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'medium',
      timeStyle: 'short',
    }).format(date);
  };

  // format time taken to 100%
  const formatTimeTaken = (timeTaken) => {
    if (timeTaken > 60) {
      const hours = Math.floor(timeTaken / 60);
      const minutes = Math.round(timeTaken % 60);
      return `${hours}h ${minutes}m`;
    } else {
      return `${Math.round(timeTaken)}m`;
    }
  };

  // On Mount
  useEffect(() => {
    handleGet();
  }, [weirdBehaviorFilter, typicalBehaviorFilter]);

  if (!details) {
    return <ProgressCardPlaceholder />;
  }

  // Function for values in small screen table
  const Section = ({ title, value }) => {
    return (
      <>
        <div className="sm:flex">
          <div className="h-full p-2 flex items-center self-center font-medium text-gray-700 dark:text-gray-400 w-44 min-w-44">
            <p className="break-words text-sm max-w-32">{title}</p>
          </div>
          <div className="p-2 self-center break-all font-medium text-gray-900 dark:text-white">{value || '—'}</div>
        </div>
      </>
    );
  };

  const convertToHoursAndMinutes = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return { hours, remainingMinutes };
  };

  const { hours, remainingMinutes } = convertToHoursAndMinutes(details.interviewObj?.duration);

  const handleResult = () => {
    if (!details.interviewObj.timeTaken) return 'Not Started';
    else if (details.interviewObj.score * 10 > 80) return 'Excellent';
    else if (details.interviewObj.score * 10 >= 50) return 'Good';
    else if (details.interviewObj.score * 10 < 50) return 'Poor';
  };
  const handleColors = () => {
    if (!details.interviewObj.timeTaken) return { label: 'bg-[#EDEEF0] text-[#6B7280]' };
    else if (details.interviewObj.score * 10 > 80)
      return {
        circle: 'text-chartExcellentCircle',
        label: 'text-chartExcellentTextColor bg-chartExcellentBackground border-chartExcellentTextColor',
      };
    else if (details.interviewObj.score * 10 >= 50)
      return { circle: 'text-chartGoodCircle', label: 'text-chartGoodTextColor bg-chartGoodBackground border-chartGoodTextColor' };
    else if (details.interviewObj.score * 10 < 50)
      return { circle: 'text-chartPoorCircle', label: 'text-chartPoorTextColor bg-chartPoorBackground border-chartPoorTextColor' };
  };

  const downloadDocument = async () => {
    try {
      const response = await api.get(`ai-interview/stages/report/${id}`, {
        responseType: 'blob',
      });
      const url = window.URL.createObjectURL(
        new Blob([response.data], {
          type: response.headers['content-type'],
        })
      );
      const a = document.createElement('a');
      a.href = url;
      a.download = 'test-report.xlsx';
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  return (
    <div className="space-y-8">
      {/* <!-- Test Meta Section --> */}
      {/* TODO  In interview make the main color baby blue, Replace the labels icons instead of purple-500 to blue-500 and purple-100 blue-100 and so on */}
      <div className="bg-[#f1ecfc] bg-opacity-30 dark:bg-darkBackgroundCard p-4 rounded-lg shadow-lg">
        <div className="flex gap-2 items-center text-center pb-2 mb-4 border-b">
          <div className="text-gray-900  dark:text-gray-100 text-xl font-semibold dark:border-gray-500 mb-2 ">
            {details?.interviewObj?.subCategory?.subCategoryName}
          </div>
          <span className="bg-purple-100 dark:bg-gray-700 text-indigo-500 dark:text-indigo-400 text-center px-4 py-2 rounded-full text-xs font-medium">
            {details.interviewObj?.category?.categoryName}
          </span>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
          {/* Date & Time */}
          <div className="flex items-center  gap-2">
            <Icon icon="mdi:calendar-month-outline" className="text-indigo-500 dark:text-indigo-400" width="24" />
            <div>
              <span className="text-gray-600 dark:text-gray-500 text-sm">Created At:</span>
              <div className="text-gray-700 dark:text-grayTextOnDarkMood  font-medium text-base"> {formatDate(details?.interviewObj?.createdAt)}</div>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Icon icon="ph:chart-bar" className="text-indigo-500 dark:text-indigo-400" width="25" />
            <div>
              <span className="text-gray-600 dark:text-gray-500 text-sm ">Difficulty:</span>
              <div className=" text-gray-700 dark:text-grayTextOnDarkMood text-base font-medium">
                <EnumText name={'QuizDifficulty'} value={details.interviewObj.difficulty || '—'} />
              </div>
            </div>
          </div>

          {/* Duration */}
          <div className="flex items-center gap-3">
            <Icon icon="mdi:clock-time-four-outline" className="text-indigo-500 dark:text-indigo-400" width="24" />
            <div>
              <span className="text-gray-600 dark:text-gray-500 text-sm ">Duration</span>
              <div className="text-gray-700 font-medium dark:text-grayTextOnDarkMood text-base">{details?.interviewObj?.duration || '—'} min </div>
            </div>
          </div>

          {/* Number of Questions */}
          <div className="flex items-center gap-3">
            <Icon icon="ph:circle-wavy-question-fill" className="text-purple-500 dark:text-purple-400" width="25" />
            <div>
              <span className="text-gray-600 dark:text-gray-500 text-sm ">Number of Questions</span>
              <div className="text-gray-700 font-medium dark:text-grayTextOnDarkMood text-base">{details?.interviewObj?.numOfQuestions || '—'}</div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-darkBackgroundCard p-6 rounded-lg shadow-xl mt-6">
        <div className="flex flex-col lg:flex-row gap-6 md:border-none">
          {/* Test Progress */}
          <div className="flex-3 grow bg-white dark:bg-darkBackgroundCard dark:border-none">
            <div className="space-y-6">
              <div className="flex flex-col md:flex-row md:items-start space-y-6 md:space-y-0 md:space-x-8">
                <div className="rounded-lg  flex-1 space-y-6">
                  {/* Applicant Information */}
                  <div className="space-y-4">
                    {/* Name, Email, and Phone */}
                    <div className="space-y-4">
                      <div className="flex gap-2 items-start">
                        <div className="flex flex-col md:flex-row items-start space-y-4 md:space-y-0 md:space-x-4 w-full ">
                          <div className="flex sm:flex-row sm:items-center space-x-4">
                            <Icon icon="mdi:account-circle" className="text-purple-500 dark:text-purple-400 " width="50" />
                            <div className="flex-1 space-y-1">
                              <p className="text-gray-800 dark:text-white text-xl font-bold">{details?.applicant?.name || '—'}</p>
                              <p className="text-gray-900 dark:text-gray-300 text-base  text-ellipsis break-all">{details.applicant?.email || '—'}</p>
                              <p className="text-gray-900 dark:text-gray-300 text-base">{details.applicant?.mobileNumber || '—'}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Test Accessed and Time Taken */}
                    <div className="space-y-4">
                      <div className="flex items-center gap-3 text-md">
                        <Icon icon="mdi:clock-outline" className="text-purple-500 dark:text-purple-400" width="22" />
                        <span className="text-gray-600 dark:text-gray-500 ">
                          Started At:{' '}
                          <span className="font-medium text-gray-700 dark:text-grayTextOnDarkMood">
                            {formatDate(details?.interviewObj?.startedAt)}
                          </span>
                        </span>
                      </div>
                      <div className="flex items-center gap-3 text-md">
                        <Icon icon="mdi:timer-sand" className="text-purple-500 dark:text-purple-400" width="22" />
                        <span className="text-gray-600  dark:text-gray-500">
                          Time Taken:{' '}
                          <span className="font-medium text-gray-700 dark:text-grayTextOnDarkMood">
                            {formatTimeTaken(details.interviewObj.timeTaken)}
                          </span>
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Question Breakdown */}
                  <div className="space-y-2 px-2">
                    <div className="flex flex-col md:flex-row justify-between">
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Score Breakdown</h2>
                      {/* Breakdown Labels */}
                      <div className="flex flex-col sm:flex-row justify-end md:justify-between items-start sm:items-center text-sm mt-2 gap-2 sm:gap-8 md:gap-2 lg:gap-8 ">
                        <div className="flex items-center gap-1">
                          <span className="w-2 h-2 inline-block bg-green-500"></span>
                          <span className="text-gray-900 dark:text-gray-100 ">Correct answers:</span>
                          <span className="text-gray-700 dark:text-gray-400 font-bold">{details.interviewObj.questionsSummary.correctAnswers}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <span className="w-2 h-2 inline-block bg-red-500"></span>
                          <span className="text-gray-900 dark:text-gray-100 ">Wrong answers:</span>
                          <span className="text-gray-700 dark:text-gray-400 font-bold">{details.interviewObj.questionsSummary.wrongAnswers}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <span className="w-2 h-2 inline-block bg-[#f59e0b]"></span>
                          <span className="text-gray-900 dark:text-gray-100">Skipped questions:</span>
                          <span className="text-gray-700 dark:text-gray-400 font-bold">
                            {details.interviewObj.questionsSummary.skippedQuestions || 0}
                          </span>
                        </div>
                        {details.interviewObj.questionsSummary.unAnsweredQuestions > 0 && (
                          <div className="flex items-center gap-1">
                            <span className="w-2 h-2 inline-block bg-gray-400"></span>
                            <span className="text-gray-00 dark:text-gray-100 font-medium">Unanswered</span>
                            <span className="text-gray-500 dark:text-gray-400">
                              {details.interviewObj.questionsSummary.unAnsweredQuestions}
                              {/* To be added when need percentage  */}
                              {/* <span className="ml-1 text-gray-900 dark:text-gray-100">
                                {(
                                  (details.interviewObj.questionsSummary.unAnsweredQuestions / details.interviewObj.questionsSummary.totalQuestions) *
                                  100
                                ).toFixed(1)}
                                %
                              </span> */}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    {/* Breakdown Bar */}
                    <div className="mt-2 h-4 bg-gray-200 flex">
                      <div
                        className="bg-green-500 h-full"
                        style={{
                          width: `${
                            (details.interviewObj.questionsSummary.correctAnswers /
                              (details.interviewObj.questionsSummary.correctAnswers + details.interviewObj.questionsSummary.wrongAnswers)) *
                            100
                          }%`,
                        }}
                      ></div>

                      <div
                        className="bg-red-500 h-full"
                        style={{
                          width: `${
                            (details.interviewObj.questionsSummary.wrongAnswers /
                              (details.interviewObj.questionsSummary.correctAnswers + details.interviewObj.questionsSummary.wrongAnswers)) *
                            100
                          }%`,
                          marginLeft: '2px', // Small space between sections
                        }}
                      ></div>

                      <div
                        className="bg-[#f59e0b] h-full"
                        style={{
                          width: `${
                            (details.interviewObj.questionsSummary.skippedQuestions / details.interviewObj.questionsSummary.totalQuestions) * 100
                          }%`,
                          marginLeft: '2px', // Small space between sections
                        }}
                      ></div>

                      {details.interviewObj.questionsSummary.unAnsweredQuestions > 0 && <div className="bg-gray-400 h-full"></div>}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Chart Progress Section */}
          <div className="font-medium dark:text-white space-y-4 self-center grow flex-1">
            <div className="flex flex-col items-center justify-center gap-4">
              {/* Test Charts */}
              <div className="relative self-center sm:col-span-2 mx-auto">
                <Result testResult={details.interviewObj.score} />
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <p className={`text-4xl ${handleColors().circle} text-center`}>{details.interviewObj.score}%</p>
                  <p className="text-center text-[#808080] text-lg font-normal text-nowrap">Total Score</p>
                </div>

                <div
                  className={`w-full text-center p-1 text-base font-medium rounded-xl absolute left-1/2 -translate-x-1/2 bottom-0 py-2 border ${
                    handleColors().label
                  }`}
                >
                  {handleResult()}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-darkBackgroundCard p-6 rounded-lg shadow-lg mt-6 flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0 md:space-x-4">
        <div className="flex space-x-3">
          <p className="text-gray-800 dark:text-gray-200 text-lg">
            <span>
              You can download the <span className="text-primaryPurple font-medium">Test Answers</span>, primarily for{' '}
              <span className="text-primaryPurple font-medium">Technical Evaluation</span>.{' '}
            </span>
            <span className="font-medium">Click the button to download the full report.</span>
          </p>
        </div>
        <div>
          {/* not used at the moment */}
          <Button tertiary onClick={downloadDocument} label="Download report" />
        </div>
      </div>
    </div>
  );
};
