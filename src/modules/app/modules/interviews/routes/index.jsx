import React from 'react';
import { Navigate } from 'react-router-dom';

import { AiInterviewsListPage } from '../pages/list';
import { AiInterviewSinglePage } from '../pages/single';
import { InterviewsMainLayout } from '../layouts/main';

export default [
  {
    path: 'interviews',
    element: <InterviewsMainLayout />,
    loader() {
      return {
        label: 'Interviews',
      };
    },
    children: [
      // Routes
      {
        path: '',
        element: <Navigate to={'/app/interviews/list'} />,
      },
      {
        path: 'list',
        element: <AiInterviewsListPage />,
        loader() {
          return {
            label: 'List',
            icon: 'ri:mail-send-line',
            title: 'Interview Managament',
            subtitle: 'Manage assigned interviews, track statuses and review applicant scores',
          };
        },
      },
      {
        path: 'view/:id',
        element: <AiInterviewSinglePage />,
        loader() {
          return {
            label: 'Progress',
            title: 'Interview Progress',
          };
        },
      },
    ],
  },
];
