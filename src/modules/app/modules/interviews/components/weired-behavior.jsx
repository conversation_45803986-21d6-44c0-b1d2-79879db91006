import React from 'react';
import { Enums, EnumText } from '/src';

export const WeiredBehavior = ({ stage }) => {
  return (
    <>
      {stage?.weirdBehavior?.map((behavior, index) => (
        <div className="py-2" key={index}>
          <span className="text-[#111827] dark:text-white font-medium">
            <EnumText name={'Logs'} value={behavior?.type} />
          </span>
          {behavior?.type === Enums.Logs.KeyboardKeyDown && (
            <span className="break-words text-[#111827] dark:text-white font-medium">
              {behavior?.data?.keyCode === 44 ? 'The PrtSc key (Screenshot)' : null} !!
            </span>
          )}
          <p className="text-grayDetail text-sm">{new Date(behavior?.createdAt).toLocaleString()}</p>
        </div>
      ))}
    </>
  );
};
