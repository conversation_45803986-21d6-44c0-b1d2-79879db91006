// React
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

// Core
import { Jumbotron, Button, useForm, Enums, Form, Api, useNotify } from '/src';

// Components
import { Stage } from '../components/stage';
import { ChooseTypeAssignAssessment } from '../components/assign/type';
import { SetupAssignAssessment } from '../components/assign/setup-interview';
import { AssignAndScheduleAssessment } from '../components/assign/assign-and-schedule';
import { ReviewAssignAssessment } from '../components/assign/review';
import { TestCreatedSucessfully } from '../../applicants/components/test-created-sucessfully';

// Flowbite
import { Spinner } from 'flowbite-react';

export const AssessmentsAssignPage = () => {
  // Hooks
  const navigate = useNavigate();
  const { type, id } = useParams();
  const { notify } = useNotify();

  // State
  const [activeStage, setActiveStage] = useState(0);
  const [disableNextButton, setDisableNextButton] = useState(false);
  const [isTestCreatedSucessfullyVisible, setTestCreatedSucessfullyVisibilty] = useState(false);
  const [quizUrl, setQuizURL] = useState(false);
  const [loading, setLoading] = useState(false);

  // Form
  const { form, setFieldValue, setFormValue, resetForm } = useForm({
    numberOfQuestions: '',
    estimationTime: 60,
    type: 1, // Interview type
    skips: '',
    applicantId: {},
    notes: '',
    startDate: null,
    dueDate: null,
    category: [],
    subCategory: [],
    difficulty: null,
    modelType: 'gpt-4.1-mini',
    avatarName: Enums?.AiAvatarModel[0].value,
    avatarDescription: 'Technical Manager',
    avatarLang: 'english',
    seniorityLevel: 1,
  });

  // Stage
  const stage = [
    {
      label: `Choose ${type.charAt(0).toUpperCase() + type.slice(1)}`,
      ...(type === 'interview' && { header: 'Choose Interview Type' }),
      component: (
        <ChooseTypeAssignAssessment
          formData={{ form, setFieldValue, setFormValue, resetForm }}
          disableButtons={{ disableNextButton, setDisableNextButton }}
        />
      ),
    },
    ...(type === 'interview'
      ? [
          {
            label: 'Setup Interview',
            header: 'Setup Interview Preferences',
            component: (
              <SetupAssignAssessment
                formData={{ form, setFieldValue, setFormValue, resetForm }}
                disableButtons={{ disableNextButton, setDisableNextButton }}
              />
            ),
          },
        ]
      : []),
    {
      label: 'Assign & Schedule',
      header: 'Assign to Applicants & Schedule',
      component: (
        <AssignAndScheduleAssessment
          formData={{ form, setFieldValue, setFormValue, resetForm }}
          disableButtons={{ disableNextButton, setDisableNextButton }}
        />
      ),
    },
    {
      label: 'Review & Confirm',
      header: `Review ${type.charAt(0).toUpperCase() + type.slice(1)}’s Details`,
      component: (
        <ReviewAssignAssessment
          formData={{ form, setFieldValue, setFormValue, resetForm }}
          disableButtons={{ disableNextButton, setDisableNextButton }}
        />
      ),
    },
  ];

  const handleSubmit = async () => {
    try {
      setLoading(true);
      if (type === 'screening') {
        /* Screening */
        const payload = {
          quizId: form?.quizId,
          startDate: form?.startDate,
          dueDate: form?.dueDate,
        };
        // if (form?.extraTime >= 1) payload.exceededTime = extraTime;
        if (Object.keys(form?.applicantId).length > 0) {
          payload.applicantId = Object.keys(form.applicantId).filter((key) => form.applicantId[key]);
        }
        const response = await Api.post('submissions/screening/single', payload);
        setQuizURL(response?.data?.quizUrl);
        setTestCreatedSucessfullyVisibilty(true);
      } else if (type === 'test') {
        /* Test */
        const payload = {
          quizId: form?.quizId,
          otherTest: true,
          startDate: form?.startDate,
          dueDate: form?.dueDate,
        };
        // if (form?.extraTime >= 1) payload.exceededTime = extraTime;
        if (Object.keys(form?.applicantId).length > 0) {
          payload.applicantId = Object.keys(form.applicantId).filter((key) => form.applicantId[key]);
        }
        const response = await Api.post('submissions/single', payload);
        setQuizURL(response?.data?.quizUrl);
        setTestCreatedSucessfullyVisibilty(true);
      } else if (type === 'interview') {
        /* Interview */
        const { applicantId, ...payload } = form;
        if (payload.notes == '') delete payload.notes;
        if (Object.keys(form?.applicantId).length > 0) {
          payload.applicantId = Object.keys(form.applicantId).filter((key) => form.applicantId[key]);
        }

        // Check if this is a template-based interview (selectedBasedTemplate exists)
        if (form.quizId) {
          // Template-based interview
          payload.type = 3; // Set type to 3 for template-based interviews
          payload.templateId = form.quizId; // Send the template ID
        } else {
          // Interactive interview
          payload.type = 2; // Keep type as 2 for interactive interviews
          payload.category = [payload.category];
          payload.numberOfQuestions = form.numberOfQuestions;
        }

        const response = await Api.post('ai-interview/single', payload);
        setQuizURL(response?.data?.quizUrl);
        setTestCreatedSucessfullyVisibilty(true);
      }
    } catch (error) {
      notify.error(error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  };

  const handelGet = async () => {
    let assessment;
    switch (type) {
      case 'screening':
        assessment = 'quizzes/single/phoneScreening';
        break;
      case 'test':
        assessment = 'quizzes/single';
        break;
      case 'interview':
        assessment = 'quizzes/single';
        break;
      default:
        break;
    }

    try {
      setLoading(true);
      const response = await Api.get(`${assessment}/${id}`);
      const responseData = Array.isArray(response.data) ? response.data[0] : response.data;
      if (!responseData) throw new Error('No data returned from API');

      // Set form values with default fallbacks
      setFieldValue('quizId')(responseData?._id);
      setFieldValue('category')(responseData?.category);
      setFieldValue('categoryName')(responseData?.categoryName);
      setFieldValue('subCategory')(responseData?.subCategory);
      setFieldValue('subCategoryName')(responseData?.subCategoryName);
      setFieldValue('difficulty')(responseData?.difficulty);
      setFieldValue('estimationTime')(responseData?.duration);
      setFieldValue('skips')(responseData?.skips);
      setFieldValue('numberOfQuestions')(responseData?.numOfQuestions);
      setActiveStage(1);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      handelGet();
    }
  }, [id]);

  return (
    <div className="relative">
      <div className="space-y-6">
        <Jumbotron />

        <Stage
          stage={stage}
          selectedStage={{
            activeStage: activeStage,
            setActiveStage: setActiveStage,
          }}
        />

        <div className="text-xl font-semibold dark:text-white">{stage[activeStage]?.header}</div>

        <Form>{stage[activeStage]?.component}</Form>

        <div className="flex justify-end gap-4">
          <Button
            className="w-[107px]"
            label={activeStage === 0 ? 'Cancel' : 'Back'}
            tertiary
            onClick={() => (activeStage === 0 ? navigate(`/app/assessment-templates/list/${type}`) : setActiveStage((prev) => prev - 1))}
          />
          <Button
            className="w-48"
            label={activeStage === stage.length - 1 ? 'Confirm' : 'Next'}
            onClick={() => (activeStage === stage.length - 1 ? handleSubmit() : setActiveStage((prev) => prev + 1))}
            disabled={disableNextButton}
          />
        </div>
      </div>

      {loading && (
        <div className="absolute z-50 left-0 right-0 bottom-0 top-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80">
          <Spinner size="lg" color="purple" />
        </div>
      )}

      {isTestCreatedSucessfullyVisible && (
        <TestCreatedSucessfully
          assignment={Object.keys(form.applicantId).filter((key) => form.applicantId[key]).length > 0}
          defaultType={<span className="capitalize">{type}</span>}
          quizUrl={quizUrl}
          onClose={() => {
            setTestCreatedSucessfullyVisibilty(false);
            navigate(`/app/assessment-templates/list/${type}`);
          }}
        />
      )}
    </div>
  );
};
