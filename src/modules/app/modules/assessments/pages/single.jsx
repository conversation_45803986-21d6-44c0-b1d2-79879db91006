// React
import { useState, useContext, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

// Core
import { ScrollableTabs, Jumbotron, CustomIcon, Api, Button, Enums, useForm, useNotify, SubscribeDialog } from '/src';

// Context
import { AppContext } from '/src/components/provider';

// Components
import { SingleContent } from '../components/single/content';

export const AssessmentSingle = () => {
  // States
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [needSubscription, setNeedSubscription] = useState(false);

  // Context
  const { userData } = useContext(AppContext);

  // Permissions
  const isSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));
  const isAdmin = userData?.roles.some((role) => ['admin'].includes(role));
  const isContentCreator = userData?.roles.some((role) => ['content-creator'].includes(role));
  const isHr = userData?.roles.some((role) => ['hr'].includes(role));

  // Hooks
  const navigate = useNavigate();
  const { id, type } = useParams();
  const { notify } = useNotify();

  // Form
  const { form, setFieldValue, setFormValue, resetForm } = useForm({});

  const tabs = [
    {
      title: 'Content',
      data: <SingleContent formData={{ form, setFieldValue, setFormValue, resetForm }} />,
    },
  ];

  const featuresObjectKey = () => {
    if (type === 'screening') return 'assignScreening';
    else if (type === 'test') return 'assignTest';
    else if (type === 'interview') return 'assignInterview';
  };

  const handleGetQuizzData = async () => {
    try {
      setLoading(true);
      const assessment = type === 'screening' ? 'quizzes/single/phoneScreening' : 'quizzes/single';
      const response = await Api.get(`${assessment}/${id}`);
      const data = type === 'screening' ? response.data[0] : response.data;

      // For screening type, ensure questions are properly formatted
      if (type === 'screening' && data.questions) {
        // Make sure questions array is properly set in the form data
        data.questionIds = data.questions.map((q) => q._id || q.id);
      }

      setFormValue(data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  };

  const getBlocksCards = () => {
    if (!form) return [];

    return [
      {
        header: 'Experience Level',
        subHeader: form.seniorityLevel ? Enums.QuizDifficulty.find((quiz) => quiz.value === form.seniorityLevel)?.label : null,
        icon: 'manInSuit',
      },
      {
        header: 'Questions',
        // Handle both questionIds and questions array for screening
        subHeader: form.questionIds?.length || form.questions?.length || 0,
        icon: 'questionInBorder',
      },
      {
        header: type === 'screening' || type === 'test' ? 'Duration' : 'Estimation time',
        subHeader: form.duration,
        icon: 'clockThree',
      },
      {
        header: 'Difficulty',
        subHeader: form.difficulty ? Enums.QuestionDifficulty.find((quiz) => quiz.value === form.difficulty)?.label : null,
        icon: 'charts',
      },
    ];
  };

  useEffect(() => {
    handleGetQuizzData();
  }, []);

  return (
    <>
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
          <Jumbotron />

          <div className="flex items-center gap-2">
            {(isSuperAdmin || isAdmin || isContentCreator) && (
              <Button label="Edit Template" onClick={() => navigate(`/app/assessment-templates/edit/${type}/${id}`)} />
            )}
            {(isSuperAdmin || isAdmin || isHr) && (
              <Button
                label="Assign Template"
                onClick={() =>
                  isSuperAdmin || userData?.features?.[featuresObjectKey()] > 0
                    ? navigate(`/app/assessment-templates/assign/${type}/${id}`)
                    : setNeedSubscription(true)
                }
                tertiary
              />
            )}
          </div>
        </div>

        <div className="p-4 space-y-4 border rounded-xl ">
          <div className="space-y-1">
            <p className="text-xl font-semibold">{form.title}</p>
            <p className="text-[#667085] dark:text-white">{form.description}</p>
          </div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4 !mt-4">
            {getBlocksCards().map((block) => (
              <div key={block.header} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="space-y-1">
                  <p className="text-sm text-[#535862]">{block.header}</p>
                  <p className="text-base font-semibold">{block.subHeader || '—'}</p>
                </div>
                <CustomIcon definedIcon={block.icon} />
              </div>
            ))}
          </div>

          <div className="flex items-center gap-2">
            <p className="text-[#535862BF] dark:text-white">Categories covered:</p>
            <div className="flex flex-wrap items-center gap-2">
              {form?.categoryName?.map((category) => (
                <p className="px-2 py-1 bg-[#F4F4F5] text-[#667085] text-[11px] font-medium rounded-md">{category}</p>
              ))}
            </div>
          </div>

          <div className="flex items-center gap-2">
            <p className="text-[#535862BF] dark:text-white">Subcategories covered:</p>
            <div className="flex flex-wrap items-center gap-2">
              {form?.subCategoryName?.map((subCategory) => (
                <p className="px-2 py-1 text-xs border font-medium rounded-full">{subCategory}</p>
              ))}
            </div>
          </div>
        </div>

        {/* <div className="pb-4">
          <ScrollableTabs
            data={tabs}
            selectedTab={{
              activeTab: activeTab,
              setActiveTab: setActiveTab,
            }}
          />
        </div> */}

        {tabs[activeTab].data}
      </div>

      {/* Need Subscription */}
      {needSubscription && <SubscribeDialog onClose={() => setNeedSubscription(false)} />}
    </>
  );
};
