// React
import { useState, useContext, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Draggable from 'react-draggable';
import * as LucideIcons from 'lucide-react';

// Core
import { useForm, Jumbotron, Api, Button, Form, CustomIcon, Icon, useNotify, useScreenSize } from '/src';

// Components
import { RandomizeAskAiPage } from '../components/create/question-bank/randomize-ask-ai';
import { ReviewDrawer } from '../components/create/review-drawer';
import { Stage } from '../components/stage';
import { QuestionBank } from '../components/create/question-bank';
import { TemplateInfo } from '../components/create/template-info';

export const AssessmentsCreatePage = () => {
  // Hooks
  const { notify } = useNotify();
  const { type, id } = useParams();
  const navigate = useNavigate();

  // Lucide
  const LucideCircleCheckBig = LucideIcons.CircleCheckBig;

  // Reference
  const formRef = useRef(null);

  // State
  const [activeStage, setActiveStage] = useState(0);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [anyQuestionHasEditMode, setAnyQuestionHasEditMode] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [isShowReviewDrawer, setIsShowReviewDrawer] = useState(false);
  const [selectedQuestionsID, setSelectedQuestionsID] = useState({});
  const [isRandomizeAskAiVisible, setRandomizeAskAiVisibilty] = useState(false);
  const [disableNextButton, setDisableNextButton] = useState(false);
  const [disabledMessage, setDisabledMessage] = useState('');

  // Set edit mode based on id presence
  useEffect(() => {
    setIsEditMode(!!id);
    if (id) {
      handelGet();
    }
  }, [id]);

  const { form, setFieldValue, setFormValue, resetForm } = useForm({
    title: '',
    description: '',
    seniorityLevel: '',
    difficulty: '',
    duration: '',
    skips: '',
  });
  // Stage
  const stage = [
    {
      label: `Choose Questions`,
      // header: 'Choose Questions',
      component: (
        <QuestionBank
          formData={{ form, setFieldValue, setFormValue, resetForm }}
          disableButtons={{ disableNextButton, setDisableNextButton, disabledMessage, setDisabledMessage }}
          selectedQuestionsID={selectedQuestionsID}
          setSelectedQuestionsID={setSelectedQuestionsID}
          anyQuestionHasEditMode={anyQuestionHasEditMode}
          setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
          isRandomizeAskAiVisible={isRandomizeAskAiVisible}
          setRandomizeAskAiVisibilty={setRandomizeAskAiVisibilty}
        />
      ),
    },

    {
      label: 'Setup Info',
      // header: `Setup Info`,
      component: (
        <TemplateInfo
          formData={{ form, setFieldValue, setFormValue, resetForm }}
          disableButtons={{ disableNextButton, setDisableNextButton, disabledMessage, setDisabledMessage }}
        />
      ),
    },
  ];

  const handleSubmit = async () => {
    // if (formRef.current && !formRef.current.checkValidity()) {
    //   //   // Trigger validation UI
    //   formRef.current.reportValidity();
    //   return; // Stop execution if form is invalid
    // }
    if (activeStage === stage.length - 1) {
      setIsLoading(true);
      let endpoint;
      let method = isEditMode ? 'put' : 'post';
      let successMessage = isEditMode ? 'updated' : 'created';

      if (type == 'interview') {
        endpoint = 'ai-interview/template/single';
      } else if (type == 'test') {
        endpoint = 'quizzes/single';
      } else endpoint = 'quizzes/single/phoneScreening';

      // Add ID to endpoint if in edit mode
      if (isEditMode) {
        endpoint = `${endpoint}/${id}`;
      }

      try {
        const payload = {
          title: form.title,
          description: form.description,
          seniorityLevel: form.seniorityLevel,
          difficulty: form.difficulty,
          duration: Number(form.duration),
          skips: form.skips,
          locked: false,
          questionIds: Object.entries(selectedQuestionsID)
            .filter(([_, value]) => value === true)
            .map(([key, _]) => key),
        };

        if (type == 'screening') {
          payload.phoneScreening = true;
        }

        await Api[method](endpoint, payload);

        notify(`${type.charAt(0).toUpperCase() + type.slice(1)} ${successMessage} successfully!`);
        navigate(`/app/assessment-templates/list/${type}`);
      } catch (error) {
        notify.error(error.response?.data?.message);
      } finally {
        setIsLoading(false);
      }
    } else {
      setActiveStage((prev) => prev + 1);
    }
  };

  const handelGet = async () => {
    let assessment;
    switch (type) {
      case 'interview':
        assessment = 'quizzes/single';
        break;
      case 'screening':
        assessment = 'quizzes/single/phoneScreening';
        break;
      case 'test':
        assessment = 'quizzes/single';
        break;
      default:
        break;
    }

    try {
      setIsLoading(true);
      const response = await Api.get(`${assessment}/${id}`);

      // Handle different response structures
      const data = Array.isArray(response.data) ? response.data[0] : response.data;

      if (!data) {
        throw new Error('No data returned from API');
      }

      // Set form values with default fallbacks
      setFormValue({
        title: data.title || '',
        description: data.description || '',
        seniorityLevel: data.seniorityLevel || '',
        difficulty: data.difficulty || '',
        duration: data.duration || '',
        skips: data.skips || '',
      });

      // Set selected questions
      if (data.questionIds && Array.isArray(data.questionIds)) {
        const questionsMap = {};
        data.questionIds.forEach((questionId) => {
          questionsMap[questionId] = true;
        });
        setSelectedQuestionsID(questionsMap);
      }
    } catch (error) {
      notify.error(error.response?.data?.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Use these styles to make black overlay visible and not scrollable
  // Make the scroll in list pages only be smooth
  useEffect(() => {
    if (isRandomizeAskAiVisible || isShowReviewDrawer) {
      document.querySelector('html').classList.add('overflow-x-hidden');
    } else {
      document.querySelector('html').classList.remove('overflow-x-hidden');
    }
  }, [isRandomizeAskAiVisible, isShowReviewDrawer]);

  return (
    <>
      <div className="space-y-4">
        <Jumbotron />

        <Stage
          stage={stage}
          selectedStage={{
            activeStage: activeStage,
            setActiveStage: setActiveStage,
          }}
        />

        <Form onSubmit={handleSubmit} ref={formRef} className="space-y-4">
          {stage[activeStage]?.component}

          <div className="flex justify-end gap-4">
            <Button
              className="w-[107px]"
              label={activeStage === 0 ? 'Cancel' : 'Back'}
              tertiary
              onClick={() => (activeStage === 0 ? navigate(`/app/assessment-templates/list/${type}`) : setActiveStage((prev) => prev - 1))}
            />
            <Button
              className="w-48"
              label={activeStage === stage.length - 1 ? (id ? 'Update' : 'Create') : 'Next'}
              onClick={() => (activeStage === stage.length - 1 ? handleSubmit() : setActiveStage((prev) => prev + 1))}
              disabled={disableNextButton}
              disabledMessage={disabledMessage}
              disabledMessageClassName="z-30"
            />
          </div>
        </Form>
      </div>

      {/* <Draggable> */}
      <div
        onClick={() => Object.keys(selectedQuestionsID)?.filter((key) => selectedQuestionsID[key])?.length > 0 && setIsShowReviewDrawer(true)}
        className={`flex justify-between items-center gap-2 px-3 py-2.5 bg-[#7E3AF2] text-white text-center fixed bottom-60 right-6 z-[60] shadow-lg rounded-full ${
          Object.keys(selectedQuestionsID)?.filter((key) => selectedQuestionsID[key])?.length > 0 ? 'cursor-pointer' : 'cursor-not-allowed'
        } `}
      >
        <LucideCircleCheckBig width={20} />
        <span className="font-medium select-none">
          Selected Questions ({Object.keys(selectedQuestionsID).filter((key) => selectedQuestionsID[key]).length})
        </span>
        <Icon icon="material-symbols:arrow-forward-ios-rounded" width={18} />
      </div>
      {/* </Draggable> */}

      {isShowReviewDrawer && (
        <ReviewDrawer
          onClose={() => setIsShowReviewDrawer(false)}
          selectedQuestionsID={selectedQuestionsID}
          setSelectedQuestionsID={setSelectedQuestionsID}
          anyQuestionHasEditMode={anyQuestionHasEditMode}
          setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
          canRemoveQuestion={true}
        />
      )}

      {isRandomizeAskAiVisible && (
        <RandomizeAskAiPage
          onClose={() => setRandomizeAskAiVisibilty(false)}
          questionsListData={{ selectedQuestionsID, setSelectedQuestionsID }}
          type={isRandomizeAskAiVisible}
          selectedQuestionsID={selectedQuestionsID}
          setSelectedQuestionsID={setSelectedQuestionsID}
          anyQuestionHasEditMode={anyQuestionHasEditMode}
          setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
        />
      )}
    </>
  );
};
