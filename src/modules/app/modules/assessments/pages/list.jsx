import { useContext, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { AppContext } from '/src/components/provider';
import { Jumbotron, Button, CustomIcon, SubscribeDialog } from '/src';
import { Dropdown, DropdownItem } from 'flowbite-react';

import { ScreeningTab } from '../components/list/screening-tab';
import { TestTab } from '../components/list/test-tab';
import { InterviewTab } from '../components/list/interview-tab';

export const AssessmentsListPage = () => {
  const navigate = useNavigate();
  const { type } = useParams();

  const { userData } = useContext(AppContext);

  const isSuperAdmin = userData?.roles.includes('super-admin');
  const isAdmin = userData?.roles.includes('admin');
  const isContentCreator = userData?.roles.includes('content-creator');
  const isHr = userData?.roles.includes('hr');

  const [needSubscription, setNeedSubscription] = useState(false);

  const dropdownAssignList = [
    {
      label: 'Screening',
      customIcon: 'screening',
      onClick: () =>
        isSuperAdmin || userData?.features?.assignScreening > 0 ? navigate('/app/assessment-templates/assign/screening') : setNeedSubscription(true),
    },
    {
      label: 'Test',
      customIcon: 'tests',
      onClick: () =>
        isSuperAdmin || userData?.features?.assignTest > 0 ? navigate('/app/assessment-templates/assign/test') : setNeedSubscription(true),
    },
    {
      label: 'Interview',
      customIcon: 'interview',
      element: (
        <span className="w-24 text-xs px-2 py-1 rounded-full shadow-md font-bold tracking-wide bg-magic-gradient bg-[length:200%] text-white overflow-hidden">
          AI Magic ✨
        </span>
      ),
      onClick: () =>
        isSuperAdmin || userData?.features?.assignInterview > 0 ? navigate('/app/assessment-templates/assign/interview') : setNeedSubscription(true),
    },
  ];

  const renderContent = () => {
    if (type === 'screening') return <ScreeningTab />;
    if (type === 'test') return <TestTab />;
    if (type === 'interview') return <InterviewTab />;
    return <div className="text-center text-gray-500">Invalid assessment type.</div>;
  };

  return (
    <div className="space-y-2">
      <div className="flex flex-wrap gap-2 justify-between items-start relative z-40">
        <Jumbotron />

        <div className="flex items-center gap-2">
          {(isSuperAdmin || isAdmin || isContentCreator) && (
            <Button
              customIcon="circledAdd"
              label="Create Template"
              onClick={() => {
                const routeMap = {
                  screening: userData?.features?.assignScreening,
                  test: userData?.features?.assignTest,
                  interview: userData?.features?.assignInterview,
                };

                const hasAccess = isSuperAdmin || routeMap[type] > 0;

                if (hasAccess) {
                  navigate(`/app/assessment-templates/create/${type}`);
                } else {
                  setNeedSubscription(true);
                }
              }}
            />
          )}

          {(isSuperAdmin || isAdmin || isHr) && (
            <Button
              label="Assign Template"
              tertiary
              onClick={() =>
                ['screening', 'test', 'interview'].includes(type) ? navigate(`/app/assessment-templates/assign/${type}`) : setNeedSubscription(true)
              }
            />
          )}
        </div>
      </div>

      {renderContent()}

      {needSubscription && <SubscribeDialog onClose={() => setNeedSubscription(false)} />}
    </div>
  );
};
