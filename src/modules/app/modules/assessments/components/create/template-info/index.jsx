// React
import { useEffect } from 'react';
import { useParams } from 'react-router-dom';

// Core
import { Card, TextInput, Textarea, Select, useValidate, Regex } from '/src';

export const TemplateInfo = ({ formData, disableButtons }) => {
  // Form
  const { form, setFieldValue, setFormValue, resetForm } = formData || {};

  // Hooks
  const { type, id } = useParams();
  const { isRequired, isNotSpaces, validateRegex, minLength, maxLength, isValidateMaxAndMinNumber, isNumber } = useValidate();

  useEffect(() => {
    disableButtons.setDisableNextButton(!form.title || !form.description || !form.seniorityLevel || !form.difficulty || !form.duration);
    disableButtons.setDisabledMessage('');
  }, [form]);

  return (
    <Card className="space-y-4">
      <div className="flex flex-wrap justify-between items-center gap-2">
        <div className="space-y-2">
          <h1 className="text-xl font-semibold ">Template Information</h1>
          <p className="text-[#A2A9BA] font-normal text-sm">Basic information about your {type} template</p>
        </div>

        {/* <Button label="Generate with AI" customIcon="stars" tertiary /> */}
      </div>

      <div className="space-y-4">
        <TextInput
          label="Template Name"
          className="dark:text-white"
          name="title"
          placeholder={`E.g., Frontend Developer ${type.charAt(0).toUpperCase() + type.slice(1)}`}
          value={form.title}
          onChange={setFieldValue('title')}
          validators={[isRequired(), validateRegex(Regex.name), minLength(2), maxLength(50)]}
        />

        <Textarea
          label="Description"
          className="dark:text-white resize-none"
          name="description"
          placeholder="Describe the purpose and scope of this template..."
          value={form.description}
          onChange={setFieldValue('description')}
          validators={[isRequired()]}
        />

        <div className="grid sm:grid-cols-2 gap-4">
          <Select
            label="Seniority Level"
            name="seniorityLevel"
            placeholder="Select seniority level"
            value={form.seniorityLevel}
            onChange={setFieldValue('seniorityLevel')}
            validators={[isRequired()]}
            lookup="$QuizDifficulty"
            dropIcon={true}
            className="w-full"
          />

          <Select
            label="Difficulty"
            name="difficulty"
            placeholder="Select difficulty level"
            value={form.difficulty}
            onChange={setFieldValue('difficulty')}
            validators={[isRequired()]}
            dropIcon={true}
            lookup="$QuestionDifficulty"
          />

          <TextInput
            label="Estimation time"
            name="duration"
            placeholder="Select estimation time"
            value={form.duration}
            onChange={setFieldValue('duration')}
            validators={[isRequired(), isNumber(), isValidateMaxAndMinNumber('min', 10), isValidateMaxAndMinNumber('max', 60)]}
            dropIcon={true}
            type="number"
            requiredLabel
          />

          {type == 'interview' && (
            <TextInput
              label="Max Skips"
              name="skips"
              placeholder="Add max question the applicants allow to skip without affecting the score "
              value={form.skips}
              onChange={setFieldValue('skips')}
              validators={[isRequired()]}
              dropIcon={true}
              className="w-full"
              type="number"
            />
          )}
        </div>
      </div>
    </Card>
  );
};
