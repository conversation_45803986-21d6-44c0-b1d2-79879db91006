// React
import { useState } from 'react';

// Core
import { Drawer, Checkbox, CustomIcon, ChartsDonut, useNotify, Card, Button, StaticData, useScreenSize } from '/src';

// Components
import { QuestionItem } from '../question-item';

// Flowbite
import { Pagination } from 'flowbite-react';

export const ReviewDrawer = ({
  onClose,
  selectedQuestionsID,
  setSelectedQuestionsID,
  anyQuestionHasEditMode,
  setAnyQuestionHasEditMode,
  canRemoveQuestion,
}) => {
  // Hooks
  const { notify } = useNotify();
  const screen = useScreenSize();

  // State
  const [reviewDrawerSelectedQuestionsID, setReviewDrawerSelectedQuestionsID] = useState(selectedQuestionsID || {});
  const [pagination, setPagination] = useState({ page: 1, size: 10 });

  const data = [
    { name: 'Easy', value: 85, color: '#4AA264' },
    { name: 'Medium', value: 5, color: '#F59E0B' },
    { name: 'Hard', value: 10, color: '#CE4132' },
  ];

  const rightData = () =>
    data?.map((item) => (
      <div key={item.name} className="flex items-center space-y-0.5 dark:text-white">
        <div className="size-3 mr-3 mt-0.5 rounded-sm" style={{ backgroundColor: item.color }}></div>
        <span className="w-32 mr-2">{item.name}</span>
        <span className="font-medium">{item.value}%</span>
      </div>
    ));

  // Computed
  const count = Object.entries(selectedQuestionsID).filter(([_, value]) => value)?.length;
  const { page, size } = pagination;
  const pagesCount = Math.max(Math.ceil(count / size), 1);
  const showingText = `${count ? page * size - size + 1 : count} - ${page * size > count ? count : page * size}`;

  return (
    <Drawer className="w-screen max-w-[400px]" onClose={onClose}>
      <Drawer.SingleView>
        <Drawer.Header
          onClose={onClose}
          headerLabel={
            <span className="text-lg self-center  dark:text-white font-semibold">
              Review Selected Questions ({Object.keys(selectedQuestionsID).filter((key) => selectedQuestionsID[key]).length})
            </span>
          }
        />

        {/* <Card className="p-2">
          <ChartsDonut data={data} rightData={rightData} rightDataStyles="" innerRadius={25} outerRadius={50} width="80%" height={150} />
        </Card> */}

        <Drawer.Body className="flex flex-col overflow-y-auto space-y-3 pr-1">
          {Object.entries(selectedQuestionsID)
            .filter(([_, value]) => value)
            .slice((page - 1) * size, page * size)
            .map(([questionId, _], index) => (
              <QuestionItem
                key={questionId}
                index={index}
                questionId={questionId}
                selectedQuestionsID={selectedQuestionsID}
                setSelectedQuestionsID={setSelectedQuestionsID}
                anyQuestionHasEditMode={anyQuestionHasEditMode}
                setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
                canEditQuestion={false}
                canRemoveQuestion={canRemoveQuestion}
              />
            ))}

          {/* Pagination */}
          {!!pagination && count > size && (
            <nav className="flex justify-center items-center px-4 my-1 sticky bottom-0 z-20" aria-label="Table navigation">
              {/* <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
                Showing <span className="font-semibold text-gray-900 dark:text-white">{showingText}</span> of{' '}
                <span className="font-semibold text-gray-900 dark:text-white">{count}</span>
              </span> */}
              {count > size && (
                <Pagination
                  theme={StaticData.paginationTheme}
                  currentPage={page}
                  onPageChange={(page) => setPagination((prev) => ({ ...prev, page: page }))}
                  showIcons
                  totalPages={pagesCount}
                  layout="pagination"
                  previousLabel=""
                  nextLabel=""
                />
              )}
            </nav>
          )}
        </Drawer.Body>

        {canRemoveQuestion && (
          <Drawer.Footer>
            <Drawer.Footer.Button
              // disabled={Object.entries(selectedQuestionsID).filter(([_, value]) => value)?.length <= 0}
              onClick={() => {
                setSelectedQuestionsID(selectedQuestionsID);
                onClose();
              }}
              label="Confirm"
              className="!w-full"
            />
          </Drawer.Footer>
        )}
      </Drawer.SingleView>
    </Drawer>
  );
};
