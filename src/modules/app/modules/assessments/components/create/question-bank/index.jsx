// React
import { useContext, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

// Components
import { QuestionItem } from '../../question-item';

// Core
import {
  Card,
  Button,
  Icon,
  useFetchList,
  Select,
  Regex,
  TextInput,
  Textarea,
  ToggleFilter,
  StaticData,
  useValidate,
  useScreenSize,
  SidebarFilterPage,
} from '/src';

// Context
import { AppContext } from '/src/components/provider';

// Flowbite
import { Pagination, Spinner } from 'flowbite-react';

export const QuestionBank = ({
  formData,
  disableButtons,
  selectedQuestionsID,
  setSelectedQuestionsID,
  anyQuestionHasEditMode,
  setAnyQuestionHasEditMode,
  isRandomizeAskAiVisible,
  setRandomizeAskAiVisibilty,
}) => {
  // State
  const [backupList, setBackupList] = useState([]);

  // Hooks
  const { userData } = useContext(AppContext);
  const screen = useScreenSize();
  const { isRequired, minLength, maxLength, validateRegex, validateEstimationTime, isNumber } = useValidate();
  const { type } = useParams();
  const initialFilters = {
    difficulty: {
      label: 'Difficulty',
      enum: 'QuestionDifficulty',
    },
    ...(userData.trackId
      ? {}
      : {
          category: {
            label: 'Category',
            lookup: 'category',
          },
        }),
    subCategory: {
      label: 'Sub Category',
      lookup: 'subcategory',
      parentLookup: { key: 'category', fieldName: 'categoryId', fieldValue: userData.trackId ? userData.trackId : null },
    },
    // topic: {
    //   label: 'Topic',
    //   lookup: 'topic',
    //   parentLookup: { key: 'subCategory', fieldName: 'subcategoryId' },
    // },
    // scope: {
    //   label: 'Scope',
    //   enum: 'Scope',
    // },
  };
  const { ready, loading, setLoading, list, count, search, pagination, filters, setFilters, refresh } = useFetchList('questions/list', {
    search: '',
    pagination: {
      page: 1,
      size: 15,
    },
    filters: initialFilters,
  });

  // Pagination
  const { page, size } = pagination;
  const pagesCount = Math.max(Math.ceil(count / size), 1);
  const showingText = `${count ? page * size - size + 1 : count} - ${page * size > count ? count : page * size}`;
  const isPaginationActive = !!pagination.update;

  const filterFeedData = Object.keys(initialFilters);

  const handleHeight = () => {
    const hideJumbotron = false;
    const isScrollableTabsExists = true;

    if (hideJumbotron) {
      if (isScrollableTabsExists) {
        return {
          table: '2xl:max-h-[calc(100vh-255px)]',
          sidebarFilter: '2xl:max-h-[calc(100vh-255px)]',
        };
      } else
        return {
          table: '',
          sidebarFilter: '',
        };
    } else {
      if (isScrollableTabsExists) {
        return {
          table: '2xl:max-h-[calc(100vh-400px)]',
          sidebarFilter: '2xl:max-h-[calc(100vh-400px)]',
        };
      } else
        return {
          table: '2xl:max-h-[calc(100vh-170px)]',
          sidebarFilter: '2xl:max-h-[calc(100vh-170px)]',
        };
    }
  };

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  useEffect(() => {
    disableButtons.setDisableNextButton(
      Object.entries(selectedQuestionsID)
        .filter(([_, value]) => value)
        .map(([key, _]) => key)?.length < 3
    );
    Object.entries(selectedQuestionsID)
      .filter(([_, value]) => value)
      .map(([key, _]) => key)?.length < 3 && disableButtons.setDisabledMessage('Select at least 3 questions');
  }, [selectedQuestionsID]);

  return (
    <Card className="!p-5 !py-4 space-y-4">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center flex-wrap gap-4">
        <div className="flex flex-col gap-2">
          <h2 className="text-xl font-semibold ">Question Bank</h2>
          <p className="text-[#A2A9BA] font-normal text-sm">
            Select questions for your {type} template (minimum <span className="font-semibold ">3</span> required)
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-4">
          <Button label="Randomize" customIcon="random" tertiary onClick={() => setRandomizeAskAiVisibilty('randomize')} />
          {/* <Button label="Ask AI" customIcon="stars" tertiary onClick={() => setRandomizeAskAiVisibilty('ask-ai')} /> */}
        </div>
      </div>

      {/* Search and filter */}
      <div className="2xl:hidden flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Icon icon="carbon:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search Questions..."
            className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
            value={search.value}
            onInput={(e) => search.update(e.target.value)}
          />
        </div>

        <div>
          <ToggleFilter filters={filters} drawerFilter={false} drawerClearAll={false} resultsFound={count} />
        </div>
      </div>

      <div className="flex justify-between gap-4 pt-2">
        <div className={`hidden 2xl:block w-full max-w-[270px] ${handleHeight()?.sidebarFilter} `}>
          <SidebarFilterPage
            filterData={{
              filterFeedData,
              setFilters,
            }}
          />
        </div>

        {/* Question list */}
        <div className={`w-full space-y-4 overflow-y-auto ${handleHeight()?.table}`}>
          {list?.map((question, index) => (
            <QuestionItem
              key={question?._id}
              index={index}
              questionId={question?._id}
              selectedQuestionsID={selectedQuestionsID}
              setSelectedQuestionsID={setSelectedQuestionsID}
              anyQuestionHasEditMode={anyQuestionHasEditMode}
              setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
              canEditQuestion={true}
              canRemoveQuestion={true}
            />
          ))}
        </div>
      </div>

      {/* Loading */}
      {loading && (
        <div className="absolute z-50 left-0 right-0 bottom-0 top-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80">
          <Spinner size="lg" color="purple" />
        </div>
      )}

      {isPaginationActive && count > size && (
        <nav
          // className="flex flex-row justify-between items-center space-y-0 px-4 pt-1 pb-2 bg-white dark:bg-darkGrayBackground bottom-0 sticky z-20"
          className="flex justify-center items-center px-4 my-1"
        >
          {/* <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
            Showing <span className="font-semibold text-gray-900 dark:text-white">{showingText}</span> of{' '}
            <span className="font-semibold text-gray-900 dark:text-white">{count}</span>
          </span> */}
          {count > size && (
            <Pagination
              theme={StaticData.paginationTheme}
              currentPage={page}
              onPageChange={(page) => pagination.update({ page })}
              showIcons
              totalPages={pagesCount}
              layout={screen.gt.md() ? 'pagination' : 'navigation'}
              previousLabel={<span className="hidden sm:block">Previous</span>}
              nextLabel={<span className="hidden sm:block">Next</span>}
            />
          )}
        </nav>
      )}
    </Card>
  );
};
