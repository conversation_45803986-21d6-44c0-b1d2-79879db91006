// React
import { useRef, useState } from 'react';

// Core
import { Drawer, Form, TextInput, useForm, useValidate, Select, MultiSelect, Regex, Enums, Button, Textarea, Api, useNotify } from '/src';

// Flowbite
import { Checkbox as FlowbiteCheckbox, Label } from 'flowbite-react';

// Components
import { QuestionItem } from '../../question-item';

export const RandomizeAskAiPage = ({ onClose, questionsListData, type, anyQuestionHasEditMode, setAnyQuestionHasEditMode }) => {
  const { list, selectedQuestionsID, setSelectedQuestionsID } = questionsListData || {};

  // State
  const [generatedQuestions, setGeneratedQuestions] = useState([]);
  const [selectedGeneratedQuestions, setSelectedGeneratedQuestions] = useState({});

  // Reference
  const subCategoryRef = useRef(null);

  // Hooks
  const { notify } = useNotify();
  const { form, setFieldValue, setFormValue, resetForm } = useForm({
    numOfQuestions: null,
    category: '',
    subCategory: '',
    questionsDifficulty: {
      1: false,
      2: false,
      3: false,
      4: false,
    },
  });

  const { isRequired, isSelected, isNumber, isValidateMaxAndMinNumber } = useValidate();

  const handleSubmit = async () => {
    if (type === 'randomize') {
      try {
        //TODO:Handle multiple category
        const response = await Api.post('questions/generate', {
          numOfQuestions: form.numOfQuestions,
          category: [form.category],
          subCategory: form.subCategory,
          questionsDifficulty: Object.keys(form.questionsDifficulty)
            .filter((key) => form.questionsDifficulty[key])
            .map(Number),
          exclude: generatedQuestions?.map((question) => question?._id),
        });
        setGeneratedQuestions((prev) => [...prev, ...response.data]);
      } catch (error) {
        notify.error(error?.response?.data?.message);
      }
    } else if (type === 'ask-ai') {
      try {
        //TODO:Handle multiple category
        //TODO:sets a maximum limit for the number of questions.
        const response = await Api.post('ai-interview/generate/questions', {
          numOfQuestions: form.numOfQuestions,
          category: [form.category],
          subCategory: form.subCategory,
          difficulty: Object.keys(form.questionsDifficulty)
            .filter((key) => form.questionsDifficulty[key])
            .map(Number),
        });
        setGeneratedQuestions((prev) => [...prev, ...response.data]);
      } catch (error) {
        notify.error(error?.response?.data?.message);
      }
    } else {
      throw new Error(`Type is not defined: ${type}`);
    }
  };

  return (
    <Drawer onClose={onClose} className="!max-w-[500px]">
      <Drawer.SingleView>
        <Drawer.Header
          headerLabel={type === 'randomize' ? 'Randomize Questions' : 'Generate Questions with AI'}
          description={
            type === 'randomize'
              ? 'Generate a random selection of questions  from question bank based on your criteria'
              : 'Create custom questions using AI based on your specifications'
          }
          className="pb-2"
        />
        <div className="h-full overflow-y-auto">
          <Drawer.Body className="flex flex-col">
            <Form className="space-y-4">
              <TextInput
                label="Question Number"
                name="numOfQuestions"
                type="number"
                placeholder="0"
                value={form.numOfQuestions}
                onChange={setFieldValue('numOfQuestions', Number)}
                validators={[isRequired()]}
              />

              <Select
                label="Category"
                name="category"
                placeholder="Search for category"
                value={form.category}
                onChange={(newCategory) => {
                  subCategoryRef.current?.blur();
                  setFieldValue('category')(newCategory);
                  setFieldValue('subCategory')(null);
                }}
                lookup="category"
                optionValueKey="_id"
                optionLabelKey="name"
                dropIcon
                requiredLabel
              />

              <MultiSelect
                key={form.category}
                label="Subcategory"
                requiredLabel
                name="subCategory"
                placeholder="Search for subcategory"
                value={Array.isArray(form.subCategory) ? form.subCategory : []}
                onChange={setFieldValue('subCategory')}
                disabled={!form.category}
                disabledMessage="Please select category first"
                lookup="subcategory"
                params={{ categoryId: form.category }}
                optionValueKey="_id"
                optionLabelKey="name"
                dropIcon
              />

              <div className="space-y-3">
                <p className="text-inputLabel dark:text-inputDarkLabel text-sm font-medium">
                  Difficulty <span className="text-red-600 dark:text-red-800"> *</span>
                </p>
                <div className="flex justify-between items-center mx-1">
                  {Enums.QuestionDifficulty.map((difficulty) => (
                    <div className="flex gap-2">
                      <FlowbiteCheckbox
                        key={difficulty.value}
                        id={difficulty.value}
                        value={difficulty.value}
                        checked={form.questionsDifficulty[difficulty.value]}
                        className="text-purple-600 focus:ring-purple-500"
                        onChange={(value) =>
                          setFieldValue('questionsDifficulty')({ ...form.questionsDifficulty, [difficulty.value]: value.target.checked })
                        }
                      />
                      <Label htmlFor={difficulty.value} className="text-inputLabel dark:text-inputDarkLabel pb-4">
                        {difficulty.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {type === 'ask-ai' && (
                <Textarea
                  label="Additional Instructions"
                  name="instructions"
                  placeholder=""
                  value={form.instructions}
                  onChange={setFieldValue('instructions')}
                  validators={[isRequired()]}
                  rows="4"
                />
              )}

              <Button
                label={type === 'randomize' ? 'Randomize Questions' : 'Generate Questions'}
                customIcon={type === 'randomize' ? 'random' : 'stars'}
                tertiary
                className="w-full"
                onClick={handleSubmit}
                disabled={
                  !form.numOfQuestions ||
                  form.category?.length === 0 ||
                  form.subCategory === null ||
                  form.subCategory?.length === 0 ||
                  !form.questionsDifficulty
                }
              />

              {generatedQuestions.length > 0 && (
                <>
                  <p className="dark:text-white font-semibold">{type === 'randomize' ? 'Preview' : 'Generated'} Questions</p>
                  {generatedQuestions?.map((questionId, index) => (
                    <QuestionItem
                      key={questionId?._id}
                      index={index}
                      questionId={questionId?._id}
                      selectedQuestionsID={selectedGeneratedQuestions}
                      setSelectedQuestionsID={setSelectedGeneratedQuestions}
                      anyQuestionHasEditMode={anyQuestionHasEditMode}
                      setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
                      canEditQuestion={false}
                      canRemoveQuestion={true}
                    />
                  ))}
                </>
              )}
            </Form>
          </Drawer.Body>
        </div>

        <Drawer.Footer.Button
          label="Add Selected Questions"
          className="!w-full"
          onClick={() => {
            setSelectedQuestionsID((prev) => ({ ...prev, ...selectedGeneratedQuestions }));
            onClose();
          }}
          disabled={Object.keys(selectedGeneratedQuestions)?.filter((key) => selectedGeneratedQuestions[key])?.length <= 0}
        />
        {type === 'randomize' && <Button label="Cancel" className="!w-full" tertiary onClick={onClose} />}
      </Drawer.SingleView>
    </Drawer>
  );
};
