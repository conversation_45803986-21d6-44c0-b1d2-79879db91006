// React
import { useContext, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

// Core
import { Icon, Api, ToggleFilter, CustomIcon, useNotify, StaticData, useScreenSize } from '/src';

// Context
import { AppContext } from '/src/components/provider';

// Components
import { QuestionItem } from '../question-item';

// Flowbite
import { Pagination } from 'flowbite-react';

export const SingleContent = ({ formData, anyQuestionHasEditMode, setAnyQuestionHasEditMode = () => {} }) => {
  const { form, setFieldValue, setFormValue, resetForm } = formData || {};

  // Context
  const { userData } = useContext(AppContext);

  // Hooks
  const { type, id } = useParams();
  const { notify } = useNotify();
  const screen = useScreenSize();

  // State
  const [isEditMode, setIsEditMode] = useState(false);
  const [assessmentData, setAssessmentData] = useState('');
  const [search, setSearch] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const size = 15;
  const count = form?.questionIds?.length;
  const pagesCount = Math.max(Math.ceil(count / size), 1);

  const handleGet = async () => {
    try {
      const response = await Api.get(`quizzes/single/${id}`);
      setAssessmentData(response.data);
    } catch (error) {
      notify.error(error.response?.data?.message);
    }
  };

  useEffect(() => {
    handleGet();
  }, []);

  return (
    <div className="space-y-4">
      <p className="font-medium">Questions Covered ({form?.questionIds?.length})</p>

      {/* Search and filter */}
      {/* <div className="flex flex-col md:flex-row gap-4 mb-6 mt-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Icon icon="carbon:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search Questions..."
            className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
            value={search}
            onInput={(e) => setSearch(e.target.value)}
          />
        </div>
        <ToggleFilter filters={[]} drawerFilter={false} drawerClearAll={false} resultsFound={false} />
      </div> */}

      <div className="space-y-3 select-none">
        {(form?.questionIds || [])?.slice((currentPage - 1) * size, currentPage * size)?.map((questionId, index) => (
          <QuestionItem
            key={questionId}
            index={index}
            questionId={questionId}
            // selectedQuestionsID={selectedQuestionsID}
            // setSelectedQuestionsID={setSelectedQuestionsID}
            anyQuestionHasEditMode={anyQuestionHasEditMode}
            setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
            canEditQuestion={true}
          />
        ))}
      </div>

      {count > size && (
        <nav
          // className="flex flex-row justify-between items-center space-y-0 px-4 pt-1 pb-2 bg-white dark:bg-darkGrayBackground bottom-0 sticky z-20"
          className="flex justify-center items-center px-4 my-1"
        >
          {/* <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
                  Showing <span className="font-semibold text-gray-900 dark:text-white">{showingText}</span> of{' '}
                  <span className="font-semibold text-gray-900 dark:text-white">{count}</span>
                </span> */}
          {count > size && (
            <Pagination
              theme={StaticData.paginationTheme}
              currentPage={currentPage}
              onPageChange={(currentPage) => setCurrentPage(currentPage)}
              showIcons
              totalPages={pagesCount}
              layout={screen.gt.md() ? 'pagination' : 'navigation'}
              previousLabel={<span className="hidden sm:block">Previous</span>}
              nextLabel={<span className="hidden sm:block">Next</span>}
            />
          )}
        </nav>
      )}
    </div>
  );
};
