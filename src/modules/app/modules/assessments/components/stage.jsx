// React
import React from 'react';

// Core
import { Icon } from '/src';

export const Stage = ({ stage, selectedStage }) => {
  return (
    <div className="flex flex-wrap items-center gap-4">
      {stage.map((singleProgress, index, array) => (
        <React.Fragment key={singleProgress.label}>
          <div key={singleProgress.label} className="flex items-center gap-4 dark:text-white">
            <p
              className={`size-10 flex justify-center items-center bg-[#9566F8] rounded-full font-semibold ${
                selectedStage.activeStage >= index ? 'bg-[#9566F8] text-white' : 'bg-[#E5E7EB] text-[#757488]'
              }`}
            >
              {selectedStage.activeStage > index ? <Icon icon="material-symbols:fitbit-check-small-rounded" width={40} /> : index + 1}
            </p>
            <p className="text-[#757488] font-medium">{singleProgress.label}</p>
          </div>
          <p className={`w-8 h-0.5 bg-[#CFD3D8] ${index === array.length - 1 && 'hidden'}`} />
        </React.Fragment>
      ))}
    </div>
  );
};
