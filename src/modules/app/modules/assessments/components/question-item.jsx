// React
import { useContext, useEffect, useRef, useState } from 'react';

// Flowbite

// Context
import { AppContext } from '/src/components/provider';

// Core
import {
  Api,
  CustomIcon,
  useNotify,
  Button,
  useScreenSize,
  TestDifficulty,
  Form,
  useForm,
  Textarea,
  Enums,
  Radio,
  Checkbox,
  StaticData,
  useValidate,
  Icon,
  useConfirmDialog,
} from '/src';

export const QuestionItem = ({
  questionId,
  selectedQuestionsID,
  setSelectedQuestionsID,
  anyQuestionHasEditMode,
  setAnyQuestionHasEditMode,
  canEditQuestion,
  canRemoveQuestion,
}) => {
  // Reference
  const textRef = useRef(null);

  // Context
  const { isViewOnly, setViewOnly } = useContext(AppContext);

  // Hooks
  const { notify } = useNotify();
  const screen = useScreenSize();
  const { isRequired, isNotSpaces } = useValidate();
  const { showConfirm, hideConfirm } = useConfirmDialog();

  let row = {};
  let selectedQuestionIds = [];
  let mainQuestionsListForm = [];

  const { form, setFieldValue, setFormValue, resetForm } = useForm(null);

  // State

  const [isExpanded, setIsExpanded] = useState(false);
  const [isOverflow, setIsOverflow] = useState(false);
  const [isEditMode, setEditMode] = useState(false);
  const [isShowFullAnswers, setShowFullAnswers] = useState(false);
  const [question, setQuestion] = useState(null);
  const [loading, setLoading] = useState(true);

  const fetchQuestionData = async () => {
    try {
      setLoading(true);
      const response = await Api.get(`questions/single/${questionId}`);
      setQuestion(response.data);
    } catch (error) {
      notify.error(error.response.data.message);
    } finally {
      setLoading(false);
    }
  };

  // const handleCheckboxChange = () => {};

  const handleGetQuestionData = async () => {
    try {
      const response = await Api.get(`questions/single/${questionId}`);
      setFormValue(response?.data);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  const toggleExpand = () => {
    handleGetQuestionData();
    setShowFullAnswers((prev) => !prev);
  };

  const handleAddOption = () => {
    if (form.options.length >= 4) return;
    const newOption = {
      id: form.options.length + 1,
      label: '',
    };
    setFieldValue('options')([...form.options, newOption]);
  };

  const handleSelectCheckbox = (value, id) => {
    const numberOfSelectedAnswers = Object.entries(form.multiChoiceAnswer).filter(([key, value]) => value).length;
    if (numberOfSelectedAnswers >= 2 && value) {
      return notify.error('Only two answers are allowed');
    }
    const newObj = { ...form.multiChoiceAnswer, [id]: value };
    setFieldValue('multiChoiceAnswer')(newObj);
  };

  const handleRemoveOption = (id) => {
    // Remove unwanted form options
    const updatedOptions = form.options.filter((option) => option.id !== id);
    setFieldValue('options')(updatedOptions);

    // Set removed form multiChoiceAnswer to false
    const updateMultiChoiceAnswer = { ...form.multiChoiceAnswer, [id]: false };
    setFieldValue('multiChoiceAnswer')(updateMultiChoiceAnswer);
  };

  const ConfirmText = () => (
    <div className="flex flex-col w-full items-center gap-2">
      <svg width="66" height="66" viewBox="0 0 66 66" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="5" y="5" width="56" height="56" rx="28" fill="#F0E7FF" />
        <rect x="5" y="5" width="56" height="56" rx="28" stroke="#F8F4FF" strokeWidth="10" />
        <g clipPath="url(#clip0_8621_5254)">
          <path
            d="M38.8085 26.3846C38.2939 25.4066 37.4884 24.5917 36.4904 24.0006C35.4929 23.4121 34.2888 23.0528 32.982 23.0528C31.3711 23.0488 30.0328 23.4685 29.0227 24.0497C28.0085 24.6288 27.5713 25.3031 27.5713 25.3031C27.4008 25.451 27.3047 25.6662 27.3087 25.8912C27.3133 26.1166 27.4171 26.3282 27.5928 26.4687L28.9964 27.593C29.2825 27.822 29.692 27.8134 29.9681 27.5727C29.9681 27.5727 30.1406 27.2611 30.6809 26.9524C31.2243 26.6458 31.9288 26.3988 32.982 26.3955C33.9006 26.3935 34.7016 26.7363 35.2482 27.2047C35.5198 27.4368 35.7227 27.6967 35.847 27.9344C35.9723 28.1741 36.0181 28.3836 36.0174 28.5428C36.0148 29.0805 35.9103 29.4322 35.7595 29.7319C35.6445 29.9557 35.4943 30.1543 35.301 30.3469C35.0125 30.6353 34.6214 30.9019 34.1825 31.1469C33.7432 31.3948 33.2698 31.613 32.7914 31.8766C32.2454 32.1789 31.6675 32.6132 31.2406 33.265C31.0277 33.5872 30.8613 33.9558 30.7556 34.3447C30.6484 34.7339 30.6001 35.1421 30.6001 35.5584C30.6001 36.0026 30.6001 36.3673 30.6001 36.3673C30.6001 36.786 30.9395 37.1255 31.3583 37.1255H33.1849C33.6036 37.1255 33.9431 36.786 33.9431 36.3673C33.9431 36.3673 33.9431 36.0026 33.9431 35.5584C33.9431 35.398 33.9614 35.2946 33.979 35.2289C34.0091 35.1308 34.0261 35.1062 34.0754 35.0466C34.1258 34.9898 34.2276 34.903 34.4152 34.7992C34.6894 34.6451 35.13 34.4369 35.6289 34.1674C36.3758 33.7589 37.2838 33.2046 38.0497 32.29C38.4306 31.8335 38.7674 31.2838 38.9998 30.6506C39.2342 30.0174 39.3608 29.3056 39.3602 28.5428C39.3595 27.7699 39.1499 27.036 38.8085 26.3846Z"
            fill="#7E3AF2"
          />
          <path
            d="M32.273 38.9297C31.1342 38.9297 30.2109 39.8533 30.2109 40.9917C30.2109 42.1298 31.1343 43.0531 32.273 43.0531C33.4111 43.0531 34.334 42.1298 34.334 40.9917C34.334 39.8533 33.4111 38.9297 32.273 38.9297Z"
            fill="#7E3AF2"
          />
        </g>
        <defs>
          <clipPath id="clip0_8621_5254">
            <rect width="20" height="20" fill="white" transform="translate(23.334 23.0527)" />
          </clipPath>
        </defs>
      </svg>

      <p className="text-xl dark:text-white">Are you sure?</p>
      <p className="items-center text-[#626262] font-light justify-center dark:text-white">
        Once confirmed, these changes will be applied to the main question as well.{' '}
      </p>
    </div>
  );

  const handleMinTwoAnswers = () => {
    let counter = 0;
    Object.entries(form.multiChoiceAnswer).forEach(([key, value]) => {
      value && counter++;
    });
    if (form.type === 2) {
      if (counter === 2) {
        return true;
      }
      return notify.error('Choose two answers');
    }
    return true;
  };

  const handleUpdate = async () => {
    if (!!handleMinTwoAnswers()) {
      if (form.title !== '') {
        showConfirm(ConfirmText(), {
          async onConfirm() {
            try {
              await Api.put(`questions/single/${questionId}`, form);
              notify('Question updated successfully!');
              // Nothing will happen in single view
              fetchQuestionData();
              // if (refresh) {
              //   // Manual selection
              //   refresh();
              // } else if (generatedQuestionsIds) {
              //   // Auto selection
              //   setQuestionDatabase([]);
              //   setSelectedQuestionIds([]);
              // }
              setShowFullAnswers(false);
              setEditMode(false);
              setAnyQuestionHasEditMode((prev) => ({ ...prev, [row?._id]: false }));
              // generatedQuestionsIds?.map((question) => handleGetQuestionGenerateData(question));
              // setQuestionDatabase((prev) => prev.map((question) => (question._id === row?._id ? { ...question, title: form.title } : question)));
            } catch (error) {
              notify.error(error?.response?.data?.message);
            } finally {
              hideConfirm();
            }
          },
          confirmLabel: 'Confirm',
          cancelLabel: 'Cancel',
        });
      } else {
        notify.error(`Content shouldn't be empty`);
      }
    }
  };

  useEffect(() => {
    if (textRef) {
      setIsOverflow(textRef?.current?.scrollHeight > textRef?.current?.offsetHeight);
    }
  }, []);

  useEffect(() => {
    if (questionId) {
      fetchQuestionData();
    }
  }, [questionId]);

  if (loading) {
    return <div className="p-4 border rounded-lg animate-pulse bg-gray-100 dark:bg-gray-700"></div>;
  }

  if (!question) {
    return (
      <div className="p-4 border rounded-lg bg-red-50 dark:bg-red-900/20">
        <p className="text-red-500 dark:text-red-400">Failed to load question data</p>
      </div>
    );
  }

  return (
    <div className={` `}>
      <Form
        className={`p-4 space-y-3 border transition-all border-[#E8E8E8] dark:border-gray-700 rounded-lg ${
          selectedQuestionsID && selectedQuestionsID[question._id] && canRemoveQuestion && 'border-purple-600 bg-[#F9F6FE] dark:bg-gray-900'
        }`}
      >
        <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
          <div className="flex gap-2 w-full">
            {/* Checkbox */}
            {/* {!canRemoveQuestion && !isEditMode && (
              <FlowbiteCheckbox
                value={row._id}
                checked={selectedQuestionIds?.includes(row._id) || mainQuestionsListForm?.questionIds?.includes(row._id)}
                className={`mt-0.5 ${mainQuestionsListForm?.questionIds?.includes(row._id) ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                // onChange={handleCheckboxChange}
                theme={StaticData?.customThemeCheckbox}
              />
            )} */}

            {/* Question text || Edit Question text */}
            {isEditMode ? (
              <div className="w-full">
                <Textarea value={form?.title} onChange={(value) => setFieldValue('title')(value)} />
              </div>
            ) : (
              <>
                {/* {!isViewOnly && canRemoveQuestion && (
                  <Checkbox
                    checked={isChecked}
                    theme={StaticData?.customThemeCheckbox}
                    />
                    )} */}
                {selectedQuestionsID && canRemoveQuestion && (
                  <div className="flex items-start justify-center pt-0.5">
                    <Checkbox
                      theme={StaticData?.customThemeCheckbox}
                      className="cursor-pointer"
                      value={selectedQuestionsID && selectedQuestionsID[question._id]}
                      onClick={() => {
                        selectedQuestionsID && setSelectedQuestionsID((prev) => ({ ...prev, [question._id]: !prev[question._id] }));
                      }}
                      // onChange={() => {
                      //   setIsChecked(!isChecked);
                      //   if (isChecked) {
                      //     mainSetFieldValueForm('questionIds')(mainQuestionsListForm?.questionIds.filter((id) => id !== row._id));
                      //   } else {
                      //     mainSetFieldValueForm('questionIds')([...mainQuestionsListForm?.questionIds, row._id]);
                      //   }
                      // }}
                    />
                  </div>
                )}

                <div className={`flex gap-1 items-start ${canRemoveQuestion ? 'text-base font-normal' : 'text-sm'}`}>
                  <p
                    className={`font-medium text-gray-900 dark:text-white text-[16px] native-break-all-words ${!isExpanded && 'line-clamp-2'}`}
                    ref={textRef}
                  >
                    {question?.title}
                  </p>
                  <div>
                    <p className={`dark:text-white text-[15px] native-break-all-words ${!isExpanded && 'line-clamp-2'}`} ref={textRef}>
                      {row?.title}
                    </p>
                    {isOverflow && (
                      <span className="text-sm text-[#333333C2] font-medium cursor-pointer" onClick={() => setIsExpanded((prev) => !prev)}>
                        {isExpanded ? 'Read less' : 'Read more'}
                      </span>
                    )}
                  </div>
                </div>
              </>
            )}
          </div>

          {/* Buttons handle view */}
          {!isEditMode && (
            <div className="flex justify-between self-end sm:self-start -order-1 sm:order-1 gap-2">
              {!isViewOnly && isShowFullAnswers && canEditQuestion && (
                <Button
                  size="sm"
                  label={!screen.lt.xs() ? 'Edit' : ''}
                  tertiary
                  customIcon="edit"
                  iconWidth={18}
                  onClick={() => {
                    setEditMode(true);
                    setAnyQuestionHasEditMode((prev) => ({ ...prev, [row?._id]: true }));
                  }}
                />
              )}

              <div className="cursor-pointer">
                <CustomIcon definedIcon={isShowFullAnswers ? 'arrowUp' : 'arrowDown'} className="text-[#8D5BF8]" onClick={toggleExpand} />
              </div>
            </div>
          )}
        </div>

        {/* Answers */}
        {isShowFullAnswers && (
          <div className="space-y-2">
            <p className={`font-medium dark:text-white ${canRemoveQuestion ? 'text-sm' : 'text-xs'}`}>Answers:</p>
            <div className="space-y-1">
              {form?.options?.map((option, index) => (
                <div key={option.id} className="w-full space-y-2 flex items-center justify-center ">
                  <div className="flex items-center gap-1">
                    {/* Radio Button for Single Choice */}
                    {form.type === Enums.QuestionType.Singlechoice && (
                      <div className="flex  items-center ">
                        <Radio
                          name={`singleChoiceAnswer${option.id}`}
                          selectionValue={option.id}
                          value={form.singleChoiceAnswer}
                          onChange={setFieldValue('singleChoiceAnswer', Number)}
                          disabled={!isEditMode}
                          className={`${isEditMode ? 'cursor-pointer' : 'cursor-not-allowed'}`}
                          theme={StaticData?.customThemeRadioButton}
                        />
                      </div>
                    )}

                    {/* Checkboxes for Multi Choice */}
                    {form.type === Enums.QuestionType.Multichoice && (
                      <div className="flex items-center">
                        <Checkbox
                          name={`multichoiceAnswer${option.id}`}
                          value={form.multiChoiceAnswer[option.id]}
                          onChange={(e) => handleSelectCheckbox(e, option.id)}
                          disabled={!isEditMode}
                          className={`${isEditMode ? 'cursor-pointer' : 'cursor-not-allowed'}`}
                          theme={StaticData?.customThemeCheckbox}
                        />
                      </div>
                    )}

                    {/* {isEditMode && (
                      <span className="text-nowrap text-xs font-medium text-inputLabel dark:text-inputDarkLabel">
                        {(form.type === 2 ? index < 3 : index < 2) && <span className="text-red-600 dark:text-red-800"> *</span>}
                      </span>
                    )} */}
                  </div>

                  {/* Answer text */}
                  {isEditMode ? (
                    <div className="flex items-center gap-2 flex-grow">
                      <div className="w-full">
                        <Textarea
                          name={`answer${option.id}`}
                          placeholder="Write your answer"
                          className="w-full bg-white"
                          value={option.label}
                          onChange={setFieldValue(`options.${index}.label`)}
                          validators={
                            form.type === 2
                              ? index < 3
                                ? [isRequired(), isNotSpaces()]
                                : [isNotSpaces()]
                              : index < 2
                              ? [isRequired(), isNotSpaces()]
                              : [isNotSpaces()]
                          }
                          requiredLabel={index < 2}
                          maxHeight="150"
                          rows={1}
                        />
                      </div>

                      {/* Show delete button only if there are more than two options */}
                      {isEditMode && (form.type === 2 ? form.options.length > 3 : form.options.length > 2) && index === form.options.length - 1 && (
                        //
                        <Button
                          tertiary
                          size="sm"
                          icon="hugeicons:delete-02"
                          width={22}
                          className="text-[#B83434] cursor-pointer"
                          onClick={() => handleRemoveOption(option.id)}
                        />
                      )}
                    </div>
                  ) : (
                    <div className="w-full px-2 py-1.5 text-sm dark:text-white border border-[#d9d9d9] dark:border-gray-700 rounded-lg">
                      {option.label}
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Add button to add more options */}
            {isEditMode && form?.options?.length < 4 && (
              <div
                className="bg-newQuAnsBg w-full my-2 py-2 rounded-lg dark:bg-newQuAnsDarkBg dark:bg-opacity-80 hover:bg-newQuAnsHoverBg mx-auto cursor-pointer"
                onClick={handleAddOption}
              >
                <button type="button" className="flex items-center justify-center mx-auto">
                  <Icon icon="icon-park-solid:add-one" className="text-primaryPurple text-opacity-60" width="20" />
                  <span className="ml-2 text-newQuAnsText font-medium dark:text-newQuAnsDarkText">New Answer</span>
                </button>
              </div>
            )}
          </div>
        )}

        {/* Question details - Difficulty, subCategory */}
        {!isEditMode && (
          <div className="flex gap-4 justify-between">
            <div className="flex gap-2 mt-2">
              {question?.categoryName && (
                <p className="px-2 py-1 bg-[#F4F4F5] text-[#667085] text-[11px] font-medium rounded-md">{question?.categoryName}</p>
              )}
              {Array.isArray(question?.subCategoryName) ? (
                <div className="flex items-center gap-2">
                  {question?.subCategoryName?.map((subCategoryName) => (
                    <p className="px-2 py-1 text-xs border font-medium rounded-full">{subCategoryName}</p>
                  ))}
                </div>
              ) : (
                <p className="px-2 py-1 text-xs border font-medium rounded-full">{question?.subCategoryName}</p>
              )}
            </div>

            <div>{question?.difficulty && <TestDifficulty difficulty={question?.difficulty} />}</div>
          </div>
        )}

        {/* Buttons handle question answers */}
        {isEditMode && (
          <div className="flex gap-2">
            <Button
              label="Cancel"
              tertiary
              size="sm"
              onClick={() => {
                setEditMode(false);
                setAnyQuestionHasEditMode((prev) => ({ ...prev, [row?._id]: false }));
                setShowFullAnswers(false);
                setFormValue(null);
              }}
            />

            <Button size="sm" label="Save Changes" className="opacity-90" onClick={handleUpdate} />
          </div>
        )}
      </Form>
    </div>
  );
};
