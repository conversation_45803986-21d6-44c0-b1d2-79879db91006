import React, { useEffect, useState } from 'react';
import { use } from 'react';

export const Categories = () => {
  const [activeCategories, setActiveCategories] = useState([]);

  const data = [
    {
      category: 'Academics',
      templatesNumber: 10,
      iconSVG: (
        <svg width="17" height="20" viewBox="0 0 14 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M12.25 0H3.5C2.57174 0 1.6815 0.379285 1.02513 1.05442C0.368749 1.72955 0 2.64522 0 3.6V14.4C0 15.3548 0.368749 16.2705 1.02513 16.9456C1.6815 17.6207 2.57174 18 3.5 18H12.25C12.7141 18 13.1592 17.8104 13.4874 17.4728C13.8156 17.1352 14 16.6774 14 16.2V1.8C14 1.32261 13.8156 0.864773 13.4874 0.527208C13.1592 0.189642 12.7141 0 12.25 0ZM1.75 3.6C1.75 3.12261 1.93437 2.66477 2.26256 2.32721C2.59075 1.98964 3.03587 1.8 3.5 1.8H12.25V10.8H3.5C2.88316 10.8027 2.27849 10.9768 1.75 11.304V3.6ZM3.5 16.2C3.03587 16.2 2.59075 16.0104 2.26256 15.6728C1.93437 15.3352 1.75 14.8774 1.75 14.4C1.75 13.9226 1.93437 13.4648 2.26256 13.1272C2.59075 12.7896 3.03587 12.6 3.5 12.6H12.25V16.2H3.5ZM5.25 5.4H8.75C8.98206 5.4 9.20462 5.30518 9.36872 5.1364C9.53281 4.96761 9.625 4.73869 9.625 4.5C9.625 4.2613 9.53281 4.03239 9.36872 3.8636C9.20462 3.69482 8.98206 3.6 8.75 3.6H5.25C5.01794 3.6 4.79538 3.69482 4.63128 3.8636C4.46719 4.03239 4.375 4.2613 4.375 4.5C4.375 4.73869 4.46719 4.96761 4.63128 5.1364C4.79538 5.30518 5.01794 5.4 5.25 5.4Z"
            fill="#A379FC"
          />
        </svg>
      ),
    },
    {
      category: 'Development',
      templatesNumber: 10,
      iconSVG: (
        <svg width="23" height="20" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M5.71619 12.7491C5.47143 12.7494 5.23508 12.6597 5.05219 12.4971L-0.0078125 7.99906L5.33319 3.25106C5.53227 3.08075 5.79023 2.99537 6.05161 3.01328C6.31298 3.03119 6.55689 3.15096 6.73088 3.34683C6.90487 3.54269 6.99504 3.79902 6.98201 4.06068C6.96898 4.32235 6.85378 4.56844 6.66119 4.74606L3.00219 7.99906L6.38019 11.0011C6.53195 11.1357 6.63916 11.3133 6.68759 11.5104C6.73601 11.7074 6.72336 11.9145 6.65132 12.1042C6.57927 12.2938 6.45123 12.4571 6.28421 12.5723C6.11719 12.6875 5.91908 12.7491 5.71619 12.7491ZM14.6612 12.7471L20.0022 7.99906L14.9422 3.50106C14.7439 3.32495 14.4839 3.23482 14.2191 3.25047C13.9544 3.26613 13.7068 3.38631 13.5307 3.58456C13.3546 3.78281 13.2644 4.04289 13.2801 4.3076C13.2958 4.57231 13.4159 4.81995 13.6142 4.99606L16.9922 7.99906L13.3332 11.2511C13.1348 11.4272 13.0145 11.6749 12.9988 11.9397C12.983 12.2045 13.0731 12.4647 13.2492 12.6631C13.4253 12.8614 13.673 12.9817 13.9378 12.9975C14.2026 13.0132 14.4628 12.9232 14.6612 12.7471ZM9.98319 14.1641L11.9832 2.16406C12.0088 2.03291 12.0078 1.89797 11.9803 1.7672C11.9529 1.63643 11.8995 1.51249 11.8234 1.4027C11.7472 1.29291 11.6498 1.1995 11.5369 1.12798C11.4241 1.05646 11.298 1.00828 11.1662 0.986289C11.0344 0.964301 10.8996 0.968949 10.7696 0.999957C10.6396 1.03097 10.5172 1.08771 10.4095 1.16683C10.3019 1.24595 10.2111 1.34585 10.1427 1.46062C10.0743 1.57539 10.0296 1.70271 10.0112 1.83506L8.01119 13.8351C7.98561 13.9662 7.98658 14.1011 8.01403 14.2319C8.04149 14.3627 8.09487 14.4866 8.17102 14.5964C8.24718 14.7062 8.34456 14.7996 8.45743 14.8711C8.57029 14.9427 8.69634 14.9908 8.82814 15.0128C8.95993 15.0348 9.0948 15.0302 9.22477 14.9992C9.35474 14.9681 9.47718 14.9114 9.58485 14.8323C9.69252 14.7532 9.78325 14.6533 9.85166 14.5385C9.92008 14.4237 9.9648 14.2964 9.98319 14.1641Z"
            fill="#A379FC"
          />
        </svg>
      ),
    },
    {
      category: 'Art & Design',
      templatesNumber: 10,
      iconSVG: (
        <svg width="23" height="22" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M11.3719 6.93942C12.0603 6.93942 12.735 6.59476 13.1255 5.96776C13.7278 5.00251 13.4317 3.72926 12.4673 3.12609C11.5021 2.52476 10.2288 2.81901 9.62568 3.78426C9.02343 4.75042 9.31951 6.02459 10.2848 6.62776C10.623 6.83767 10.9998 6.93942 11.3719 6.93942ZM10.7926 4.51392C10.9933 4.19217 11.4187 4.09226 11.7395 4.29484C11.8953 4.39109 12.0044 4.54326 12.0457 4.72292C12.0869 4.90259 12.0567 5.08592 11.9595 5.24176C11.7597 5.56351 11.3343 5.66251 11.0126 5.46084C10.6908 5.26009 10.5928 4.83476 10.7926 4.51392ZM6.82435 9.69951C7.01501 9.69951 7.20751 9.67751 7.39818 9.63351C8.0536 9.48134 8.61093 9.08259 8.96751 8.51151C9.32409 7.94042 9.43684 7.26484 9.28468 6.60851C9.13251 5.95217 8.73468 5.39576 8.16268 5.03917C7.5916 4.68259 6.91693 4.57076 6.25968 4.72109C5.60426 4.87326 5.04601 5.27201 4.69035 5.84309C4.33376 6.41509 4.22101 7.09067 4.37318 7.74701C4.52535 8.40242 4.9241 8.95976 5.49518 9.31634C5.90035 9.56843 6.35868 9.69951 6.82435 9.69951ZM5.85635 6.57092C6.0186 6.31151 6.27251 6.13092 6.57043 6.06126C6.8711 5.99067 7.17543 6.04201 7.43485 6.20517C7.69426 6.36742 7.87576 6.62042 7.94543 6.91834C8.01418 7.21626 7.96376 7.52334 7.80151 7.78276C7.63926 8.04217 7.38626 8.22276 7.08835 8.29242C6.79043 8.36301 6.48335 8.31167 6.22301 8.14851C5.9636 7.98717 5.78301 7.73417 5.71335 7.43626C5.64368 7.13834 5.6941 6.83126 5.85635 6.57092ZM8.45968 10.6253C7.49351 10.024 6.22026 10.3192 5.61801 11.2835C5.01576 12.2497 5.31185 13.5238 6.2771 14.127C6.61535 14.3369 6.99209 14.4387 7.36426 14.4387C8.05359 14.4387 8.72734 14.094 9.11784 13.467C9.72009 12.5027 9.42401 11.2285 8.45968 10.6253ZM7.95093 12.7401C7.75201 13.0618 7.32668 13.1618 7.00401 12.9592C6.68226 12.7584 6.58418 12.3331 6.78401 12.0123C6.98385 11.6914 7.40918 11.5906 7.73184 11.7932C8.05359 11.9939 8.15168 12.4183 7.95093 12.7401ZM17.355 1.86659C16.6748 1.63559 15.9543 1.78226 15.4227 2.19292C15.254 2.02426 15.0826 1.85742 14.8846 1.70526C14.6848 1.55584 14.4849 1.42567 14.2787 1.29826C12.3528 0.0937577 10.0483 -0.245409 7.7896 0.347674C5.54743 0.935258 3.61876 2.36892 2.35743 4.38651C-0.249571 8.56834 0.856846 13.9858 4.82143 16.4608C5.47134 16.8678 6.18085 17.1786 6.9261 17.3839C7.29001 17.4848 7.66034 17.5343 8.02793 17.5343C9.07293 17.5343 10.095 17.1355 10.8934 16.415C11.0126 16.6029 11.1363 16.7817 11.2665 16.9375C11.7294 17.493 12.1694 17.7744 12.5792 17.7744C12.6543 17.7744 12.7286 17.7643 12.8019 17.746C13.3758 17.5948 13.4408 17.0328 13.4757 16.7313C13.5096 16.4361 13.5481 16.1015 13.7965 15.7238C13.9918 15.4259 14.1934 15.2958 14.4253 15.1463C14.7269 14.952 15.1009 14.71 15.3209 14.1673C15.5583 13.5807 15.518 12.8932 15.2806 12.2983C15.2971 12.2909 15.3136 12.2882 15.3301 12.2808C15.8251 12.0379 16.1954 11.6172 16.3742 11.0965L18.6383 4.48734C19.0068 3.41117 18.4312 2.23601 17.3559 1.86751L17.355 1.86659ZM7.2946 16.0575C6.66668 15.8852 6.09651 15.6358 5.55201 15.2939C2.2291 13.2195 1.31976 8.65267 3.52526 5.11434C4.59868 3.39651 6.23676 2.17551 8.13884 1.67776C10.029 1.18276 11.9494 1.46326 13.5536 2.46517C13.7287 2.57334 13.8964 2.68151 14.0559 2.80159C14.2878 2.97851 14.4813 3.17651 14.6554 3.38276L12.4719 9.75817C12.3225 10.1945 12.3344 10.6446 12.4664 11.0525C11.6084 11.3587 10.8201 12.0544 10.4443 12.862C10.1097 13.5798 10.1115 14.3424 10.293 15.0483C9.54501 15.9677 8.40285 16.3683 7.29643 16.0566L7.2946 16.0575ZM14.0458 13.6503C13.9945 13.7768 13.9313 13.8273 13.6801 13.9895C13.3941 14.1738 13.0036 14.4268 12.6479 14.9667C12.4215 15.3104 12.2959 15.6386 12.2217 15.9255C12.0402 15.6844 11.8495 15.3673 11.7129 14.9933C11.5626 14.5844 11.4379 13.9831 11.69 13.4423C11.9183 12.9528 12.4472 12.4917 12.9761 12.3212C13.0668 12.2918 13.2034 12.2579 13.3437 12.2579C13.4454 12.2579 13.5499 12.2763 13.6407 12.3267C13.9991 12.5274 14.2154 13.2333 14.0458 13.6503ZM17.3367 4.04092L15.0725 10.6501C15.0129 10.8233 14.8892 10.9645 14.7251 11.0452C14.5601 11.1249 14.3731 11.1378 14.1998 11.0773C13.8414 10.9544 13.6498 10.563 13.7727 10.2046L16.0368 3.59542C16.134 3.31034 16.4017 3.13067 16.6877 3.13067C16.761 3.13067 16.8362 3.14259 16.9095 3.16826C17.2688 3.29109 17.4595 3.68342 17.3367 4.04092Z"
            fill="#A379FC"
          />
        </svg>
      ),
    },
    {
      category: 'Business',
      templatesNumber: 10,
      iconSVG: (
        <svg width="23" height="22" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M11.3719 6.93942C12.0603 6.93942 12.735 6.59476 13.1255 5.96776C13.7278 5.00251 13.4317 3.72926 12.4673 3.12609C11.5021 2.52476 10.2288 2.81901 9.62568 3.78426C9.02343 4.75042 9.31951 6.02459 10.2848 6.62776C10.623 6.83767 10.9998 6.93942 11.3719 6.93942ZM10.7926 4.51392C10.9933 4.19217 11.4187 4.09226 11.7395 4.29484C11.8953 4.39109 12.0044 4.54326 12.0457 4.72292C12.0869 4.90259 12.0567 5.08592 11.9595 5.24176C11.7597 5.56351 11.3343 5.66251 11.0126 5.46084C10.6908 5.26009 10.5928 4.83476 10.7926 4.51392ZM6.82435 9.69951C7.01501 9.69951 7.20751 9.67751 7.39818 9.63351C8.0536 9.48134 8.61093 9.08259 8.96751 8.51151C9.32409 7.94042 9.43684 7.26484 9.28468 6.60851C9.13251 5.95217 8.73468 5.39576 8.16268 5.03917C7.5916 4.68259 6.91693 4.57076 6.25968 4.72109C5.60426 4.87326 5.04601 5.27201 4.69035 5.84309C4.33376 6.41509 4.22101 7.09067 4.37318 7.74701C4.52535 8.40242 4.9241 8.95976 5.49518 9.31634C5.90035 9.56843 6.35868 9.69951 6.82435 9.69951ZM5.85635 6.57092C6.0186 6.31151 6.27251 6.13092 6.57043 6.06126C6.8711 5.99067 7.17543 6.04201 7.43485 6.20517C7.69426 6.36742 7.87576 6.62042 7.94543 6.91834C8.01418 7.21626 7.96376 7.52334 7.80151 7.78276C7.63926 8.04217 7.38626 8.22276 7.08835 8.29242C6.79043 8.36301 6.48335 8.31167 6.22301 8.14851C5.9636 7.98717 5.78301 7.73417 5.71335 7.43626C5.64368 7.13834 5.6941 6.83126 5.85635 6.57092ZM8.45968 10.6253C7.49351 10.024 6.22026 10.3192 5.61801 11.2835C5.01576 12.2497 5.31185 13.5238 6.2771 14.127C6.61535 14.3369 6.99209 14.4387 7.36426 14.4387C8.05359 14.4387 8.72734 14.094 9.11784 13.467C9.72009 12.5027 9.42401 11.2285 8.45968 10.6253ZM7.95093 12.7401C7.75201 13.0618 7.32668 13.1618 7.00401 12.9592C6.68226 12.7584 6.58418 12.3331 6.78401 12.0123C6.98385 11.6914 7.40918 11.5906 7.73184 11.7932C8.05359 11.9939 8.15168 12.4183 7.95093 12.7401ZM17.355 1.86659C16.6748 1.63559 15.9543 1.78226 15.4227 2.19292C15.254 2.02426 15.0826 1.85742 14.8846 1.70526C14.6848 1.55584 14.4849 1.42567 14.2787 1.29826C12.3528 0.0937577 10.0483 -0.245409 7.7896 0.347674C5.54743 0.935258 3.61876 2.36892 2.35743 4.38651C-0.249571 8.56834 0.856846 13.9858 4.82143 16.4608C5.47134 16.8678 6.18085 17.1786 6.9261 17.3839C7.29001 17.4848 7.66034 17.5343 8.02793 17.5343C9.07293 17.5343 10.095 17.1355 10.8934 16.415C11.0126 16.6029 11.1363 16.7817 11.2665 16.9375C11.7294 17.493 12.1694 17.7744 12.5792 17.7744C12.6543 17.7744 12.7286 17.7643 12.8019 17.746C13.3758 17.5948 13.4408 17.0328 13.4757 16.7313C13.5096 16.4361 13.5481 16.1015 13.7965 15.7238C13.9918 15.4259 14.1934 15.2958 14.4253 15.1463C14.7269 14.952 15.1009 14.71 15.3209 14.1673C15.5583 13.5807 15.518 12.8932 15.2806 12.2983C15.2971 12.2909 15.3136 12.2882 15.3301 12.2808C15.8251 12.0379 16.1954 11.6172 16.3742 11.0965L18.6383 4.48734C19.0068 3.41117 18.4312 2.23601 17.3559 1.86751L17.355 1.86659ZM7.2946 16.0575C6.66668 15.8852 6.09651 15.6358 5.55201 15.2939C2.2291 13.2195 1.31976 8.65267 3.52526 5.11434C4.59868 3.39651 6.23676 2.17551 8.13884 1.67776C10.029 1.18276 11.9494 1.46326 13.5536 2.46517C13.7287 2.57334 13.8964 2.68151 14.0559 2.80159C14.2878 2.97851 14.4813 3.17651 14.6554 3.38276L12.4719 9.75817C12.3225 10.1945 12.3344 10.6446 12.4664 11.0525C11.6084 11.3587 10.8201 12.0544 10.4443 12.862C10.1097 13.5798 10.1115 14.3424 10.293 15.0483C9.54501 15.9677 8.40285 16.3683 7.29643 16.0566L7.2946 16.0575ZM14.0458 13.6503C13.9945 13.7768 13.9313 13.8273 13.6801 13.9895C13.3941 14.1738 13.0036 14.4268 12.6479 14.9667C12.4215 15.3104 12.2959 15.6386 12.2217 15.9255C12.0402 15.6844 11.8495 15.3673 11.7129 14.9933C11.5626 14.5844 11.4379 13.9831 11.69 13.4423C11.9183 12.9528 12.4472 12.4917 12.9761 12.3212C13.0668 12.2918 13.2034 12.2579 13.3437 12.2579C13.4454 12.2579 13.5499 12.2763 13.6407 12.3267C13.9991 12.5274 14.2154 13.2333 14.0458 13.6503ZM17.3367 4.04092L15.0725 10.6501C15.0129 10.8233 14.8892 10.9645 14.7251 11.0452C14.5601 11.1249 14.3731 11.1378 14.1998 11.0773C13.8414 10.9544 13.6498 10.563 13.7727 10.2046L16.0368 3.59542C16.134 3.31034 16.4017 3.13067 16.6877 3.13067C16.761 3.13067 16.8362 3.14259 16.9095 3.16826C17.2688 3.29109 17.4595 3.68342 17.3367 4.04092Z"
            fill="#A379FC"
          />
        </svg>
      ),
    },
    {
      category: 'Soft Skills',
      templatesNumber: 10,
      iconSVG: (
        <svg width="23" height="22" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M11.3719 6.93942C12.0603 6.93942 12.735 6.59476 13.1255 5.96776C13.7278 5.00251 13.4317 3.72926 12.4673 3.12609C11.5021 2.52476 10.2288 2.81901 9.62568 3.78426C9.02343 4.75042 9.31951 6.02459 10.2848 6.62776C10.623 6.83767 10.9998 6.93942 11.3719 6.93942ZM10.7926 4.51392C10.9933 4.19217 11.4187 4.09226 11.7395 4.29484C11.8953 4.39109 12.0044 4.54326 12.0457 4.72292C12.0869 4.90259 12.0567 5.08592 11.9595 5.24176C11.7597 5.56351 11.3343 5.66251 11.0126 5.46084C10.6908 5.26009 10.5928 4.83476 10.7926 4.51392ZM6.82435 9.69951C7.01501 9.69951 7.20751 9.67751 7.39818 9.63351C8.0536 9.48134 8.61093 9.08259 8.96751 8.51151C9.32409 7.94042 9.43684 7.26484 9.28468 6.60851C9.13251 5.95217 8.73468 5.39576 8.16268 5.03917C7.5916 4.68259 6.91693 4.57076 6.25968 4.72109C5.60426 4.87326 5.04601 5.27201 4.69035 5.84309C4.33376 6.41509 4.22101 7.09067 4.37318 7.74701C4.52535 8.40242 4.9241 8.95976 5.49518 9.31634C5.90035 9.56843 6.35868 9.69951 6.82435 9.69951ZM5.85635 6.57092C6.0186 6.31151 6.27251 6.13092 6.57043 6.06126C6.8711 5.99067 7.17543 6.04201 7.43485 6.20517C7.69426 6.36742 7.87576 6.62042 7.94543 6.91834C8.01418 7.21626 7.96376 7.52334 7.80151 7.78276C7.63926 8.04217 7.38626 8.22276 7.08835 8.29242C6.79043 8.36301 6.48335 8.31167 6.22301 8.14851C5.9636 7.98717 5.78301 7.73417 5.71335 7.43626C5.64368 7.13834 5.6941 6.83126 5.85635 6.57092ZM8.45968 10.6253C7.49351 10.024 6.22026 10.3192 5.61801 11.2835C5.01576 12.2497 5.31185 13.5238 6.2771 14.127C6.61535 14.3369 6.99209 14.4387 7.36426 14.4387C8.05359 14.4387 8.72734 14.094 9.11784 13.467C9.72009 12.5027 9.42401 11.2285 8.45968 10.6253ZM7.95093 12.7401C7.75201 13.0618 7.32668 13.1618 7.00401 12.9592C6.68226 12.7584 6.58418 12.3331 6.78401 12.0123C6.98385 11.6914 7.40918 11.5906 7.73184 11.7932C8.05359 11.9939 8.15168 12.4183 7.95093 12.7401ZM17.355 1.86659C16.6748 1.63559 15.9543 1.78226 15.4227 2.19292C15.254 2.02426 15.0826 1.85742 14.8846 1.70526C14.6848 1.55584 14.4849 1.42567 14.2787 1.29826C12.3528 0.0937577 10.0483 -0.245409 7.7896 0.347674C5.54743 0.935258 3.61876 2.36892 2.35743 4.38651C-0.249571 8.56834 0.856846 13.9858 4.82143 16.4608C5.47134 16.8678 6.18085 17.1786 6.9261 17.3839C7.29001 17.4848 7.66034 17.5343 8.02793 17.5343C9.07293 17.5343 10.095 17.1355 10.8934 16.415C11.0126 16.6029 11.1363 16.7817 11.2665 16.9375C11.7294 17.493 12.1694 17.7744 12.5792 17.7744C12.6543 17.7744 12.7286 17.7643 12.8019 17.746C13.3758 17.5948 13.4408 17.0328 13.4757 16.7313C13.5096 16.4361 13.5481 16.1015 13.7965 15.7238C13.9918 15.4259 14.1934 15.2958 14.4253 15.1463C14.7269 14.952 15.1009 14.71 15.3209 14.1673C15.5583 13.5807 15.518 12.8932 15.2806 12.2983C15.2971 12.2909 15.3136 12.2882 15.3301 12.2808C15.8251 12.0379 16.1954 11.6172 16.3742 11.0965L18.6383 4.48734C19.0068 3.41117 18.4312 2.23601 17.3559 1.86751L17.355 1.86659ZM7.2946 16.0575C6.66668 15.8852 6.09651 15.6358 5.55201 15.2939C2.2291 13.2195 1.31976 8.65267 3.52526 5.11434C4.59868 3.39651 6.23676 2.17551 8.13884 1.67776C10.029 1.18276 11.9494 1.46326 13.5536 2.46517C13.7287 2.57334 13.8964 2.68151 14.0559 2.80159C14.2878 2.97851 14.4813 3.17651 14.6554 3.38276L12.4719 9.75817C12.3225 10.1945 12.3344 10.6446 12.4664 11.0525C11.6084 11.3587 10.8201 12.0544 10.4443 12.862C10.1097 13.5798 10.1115 14.3424 10.293 15.0483C9.54501 15.9677 8.40285 16.3683 7.29643 16.0566L7.2946 16.0575ZM14.0458 13.6503C13.9945 13.7768 13.9313 13.8273 13.6801 13.9895C13.3941 14.1738 13.0036 14.4268 12.6479 14.9667C12.4215 15.3104 12.2959 15.6386 12.2217 15.9255C12.0402 15.6844 11.8495 15.3673 11.7129 14.9933C11.5626 14.5844 11.4379 13.9831 11.69 13.4423C11.9183 12.9528 12.4472 12.4917 12.9761 12.3212C13.0668 12.2918 13.2034 12.2579 13.3437 12.2579C13.4454 12.2579 13.5499 12.2763 13.6407 12.3267C13.9991 12.5274 14.2154 13.2333 14.0458 13.6503ZM17.3367 4.04092L15.0725 10.6501C15.0129 10.8233 14.8892 10.9645 14.7251 11.0452C14.5601 11.1249 14.3731 11.1378 14.1998 11.0773C13.8414 10.9544 13.6498 10.563 13.7727 10.2046L16.0368 3.59542C16.134 3.31034 16.4017 3.13067 16.6877 3.13067C16.761 3.13067 16.8362 3.14259 16.9095 3.16826C17.2688 3.29109 17.4595 3.68342 17.3367 4.04092Z"
            fill="#A379FC"
          />
        </svg>
      ),
    },
  ];

  const handleCategoryClick = (index) => {
    if (activeCategories.includes(index)) {
      const newActiveCategories = [...activeCategories];
      newActiveCategories.splice(newActiveCategories.indexOf(index), 1);
      setActiveCategories(newActiveCategories);
    } else {
      setActiveCategories([...activeCategories, index]);
    }
  };

  return (
    <div>
      <div className="grid grid-cols-5 gap-5">
        {data.map((item, index) => (
          <div
            onClick={() => handleCategoryClick(index)}
            className={`relative flex flex-col items-center border ${
              activeCategories.includes(index) ? 'border-[#702DFF]' : 'border-[#E2E8F0]'
            } rounded-md p-5 justify-center cursor-pointer`}
          >
            <div className="bg-[#EFEAFC] rounded-full p-4 w-fit">{item.iconSVG}</div>
            <p className={`font-medium text-xl mt-2 dark:text-white ${activeCategories.includes(index) && '!text-[#8D5BF8]'}`}>{item.category}</p>
            <p className="text-[#6476A5] dark:text-[#a3aec9] text-sm">
              {item.templatesNumber} template{item.templatesNumber > 1 && 's'}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};
