// React
import { useContext, useEffect, useState } from 'react';
import { Navigate, useNavigate, useParams } from 'react-router-dom';

// Flowbite
import { Datepicker } from 'flowbite-react';

// Context
import { AppContext } from '/src/components/provider';

// Core
import { useFetchList, TestDifficulty, TestSeniorityLevel, AvarageScore, useScreenSize, Table, FormatDate } from '/src';

export const InterviewTab = () => {
  // User Data
  const { userData } = useContext(AppContext);

  // Permissions
  const isSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));
  const isAdmin = userData?.roles.some((role) => ['admin'].includes(role));
  const isContentCreator = userData?.roles.some((role) => ['content-creator'].includes(role));
  const isHr = userData?.roles.some((role) => ['hr'].includes(role));

  // State
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);

  // Hooks
  const navigate = useNavigate();
  const screen = useScreenSize();
  const { type } = useParams();
  const initialFilters = {
    ...(userData.trackId
      ? {}
      : {
          category: {
            label: 'Category',
            lookup: 'category',
          },
        }),
    // subCategory: {
    //   label: 'Sub Category',
    //   lookup: 'subcategory',
    //   parentLookup: { key: 'category', fieldName: 'categoryId', fieldValue: null },
    // },
    seniortyLevel: {
      label: 'Seniorty Level',
      enum: 'QuizDifficulty',
    },
    difficulty: {
      label: 'Difficulty',
      enum: 'QuestionDifficulty',
    },
  };
  const { ready, loading, setLoading, list, count, filters, setFilters, search, pagination, refresh, handleDates } = useFetchList(
    'ai-interview/template/list',
    {
      pagination: {
        page: 1,
        size: 20,
      },
      filters: initialFilters,
    }
  );

  const filterFeedData = Object.keys(initialFilters);

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);
  return (
    <>
      <Table
        ready={ready}
        loading={loading}
        title="Interview Templates"
        searchPlaceholder="Search for template name..."
        count={count}
        search={search}
        filters={filters}
        setFilters={setFilters}
        // filterFeedData={filterFeedData}
        // drawerFilter={{
        //   filterCountNumber: filterCountNumber,
        //   isShowDrawerFilter: isShowDrawerFilter,
        //   setShowDrawerFilter: setShowDrawerFilter,
        // }}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        slots={{
          name: (_, row) => {
            return (
              <div
                onClick={() => (isSuperAdmin || isAdmin || isContentCreator) && navigate(`/app/assessment-templates/view/${type}/${row?._id}`)}
                className={`capitalize font-semibold truncate dark:text-white text-black ${
                  (isSuperAdmin || isAdmin || isContentCreator) && 'cursor-pointer'
                }`}
              >
                {row?.title}
              </div>
            );
          },
          // description: (_, row) => {
          //   return <div className="capitalize text-sm text-[#757488] truncate">{row?.description}</div>;
          // },
          category: (_, row) => {
            return row?.categoryName?.length > 0 ? (
              <div className="flex items-center gap-2">
                {row?.categoryName?.slice(0, 2)?.map((category) => (
                  <p key={category} className="px-2 py-1 bg-[#F4F4F5] text-[#667085] text-[11px] font-medium rounded-md truncate">
                    {category}
                  </p>
                ))}
                {row?.categoryName?.length > 2 && <p className="border rounded-full p-1 px-3 text-xs">+{row?.categoryName?.length - 2}</p>}
              </div>
            ) : (
              '—'
            );
          },
          // subCategory: (_, row) => {
          //   return row?.subCategoryName?.length > 0 ? (
          //     <div className="flex items-center gap-2">
          //       {row?.subCategoryName?.slice(0, 1)?.map((category) => (
          //         <>
          //           <p key={category} className="border rounded-full p-1 px-3 text-sm font-semibold truncate">
          //             {category}
          //           </p>
          //           {row?.subCategoryName?.length > 1 && (
          //             <p key={category} className="border rounded-full p-1 px-3 text-sm font-semibold">
          //               +{row?.subCategoryName?.length - 1}
          //             </p>
          //           )}
          //         </>
          //       ))}
          //     </div>
          //   ) : (
          //     '—'
          //   );
          // },
          difficulty: (_, row) => {
            return (
              <div className="w-fit">
                <TestDifficulty difficulty={row?.difficulty} />
              </div>
            );
          },
          seniortyLevel: (_, row) => {
            return (
              <div className="w-fit">
                <TestSeniorityLevel seniorityLevel={row?.seniorityLevel} />
              </div>
            );
          },
          numberOfQuestion: (_, row) => {
            return (
              <div className="flex items-center gap-1 text-sm text-[#56555C]">
                <span className="font-medium">{row?.numOfQuestions} </span>
                <span className="font-normal">{row?.numOfQuestions > 1 ? 'questions' : 'question'}</span>
              </div>
            );
          },
          duration: (_, row) => {
            return (
              <div className="text-sm text-[#535862] truncate">
                <span className="font-medium mr-1">{row?.duration}</span>
                <span className="font-normal">min</span>
              </div>
            );
          },
        }}
        columns={[
          {
            key: 'name',
            label: 'Name',
            primary: true,
            width: '22%',
          },
          // {
          //   key: 'description',
          //   label: 'Description',
          //   primary: true,
          //   width: '23%',
          // },
          {
            key: 'category',
            label: 'Category',
            primary: true,
            width: '20%',
          },
          // {
          //   key: 'subCategory',
          //   label: 'Sub Category',
          //   primary: true,
          //   width: '20%',
          // },
          {
            key: 'seniortyLevel',
            label: 'Seniorty Level',
            // primary: true,
            width: '15%',
          },
          {
            key: 'difficulty',
            label: 'Difficulty',
            // primary: true,
            width: '15%',
          },
          {
            key: 'numberOfQuestion',
            label: 'No.Question',
            // primary: true,
            width: '13%',
          },
          {
            key: 'duration',
            label: 'Duration',
            // primary: true,
            width: '10%',
          },
          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_, row) {
              return [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  path: `/app/assessment-templates/view/${type}/${row?._id}`,
                },
                ...(isSuperAdmin || isAdmin || isContentCreator
                  ? [
                      {
                        label: 'Update',
                        customIcon: 'edit',
                        iconWidth: '22',
                        iconHeight: '22',
                        color: 'text-black dark:text-white',
                        path: `/app/assessment-templates/edit/${type}/${row?._id}`,
                      },
                    ]
                  : []),
                // {
                //   label: 'Assign',
                //   customIcon: 'assign',
                //   iconWidth: '22',
                //   iconHeight: '22',
                //   color: 'text-black dark:text-white',
                //   // onClick: () => {},
                //   dropDown: [
                //     {
                //       label: 'Interview',
                //       color: 'text-black dark:text-white',
                //       customIcon: 'interview',
                //       element: (
                //         <span
                //           className={`w-24 text-xs px-2 py-1 rounded-full shadow-md font-bold tracking-wide bg-magic-gradient bg-[length:200%]  text-white overflow-hidden`}
                //         >
                //           AI Magic ✨
                //         </span>
                //       ),
                //       onClick: () => {
                //         if (isSuperAdmin || userData?.features?.assignInterview > 0) {
                //           setAssignInterviewTestVisible(true);
                //           setApplicantDetails(row);
                //         } else {
                //           setNeedSubscription(true);
                //         }
                //       },
                //     },
                //   ],
                // },
              ];
            },
          },
        ]}
        // multiSelectedRow={{
        //   selectedIds: selectedIds,
        //   setSelectedIds: setSelectedIds,
        //   handleArchiveSelectedIds: handleArchiveSelectedIds,
        // }}
        noDataFound={{
          customIcon: 'applicant',
          message: 'No assessment created yet',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        // addButtonLabel=""
        // onClickAdd={() => {}}
        // actions={[]}
        hideJumbotron
        isScrollableTabsExists
      />
    </>
  );
};
