// React
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

// Core
import { CustomIcon, useLookups, Enums, Icon, Api, useNotify, useScreenSize, StaticData } from '/src';

// Flowbite
import { Pagination } from 'flowbite-react';

// Date format
import { format, isValid } from 'date-fns';

export const ReviewAssignAssessment = ({ formData }) => {
  // Form
  const { form, setFieldValue, setFormValue, resetForm } = formData || {};

  // State
  const [applicantsData, setApplicantsData] = useState({});
  const [currentPage, setCurrentPage] = useState(1);

  // Hooks
  const { notify } = useNotify();
  const { type } = useParams();
  const screen = useScreenSize();

  // Pagination settings
  const itemsPerPage = 4;

  // Dynamic blocks cards based on form data
  const getBlocksCards = () => {
    const cards = [];

    // Experience Level
    if (form.seniorityLevel) {
      const seniorityLabel = Enums.QuizDifficulty.find((quiz) => quiz.value === form.seniorityLevel)?.label || 'Not specified';
      cards.push({ header: 'Experience Level', subHeader: seniorityLabel, icon: 'manInSuit' });
    }

    // Questions
    if (form.numberOfQuestions) {
      cards.push({ header: 'Questions', subHeader: form.numberOfQuestions, icon: 'questionInBorder' });
    }

    // Duration
    if (form.estimationTime) {
      cards.push({ header: 'Duration', subHeader: `${form.estimationTime} mins`, icon: 'clockThree' });
    }

    // Difficulty
    if (form.seniorityLevel) {
      const difficultyLabel = Enums.QuestionDifficulty.find((quiz) => quiz.value === form.seniorityLevel)?.label || 'Not specified';
      cards.push({ header: 'Difficulty', subHeader: difficultyLabel, icon: 'charts' });
    }

    return cards;
  };

  const formatDate = (customDate) => {
    const date = new Date(customDate || Date.now());
    if (!isValid(date)) {
      return 'Invalid date';
    }
    return format(date, "MMMM dd, yyyy, 'at' hh:mm a");
  };

  const handleGetApplicant = async (id) => {
    try {
      const response = await Api.get(`applicants/single/${id}`);
      setApplicantsData((prev) => ({ ...prev, [id]: response.data }));
    } catch (error) {
      notify.error(error.response?.data?.message || 'Failed to fetch applicant data');
    }
  };

  const fetchApplicants = async () => {
    try {
      if (form.applicantId) {
        Object.keys(form.applicantId)
          .filter((key) => form.applicantId[key])
          .forEach((id) => handleGetApplicant(id));
      }
    } catch (error) {
      notify.error('Failed to fetch applicants');
    }
  };

  useEffect(() => {
    fetchApplicants();
  }, []);

  // Get selected applicants count
  const selectedApplicantsCount = form.applicantId ? Object.keys(form.applicantId).filter((key) => form.applicantId[key]).length : 0;

  // Get paginated applicants
  const getPaginatedApplicants = () => {
    const applicantsList = Object.values(applicantsData);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return applicantsList.slice(startIndex, endIndex);
  };

  // Calculate total pages
  const totalPages = Math.ceil(Object.keys(applicantsData).length / itemsPerPage);

  // Use the paginated applicants in the render
  const paginatedApplicants = getPaginatedApplicants();

  return (
    <div className="p-4 space-y-6 border">
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <p className="text-2xl font-semibold capitalize">
            {form.title} {type}
          </p>

          {type === 'interview' && form.type === 2 && (
            <button className="flex flex-wrap items-center gap-2 px-6 py-2 border border-[#E7E7E7] rounded-xl">
              <CustomIcon definedIcon="interActiveAiInterviewStars" />
              <span className="text-[#5A399E] text-sm font-semibold">Interactive AI Interview</span>
            </button>
          )}
        </div>

        {/* {form.categoryName?.length > 0 && (
          <p className="text-[#9CA3AF]">
            Comprehensive assessment focusing on
            <span className="capitalize"> {Array.isArray(form.categoryName) ? form.categoryName?.join(', ') : form.categoryName}</span>
          </p>
        )} */}

        {form?.categoryName && (
          <div className="flex items-center gap-2">
            <p className="text-[#535862BF] dark:text-white">Categories covered:</p>
            <div className="flex flex-wrap items-center gap-2">
              {form?.categoryName?.map((category) => (
                <p className="px-2 py-1 bg-[#F4F4F5] text-[#667085] text-[11px] font-medium rounded-md">{category}</p>
              ))}
            </div>
          </div>
        )}

        {form?.subCategoryName && (
          <div className="flex items-center gap-2">
            <p className="text-[#535862BF] dark:text-white">Subcategories covered:</p>
            <div className="flex flex-wrap items-center gap-2">
              {form?.subCategoryName?.map((subCategory) => (
                <p className="px-2 py-1 text-xs border font-medium rounded-full">{subCategory}</p>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* {form.subCategoryName?.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {form.subCategoryName?.map((singleSubCategoryName) => (
            <div key={singleSubCategoryName} className="min-w-16 px-3 py-1 bg-[#F3F4F6] text-[13px] font-medium text-center rounded-xl capitalize">
              {singleSubCategoryName}
            </div>
          ))}
        </div>
      )} */}

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
        {getBlocksCards().map((block) => (
          <div key={block.header} className="flex items-center justify-between p-3 border rounded-lg">
            <div>
              <p className="text-sm text-[#535862]">{block.header}</p>
              <p className="text-lg font-semibold">{block.subHeader || '—'}</p>
            </div>
            <CustomIcon definedIcon={block.icon} />
          </div>
        ))}
      </div>

      <div className="p-4 space-y-3 bg-[#F9FAFB] border border-[#EFF0F3] rounded-lg">
        <p className="text-[#6B7280] font-medium">
          <span className="capitalize">{type}</span> is available from:
        </p>
        <p className="flex items-center flex-wrap gap-2 text-[#3C3D3E] dark:text-white text-[15px] font-medium">
          <span>{formatDate(form.startDate)}</span>
          <Icon icon="line-md:arrow-right" width={16} />
          <span>{formatDate(form.dueDate)}</span>
        </p>
      </div>

      {type === 'interview' && form.avatarName && (
        <>
          <hr />
          <p className="font-semibold">Interviewer</p>

          <div className="flex flex-wrap justify-between items-center gap-2 px-6 py-4 bg-[#F3F4F6] rounded-xl">
            <div className="flex items-center gap-3">
              {Enums.AiAvatarModel.find((model) => model.value === form.avatarName) ? (
                <img
                  className="size-[75px] rounded-full"
                  src={`/images/models/${Enums.AiAvatarModel.find((model) => model.value === form.avatarName)?.iconPath}`}
                  alt="Avatar"
                />
              ) : (
                <div className="size-[75px] rounded-full bg-gray-200 flex items-center justify-center">
                  <Icon icon="mdi:account" width={40} />
                </div>
              )}
              <div>
                <p className="text-xl font-semibold text-[#181D27] capitalize">{form.avatarName}</p>
                <p className="text-[#535862] text-sm">{form.avatarDescription}</p>
              </div>
            </div>

            {form.avatarLang && (
              <div className="flex items-center gap-2">
                <Icon
                  icon={Enums.InterviewLanguage.find((lang) => form.avatarLang === lang.value)?.icon || 'twemoji:flag-for-flag-united-states'}
                  width={40}
                />
                <p className="font-semibold capitalize">{form.avatarLang}</p>
              </div>
            )}
          </div>
        </>
      )}

      {selectedApplicantsCount > 0 && (
        <div>
          <hr />
          <p className="font-semibold mt-4">Assigned Applicants ({selectedApplicantsCount})</p>

          <div className="my-4 space-y-4">
            {paginatedApplicants?.map((singleApplicant, index, array) => (
              <React.Fragment key={singleApplicant._id}>
                <div className="flex gap-4 mx-6">
                  <div className="size-11 flex justify-center items-center bg-[#EDE9FE] text-[#8D5BF8] font-semibold border border-[#E1E4E8] rounded-full">
                    {singleApplicant?.name
                      ?.split(' ')
                      .map((word) => word[0])
                      .join('')
                      .toUpperCase()}
                  </div>
                  <div>
                    <p className="text-[#181D27] font-medium">{singleApplicant.name}</p>
                    <p className="text-[#535862] text-[13px]">{singleApplicant.email}</p>
                  </div>
                </div>
                {index < array.length - 1 && <hr />}
              </React.Fragment>
            ))}
          </div>
        </div>
      )}

      {/* Pagination */}
      <div className="flex justify-center items-center px-4 my-1">
        <Pagination
          theme={StaticData.paginationTheme}
          currentPage={currentPage}
          onPageChange={(page) => setCurrentPage(page)}
          showIcons
          totalPages={totalPages}
          layout={screen.gt.md() ? 'pagination' : 'navigation'}
          previousLabel={<span className="hidden sm:block">Previous</span>}
          nextLabel={<span className="hidden sm:block">Next</span>}
        />
      </div>
    </div>
  );
};
