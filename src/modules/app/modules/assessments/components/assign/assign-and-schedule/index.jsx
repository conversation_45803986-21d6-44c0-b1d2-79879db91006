// React
import { useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

// Rsuite
import { DatePicker, DateRangePicker } from 'rsuite';

// Context
import { AppContext } from '/src/components/provider';

// Core
import { Icon, useFetchList, TestSeniorityLevel, useScreenSize, useValidate, Table, useNotify, EnumText, AvarageScore } from '/src';

// Flowbite
import { Tooltip } from 'flowbite-react';

// Components
import { ApplicantsSingleDialog } from '../../../../applicants/components/single-dialog';

export const AssignAndScheduleAssessment = ({ formData, disableButtons }) => {
  // User Data
  const { userData } = useContext(AppContext);
  const navigate = useNavigate();

  // Permissions
  const isPermitted = userData?.roles.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const isSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));

  // State
  const [selectAll, setSelectAll] = useState(false);
  const [startDate, setStartDate] = useState(new Date());
  const [dueDate, setDueDate] = useState(() => {
    const result = new Date(startDate);
    result.setDate(result.getDate() + 1);
    return result;
  });
  const [showNote, setShowNote] = useState(true);
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);

  // Form
  const { form, setFieldValue, setFormValue, resetForm } = formData || {};

  // Datepicker
  const { beforeToday } = DateRangePicker;

  // Hooks
  const screen = useScreenSize();
  const { notify } = useNotify();
  const { isRequired, validateRegex, minLength, maxLength, countryCodeNumberValid } = useValidate();
  const initialFilters = {
    ...(userData.trackId
      ? {}
      : {
          track: {
            label: 'Interests',
            lookup: 'category',
          },
        }),
  };
  const { ready, loading, setLoading, list, count, filters, setFilters, search, pagination, refresh } = useFetchList('applicants/list', {
    search: '',
    pagination: {
      page: 1,
      size: 10,
    },
    filters: initialFilters,
  });

  const filterFeedData = Object.keys(initialFilters);

  useEffect(() => {
    setFieldValue('applicantId')(Object.fromEntries(selectedIds.map((key) => [key, true])));
  }, [selectedIds]);

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  // Start & End date time from this page renderd
  useEffect(() => {
    setFieldValue('startDate')(startDate);
    setFieldValue('dueDate')(dueDate);
  }, [startDate, dueDate]);

  useEffect(() => {
    // disableButtons.setDisableNextButton(Object.keys(form.applicantId).filter((key) => form.applicantId[key]).length <= 0);
    disableButtons.setDisableNextButton(false); // Can assign random quiz
  }, [form.applicantId]);

  return (
    <>
      <div className="space-y-4 relative">
        <div className="p-4 space-y-3 bg-[#F9F6FE] rounded-lg">
          <div className="flex items-center gap-2 text-[#6544AB] text-base font-semibold">
            <Icon icon="akar-icons:link-chain" width={22} />
            <p>Public Link Will Be Generated After Assigning</p>
          </div>
          <p className="text-sm font-medium text-[#403E4E]">A public shareable link will be created for this assignment that anyone can access.</p>
          <p className="text-sm font-semibold text-[#403E4E]">You can optionally assign specific applicants below, or skip this step.</p>
        </div>

        <Table
          ready={ready}
          loading={loading}
          title="Applicant list"
          searchPlaceholder="Search for name or email..."
          count={count}
          search={search}
          filters={filters}
          setFilters={setFilters}
          // filterFeedData={filterFeedData}
          // drawerFilter={{
          //   filterCountNumber: filterCountNumber,
          //   isShowDrawerFilter: isShowDrawerFilter,
          //   setShowDrawerFilter: setShowDrawerFilter,
          // }}
          pagination={pagination}
          rows={list}
          backupRows={backupList}
          slots={{
            name: (_, row) => {
              return <div className="truncate font-medium text-[#101828]  text-sm dark:text-white">{row?.name}</div>;
            },
            email: (_, row) => {
              return (
                <div className="flex items-center gap-2">
                  <div
                    className="font-medium text-gray-900 dark:text-white cursor-pointer truncate max-w-[85%]"
                    onClick={() => navigate(`/app/applicants/progress/${row._id}`)}
                  >
                    {row.email}
                  </div>
                  {row.email && (
                    <Tooltip
                      content="Copy Email"
                      placement="bottom"
                      arrow={false}
                      className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                    >
                      <span
                        onClick={() => {
                          navigator.clipboard.writeText(row.email);
                          notify('Email copied');
                        }}
                        className="cursor-pointer"
                      >
                        <Icon
                          icon="ooui:copy-ltr"
                          className="text-gray-500 dark:text-gray-400 text-base hover:text-gray-700 dark:hover:text-gray-300"
                          width="16"
                        />
                      </span>
                    </Tooltip>
                  )}
                </div>
              );
            },
            seniorityLevel: (_, row) => {
              return (
                <div className="w-fit">
                  <TestSeniorityLevel seniorityLevel={row?.seniorityLevel} />
                </div>
              );
            },
            averageScore: (_, row) => {
              return (
                <div className="w-fit">
                  <AvarageScore score={row?.averageScore} label={row?.scoreLabel} />
                </div>
              );
            },
          }}
          columns={[
            {
              key: 'name',
              label: 'Name',
              primary: true,
              // width: '22%',
            },
            {
              key: 'email',
              label: 'Email',
              primary: true,
              // width: '23%',
            },
            // {
            //   key: 'role',
            //   label: 'Role',
            //   primary: true,
            //   width: '18%',
            // },
            {
              key: 'seniorityLevel',
              label: 'Seniority Level',
              primary: true,
              width: '18%',
            },
            {
              key: 'averageScore',
              label: 'Average Score',
              // primary: true,
              width: '17%',
            },
          ]}
          multiSelectedRow={{
            selectedIds: selectedIds,
            setSelectedIds: setSelectedIds,
          }}
          noDataFound={{
            customIcon: 'applicant',
            message: 'No assessment created yet',
          }}
          noDataFoundIconWidth="60"
          noDataFoundIconHeight="60"
          showMoreMap={showMoreMap}
          setShowMoreMap={setShowMoreMap}
          // addButtonLabel=""
          // onClickAdd={() => {}}
          // actions={[]}
          hideJumbotron
          isScrollableTabsExists
        />

        {/* Need to add to table main buttons */}
        {/* <div className="flex flex-wrap gap-2 justify-between items-center">
            <p className="text-base font-semibold dark:text-white">Select Applicants</p>
            <Button label="New Applicant" icon="mdi:add" tertiary onClick={() => setCreateDialogVisibility(true)} />
          </div>

          <div className="flex flex-wrap items-start gap-4">
            <div className="flex justify-between items-center grow space-y-0 rounded-lg relative">
              <Icon icon="carbon:search" width="20" className="size-5 text-gray-500 dark:text-gray-400 absolute left-3 pointer-events-none" />
              <input
                type="text"
                placeholder="Search..."
                className="w-full p-2 pl-10 dark:bg-gray-700 bg-gray-white text-[13.5px] text-gray-800 border border-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white truncate rounded-lg focus:ring-0 focus:border-gray-300 shadow-sm"
                value={search.value}
                onInput={(e) => search.update(e.target.value)}
              />
            </div>

            <Select
              name="seniorityLevel"
              value={form.seniorityLevel}
              onChange={setFieldValue('seniorityLevel', Number)}
              lookup="$QuizDifficulty"
              dropIcon={true}
              validators={[isRequired()]}
              placeholder="Select applicant’s level"
              requiredLabel
            />

            <Select
              requiredLabel
              name="category"
              required
              placeholder="Search for category"
              value={form.category}
              onChange={setFieldValue('category')}
              lookup="category"
              optionValueKey="_id"
              optionLabelKey="name"
              dropIcon={true}
              validators={[isRequired()]}
            />
          </div> */}

        <div className="p-4 border space-y-4 rounded-lg">
          <p className="text-base font-semibold dark:text-white">Schedule Interview</p>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <p className="text-sm font-semibold text-[#8C939F] dark:text-white">
                Start Date <span className="text-red-600 ml-[1px]">*</span>
              </p>
              <DatePicker
                format="dd/MM/yyyy hh:mm aa"
                value={form.startDate}
                onChange={setFieldValue('startDate')}
                className="w-full grow"
                shouldDisableDate={beforeToday()}
                placement="topStart"
                showMeridiem
              />
            </div>

            <div className="space-y-2">
              <p className="text-sm font-semibold text-[#8C939F] dark:text-white">
                End Date <span className="text-red-600 ml-[1px]">*</span>
              </p>
              <DatePicker
                format="dd/MM/yyyy hh:mm aa"
                value={form.dueDate}
                onChange={setFieldValue('dueDate')}
                className="w-full"
                shouldDisableDate={beforeToday()}
                placement="topStart"
                showMeridiem
              />
            </div>
          </div>

          {showNote && (
            <div className="p-3 space-y-3 bg-[#F5F9FF] dark:bg-transparent border border-[#EAEEF4] rounded-lg">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <Icon icon="material-symbols:info-i-rounded" width={15} className="p-0.5 bg-[#7CCCEF] text-white rounded-lg" />
                  <span className="dark:text-white text-sm font-semibold">Note</span>
                </div>

                <Icon icon="mdi:close" onClick={() => setShowNote(false)} width={20} className="text-[#979FA9] cursor-pointer" />
              </div>
              <p className="text-[#2F3F53] dark:text-white">
                This assessment will be valid for <span className="font-medium">24 hours from today’s date and you can customize it.</span> Applicants
                will have a <span className="font-medium">60-minute</span> window to complete the assessment once they start. The exact availability
                time and deadline will be shared via email in <span className="font-medium">UTC time</span> format.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Create new applicant */}
      {isCreateDialogVisible && <ApplicantsSingleDialog onClose={() => setCreateDialogVisibility(false)} onCreate={refresh} />}
    </>
  );
};
