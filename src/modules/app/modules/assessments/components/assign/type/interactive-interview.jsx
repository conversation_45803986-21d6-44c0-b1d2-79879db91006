// React
import { useState, useRef, useEffect } from 'react';

// Core
import { Form, useValidate, MultiSelect, Select, TextInput, Regex, Card } from '/src';

export const InteractiveInterview = ({ formData, disableButtons }) => {
  // Reference
  const subCategoryRef = useRef(null);

  // State
  const [loading, setLoading] = useState(false);

  // Hooks
  const { isRequired, isNumber, isValidateMaxAndMinNumber } = useValidate();

  // Form
  const { form, setFieldValue, setFormValue, resetForm } = formData || {};

  useEffect(() => {
    disableButtons.setDisableNextButton(!form.category || !form.subCategory || !form.difficulty || !form.estimationTime || !form.numberOfQuestions);
  }, [form]);

  return (
    <Card className="space-y-4">
      <div className="space-y-2">
        <h2 className="text-xl font-semibold ">Interactive Assessment Settings</h2>
        <p className="font-normal text-base text-[#757488]">
          Create a custom interview on-the-fly with AI-generated questions based on your preferences.
        </p>
      </div>

      <Form className="grid sm:grid-cols-2 gap-4 my-2">
        <Select
          label="Category"
          name="category"
          value={form.category}
          disabled={loading}
          onChange={(newCategory) => {
            subCategoryRef.current?.blur();
            setFieldValue('category')(newCategory);
            setFieldValue('subCategory')(null);
          }}
          lookup="category"
          optionValueKey="_id"
          optionLabelKey="name"
          dropIcon
          requiredLabel
          creationOptions={{
            url: 'lookups/category/single',
            fieldName: 'name',
            validation: Regex.categorySubcategoryTopic,
          }}
          placeholder="Search for category..."
        />

        <Select
          disabled={loading}
          name="difficulty"
          label="Difficulty"
          lookup="$QuizDifficulty"
          value={form.difficulty}
          onChange={setFieldValue('difficulty')}
          dropIcon
          requiredLabel
          placeholder="Choose a difficulty level..."
        />

        <TextInput
          disabled={loading}
          name="estimationTime"
          label="Duration ( minutes )"
          placeholder="Estimation time"
          // labelTooltip="Expected time for the interview in minutes."
          value={form.estimationTime}
          onChange={setFieldValue('estimationTime')}
          validators={[isNumber(), isRequired(), isValidateMaxAndMinNumber('min', 10), isValidateMaxAndMinNumber('max', 240)]}
          requiredLabel
          type="number"
          min={10}
        />

        <TextInput
          disabled={loading}
          name="skips"
          label="Max Skips"
          // labelTooltip="Maximum skips allowed without affecting the score."
          placeholder="Choose maximum allows for applicant to skip questions"
          value={form.skips}
          onChange={setFieldValue('skips', Number)}
          validators={[isNumber(), isRequired()]}
          requiredLabel
        />

        <MultiSelect
          key={form.category}
          label="Subcategory"
          requiredLabel
          name="subCategory"
          placeholder="Search for subcategory"
          value={Array.isArray(form.subCategory) ? form.subCategory : []}
          onChange={(newSubCategory) => setFieldValue('subCategory')(newSubCategory)}
          disabled={!form.category || loading}
          disabledMessage="Please select category first"
          lookup="subcategory"
          params={{ categoryId: form.category }}
          creationOptions={{
            url: 'lookups/subCategory/single',
            fieldName: 'name',
            validation: Regex.categorySubcategoryTopic,
          }}
          optionValueKey="_id"
          optionLabelKey="name"
          dropIcon
        />

        <TextInput
          disabled={loading}
          name="numberOfQuestions"
          label="Number of Questions"
          placeholder="Number of questions"
          value={form.numberOfQuestions}
          onChange={setFieldValue('numberOfQuestions')}
          validators={[isNumber(), isRequired()]}
          requiredLabel
        />
      </Form>
    </Card>
  );
};
