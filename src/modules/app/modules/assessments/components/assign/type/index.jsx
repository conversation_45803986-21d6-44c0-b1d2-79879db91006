// React
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

// Core
import { CustomIcon } from '/src';

// Component
import { TemplateBasedInterview } from './template-based-interview';
import { InteractiveInterview } from './interactive-interview';

export const ChooseTypeAssignAssessment = ({ formData, disableButtons }) => {
  // Hooks
  const { type } = useParams();

  // Form
  const { form, setFieldValue, setFormValue, resetForm } = formData || {};

  // Interview Type
  const interviewType = [
    {
      id: 1,
      label: 'Template-Based Interview',
      description: 'Use a pre-defined template with carefully selected questions for consistent evaluation.',
      icon: 'imoChatInterviewAssign',
      component: <TemplateBasedInterview formData={formData} disableButtons={disableButtons} />,
    },
    {
      id: 2,
      label: 'Interactive Interview',
      description: ' Create a custom interview on-the-fly with AI-generated questions based on applicant’s responses.',
      icon: 'interactiveInterviewAssign',
      component: <InteractiveInterview formData={formData} disableButtons={disableButtons} />,
    },
  ];

  // Selected Interview Type
  const [selectedType, setSelectedType] = useState(0);

  useEffect(() => {
    resetForm();
  }, [selectedType]);

  return (
    <div className="space-y-4">
      {type === 'interview' && (
        <div className="flex flex-wrap gap-6">
          {interviewType.map((type, index) => (
            <div
              key={type?.id}
              onClick={() => setSelectedType(index)}
              className={`w-[400px] text-center px-3 py-6 space-y-2 border rounded-md cursor-pointer ${
                selectedType === index && 'bg-[#F9F6FF] border-[#8D5BF8]'
              }`}
            >
              <div className="w-fit m-auto">
                <CustomIcon definedIcon={type?.icon} />
              </div>
              <p className={`text-lg font-medium ${selectedType !== index && 'dark:text-white'}`}>{type?.label}</p>
              <p className="text-[#757488]">{type?.description}</p>
            </div>
          ))}
        </div>
      )}

      {interviewType[selectedType]?.component}
    </div>
  );
};
