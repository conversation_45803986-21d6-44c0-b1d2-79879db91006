// React
import { useEffect, useState } from 'react';

// Flowbite
import { Label, Checkbox } from 'flowbite-react';

// Core
import { Card, Enums, Radio, Icon, Textarea } from '/src';

export const SetupAssignAssessment = ({ formData, disableButtons }) => {
  // Form
  const { form, setFieldValue, setFormValue, resetForm } = formData || {};

  useEffect(() => {
    disableButtons.setDisableNextButton(!form.avatarName || !form.avatarLang);
  }, [form]);

  return (
    <Card className="space-y-8">
      <div className="space-y-4">
        <div className="space-y-1">
          <p className="dark:text-white font-semibold">Select Interviewer</p>
          <p className="text-[#667085] text-sm">Select a virtual interviewer who will conduct the session.</p>
        </div>

        <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {Enums.AiAvatarModel.map((interviewer, index) => (
            <div key={interviewer.value}>
              <Radio
                key={interviewer?.value}
                name={interviewer?.value}
                selectionValue={interviewer?.value}
                value={form.avatarName}
                onChange={setFieldValue('avatarName')}
                lookup="$AiAvatarModel"
                className="hidden"
              />
              <Label
                htmlFor={interviewer?.value}
                className={`flex items-center gap-4 p-6 border rounded-lg cursor-pointer ${
                  form.avatarName === interviewer.value && 'bg-[#F5F2FC] border-[#9566F8]'
                }`}
              >
                <img src={`/images/models/${interviewer.iconPath}`} className="size-16 rounded-full" />
                <div>
                  <p className={`text-[#181D27] text-lg font-medium capitalize ${form.avatarName !== interviewer.value && 'dark:text-white'}`}>
                    {interviewer.value}
                  </p>
                  <p className={`text-[#535862] text-sm ${form.avatarName !== interviewer.value && 'dark:text-white'}`}>{form.avatarDescription}</p>
                </div>
              </Label>
            </div>
          ))}
        </div>
      </div>

      <div className="space-y-4">
        <div className="space-y-1">
          <p className="dark:text-white font-semibold">Interview Language</p>
          <p className="text-[#667085] text-sm">Choose the language in which the interview should be conducted.</p>
        </div>

        <div className="flex gap-8">
          {Enums.InterviewLanguage.map((language, index) => (
            <Label
              key={language.value}
              htmlFor={language?.value}
              className={`w-56 h-[50px] flex items-center gap-2 px-2 border rounded-lg cursor-pointer ${
                form.avatarLang === language.value && 'bg-[#F5F2FC] border-[#9566F8]'
              }`}
            >
              <div className="pl-2">
                <Radio
                  key={language?.value}
                  name={language?.value}
                  selectionValue={language?.value}
                  value={form.avatarLang}
                  onChange={setFieldValue('avatarLang')}
                  lookup={'$InterviewLanguage'}
                />
              </div>
              <Icon icon={language.icon} width={30} />
              <p className={`text-[#181D27] text-sm font-semibold capitalize ${form.avatarLang !== language.value && 'dark:text-white'}`}>
                {language.value}
              </p>
            </Label>
          ))}
        </div>
      </div>

      <div className="space-y-4">
        <div className="space-y-1">
          <p className="dark:text-white font-semibold">Interview Instructions</p>
          <p className="text-[#667085] text-sm">Add any specific instructions for the interviewer to conduct...</p>
        </div>

        <Textarea
          name="notes"
          placeholder="E.g., Focus on backend questions, avoid asking about previous experience, ensure 20-minute limit."
          value={form.notes}
          onChange={setFieldValue('notes')}
          rows="4"
        />
      </div>

      <div className="space-y-4 mt-4">
        <div className="space-y-1">
          <p className="dark:text-white font-semibold">Recording Options</p>
          <p className="text-[#667085] text-sm">Configure recording settings for this interview.</p>
        </div>
        
        <div className="flex items-center">
          <Checkbox
            name="recordInterview"
            checked={form.recordInterview}
            onChange={(e) => setFieldValue('recordInterview')(e.target.checked)}
          />
          <Label htmlFor="recordInterview" className="ml-2 text-[#181D27] dark:text-white">
            Record Interview Session
          </Label>
        </div>
      </div>
    </Card>
  );
};
