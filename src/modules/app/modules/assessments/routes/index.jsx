// React
import { Navigate } from 'react-router-dom';

// Components
import { AssessmentsMainLayout } from '../layouts/main';
import { AssessmentsListPage } from '../pages/list';
import { AssessmentsCreatePage } from '../pages/create';
import { AssessmentsAssignPage } from '../pages/assign';
import { AssessmentSingle } from '../pages/single';

// Core
import { Api } from '/src';

export default [
  {
    path: 'assessment-templates',
    element: <AssessmentsMainLayout />,
    children: [
      // Default
      {
        path: '',
        element: <Navigate to="/app/assessment-templates/list" />,
      },

      // Routes
      {
        path: 'list/:type?',
        element: <AssessmentsListPage />,
        loader() {
          return {
            label: 'Assessment Templates',
            icon: 'material-symbols:person-raised-hand-rounded',
            title: 'Assessment Templates',
            subtitle: 'Create, manage and assign assessment templates.',
          };
        },
      },

      {
        path: 'create/:type',
        element: <AssessmentsCreatePage />,
        loader({ params }) {
          const typeTitle = {
            interview: 'Interview',
            test: 'Test',
            screening: 'Screening',
          };

          const type = params.type || 'assessment';
          const title = `Create ${typeTitle[type] || 'Assessment'} Template`;

          return {
            label: 'Assessment Templates',
            icon: 'material-symbols:person-raised-hand-rounded',
            title: title,
            subtitle: `Create and manage assessment templates for ${typeTitle[type]}`,
          };
        },
      },

      {
        path: 'assign/:type/:id?',
        element: <AssessmentsAssignPage />,
        async loader({ params }) {
          const typeTitle = {
            interview: 'Interview',
            test: 'Test',
            screening: 'Screening',
          };

          let templateTitle = '';
          if (params?.id) {
            try {
              const assessmentPath = params?.type === 'screening' ? 'quizzes/single/phoneScreening' : 'quizzes/single';
              const response = await Api.get(`${assessmentPath}/${params?.id}`);
              templateTitle = params?.type === 'screening' ? response?.data[0]?.title : response?.data?.title;
            } catch (error) {
              throw new Error(`Test not exist: ${id}`);
            } finally {
            }
          }

          const type = params.type || 'assessment';
          const title = params?.id ? (
            <span className="capitalize">
              Schedule {templateTitle} {params?.type}
            </span>
          ) : (
            `Assign ${typeTitle[type] === 'Interview' ? `an ${typeTitle[type]}` : `a ${typeTitle[type]}`}`
          );
          return {
            label: '',
            icon: '',
            title: title,
            subtitle: `Initiate a structured ${typeTitle[type]} process by assigning tailored ${typeTitle[type]}s to applicants.`,
          };
        },
      },

      {
        path: 'view/:type/:id',
        element: <AssessmentSingle />,
        loader({ params }) {
          const typeTitle = {
            interview: 'Interview',
            test: 'Test',
            screening: 'Screening',
          };
          const type = params.type || 'assessment';
          const title = `${typeTitle[type] || 'Assessment'} Template`;
          return {
            label: '',
            icon: '',
            title: title,
            subtitle: `Easily review and manage all your assessments for streamlined evaluation and tracking.`,
          };
        },
      },

      {
        path: 'edit/:type/:id',
        element: <AssessmentsCreatePage />,
        loader({}) {
          return {
            label: '',
            icon: '',
            title: 'Assessment Overview',
            subtitle: `Easily review and manage all your assessments for streamlined evaluation and tracking.`,
          };
        },
      },
    ],
  },
];
