import React from 'react';
import { Navigate } from 'react-router-dom';

import { UsersMainLayout } from '../layouts/main';

import { UsersListPage } from '../pages/list';

export default [
  {
    path: 'users',
    element: <UsersMainLayout />,
    // loader() {
    //   return {
    //     label: 'Users',
    //   };
    // },
    children: [
      // Default
      {
        path: '',
        element: <Navigate to="/app/users/list" />,
      },

      // Routes
      {
        path: 'list',
        element: <UsersListPage />,
        loader() {
          return {
            label: 'User Managament',
            icon: 'material-symbols:person-raised-hand-rounded',
            title: 'User Management',
            subtitle: 'View and manage all users.',
          };
        },
      },
    ],
  },
];
