// React
import { useContext, useEffect, useState } from 'react';

// Context
import { AppContext } from '/src/components/provider';

// Core
import { Table, useFetchList, Icon, EnumText, useScreenSize, useNotify, SubscribeDialog, SidebarFilterDrawer } from '/src';

// Components
import { UsersSIngleDialog } from '../components/single-dialog';

// Flowbite
import { Tooltip } from 'flowbite-react';

export const UsersListPage = () => {
  // User Data
  const { userData, sidebarFilter, setSidebarFilter, sidebarSearch, setSidebarSearch } = useContext(AppContext);
  const isSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));

  // State
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [needSubscription, setNeedSubscription] = useState(false);
  const [handleGet, setHandleGet] = useState(false);
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);
  const [filterCountNumber, setFilterCountNumber] = useState(0);

  // hook
  const screen = useScreenSize();
  const { notify } = useNotify();

  const initialFilters = {
    role: {
      label: 'Role',
      enum: 'Role',
    },
    gender: {
      label: 'Gender',
      enum: 'Gender',
    },
    // scope: {
    //   label: 'Scope',
    //   enum: 'Scope',
    // },
  };
  const { ready, loading, setLoading, list, count, filters, setFilters, search, pagination, refresh } = useFetchList('users/list', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: initialFilters,
  });

  const filterFeedData = Object.keys(initialFilters);

  // Archive Submission
  const ConfirmText = (value) => {
    return (
      <div>
        <div className="flex mx-auto p-4 mb-7 bg-[#ddd1f8] w-24 h-24 rounded-full">
          <div className="flex mx-auto mb-7 bg-[#cab6f5] w-16 h-16 justify-center rounded-full">
            <Icon icon="hugeicons:archive-02" className="text-[#9061F9]" width="40" />
          </div>
        </div>
        {value ? (
          <p>
            Once confirmed, {value} user{value > 1 && 's'} will be archived permanently!
          </p>
        ) : (
          <p>Once confirmed, This user will be archived permanently!</p>
        )}
      </div>
    );
  };

  const clearFilter = () => {
    // resetForm();
    setFilters({});
  };

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
    // setSidebarSearch(search);
    // setSidebarFilter({ filterFeedData, setFilters });
  }, [list]);

  // Use these styles to make black overlay visible and not scrollable
  // Make the scroll in list pages only be smooth
  useEffect(() => {
    if (isShowDrawerFilter) {
      document.querySelector('html').classList.add('overflow-x-hidden');
    } else {
      document.querySelector('html').classList.remove('overflow-x-hidden');
    }
  }, [isShowDrawerFilter]);

  return (
    <>
      <Table
        ready={ready}
        loading={loading}
        title="Users List"
        addButtonLabel="Create User"
        searchPlaceholder={screen.customScreen ? 'Search by user name or email' : 'Name or email'}
        count={count}
        search={search}
        filters={filters}
        // setFilters={setFilters}
        // filterFeedData={Object.keys(filterList)}
        // drawerFilter={{
        //   filterCountNumber: filterCountNumber,
        //   isShowDrawerFilter: isShowDrawerFilter,
        //   setShowDrawerFilter: setShowDrawerFilter,
        // }}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        onClickAdd={() => {
          if (isSuperAdmin || userData?.features?.users > 0) {
            setCreateDialogVisibility(true), setHandleGet(false);
          } else {
            setNeedSubscription(true);
          }
        }}
        slots={{
          name: (_, row) => {
            return (
              <div className="flex relative gap-2">
                <div className="w-full">
                  <div className="break-words overflow-auto whitespace-normal text-clip capitalize font-medium text-gray-800 dark:text-grayTextOnDarkMood">
                    <p className={`lg:truncate ${!showMoreMap[row._id] && 'truncate sm:overflow-visible sm:whitespace-normal'}`}>{row?.name}</p>
                  </div>
                  {screen.gt.md() && (
                    <Tooltip content={row?.name} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                      <div className="w-[92%] h-full absolute left-0 top-0"></div>
                    </Tooltip>
                  )}
                </div>
              </div>
            );
          },
          email: (_, row) => {
            return (
              <div className="flex relative gap-2">
                <div className="w-full">
                  <div className="flex items-center gap-2 relative">
                    <div className="truncate max-w-[85%]">
                      {row?.email ? (
                        <Tooltip
                          content={row.email}
                          placement="bottom"
                          arrow={false}
                          className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                        >
                          <span className="text-[#626874] dark:text-gray-400 truncate">{row.email}</span>
                        </Tooltip>
                      ) : (
                        <span className="text-[#626874] dark:text-gray-400">—</span>
                      )}
                    </div>
                    {row?.email && (
                      <span
                        onClick={() => {
                          navigator.clipboard.writeText(row.email);
                          notify('Email copied');
                        }}
                        className="inline-block cursor-pointer text-gray-500 dark:text-gray-400"
                      >
                        <Tooltip
                          content="Copy Email"
                          placement="bottom"
                          arrow={false}
                          className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                        >
                          <Icon icon="ooui:copy-ltr" className="relative text-[#798296] text-base" />
                        </Tooltip>
                      </span>
                    )}
                  </div>
                </div>
              </div>
            );
          },
          customRolesName: (_, row) => {
            // Assign background and text colors based on role
            let roleColor;
            let roleTextColor;
            if (row.roles && row.roles.name) {
              const roleName = row.roles.name;
              switch (roleName) {
                case 'super-admin':
                  roleColor = 'bg-purple-100 dark:bg-purple-700 dark:bg-opacity-50';
                  roleTextColor = 'text-purple-500 dark:text-white capitalize';
                  break;
                case 'admin':
                  roleColor = 'bg-purple-100 dark:bg-purple-700 dark:bg-opacity-50';
                  roleTextColor = 'text-purple-500 dark:text-white capitalize';
                  break;
                case 'hr':
                  roleColor = 'bg-blue-100 dark:bg-blue-700 dark:bg-opacity-50';
                  roleTextColor = 'text-blue-500 dark:text-white uppercase';
                  break;
                case 'content-creator':
                  roleColor = 'bg-pink-100 dark:bg-pink-700 dark:bg-opacity-50';
                  roleTextColor = 'text-pink-500 dark:text-white capitalize';
                  break;
                default:
                  roleColor = 'bg-gray-100 dark:bg-gray-700 bg-opacity-30 dark:bg-opacity-50';
                  roleTextColor = 'text-gray-800 dark:text-white capitalize';
              }
            }
            return (
              <div className={`w-fit rounded-full px-4 lg:px-3 text-xs font-medium py-1 ${roleColor} ${roleTextColor} dark:opacity-70 truncate`}>
                {row.roles.viewName}
              </div>
            );
          },
          customTrackName: (_, row) => {
            const element = row.track?.name || '—';
            return (
              <div className="flex gap-x-1 relative">
                <div className={`break-words overflow-auto whitespace-normal text-clip`}>
                  <div className="rounded-full py-1 text-gray-500 capitalize font-medium dark:text-gray-400 lg:truncate">{element}</div>
                </div>
                {screen.gt.md() && (
                  <Tooltip content={element} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                    <div className="w-full h-full absolute left-0 top-0"></div>
                  </Tooltip>
                )}
              </div>
            );
          },
          gender: (_, row) => {
            return (
              <div className="rounded-full py-1 capitalize font-normal text-gray-400 dark:text-gray-400 truncate">
                <EnumText name={'Gender'} value={row.gender} />
              </div>
            );
          },
        }}
        columns={[
          {
            key: 'name',
            label: 'Name',
            primary: true,
            width: '20%',
          },
          {
            key: 'email',
            label: 'Email',
            primary: true,
            width: '20%',
          },
          {
            key: 'customRolesName', // 'roles.name'
            label: 'Interstest',
            primary: true,
            width: '15%',
          },
          {
            key: 'customTrackName', // 'track.name'
            label: 'Interests',
            width: '15%',
          },
          {
            key: 'gender',
            label: 'Gender',
            width: '10%',
            // enum: 'Gender',
          },
          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_, row) {
              return [
                {
                  label: 'Edit',
                  customIcon: 'edit',
                  color: 'text-black dark:text-white',
                  onClick: () => {
                    setCreateDialogVisibility(true);
                    setHandleGet(row._id);
                  },
                },
              ];
            },
          },
        ]}
        noDataFound={{
          customIcon: 'users',
          message: 'No users created yet',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
      />

      {isCreateDialogVisible && <UsersSIngleDialog onClose={() => setCreateDialogVisibility(false)} onCreate={refresh} id={handleGet} />}

      {needSubscription && <SubscribeDialog onClose={() => setNeedSubscription(false)} />}

      {/* Filter Drawer */}
      {isShowDrawerFilter && (
        <SidebarFilterDrawer
          drawerFilter={{
            // element: drawerFilter,
            // count: count,
            drawerClearAll: clearFilter,
            // isShowDrawerFilter: isShowDrawerFilter,
            setShowDrawerFilter: setShowDrawerFilter,
            // isAnyFilterApplied: isAnyFilterApplied,
          }}
          filterData={{
            filterFeedData,
            setFilters,
          }}
        />
      )}
    </>
  );
};
