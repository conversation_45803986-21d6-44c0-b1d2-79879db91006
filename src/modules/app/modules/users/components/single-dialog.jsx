import React, { useEffect, useState, useContext } from 'react';

import { useNavigate } from 'react-router-dom';

import { Dialog, Form, TextInput, Button, useForm, useNotify, Select, Api, Regex, useValidate, Icon, RadioGroup, Enums } from '/src';
import { AppContext } from '/src/components/provider';

export const UsersSIngleDialog = ({ onClose, onCreate, id }) => {
  // Hooks
  const { isRequired, validateRegex, minLength, maxLength, validatePasswordRegex, isNotSpaces } = useValidate();
  const { notify } = useNotify();
  const { updateUser } = useContext(AppContext);
  const navigate = useNavigate();

  // State
  const [roles, setRoles] = useState([]);
  const [confirmPassword, setConfirmPassword] = useState();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showError, setShowError] = useState(false);

  // Form
  const { form, setFieldValue, setFormValue } = useForm({
    name: '',
    email: '',
    password: '',
    roles: '', // roleId
    roleName: '',
    roleViewName: '',
    gender: 1,
    track: '',
  });

  const handleGet = async () => {
    try {
      const { data } = await Api.get(`users/single/${id}`);
      setFormValue({
        ...data,
        track: data.track?.trackId,
        roles: data.roles?.roleId,
        roleName: data.roles?.name,
        roleViewName: data.roles?.viewName,
      });
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  const handleInsert = async (e) => {
    if (form.password && confirmPassword !== form.password) {
      setShowError(true);
    } else {
      try {
        await Api.post('users/single', form);
        onCreate();
        notify('User created successfully!');
        onClose();
      } catch (error) {
        notify.error(error.response.data.message);
      }
    }
  };

  const handleUpdate = async (e) => {
    if (form.password && confirmPassword !== form.password) {
      setShowError(true);
    } else {
      try {
        const response = await Api.put(`users/single/${id}`, form);
        if (response?.data.access_token) {
          localStorage.setItem('userData', JSON.stringify(response.data));
          updateUser(response.data);
          onCreate();
          notify('User updated successfully!');
          onClose();
          navigate('/');
        } else {
          onCreate();
          notify('User updated successfully!');
          onClose();
        }
      } catch (error) {
        notify.error(error.response.data.message);
      }
    }
  };

  const handleSearch = (endpoint, action) => async (keyword) => {
    try {
      const result = await Api.get(endpoint, { keyword });
      action(result?.data);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  const handleShow = (type) => {
    if (type === 'pass') setShowPassword(!showPassword);
    else setShowConfirmPassword(!showConfirmPassword);
  };

  // Getters
  const isEditMode = () => !!form._id;

  // On mount
  useEffect(() => {
    if (id) {
      handleGet();
    }
  }, []);

  useEffect(() => {
    if (confirmPassword === form.password) setShowError(false);
  }, [confirmPassword]);

  return (
    <Dialog size="lg" show popup modalHeader={id ? 'Edit User' : 'Create User'} onClose={onClose} overflowVisible={true}>
      {/* Creation Form */}

      <Form onSubmit={isEditMode() ? handleUpdate : handleInsert}>
        <div className="grid gap-4">
          <div className="flex flex-col gap-2">
            <TextInput
              label="Name"
              name="Name"
              placeholder="Name"
              value={form.name}
              onChange={setFieldValue('name')}
              validators={[isRequired(), validateRegex(Regex.name), minLength(2), maxLength(50), isNotSpaces()]}
              requiredLabel
            />

            <div className="my-1">
              <RadioGroup
                requiredLabel
                name="gender"
                label="Gender"
                value={form.gender}
                onChange={setFieldValue('gender', Number)}
                className="text-inputLabel dark:text-inputDarkLabel"
                lookup="$Gender"
              />
            </div>
            <TextInput
              name="email"
              label="Email"
              placeholder="Email"
              value={form.email}
              onChange={setFieldValue('email')}
              validators={[isRequired(), validateRegex(Regex.email)]}
              requiredLabel
            />
            <div className="flex w-full ">
              <div className="w-full relative">
                <TextInput
                  label="Password"
                  name="password"
                  placeholder="Password"
                  autoComplete="new-password"
                  type={showPassword ? 'text' : 'password'}
                  value={form.password}
                  rightIcon={() => {}}
                  onChange={setFieldValue('password')}
                  validators={
                    !id
                      ? [isRequired(), isNotSpaces(), minLength(8), maxLength(50), validatePasswordRegex(Regex.password)]
                      : form.password
                      ? [isNotSpaces(), minLength(8), maxLength(50), validatePasswordRegex(Regex.password)]
                      : []
                  }
                  requiredLabel
                />
              </div>

              <div className="mt-[29px] absolute right-1" onClick={() => handleShow('pass')}>
                <Icon
                  className="ml-3 p-5  w-8 h-8 rounded-md cursor-pointer text-gray-500 dark:text-gray-400"
                  width="25"
                  icon={!showPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                />
              </div>
            </div>
            <div className="flex w-full">
              <div className="w-full relative">
                <TextInput
                  label="Confirm Password"
                  name="confirm password"
                  placeholder="Confirm password"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(value) => setConfirmPassword(value)}
                  disabled={!form.password}
                  requiredLabel
                  rightIcon={() => {}}
                />
              </div>

              <div className="mt-[29px] absolute right-1" onClick={() => handleShow()}>
                <Icon
                  className="ml-3 p-5  w-8 h-8 rounded-md cursor-pointer text-gray-500 dark:text-gray-400"
                  width="25"
                  icon={!showConfirmPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                />
              </div>
            </div>
            {(confirmPassword && confirmPassword !== form.password) || showError ? (
              <label className="text-red-500 text-sm">Confirm password doesn't match the password</label>
            ) : null}
            <div className="space-y-3">
              <Select
                label="Role"
                name="role"
                value={form.roles}
                lookup={roles}
                optionValueKey="_id"
                optionLabelKey="viewName"
                placeholder="Find roles by name..."
                onSearch={handleSearch('roles/single/search', setRoles)}
                dropIcon={true}
                onChange={(event) => {
                  const [selectedRole] = roles.filter((singleRole) => singleRole._id === event);
                  setFieldValue('roles')(selectedRole?._id);
                  setFieldValue('roleName')(selectedRole?.name);
                  setFieldValue('roleViewName')(selectedRole?.viewName); // NB: This has no use here, it is used only with the optionLabelKey
                }}
                requiredLabel
                validators={[isRequired()]}
              />
            </div>
          </div>
          {form.roleName === 'content-creator' && (
            <div>
              <Select
                label="Track"
                name="track"
                value={form.track}
                lookup="category"
                optionValueKey="_id"
                optionLabelKey="name"
                dropIcon={true}
                onChange={(selectedTrack) => {
                  setFieldValue('track')(selectedTrack);
                }}
                validators={[isRequired()]}
                creationOptions={{
                  url: 'lookups/category/single',
                  fieldName: 'name',
                  validation: Regex.categorySubcategoryTopic,
                }}
                requiredLabel
              />
            </div>
          )}
        </div>

        <Button className="w-full mt-2" type="submit" label={`${!!id ? 'Update' : 'Create'}`} />
      </Form>
    </Dialog>
  );
};
