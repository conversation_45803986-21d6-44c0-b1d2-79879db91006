// Core
import { useState, useEffect, useContext } from 'react';
import { Icon, Button, useNotify, Api } from '/src';
import { useParams } from 'react-router-dom';
import { addDays, format, isValid } from 'date-fns';
import { AppContext } from '/src/components/provider';

export const CurrentPlanSummary = () => {
  // Hooks
  const { id } = useParams();

  // states
  const [plansData, setPlansData] = useState(null);
  const { notify } = useNotify();
  const { userData } = useContext(AppContext);

  const orgId = id ?? userData.organizationId;
  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get(`organizations/plan/overview/${orgId}`);
      setPlansData(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  const formatDate = (customDate) => {
    const date = new Date(customDate);
    if (!isValid(date)) {
      return 'Invalid date';
    }
    return format(date, "MMMM dd, yyyy, 'at' hh:mm a");
  };

  // Calculate overall usage percentage
  const calculateOverallUsagePercentage = () => {
    if (!plansData || !plansData.features || !plansData.usage) return 0;

    let totalUsage = 0;
    let totalLimit = 0;

    // Calculate total usage and total limit across all features
    Object.keys(plansData.features).forEach((featureName) => {
      const limit = plansData.features[featureName];
      const usage = plansData.usage[featureName] || 0;

      // Skip unlimited features (-1)
      if (limit !== -1) {
        totalUsage += usage;
        totalLimit += limit;
      }
    });

    return totalLimit > 0 ? Math.min(Math.round((totalUsage / totalLimit) * 100), 100) : 0;
  };

  // Effects

  useEffect(() => {
    handleGet();
  }, []);

  return (
    <div className="h-full space-y-4 p-2 rounded-xl">
      {plansData && Object.keys(plansData).length > 0 ? (
        <div className="space-y-4">
          <div className="grid xssm:grid-cols-2 sm:grid-cols-4 gap-x-2 uppercase">
            <div className="space-y-1">
              <p className="text-[#566577] dark:text-gray-400 text-sm font-medium">Plan Name</p>
              <p className="text-[#2C2C2C] dark:text-white text-sm font-semibold">{plansData.name}</p>
            </div>
            <div className="space-y-1">
              <p className="text-[#566577] dark:text-gray-400 text-sm font-medium">Billing Cycle</p>
              {/* <p className="text-[#2C2C2C] dark:text-white text-sm font-semibold">{plansData.billingCycle}</p> */}
              <p className="text-[#2C2C2C] dark:text-white text-sm font-semibold">Monthly</p>
            </div>
            <div className="space-y-1">
              <p className="text-[#566577] dark:text-gray-400 text-sm font-medium">Plan Cost</p>
              <p className="text-[#2C2C2C] dark:text-white text-sm font-semibold">{plansData.price}</p>
            </div>
            <div className="space-y-1">
              <p className="text-[#566577] dark:text-gray-400 text-sm font-medium">Expiry Date</p>
              <p className="text-[#2C2C2C] dark:text-white text-sm font-semibold">{formatDate(plansData.endDate)}</p>
            </div>
          </div>

          <div className="mt-6 space-y-8">
            <div className="flex justify-between items-center">
              <p className="text-[#566577] dark:text-gray-400 text-sm font-medium uppercase">Usage</p>
              <p className="text-[#2C2C2C] dark:text-white font-semibold text-sm">
                {plansData.features && plansData.usage && <>{calculateOverallUsagePercentage()}% Used</>}
              </p>
            </div>
            {plansData.usageWarning && <p className="text-[#D71111] font-semibold">{plansData.usageWarning}</p>}
          </div>

          {plansData.features && plansData.usage && (
            <div className="w-full h-6 bg-[#EAEAEA] rounded-lg overflow-hidden">
              <div className="h-full bg-[#986CFA]" style={{ width: `${calculateOverallUsagePercentage()}%` }} />
            </div>
          )}
        </div>
      ) : (
        <div className={`flex flex-col mt-5 items-center text-center`}>
          <Icon icon="iconoir:warning-circle" className="dark:text-gray-500 text-gray-400" width="50" />
          <p className={`text-gray-400 mt-2`}>No current plan summary Found</p>
        </div>
      )}
    </div>
  );
};
