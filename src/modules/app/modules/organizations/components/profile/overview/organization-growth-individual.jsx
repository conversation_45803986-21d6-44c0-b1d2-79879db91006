// Core
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import { Icon, ChartsWavy, useNotify, Api } from '/src';

// Flowbite
import { Rating } from 'flowbite-react';

export const OrganizationGrowthIndividual = () => {
  // Hooks
  const { id } = useParams();

  const [organizationGrowthData, setOrganizationGrowthData] = useState({
    usersCount: 0,
    applicantCount: 0,
    usersGrowth: Array(12)
      .fill()
      .map((_, i) => ({ month: i + 1, count: 0 })),

    applicantGrowth: Array(12)
      .fill()
      .map((_, i) => ({ month: i + 1, count: 0 })),
  });
  const { notify } = useNotify();

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get(`organizations/growth/${id}`);
      setOrganizationGrowthData(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  const userInfoTitle = 'Users';

  // Bouns thing if we need to match things in figma at the future

  const calculateGrowth = (growthArray) => {
    // Find the last two non-zero values to calculate growth
    const nonZeroMonths = growthArray.filter((item) => item.count > 0);

    if (nonZeroMonths.length >= 2) {
      const current = nonZeroMonths[nonZeroMonths.length - 1].count;
      const previous = nonZeroMonths[nonZeroMonths.length - 2].count;

      if (previous > 0) {
        return ((current - previous) / previous) * 100;
      }
    }

    return 0; // Default growth if can't be calculated
  };

  const usersGrowthPercentage = calculateGrowth(organizationGrowthData.usersGrowth || []);
  const applicantGrowthPercentage = calculateGrowth(organizationGrowthData.applicantGrowth || []);

  const allData = [
    {
      type: 1,
      title: `users`,
      users: `${organizationGrowthData?.usersCount} `,
      gain: usersGrowthPercentage > 0 ? usersGrowthPercentage.toFixed(1) : null,
      lose: usersGrowthPercentage < 0 ? Math.abs(usersGrowthPercentage).toFixed(1) : null,
    },
    {
      type: 2,
      title: 'Applicants',
      applicants: `${organizationGrowthData?.applicantCount} `,
      gain: applicantGrowthPercentage > 0 ? applicantGrowthPercentage.toFixed(1) : null,
      lose: applicantGrowthPercentage < 0 ? Math.abs(applicantGrowthPercentage).toFixed(1) : null,
    },
    {
      info: `This week the ${userInfoTitle} increased by`,
    },
  ];

  const colorType = (type) => {
    if (type === 1) return 'bg-[#5DC6C0]';
    else if (type === 2) return 'bg-[#6599F7]';
  };

  // Transform data for the chart
  const chartsdata = Array.from({ length: 12 }, (_, i) => {
    const monthIndex = i + 1; // 1-12 for data lookup
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    return {
      x: monthNames[i], // 0-11 for month names
      a: organizationGrowthData?.applicantGrowth?.find((item) => item.month === monthIndex)?.count || 0,
      b: organizationGrowthData?.usersGrowth?.find((item) => item.month === monthIndex)?.count || 0,
    };
  });

  useEffect(() => {
    handleGet();
  }, []);

  const chartLabels = {
    a: 'applicantCount',
    b: 'usersCount',
  };

  return (
    <div className="p-2">
      <div className="-ml-8">
        <ChartsWavy data={chartsdata} dataKeys={chartLabels} />
      </div>

      <div>
        {allData?.map((data) => {
          return (
            <div key={data?.id} className="flex justify-between pt-6 dark:text-white text-sm  font-medium">
              <div className="flex items-start  gap-2.5">
                {data?.type && <p className={`size-3 ${colorType(data?.type)} rounded-[2px] mt-1`} />}
                <div className="space-y-3">
                  <p>{data?.title}</p>
                  <div className="flex gap-2">
                    {data?.users && <p className="text-gray-500 text-xs">{data.users} users</p>}
                    {data?.applicants && <p className="text-gray-500 text-xs">{data.applicants} applicants</p>}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="flex gap-0.5">
                  {data?.starRating && (
                    <>
                      <Rating>
                        <Rating.Star />
                      </Rating>
                      <p>{data?.starRating}</p>
                    </>
                  )}
                </div>
                {(data?.gain || data?.lose) && (
                  <div className={`flex items-center gap-0.5 ${data?.gain ? 'text-[#24c081]' : 'text-[#ef4444]'}`}>
                    {data?.gain && <Icon icon="mingcute:arrow-up-line" />}
                    {data?.lose && <Icon icon="mingcute:arrow-down-line" />}
                    <p>{data?.gain || data?.lose}%</p>
                  </div>
                )}
              </div>
            </div>
          );
        })}
        {/* 
        <div className="flex dark:text-white">
          <p>This week the {userInfoTitle} increased by</p>
          <span className="text-[#149041] flex ">
            <Icon icon="mingcute:arrow-up-line" />
            {usersGrowthPercentage > 0 ? usersGrowthPercentage.toFixed(1) : '0'}%
          </span>
        </div> */}
      </div>
    </div>
  );
};
