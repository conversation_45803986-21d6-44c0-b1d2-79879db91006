// Core
import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';

import { ChartsDonut, useNotify, Api } from '/src';

export const UsersCard = () => {
  // Hooks
  const { id } = useParams();

  // states
  const { notify } = useNotify();
  const [users, setUsers] = useState(null);

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get(`organizations/users/${id}`);
      setUsers(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };
  const data =
    users?.userStatistics?.map((item) => {
      return {
        name: item.role,
        value: item.value,
        color: item.role === 'Admin' ? '#C893FD' : item.role === 'Content Creator' ? '#FEC4F8' : item.role === 'HR' ? '#BDD6FF' : '#=',
      };
    }) || [];

  const rightData = () =>
    data?.map((item) => (
      <div key={item.name} className="flex items-center space-y-0.5 dark:text-white">
        <div className="size-3 mr-3 mt-0.5 rounded-sm" style={{ backgroundColor: item.color }}></div>
        <span className="w-32 mr-2">{item.name}</span>
        <span className="font-medium">{item.value}%</span>
      </div>
    ));

  useEffect(() => {
    handleGet();
  }, []);
  return <ChartsDonut data={data} rightData={rightData} rightDataStyles="" innerRadius={45} outerRadius={60} width="80%" height={150} />;
};
