// React
import { useState, useEffect } from 'react';

// Flowbite
import { Datepicker } from 'flowbite-react';

// Core
import { Icon, Steps, useNotify, Api } from '/src';

// date
import { formatDistanceToNow, isToday, isYesterday, format } from 'date-fns';

export const ActivityProfile = () => {
  const { notify } = useNotify();

  // Search
  const [activityLogData, setActivityLogData] = useState([]);
  const [search, setSearch] = useState('');

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get('organizations/activity/logs');
      setActivityLogData(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  const formatLastActive = (dateString) => {
    const date = new Date(dateString);
    if (isToday(date)) {
      return 'Today';
    }
    if (isYesterday(date)) {
      return 'Yesterday';
    }
    return formatDistanceToNow(date, { addSuffix: true });
  };

  const steps = [
    {
      data: activityLogData?.map((item) => ({
        icon: item.action === 'downgrade' ? 'arrowDownRight' : 'arrowUpRight',
        header: (
          <p>
            Organization <span className="text-black font-medium underline">{item.orgName}</span>{' '}
            <span className="text-black font-medium underline">{item.action}</span> Plan To{' '}
            <span className="text-black font-medium underline">{item.toPlan}</span>
          </p>
        ),
        description: formatLastActive(item.actionDate),
      })),
    },
  ];

  // Effects
  useEffect(() => {
    handleGet();
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center gap-2">
        {/* Search bar */}
        <div className="flex justify-between items-center grow space-y-0 rounded-lg relative">
          <Icon icon="carbon:search" width="20" className="size-5 text-gray-500 dark:text-gray-400 absolute left-3 pointer-events-none" />
          <input
            type="text"
            placeholder="Search..."
            className="w-full p-2 pl-10 dark:bg-gray-700 bg-gray-white text-[13.5px] text-gray-800 border border-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white truncate rounded-lg focus:ring-0 focus:border-gray-300 shadow-sm"
            value={search}
            onInput={(e) => setSearch(e.target.value)}
          />
        </div>

        <div className="calendar flex flex-row justify-end items-center gap-2 text-gray-700 dark:text-gray-300 rounded-lg h-fit">
          <Datepicker
            className="inline-block w-full sm:w-44"
            // onSelectedDateChanged={() => {}}
            showTodayButton={false}
            showClearButton={false}
            // value=""
          />
        </div>
      </div>

      <div className="space-y-8">
        {steps.map((step) => (
          <Steps step={step} />
        ))}
      </div>
    </div>
  );
};
