// React
import { useContext, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

// Flowbite
import { Tooltip } from 'flowbite-react';

// Context
import { AppContext } from '/src/components/provider';

// Core
import {
  Icon,
  CustomIcon,
  ToggleFilter,
  useFetchList,
  Button,
  TestDifficulty,
  TestSeniorityLevel,
  AvarageScore,
  StaticData,
  useScreenSize,
  Table,
  FormatDate,
  EnumText,
  useNotify,
} from '/src';

// Flowbite
import {} from 'flowbite-react';

// Components
// import { InlineFilter } from '../../inline-filter';
import { UsersSIngleDialog } from '../../../../users/components/single-dialog';

export const UsersProfile = () => {
  // User Data
  const { userData } = useContext(AppContext);

  // State
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);
  const [isEditUserDialogVisible, setEditUserDialogVisiblity] = useState(false);

  // Hooks
  const screen = useScreenSize();
  const { id } = useParams();
  const { notify } = useNotify();
  const initialFilters = {
    seniortyLevel: {
      label: 'Seniorty Level',
      enum: 'QuizDifficulty',
    },
    ...(userData.trackId
      ? {}
      : {
          track: {
            label: 'Inerests',
            lookup: 'category',
          },
        }),
    difficulty: {
      label: 'Difficulty',
      enum: 'QuestionDifficulty',
    },
  };
  // Endpoints = ['users/list', 'users/list/admin', 'users/list/content-creator', 'users/list/hr'];
  const { ready, loading, list, count, search, pagination, filters, setFilters, refresh } = useFetchList('users/single/organization', {
    search: '',
    id: id,
    pagination: {
      page: 1,
      size: 20,
    },
    filters: initialFilters,
  });

  const filterFeedData = Object.keys(initialFilters);

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  return (
    <div className="space-y-4">
      {/* <InlineFilter
        data={data}
        selectedInlineFilter={{
          activeInlineFilter: activeInlineFilter,
          setActiveInlineFilter: setActiveInlineFilter,
        }}
      /> */}

      {/* Need to add to table main buttons */}
      {/* <button className="inline-flex items-center gap-2 justify-center h-10 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
        <Icon icon="ion:filter" width="22" />
        <span className="hidden sm:block">Export</span>
      </button> */}

      <Table
        ready={ready}
        loading={loading}
        title="Users List"
        searchPlaceholder={screen.customScreen ? 'Search by user name or email' : 'Name or email'}
        count={count}
        search={search}
        filters={filters}
        // setFilters={setFilters}
        // filterFeedData={Object.keys(initialFilters)}
        // drawerFilter={{
        //   filterCountNumber: filterCountNumber,
        //   isShowDrawerFilter: isShowDrawerFilter,
        //   setShowDrawerFilter: setShowDrawerFilter,
        // }}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        slots={{
          name: (_, row) => {
            return (
              <div className="flex relative gap-2">
                <div className="w-full">
                  <div className="break-words overflow-auto whitespace-normal text-clip capitalize font-medium text-gray-800 dark:text-grayTextOnDarkMood">
                    <p className={`lg:truncate ${!showMoreMap[row._id] && 'truncate sm:overflow-visible sm:whitespace-normal'}`}>{row?.name}</p>
                  </div>
                  {screen.gt.md() && (
                    <Tooltip content={row?.name} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                      <div className="w-[92%] h-full absolute left-0 top-0"></div>
                    </Tooltip>
                  )}
                </div>
              </div>
            );
          },
          email: (_, row) => {
            return (
              <div className="flex relative gap-2">
                <div className="w-full">
                  <div className="flex items-center gap-2">
                    <span className="text-[#626874] dark:text-gray-400 truncate">{row?.email}</span>
                    <span
                      onClick={() => {
                        navigator.clipboard.writeText(row.email);
                        notify('Email copied');
                      }}
                      className="inline-block cursor-pointer text-gray-500 dark:text-gray-400"
                    >
                      <Tooltip
                        content="Copy Email"
                        placement="bottom"
                        arrow={false}
                        className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                      >
                        <Icon icon="ooui:copy-ltr" className="relative text-[#798296] text-base" />
                      </Tooltip>
                    </span>
                  </div>
                </div>
              </div>
            );
          },
          customRolesName: (_, row) => {
            // Assign background and text colors based on role
            let roleColor;
            let roleTextColor;
            if (row.roles && row.roles.name) {
              const roleName = row.roles.name;
              switch (roleName) {
                case 'super-admin':
                  roleColor = 'bg-purple-100 dark:bg-purple-700 dark:bg-opacity-50';
                  roleTextColor = 'text-purple-500 dark:text-white capitalize';
                  break;
                case 'admin':
                  roleColor = 'bg-purple-100 dark:bg-purple-700 dark:bg-opacity-50';
                  roleTextColor = 'text-purple-500 dark:text-white capitalize';
                  break;
                case 'hr':
                  roleColor = 'bg-blue-100 dark:bg-blue-700 dark:bg-opacity-50';
                  roleTextColor = 'text-blue-500 dark:text-white uppercase';
                  break;
                case 'content-creator':
                  roleColor = 'bg-pink-100 dark:bg-pink-700 dark:bg-opacity-50';
                  roleTextColor = 'text-pink-500 dark:text-white capitalize';
                  break;
                default:
                  roleColor = 'bg-gray-100 dark:bg-gray-700 bg-opacity-30 dark:bg-opacity-50';
                  roleTextColor = 'text-gray-800 dark:text-white capitalize';
              }
            }
            return (
              <div className={`w-fit rounded-full px-4 lg:px-3 text-xs font-medium py-1 ${roleColor} ${roleTextColor} dark:opacity-70 truncate`}>
                {row.roles?.viewName}
              </div>
            );
          },
          status: (_, row) => {
            return (
              <p
                className={`w-fit flex items-center gap-1.5 px-1.5 py-0.5 border border-[#D5D7DA] rounded-lg ${
                  row?.activeStatus === 'active' ? 'bg-white text-[#414651]' : 'bg-[#f4f5f7] text-[#7A8B9F]'
                }`}
              >
                {row?.activeStatus === 'active' && <span className="size-2 bg-[#17B26A] rounded-full" />}
                <span className="capitalize">{row?.activeStatus}</span>
              </p>
            );
          },
          gender: (_, row) => {
            return (
              <div className="rounded-full py-1 capitalize font-normal text-gray-400 dark:text-gray-400 truncate">
                <EnumText name={'Gender'} value={row.gender} />
              </div>
            );
          },
        }}
        columns={[
          {
            key: 'name',
            label: 'Name',
            primary: true,
            width: '20%',
          },
          {
            key: 'email',
            label: 'Email',
            primary: true,
            width: '20%',
          },
          {
            key: 'customRolesName',
            label: 'Role',
            primary: true,
            width: '15%',
          },
          {
            key: 'status',
            label: 'Status',
            primary: true,
            width: '15%',
          },
          {
            key: 'gender',
            label: 'Gender',
            width: '10%',
          },
          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons() {
              return [
                {
                  label: 'Edit',
                  customIcon: 'edit',
                  color: 'text-black dark:text-white',
                  onClick: () => {
                    setEditUserDialogVisiblity(true);
                  },
                },
                // {
                //   label: 'Assign',
                //   customIcon: 'assign',
                //   iconWidth: '22',
                //   iconHeight: '22',
                //   color: 'text-black dark:text-white',
                //   // onClick: () => {},
                //   dropDown: [
                //     {
                //       label: 'Interview',
                //       color: 'text-black dark:text-white',
                //       customIcon: 'interview',
                //       element: (
                //         <span
                //           className={`w-24 text-xs px-2 py-1 rounded-full shadow-md font-bold tracking-wide bg-magic-gradient bg-[length:200%]  text-white overflow-hidden`}
                //         >
                //           AI Magic ✨
                //         </span>
                //       ),
                //       onClick: () => {
                //         if (isSuperAdmin || userData?.features?.assignInterview > 0) {
                //           setAssignInterviewTestVisible(true);
                //           setApplicantDetails(row);
                //         } else {
                //           setNeedSubscription(true);
                //         }
                //       },
                //     },
                //   ],
                // },
              ];
            },
          },
        ]}
        // multiSelectedRow={{
        //   selectedIds: selectedIds,
        //   setSelectedIds: setSelectedIds,
        //   handleArchiveSelectedIds: handleArchiveSelectedIds,
        // }}
        noDataFound={{
          customIcon: 'users',
          message: 'No users created yet',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        // addButtonLabel=""
        // onClickAdd={() => {}}
        // actions={[]}
        hideJumbotron
        isScrollableTabsExists
      />

      {isEditUserDialogVisible && <UsersSIngleDialog onClose={() => setEditUserDialogVisiblity(false)} />}
    </div>
  );
};
