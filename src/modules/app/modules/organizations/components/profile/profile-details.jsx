// React
import { useEffect, useState } from 'react';
import { CircularProgressbar, buildStyles } from 'react-circular-progressbar';
import { useParams } from 'react-router-dom';
import { isValid, format } from 'date-fns';

// Core
import { Button, useForm, Form, useValidate, TextInput, Regex, Icon, RadioGroup, Dialog, Api } from '/src';

// Flowbite
import { Tooltip } from 'flowbite-react';
import { EditProfile } from '../edit-profile';

export const ProfileDetails = () => {
  const { id } = useParams();
  // State
  const [isSignUpDialogVisible, setSignUpDialogVisibilty] = useState(false);
  const [showPassword, setShowPassword] = useState({ password: false, confirmPassword: false });

  // Hooks

  // Form
  const { form, setFieldValue, setFormValue, resetForm } = useForm({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    mobileNumber: '',
    location: '',
  });

  const formatDate = (customDate) => {
    const date = new Date(customDate || Date.now());
    if (!isValid(date)) {
      return 'Invalid date';
    }
    return format(date, 'dd MMMM , yyyy');
  };

  const handleGet = async () => {
    try {
      const response = await Api.get(`organizations/single/${id}`);
      setFormValue(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };
  const onSubmit = () => setSignUpDialogVisibilty(false);

  useEffect(() => {
    handleGet();
  }, []);

  return (
    <div className="flex flex-wrap px-4 justify-between">
      <div className="flex gap-8 items-center">
        {/* Circle chart */}
        <div className="relative w-28">
          <div className="xsmd:min-w-32 mx-auto">
            <img src="/images/avatar-male.svg" className="w-full " alt="Avatar" />
          </div>
        </div>

        {/* Info name */}
        <div className="space-y-2">
          <div className="flex gap-3 items-center">
            <p className="text-2xl font-semibold dark:text-white">{form.name}</p>
            {/* <div className="bg-[#F9F5FF] border border-[#E9EAEB] px-2 rounded-full w-fit h-fit text-[#6941C6] font-medium">{form.plan}xxx</div> */}
          </div>
          <p className="text-[#566577] dark:text-white text-sm">Joined {formatDate(form.joinDate)}</p>
          {form.location && (
            <div className="flex items-center gap-[2px]">
              <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M3.17969 7.79389C3.19049 4.61345 5.77751 2.04395 8.95795 2.05472C12.1384 2.06556 14.7079 4.65258 14.6971 7.83302V7.89823C14.6579 9.96563 13.5036 11.8765 12.0884 13.37C11.279 14.2104 10.3752 14.9545 9.39491 15.5874C9.13278 15.8141 8.74398 15.8141 8.48186 15.5874C7.02049 14.6362 5.73789 13.4353 4.69273 12.0395C3.7612 10.8225 3.23231 9.34521 3.17969 7.81345V7.79389Z"
                  stroke="#566577"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M8.9395 9.75009C9.95888 9.75009 10.7853 8.92372 10.7853 7.90434C10.7853 6.88496 9.95888 6.05859 8.9395 6.05859C7.92012 6.05859 7.09375 6.88496 7.09375 7.90434C7.09375 8.92372 7.92012 9.75009 8.9395 9.75009Z"
                  stroke="#566577"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>

              <p className="text-[#0E233C] dark:text-white">{form.location}</p>
            </div>
          )}
        </div>
      </div>

      {/* Info email, mobileNumber number */}
      <div className="w-60 flex items-center">
        <div className="space-y-2">
          {/* mobileNumber number */}
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <svg width="18" height="16" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M2.78586 0.819726C3.82624 -0.216199 5.53926 -0.0318042 6.4104 1.13349L7.48809 2.57507C8.19692 3.52324 8.13438 4.84843 7.29298 5.68622L7.08892 5.88941C7.08 5.91534 7.05839 5.99682 7.08238 6.15169C7.13641 6.50044 7.42723 7.24023 8.6481 8.45587C9.86853 9.67108 10.6125 9.96202 10.9657 10.0163C11.1258 10.0409 11.2095 10.0179 11.2354 10.0089L11.5841 9.66169C12.3321 8.9169 13.4815 8.77768 14.4076 9.28182L16.0404 10.1707C17.4385 10.9318 17.7918 12.8349 16.6463 13.9755L15.4322 15.1844C15.0497 15.5653 14.5353 15.8829 13.9075 15.9415C12.3614 16.0858 8.75639 15.9016 4.96853 12.13C1.43211 8.60871 0.753472 5.53797 0.667607 4.02499L1.30755 3.98857L0.667607 4.02499C0.62419 3.25994 0.985204 2.61267 1.44442 2.15542L2.78586 0.819726ZM5.38416 1.90284C4.95118 1.32366 4.14416 1.27761 3.68976 1.73006L2.34832 3.06576C2.06635 3.34652 1.93069 3.65593 1.9475 3.95215C2.01572 5.15426 2.5638 7.92519 5.87243 11.2197C9.34351 14.6759 12.5494 14.7789 13.7885 14.6633C14.0417 14.6396 14.2935 14.5079 14.5283 14.274L15.7424 13.0652C16.2359 12.5738 16.127 11.679 15.4281 11.2986L13.7953 10.4097C13.3445 10.1643 12.8162 10.2452 12.488 10.572L12.0988 10.9596L11.6468 10.5044C12.0988 10.9596 12.0982 10.9602 12.0976 10.9608L12.0963 10.9621L12.0937 10.9646L12.0882 10.9699L12.0756 10.9816C12.0666 10.9898 12.0564 10.9988 12.0448 11.0084C12.0215 11.0277 11.993 11.0495 11.9589 11.0724C11.8904 11.1183 11.8001 11.1682 11.6866 11.2106C11.4551 11.2969 11.1498 11.3433 10.7714 11.2852C10.0306 11.1714 9.04923 10.6656 7.7442 9.36621C6.43961 8.0672 5.9303 7.08903 5.81558 6.34849C5.75691 5.96983 5.8037 5.66403 5.89098 5.43205C5.93376 5.31836 5.98417 5.22796 6.03043 5.15959C6.0535 5.12549 6.07546 5.09698 6.09484 5.07383C6.10453 5.06225 6.11358 5.05199 6.12182 5.04303L6.13356 5.03055L6.13893 5.02502L6.14149 5.02243L6.14273 5.02118C6.14335 5.02057 6.14396 5.01996 6.59591 5.47513L6.14396 5.01996L6.38908 4.77589C6.75538 4.41116 6.80669 3.80569 6.46185 3.34442L5.38416 1.90284Z"
                  fill="#566577"
                />
              </svg>
            </div>
            <p className="text-[#0E233C] dark:text-white flex-grow">{form.mobileNumber}</p>
            <Tooltip content="Copy mobile number">
              <div
                className="cursor-pointer p-1.5 rounded-md border dark:border-gray-700"
                onClick={() => navigator.clipboard.writeText(form.mobileNumber)}
              >
                <svg width="15" height="15" viewBox="0 0 10 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M2.15299 2.29682V1.11738C2.15299 0.860623 2.36335 0.652346 2.62267 0.652346H6.51295C6.63667 0.651989 6.75835 0.700108 6.84871 0.79088L9.37087 3.32264C9.45931 3.4114 9.50611 3.52926 9.50611 3.64914V10.1865C9.50611 10.4433 9.29587 10.6516 9.03643 10.6516H7.84531V11.8309C7.84531 12.0878 7.63495 12.2959 7.37563 12.2959H0.961868C0.702428 12.2959 0.492188 12.0878 0.492188 11.8309V2.76185C0.492188 2.50498 0.702428 2.29682 0.961868 2.29682H2.15299ZM2.15299 2.76185H0.961868V11.8309H7.37563V10.6516H2.62267C2.36335 10.6516 2.15299 10.4433 2.15299 10.1865V2.76185ZM6.04459 1.11738H2.62267V10.1865H9.03643V4.11417H6.51427C6.25495 4.11417 6.04459 3.90601 6.04459 3.64914V1.11738ZM6.51427 1.11738V3.64914H9.03643L6.51427 1.11738Z"
                    fill="#566577"
                  />
                </svg>
              </div>
            </Tooltip>
          </div>

          {/* Email */}
          <div className="flex items-center gap-2">
            <div className="flex gap-2 items-center">
              <svg width="18" height="15" viewBox="0 0 18 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M7.28372 0.179688H10.7111C12.2425 0.179675 13.4556 0.179664 14.4049 0.3073C15.3819 0.438657 16.1727 0.715421 16.7964 1.33906C17.42 1.96269 17.6968 2.75348 17.8281 3.7305C17.9558 4.67985 17.9557 5.89288 17.9557 7.42435V7.51836C17.9557 9.04983 17.9558 10.2629 17.8281 11.2122C17.6968 12.1892 17.42 12.98 16.7964 13.6037C16.1727 14.2273 15.3819 14.5041 14.4049 14.6354C13.4556 14.763 12.2425 14.763 10.7111 14.763H7.28372C5.75225 14.763 4.53922 14.763 3.58988 14.6354C2.61286 14.5041 1.82207 14.2273 1.19843 13.6037C0.574796 12.98 0.298032 12.1892 0.166675 11.2122C0.039039 10.2629 0.0390496 9.04984 0.0390628 7.51837V7.42434C0.0390496 5.89287 0.039039 4.67985 0.166675 3.7305C0.298032 2.75348 0.574796 1.96269 1.19843 1.33906C1.82207 0.715421 2.61286 0.438657 3.58988 0.3073C4.53922 0.179664 5.75225 0.179675 7.28372 0.179688ZM3.75644 1.54615C2.91803 1.65887 2.43499 1.87027 2.08232 2.22294C1.72964 2.57562 1.51825 3.05865 1.40553 3.89706C1.29039 4.75345 1.28906 5.88234 1.28906 7.47135C1.28906 9.06037 1.29039 10.1893 1.40553 11.0456C1.51825 11.8841 1.72964 12.3671 2.08232 12.7198C2.43499 13.0724 2.91803 13.2838 3.75644 13.3966C4.61282 13.5117 5.74171 13.513 7.33073 13.513H10.6641C12.2531 13.513 13.382 13.5117 14.2384 13.3966C15.0768 13.2838 15.5598 13.0724 15.9125 12.7198C16.2652 12.3671 16.4765 11.8841 16.5893 11.0456C16.7044 10.1893 16.7057 9.06037 16.7057 7.47135C16.7057 5.88234 16.7044 4.75345 16.5893 3.89706C16.4765 3.05865 16.2652 2.57562 15.9125 2.22294C15.5598 1.87027 15.0768 1.65887 14.2384 1.54615C13.382 1.43102 12.2531 1.42969 10.6641 1.42969H7.33073C5.74171 1.42969 4.61282 1.43102 3.75644 1.54615ZM3.51726 3.73791C3.73824 3.47273 4.13234 3.43691 4.39751 3.65788L6.19659 5.15712C6.97406 5.805 7.51384 6.25337 7.96955 6.54646C8.41068 6.83018 8.70984 6.92542 8.9974 6.92542C9.28496 6.92542 9.58411 6.83018 10.0252 6.54646C10.481 6.25337 11.0207 5.805 11.7982 5.15711L13.5973 3.65788C13.8625 3.43691 14.2566 3.47273 14.4775 3.73791C14.6985 4.00308 14.6627 4.39718 14.3975 4.61816L12.5671 6.1435C11.8285 6.75905 11.2298 7.25796 10.7014 7.59779C10.151 7.95179 9.61496 8.17542 8.9974 8.17542C8.37983 8.17542 7.84379 7.95179 7.29338 7.59779C6.76499 7.25796 6.16633 6.75905 5.4277 6.14351L3.59728 4.61816C3.33211 4.39718 3.29628 4.00308 3.51726 3.73791Z"
                  fill="#566577"
                />
              </svg>
            </div>
            <p className="text-[#0E233C] dark:text-white truncate flex-grow">{form.email}</p>
            <Tooltip content="Copy email">
              <div className="cursor-pointer p-1.5 rounded-md border dark:border-gray-700" onClick={() => navigator.clipboard.writeText(form.email)}>
                <svg width="15" height="15" viewBox="0 0 10 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M2.15299 2.29682V1.11738C2.15299 0.860623 2.36335 0.652346 2.62267 0.652346H6.51295C6.63667 0.651989 6.75835 0.700108 6.84871 0.79088L9.37087 3.32264C9.45931 3.4114 9.50611 3.52926 9.50611 3.64914V10.1865C9.50611 10.4433 9.29587 10.6516 9.03643 10.6516H7.84531V11.8309C7.84531 12.0878 7.63495 12.2959 7.37563 12.2959H0.961868C0.702428 12.2959 0.492188 12.0878 0.492188 11.8309V2.76185C0.492188 2.50498 0.702428 2.29682 0.961868 2.29682H2.15299ZM2.15299 2.76185H0.961868V11.8309H7.37563V10.6516H2.62267C2.36335 10.6516 2.15299 10.4433 2.15299 10.1865V2.76185ZM6.04459 1.11738H2.62267V10.1865H9.03643V4.11417H6.51427C6.25495 4.11417 6.04459 3.90601 6.04459 3.64914V1.11738ZM6.51427 1.11738V3.64914H9.03643L6.51427 1.11738Z"
                    fill="#566577"
                  />
                </svg>
              </div>
            </Tooltip>
          </div>
        </div>
      </div>

      {/* Buttons */}
      <Button className="rounded-2xl" tertiary label="Edit Profile" customIcon="edit" onClick={() => setSignUpDialogVisibilty(true)} />

      {/* !* we are using refetch here to refetch handleget in EditProf comp */}
      {isSignUpDialogVisible && <EditProfile refetch={handleGet} onClose={() => setSignUpDialogVisibilty(false)} />}
    </div>
  );
};
