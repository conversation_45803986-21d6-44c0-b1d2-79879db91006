// React
import { useContext, useEffect, useState } from 'react';
import { useParams, Navigate, useNavigate } from 'react-router-dom';

// Date format
import { addDays, format, isValid } from 'date-fns';

// Flowbite
import { Tooltip } from 'flowbite-react';

// Context
import { AppContext } from '/src/components/provider';

// Core
import {
  Icon,
  CustomIcon,
  ToggleFilter,
  useFetchList,
  Button,
  TestDifficulty,
  TestSeniorityLevel,
  AvarageScore,
  StaticData,
  useScreenSize,
  Table,
  FormatDate,
  useNotify,
  Api,
  Enums,
  EnumText,
} from '/src';

// Components
import { InlineFilter } from '../../inline-filter';
import { PlanManagement } from './plan-management';

export const PlanAndBillingsProfile = () => {
  // User Data
  const { userData } = useContext(AppContext);

  // Permissions
  const isPermitted = userData?.roles.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const isSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));

  // State
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);
  const [isPlanManagementVisible, setPlanManagementVisibilty] = useState(false);
  const [organizationPlanOverviewData, setOrganizationPlanOverviewData] = useState([]);

  // Hooks
  const navigate = useNavigate();
  const screen = useScreenSize();
  const { notify } = useNotify();
  const { id } = useParams();
  const initialFilters = {
    seniortyLevel: {
      label: 'Seniorty Level',
      enum: 'QuizDifficulty',
    },
    ...(userData.trackId
      ? {}
      : {
          category: {
            label: 'Category',
            lookup: 'category',
          },
        }),
    difficulty: {
      label: 'Difficulty',
      enum: 'QuestionDifficulty',
    },
  };
  const { ready, loading, setLoading, list, count, search, pagination, filters, setFilters, refresh, handleDates } = useFetchList(
    `/subscription/single/organization`,
    {
      search: '',
      pagination: {
        page: 1,
        size: 20,
      },
      filters: initialFilters,
    }
  );

  const filterFeedData = Object.keys(initialFilters);

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get(`organizations/plan/overview/${id}`);
      setOrganizationPlanOverviewData(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '—';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'medium',
      timeStyle: 'short',
    }).format(date);
  };

  const handleStatus = (type) => {
    if (type === 2) {
      // Green
      return {
        className: 'bg-[#ecfdf3] text-[#067647] border border-[#abefc6]',
      };
    }
    if (type === 4) {
      // Red
      return {
        className: 'bg-[#fef3f2] text-[#B42318] border border-[#FECDCA]',
      };
    }
    if (type === 1) {
      // Yellow
      return {
        className: 'bg-[#f9f7ce] text-[#ab6512] border border-[#fee3ca]',
      };
    }
    if (type === 3) {
      // Blue
      return {
        className: 'bg-[#eff6ff] text-[#374151]   border border-[#bfdbfe]',
      };
    }
    if (type === 5) {
      return {
        className: 'bg-[#f3f4f6] text-[#1d4ed8]  border border-[#d1d5db]',
      };
    }
  };

  // Use these styles to make black overlay visible and not scrollable
  // Make the scroll in list pages only be smooth
  useEffect(() => {
    if (isPlanManagementVisible) {
      document.querySelector('html').classList.add('overflow-x-hidden');
    } else {
      document.querySelector('html').classList.remove('overflow-x-hidden');
    }
  }, [isPlanManagementVisible]);

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  useEffect(() => {
    handleGet();
  }, []);

  return (
    <div>
      <div className="space-y-4">
        {organizationPlanOverviewData?.length > 0 && (
          <div className="flex justify-between items-center p-5 rounded-xl shadow-md border bg-gradient-to-b from-white to-[#EADBF7]">
            <div className="space-y-2">
              {organizationPlanOverviewData?.map((plan, _id) => {
                <>
                  <h2 className="text-[#5E2EC3] font-semibold text-lg">{plan.name}</h2>
                  <div className="flex items-end gap-2 text-2xl font-bold text-black">
                    {plan.price}
                    <span className="text-sm font-normal text-gray-500">/per year</span>
                    <span className="text-sm text-[#566577] font-semibold">Expiry date {formatDate(plan.endDate)}</span>
                  </div>
                  <Button label="Manage Plan" className="rounded-2xl" tertiary onClick={() => setPlanManagementVisibilty(true)} />
                </>;
              })}
            </div>
          </div>
        )}

        {/* Need to add to table main buttons */}
        {/* <div className="calendar flex flex-row justify-end items-center gap-2 text-gray-700 dark:text-gray-300 rounded-lg h-fit">
          <Datepicker
            className="inline-block w-full sm:w-44"
            // onSelectedDateChanged={() => {}}
            showTodayButton={false}
            showClearButton={false}
            // value=""
          />
        </div> */}

        <Table
          ready={ready}
          loading={loading}
          title="Plans and Billings List"
          searchPlaceholder="Search for plans and billings..."
          count={count}
          search={search}
          filters={filters}
          // setFilters={setFilters}
          // filterFeedData={filterFeedData}
          // drawerFilter={{
          //   filterCountNumber: filterCountNumber,
          //   isShowDrawerFilter: isShowDrawerFilter,
          //   setShowDrawerFilter: setShowDrawerFilter,
          // }}
          pagination={pagination}
          rows={list}
          backupRows={backupList}
          slots={{
            invoiceId: (value) => {
              return (
                !!value && (
                  <div className="flex items-center gap-2">
                    <p className="text-black dark:text-white font-medium truncate">{value || '—'}</p>
                    <Tooltip
                      content="Copy Invoice ID"
                      placement="bottom"
                      arrow={false}
                      className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                    >
                      <span
                        onClick={() => {
                          navigator.clipboard.writeText(value);
                          notify('Invoice ID copied');
                        }}
                        className="cursor-pointer"
                      >
                        <Icon
                          icon="ooui:copy-ltr"
                          className="text-gray-500 dark:text-gray-400 text-base hover:text-gray-700 dark:hover:text-gray-300"
                          width="16"
                        />
                      </span>
                    </Tooltip>
                  </div>
                )
              );
            },
            status: (_, row) => {
              return (
                <span className={`${handleStatus(row?.status)?.className} px-2 py-1 rounded-3xl`}>
                  <EnumText name="SubscriptionStatus" value={row?.status} />
                </span>
              );
            },
            amount: (_, row) => {
              return (
                <p className="dark:text-white">
                  {row?.price} {row?.currency}
                </p>
              );
            },
            planName: (value) => {
              return <p className="dark:text-white">{value}</p>;
            },
          }}
          columns={[
            {
              key: 'invoiceId',
              label: 'Invoice',
              primary: true,
              width: '22%',
            },
            {
              key: 'status',
              label: 'Status',
              primary: true,
              width: '18%',
            },
            {
              key: 'amount',
              label: 'Amount',
              // primary: true,
              width: '15%',
            },
            {
              key: 'planName',
              label: 'Plan',
              // primary: true,
              width: '15%',
            },
          ]}
          // multiSelectedRow={{
          //   selectedIds: selectedIds,
          //   setSelectedIds: setSelectedIds,
          //   handleArchiveSelectedIds: handleArchiveSelectedIds,
          // }}
          noDataFound={{
            customIcon: 'applicant',
            message: 'No assessment created yet',
          }}
          noDataFoundIconWidth="60"
          noDataFoundIconHeight="60"
          showMoreMap={showMoreMap}
          setShowMoreMap={setShowMoreMap}
          // addButtonLabel=""
          // onClickAdd={() => {}}
          // actions={[]}
          hideJumbotron
          isScrollableTabsExists
        />
      </div>

      {isPlanManagementVisible && <PlanManagement onClose={() => setPlanManagementVisibilty(false)} />}
    </div>
  );
};
