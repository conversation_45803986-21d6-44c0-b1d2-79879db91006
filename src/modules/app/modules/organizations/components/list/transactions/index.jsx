// React
import { useContext, useEffect, useState } from 'react';
import { Navigate, useNavigate, useParams } from 'react-router-dom';

// Flowbite
import { Datepicker, Tooltip } from 'flowbite-react';

// Context
import { AppContext } from '/src/components/provider';

// Date Format
import { isValid, format } from 'date-fns';

// Core
import { Icon, useFetchList, EnumText, Table, FormatDate, useScreenSize, useNotify } from '/src';

export const TransactionsList = () => {
  // User Data
  const { userData } = useContext(AppContext);
  const { notify } = useNotify();

  // Permissions
  const isPermitted = userData?.roles.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const isSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));

  // State
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);

  // Hooks
  const navigate = useNavigate();
  const screen = useScreenSize();
  const { id } = useParams();
  const { ready, loading, count, list, search, filters, setFilters, pagination } = useFetchList(`/subscription/list`, {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: {
      status: {
        label: 'Status',
        enum: 'SubscriptionStatus',
      },
    },
  });

  const handleStatus = (type) => {
    if (type === 2) {
      // Green
      return {
        className: 'bg-[#ecfdf3] text-[#067647] border border-[#abefc6]',
      };
    }
    if (type === 4) {
      // Red
      return {
        className: 'bg-[#fef3f2] text-[#B42318] border border-[#FECDCA]',
      };
    }
    if (type === 1) {
      // Yellow
      return {
        className: 'bg-[#f9f7ce] text-[#ab6512] border border-[#fee3ca]',
      };
    }
    if (type === 3) {
      // Blue
      return {
        className: 'bg-[#eff6ff] text-[#374151]   border border-[#bfdbfe]',
      };
    }
    if (type === 5) {
      return {
        className: 'bg-[#f3f4f6] text-[#1d4ed8]  border border-[#d1d5db]',
      };
    }
  };

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  return (
    <>
      {/* Export button */}
      {/* <div className="flex justify-end">
        <button className="inline-flex items-center gap-2 justify-center h-10 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
          <Icon icon="ion:filter" width="22" />
          <span className="hidden sm:block">Export</span>
        </button>
      </div> */}

      <Table
        ready={ready}
        loading={loading}
        title="Transactions List"
        searchPlaceholder="Search for organization...."
        count={count}
        search={search}
        filters={filters}
        setFilters={setFilters}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        columns={[
          {
            key: 'organizationName',
            label: 'Organization Name',
            width: '18%',
            primary: true,
          },
          {
            key: 'organizationEmail',
            label: 'Organization Email',
            width: '18%',
            primary: true,
          },
          {
            key: 'invoiceId',
            label: 'Invoice',
            width: '18%',
            primary: true,
          },
          {
            key: 'status',
            label: 'Status',
            width: '10%',
            primary: true,
          },
          {
            key: 'amount',
            label: 'Amount',
            width: '15%',
          },
          // {
          //   key: 'actions',
          //   label: 'Actions',
          //   width: '5%',
          //   buttons: () => [
          //     {
          //       label: 'Download',
          //       customIcon: 'cloudArrowDown',
          //       color: 'text-black dark:text-white',
          //       onClick: () => navigate(`/app/organizations/profile/${id}`),
          //     },
          //   ],
          // },
        ]}
        slots={{
          organizationEmail: (value) => {
            return (
              <div className="flex items-center gap-2">
                <div className="truncate max-w-[85%]">
                  {value ? (
                    <Tooltip
                      content={value}
                      placement="bottom"
                      arrow={false}
                      className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                    >
                      <span className="text-[#626874] dark:text-gray-400 truncate">{value}</span>
                    </Tooltip>
                  ) : (
                    <span className="text-[#626874] dark:text-gray-400">—</span>
                  )}
                </div>
                {value && (
                  <Tooltip
                    content="Copy Email"
                    placement="bottom"
                    arrow={false}
                    className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                  >
                    <span
                      onClick={() => {
                        navigator.clipboard.writeText(value);
                        notify('Email copied');
                      }}
                      className="cursor-pointer"
                    >
                      <Icon
                        icon="ooui:copy-ltr"
                        className="text-gray-500 dark:text-gray-400 text-base hover:text-gray-700 dark:hover:text-gray-300"
                        width="16"
                      />
                    </span>
                  </Tooltip>
                )}
              </div>
            );
          },
          invoiceId: (value) => {
            return (
              <div className="flex items-center gap-2">
                <p className="text-black dark:text-white font-medium truncate">{value || '—'}</p>
                <Tooltip
                  content="Copy Invoice ID"
                  placement="bottom"
                  arrow={false}
                  className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                >
                  <span
                    onClick={() => {
                      navigator.clipboard.writeText(value);
                      notify('Invoice ID copied');
                    }}
                    className="cursor-pointer"
                  >
                    <Icon
                      icon="ooui:copy-ltr"
                      className="text-gray-500 dark:text-gray-400 text-base hover:text-gray-700 dark:hover:text-gray-300"
                      width="16"
                    />
                  </span>
                </Tooltip>
              </div>
            );
          },
          createdAt: (_, row) => {
            return <FormatDate customDate={row?.startDate} />;
          },
          status: (value) => {
            return (
              <span className={`${handleStatus(value)?.className} px-2 py-1 rounded-3xl`}>
                <EnumText name="SubscriptionStatus" value={value} />
              </span>
            );
          },
          amount: (_, row) => {
            return (
              <p className="dark:text-white">
                {row?.price} {row?.currency}
              </p>
            );
          },
        }}
        // multiSelectedRow={{
        //   selectedIds: selectedIds,
        //   setSelectedIds: setSelectedIds,
        //   handleArchiveSelectedIds: handleArchiveSelectedIds,
        // }}
        noDataFound={{
          customIcon: 'applicant',
          message: 'No assessment created yet',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        // addButtonLabel=""
        // onClickAdd={() => {}}
        // actions={[]}
        hideJumbotron
        isScrollableTabsExists
      />
    </>
  );
};
