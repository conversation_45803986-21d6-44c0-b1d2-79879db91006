import React, { useRef, useState } from 'react';
import { En<PERSON>, Button, ImageUploader } from '/src';
import { UploadDialog } from './upload-dialog';

export const AvatarList = () => {
  // State
  const [uploadDiologVisible, setUploadDiologVisible] = useState(false);

  return (
    <>
      <div className="px-3 py-4 border border-[#F4F4F4] rounded-lg space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex gap-2 items-center">
            <p className="font-medium dark:text-white">Avatars</p>
            <div className="py-[1px] px-2 text-white bg-[#8D5BF8] rounded-lg text-sm">{Enums?.AiAvatarModel.length}</div>
          </div>
          <Button label="Personalize Avatar" size="sm" onClick={() => setUploadDiologVisible(true)} />
        </div>
        <div className="grid xssm:grid-cols-2 md:grid-cols-4 gap-10">
          {Enums?.AiAvatarModel?.map((model) => (
            <div className="flex items-center justify-center border border-[#F6F7FA] rounded-md px-4 py-3 gap-6 shadow-sm">
              <p htmlFor={model.value} className="ml-2 dark:text-inputDarkLabel capitalize cursor-pointer font-semibold">
                {model?.value}
              </p>
              <img
                src={`/images/models/${model?.iconPathOut}`}
                alt="Icon"
                className="h-20 rounded-xl cursor-pointer"
                onClick={() => setFieldValue('avatarName')(model?.value)}
              />
            </div>
          ))}
        </div>
      </div>
      {uploadDiologVisible && <UploadDialog onClose={() => setUploadDiologVisible(false)} />}
    </>
  );
};
