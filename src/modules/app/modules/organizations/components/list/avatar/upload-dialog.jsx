import React, { useState } from 'react';
import { Dialog, ImageUploader, TextInput, Form, Button, useForm, useNotify } from '/src';

export const UploadDialog = ({ onClose }) => {
  const { notify } = useNotify();

  // Form state
  const { form, setFieldValue } = useForm({
    image: '',
    name: ''
  });

  // Loading state
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      setLoading(true);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      notify('Avatar uploaded successfully!');
      onClose();
    } catch (error) {
      notify.error('Failed to upload avatar');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      size="lg"
      show
      popup
      onClose={onClose}
      overflowVisible={true}
      modalHeader="Personalize Your Avatar"
      subModalHeader={
        <p className="text-sm text-gray-400">Upload your photo, see your AI avatar in 24 hours!</p>
      }
    >
      <Form onSubmit={handleSubmit}>
        <div className="space-y-5">
          <ImageUploader
            label="Upload Image"
            value={form.image}
            onChange={setFieldValue('image')}
          />

          <TextInput
            label="Name"
            placeholder="Enter avatar's name"
            value={form.name}
            onChange={setFieldValue('name')}
          />

          <Button
            type="submit"
            label="Send"
            className='w-full'
            loading={loading}
            disabled={!form.image || loading}
          />
        </div>
      </Form>
    </Dialog>
  );
};
