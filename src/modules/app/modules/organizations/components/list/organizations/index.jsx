// React
import { useContext, useEffect, useState } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';

// Flowbite
import {} from 'flowbite-react';

// Context
import { AppContext } from '/src/components/provider';

// Format Date
import { addDays, format, isValid } from 'date-fns';

// Core
import {
  Icon,
  CustomIcon,
  useFetchList,
  Button,
  TestDifficulty,
  TestSeniorityLevel,
  AvarageScore,
  StaticData,
  useScreenSize,
  Table,
  FormatDate,
  useNotify,
} from '/src';

// Flowbite
import { Tooltip } from 'flowbite-react';

// Components
import { InlineFilter } from '../../inline-filter';

export const OrganizationList = () => {
  // User Data
  const { userData } = useContext(AppContext);

  // Permissions
  const isPermitted = userData?.roles.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const isSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));

  // State
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);
  const [activeInlineFilter, setActiveInlineFilter] = useState(0);

  // Hooks
  const navigate = useNavigate();
  const screen = useScreenSize();
  const { notify } = useNotify();

  const initialFilters = {
    plan: {
      label: 'Plan',
      enum: 'PlanType',
    },
  };

  const { ready, loading, count, list, refresh, filters, setFilters, search, pagination, handleDates } = useFetchList(`/organizations/list`, {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: initialFilters,
  });

  const filterFeedData = Object.keys(initialFilters);

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  return (
    <div className="space-y-4">
      {/* we will use it later */}
      {/* <InlineFilter
          // data={data}
          selectedInlineFilter={{
            activeInlineFilter: activeInlineFilter,
            setActiveInlineFilter: setActiveInlineFilter,
          }}
        /> */}

      {/* <div className="calendar flex flex-row justify-end items-center gap-2 text-gray-700 dark:text-gray-300 rounded-lg h-fit">
          <Datepicker
            className="inline-block w-full sm:w-44"
            // onSelectedDateChanged={() => {}}
            showTodayButton={false}
            showClearButton={false}
            // value=""
          />
        </div> */}

      <Table
        ready={ready}
        loading={loading}
        title="Organizations List"
        searchPlaceholder="Search for organization..."
        count={count}
        search={search}
        filters={filters}
        setFilters={setFilters}
        // filterFeedData={filterFeedData}
        // drawerFilter={{
        //   filterCountNumber: filterCountNumber,
        //   isShowDrawerFilter: isShowDrawerFilter,
        //   setShowDrawerFilter: setShowDrawerFilter,
        // }}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        slots={{
          name: (_, row) => {
            return (
              <div className="flex items-center gap-2">
                <img src="/images/avatar-male-img.png" alt="Avatar" className="rounded-full w-10 h-10 object-cover" />
                <p className="text-black dark:text-white font-medium truncate">{row?.name}</p>
              </div>
            );
          },
          email: (_, row) => {
            return (
              <div className="flex relative gap-2">
                <div className="w-full">
                  <div className="flex items-center gap-2 relative">
                    <div className="truncate max-w-[85%]">
                      {row?.email ? (
                        <Tooltip
                          content={row.email}
                          placement="bottom"
                          arrow={false}
                          className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                        >
                          <span className="text-[#626874] dark:text-gray-400 truncate">{row.email}</span>
                        </Tooltip>
                      ) : (
                        <span className="text-[#626874] dark:text-gray-400">—</span>
                      )}
                    </div>
                    {row?.email && (
                      <span
                        onClick={() => {
                          navigator.clipboard.writeText(row.email);
                          notify('Email copied');
                        }}
                        className="inline-block cursor-pointer text-gray-500 dark:text-gray-400"
                      >
                        <Tooltip
                          content="Copy Email"
                          placement="bottom"
                          arrow={false}
                          className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                        >
                          <Icon icon="ooui:copy-ltr" className="relative text-[#798296] text-base" />
                        </Tooltip>
                      </span>
                    )}
                  </div>
                </div>
              </div>
            );
          },
          plan: (_, row) => {
            return '—';
          },
          lastActive: (_, row) => {
            return (
              <p className="text-[#656575] truncate">
                <FormatDate customDate={row?.startDate} />
              </p>
            );
          },
        }}
        columns={[
          {
            key: 'name',
            label: 'Organization Name',
            primary: true,
            width: '20%',
          },
          {
            key: 'email',
            label: 'Organization Email',
            primary: true,
            width: '20%',
          },
          {
            key: 'plan',
            label: 'Plan',
            primary: true,
            width: '23%',
          },
          {
            key: 'lastActive',
            label: 'Last Active',
            primary: true,
            width: '18%',
          },
          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_, row) {
              return [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  path: `/app/organizations/profile/${row?._id}`,
                },
                // {
                //   label: 'Assign',
                //   customIcon: 'assign',
                //   iconWidth: '22',
                //   iconHeight: '22',
                //   color: 'text-black dark:text-white',
                //   // onClick: () => {},
                //   dropDown: [
                //     {
                //       label: 'Interview',
                //       color: 'text-black dark:text-white',
                //       customIcon: 'interview',
                //       element: (
                //         <span
                //           className={`w-24 text-xs px-2 py-1 rounded-full shadow-md font-bold tracking-wide bg-magic-gradient bg-[length:200%]  text-white overflow-hidden`}
                //         >
                //           AI Magic ✨
                //         </span>
                //       ),
                //       onClick: () => {
                //         if (isSuperAdmin || userData?.features?.assignInterview > 0) {
                //           setAssignInterviewTestVisible(true);
                //           setApplicantDetails(row);
                //         } else {
                //           setNeedSubscription(true);
                //         }
                //       },
                //     },
                //   ],
                // },
              ];
            },
          },
        ]}
        // multiSelectedRow={{
        //   selectedIds: selectedIds,
        //   setSelectedIds: setSelectedIds,
        //   handleArchiveSelectedIds: handleArchiveSelectedIds,
        // }}
        noDataFound={{
          customIcon: 'applicant',
          message: 'No assessment created yet',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        // addButtonLabel=""
        // onClickAdd={() => {}}
        // actions={[]}
        hideJumbotron
        isScrollableTabsExists
      />
    </div>
  );
};
