// React
import { useState, useEffect, useContext } from 'react';

// Flowbite
import { Datepicker, Select as SelectFlowbite } from 'flowbite-react';

// Core
import { Icon, Steps, useNotify, Api } from '/src';
// date
import { formatDistanceToNow, isToday, isYesterday, format } from 'date-fns';

// Components
import { AppContext } from '/src/components/provider';

export const ActivityList = () => {
  const { userData } = useContext(AppContext);
  const { notify } = useNotify();

  // Search
  const [search, setSearch] = useState('');
  const [activityLogData, setActivityLogData] = useState([]);

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get('organizations/activity/list');
      setActivityLogData(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  const formatLastActive = (dateString) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    // Check if date is valid before using date-fns functions
    if (isNaN(date.getTime())) return 'Invalid date';
    
    if (isToday(date)) {
      return 'Today';
    }
    if (isYesterday(date)) {
      return 'Yesterday';
    }
    return formatDistanceToNow(date, { addSuffix: true });
  };

  const steps = [
    {
      mainLabel: 'Today',
      data: activityLogData.map((item) => ({
        icon: item.action === 'Activated' ? 'arrowUpRight' : 'arrowDownRight',
        header: (
          <p>
            {userData.name} <span className="text-black font-medium underline">{item.name}</span> {item.action} organization{' '}
            <span className="text-black font-medium underline">{item.name}</span>
          </p>
        ),
        description: formatLastActive(item.lastActive),
      })),
    },
  ];

  const handleCountAllLogs = () => {
    let counter = 0;
    steps.map((step) => (counter = counter + step.data.length));
    return counter;
  };

  // Effects
  useEffect(() => {
    handleGet();
  }, []);

  return (
    <div className="space-y-4">
      {/* <h2 className="flex items-center gap-2 text-base dark:text-white">
        Activity Logs
        <span className="bg-[#f9f5ff] dark:bg-[#1f1a2e] text-primaryPurple text-sm font-medium rounded-full px-2.5 border">
          {handleCountAllLogs()}
        </span>
      </h2> */}

      <div className="flex justify-between items-center gap-2">
        {/* Search bar */}
        <div className="flex justify-between items-center grow space-y-0 rounded-lg relative">
          <Icon icon="carbon:search" width="20" className="size-5 text-gray-500 dark:text-gray-400 absolute left-3 pointer-events-none" />
          <input
            type="text"
            placeholder="Search..."
            className="w-full p-2 pl-10 dark:bg-gray-700 bg-gray-white text-[13.5px] text-gray-800 border border-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white truncate rounded-lg focus:ring-0 focus:border-gray-300 shadow-sm"
            value={search}
            onInput={(e) => setSearch(e.target.value)}
          />
        </div>

        <SelectFlowbite id="countries" required className="w-72">
          <option>All Companies</option>
          <option>First</option>
          <option>Second</option>
          <option>Third</option>
        </SelectFlowbite>

        <div className="calendar flex flex-row justify-end items-center gap-2 text-gray-700 dark:text-gray-300 rounded-lg h-fit">
          <Datepicker
            className="inline-block w-full sm:w-44"
            // onSelectedDateChanged={() => {}}
            showTodayButton={false}
            showClearButton={false}
            // value=""
          />
        </div>
      </div>

      <div className="space-y-8">
        {steps.map((step) => (
          <Steps key={step.data.icon} step={step} />
        ))}
      </div>
    </div>
  );
};
