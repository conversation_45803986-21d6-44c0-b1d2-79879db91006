import { useEffect, useState } from 'react';
import { Button, useForm, Form, useValidate, TextInput, Regex, Icon, RadioGroup, Dialog, Api, useNotify } from '/src';
import { useParams } from 'react-router-dom';

export const EditProfile = ({ onClose, refetch }) => {
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const { notify } = useNotify();

  const handleGet = async () => {
    try {
      const response = await Api.get(`organizations/single/${id}`);

      setFormValue({
        ...response.data,
        password: '',
        confirmPassword: '',
      });
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  const { isRequired, minLength, maxLength, validateRegex, validatePasswordRegex } = useValidate();

  const [showPassword, setShowPassword] = useState({ password: false, confirmPassword: false });
  const [editOrganization, setEditOrganization] = useState('');

  // Form
  const { form, setFieldValue, setFormValue, resetForm } = useForm({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    mobileNumber: '',
    location: '',
  });

  useEffect(() => {
    handleGet();
  }, []);

  const handlePost = async () => {
    try {
      const response = await Api.put(`organizations/single/${id}`, form);
      onClose();

      //   !*refetch here to reload and refetch new data
      refetch();
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  return (
    <Dialog modalHeader="Edit Profile" size="lg" show popup onClose={onClose} overflowVisible={true}>
      <Form className="space-y-5" onSubmit={handlePost}>
        <TextInput
          name="name"
          label="Name"
          placeholder="Name"
          disabled={loading}
          value={form.name}
          onChange={setFieldValue('name')}
          validators={[isRequired(), minLength(3), maxLength(100), validateRegex(Regex.name)]}
        />
        <TextInput
          name="email"
          label="Email"
          placeholder="Email"
          disabled={loading}
          value={form.email}
          onChange={setFieldValue('email')}
          validators={[isRequired(), validateRegex(Regex.email)]}
        />
        <div className="flex w-full">
          <div className="w-full">
            <TextInput
              label="Password"
              name="password"
              placeholder="Password"
              autoComplete="new-password"
              type={showPassword.password ? 'text' : 'password'}
              value={form.password}
              onChange={setFieldValue('password')}
              validators={[isRequired(), minLength(8), maxLength(50), validatePasswordRegex(Regex.password)]}
            />
          </div>

          <div className="mt-8" onClick={() => setShowPassword((prev) => ({ ...prev, password: !prev.password }))}>
            <Icon
              className="ml-3 p-5 w-8 h-8 rounded-md cursor-pointer bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400"
              width="25"
              icon={!showPassword.password ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
            />
          </div>
        </div>
        <div className="flex">
          <div className="w-full">
            <TextInput
              label="Confirm Password"
              name="confirm password"
              placeholder="Confirm password"
              type={showPassword.confirmPassword ? 'text' : 'password'}
              value={form.confirmPassword}
              onChange={setFieldValue('confirmPassword')}
              validators={[isRequired()]}
            />

            {form.password !== form.confirmPassword && form.confirmPassword ? (
              <label className="text-red-500 text-sm">Confirm password doesn't match the password</label>
            ) : null}
          </div>

          <div className="mt-8" onClick={() => setShowPassword((prev) => ({ ...prev, confirmPassword: !prev.confirmPassword }))}>
            <Icon
              className="ml-3 p-5 w-8 h-8 rounded-md cursor-pointer bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400"
              width="25"
              icon={!showPassword.confirmPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
            />
          </div>
        </div>

        <Button type="submit" label="update" icon="mdi:send" loading={loading} className="w-full" gradientMonochrome="purple" />
      </Form>
    </Dialog>
  );
};
