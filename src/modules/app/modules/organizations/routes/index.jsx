import { Navigate, Outlet } from 'react-router-dom';

import { OrganizationsMainLayout } from '../layouts/main';

import { OrganizationsListPage } from '../pages/list';
import { OrganizationsProfile } from '../pages/profile';

export default [
  {
    path: 'organizations',
    element: <OrganizationsMainLayout />,
    children: [
      {
        path: '',
        element: <Navigate to="/app/organizations/list" />,
      },
      {
        path: '',
        element: <Outlet />,
        loader() {
          return {
            label: 'Organization',
            title: 'Organization Management',
            subtitle: 'Here, you can manage organizations, track performance and monitor activity.',
          };
        },
        children: [
          {
            path: 'list',
            element: <OrganizationsListPage />,
          },
          {
            path: 'profile/:id',
            element: <OrganizationsProfile />,
            loader() {
              return {
                label: 'Organization’s Profile',
                title: '',
              };
            },
          },
        ],
      },
    ],
  },
];
