// React
import { useState } from 'react';

// Core
import { Jumbotron, ScrollableTabs } from '/src';

// Components
import { ProfileDetails } from '../components/profile/profile-details';
import { OverviewProfile } from '../components/profile/overview';
import { UsersProfile } from '../components/profile/users';
import { PlanAndBillingsProfile } from '../components/profile/plan-and-billings';
import { TicketsProfile } from '../components/profile/tickets';
import { ActivityProfile } from '../components/profile/activity';

export const OrganizationsProfile = () => {
  // State
  const [activeTab, setActiveTab] = useState(0);

  // Data
  const tabs = [
    {
      title: 'overview',
      component: (
        <OverviewProfile
          selectedTab={{
            activeTab: activeTab,
            setActiveTab: setActiveTab,
          }}
        />
      ),
    },
    {
      title: 'users',
      component: (
        <UsersProfile
          selectedTab={{
            activeTab: activeTab,
            setActiveTab: setActiveTab,
          }}
        />
      ),
    },
    {
      title: 'plan & billings',
      component: (
        <PlanAndBillingsProfile
          selectedTab={{
            activeTab: activeTab,
            setActiveTab: setActiveTab,
          }}
        />
      ),
    },
    // {
    //   title: 'tickets',
    //   component: (
    //     <TicketsProfile
    //       selectedTab={{
    //         activeTab: activeTab,
    //         setActiveTab: setActiveTab,
    //       }}
    //     />
    //   ),
    // },
    // {
    //   title: 'activity logs',
    //   component: (
    //     <ActivityProfile
    //       selectedTab={{
    //         activeTab: activeTab,
    //         setActiveTab: setActiveTab,
    //       }}
    //     />
    //   ),
    // },
  ];

  return (
    <div className="space-y-3">
      <Jumbotron />

      <ProfileDetails />

      <ScrollableTabs
        data={tabs}
        selectedTab={{
          activeTab: activeTab,
          setActiveTab: setActiveTab,
        }}
      />

      {tabs[activeTab]?.component}
    </div>
  );
};
