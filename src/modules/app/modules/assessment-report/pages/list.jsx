// React
import { useState } from 'react';

// Core
import { Jumbotron, ScrollableTabs } from '/src';

// Components
import { TestList } from '../components/list/test-list';
import { ScreeningList } from '../components/list/screening-list';
import { InterviewList } from '../components/list/interview-list';

export const AssessmentReportPage = () => {
  // states
  const [activeTab, setActiveTab] = useState(0);

  // tabs
  const tabs = [
    { title: 'Screenings', data: <ScreeningList /> },
    { title: 'Tests ', data: <TestList /> },
    { title: 'Interviews', data: <InterviewList /> },
  ];

  return (
    <div className="space-y-4">
      <Jumbotron />

      <ScrollableTabs
        data={tabs}
        selectedTab={{
          activeTab: activeTab,
          setActiveTab: setActiveTab,
        }}
      />

      {tabs[activeTab].data}
    </div>
  );
};
