// React
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
// Date formate
import { addDays, format, isValid, formatDistanceToNow } from 'date-fns';

// Core
import { Jumbotron, Button, CustomIcon, Icon, ScrollableTabs, useNotify, Card, Api, useForm, EnumText, NoDataFound } from '/src';

// Components
import { CandidateProfileCard } from '../components/single/candiadte-card';
import { GeneratedLinks } from '../components/single/generated-links';
import { AssignedApplicants } from '../components/single/assigned-applicants';
import { ReviewDrawer } from '../../assessments/components/create/review-drawer';

export const AssessmentReport = () => {
  // State
  const [activeTab, setActiveTab] = useState(0);
  const [isShowReviewDrawer, setIsShowReviewDrawer] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedQuestionsID, setSelectedQuestionsID] = useState({});
  const [anyQuestionHasEditMode, setAnyQuestionHasEditMode] = useState({});
  const [apiData, setApiData] = useState([]);
  const [candidateCardData, setCandidateCardData] = useState('');

  // Hooks
  const { notify } = useNotify();
  const { type, id, quizId } = useParams();

  const tabs = [
    { title: 'Assigned Applicants', data: <AssignedApplicants /> },
    { title: 'Generated Links', data: <GeneratedLinks /> },
  ];

  const cards = [{ data: <CandidateProfileCard data={candidateCardData[activeTab]} /> }];

  const formatDate = (customDate) => {
    const date = new Date(customDate || Date.now());
    if (!isValid(date)) {
      return 'Invalid date';
    }
    return format(date, 'dd MMMM , yyyy');
  };

  const getBlocksCards = () => {
    return [
      {
        header: 'Experience Level',
        subHeader: <EnumText name={'QuizDifficulty'} value={apiData.seniorityLevel} />,
        icon: 'manInSuit',
      },
      {
        header: 'Questions',
        // Handle both questionIds and questions array for screening
        subHeader: apiData.numOfQuestions,
        icon: 'questionInBorder',
      },
      {
        header: 'Duration',
        subHeader: apiData.duration,
        icon: 'clockThree',
      },
      {
        header: 'Difficulty',
        subHeader: <EnumText name={'QuestionDifficulty'} value={apiData.difficulty} />,
        icon: 'charts',
      },
    ];
  };

  const { form, setFieldValue, setFormValue, resetForm } = useForm({
    title: '',
    description: '',
    seniorityLevel: '',
    difficulty: '',
    duration: '',
    skips: '',
  });

  // const handelGet = async () => {
  //   let assessment;
  //   switch (type) {
  //     case 'interview':
  //       assessment = 'quizzes/single';
  //       break;
  //     case 'screening':
  //       assessment = 'quizzes/single/phoneScreening';
  //       break;
  //     case 'test':
  //       assessment = 'quizzes/single';
  //       break;
  //     default:
  //       break;
  //   }

  //   try {
  //     setLoading(true);
  //     const response = await Api.get(`${assessment}/${id}`);
  //     // Handle different response structures
  //     const data = Array.isArray(response.data) ? response.data[0] : response.data;
  //   } catch (error) {
  //     notify.error(error?.response?.data?.message);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const handleGetData = async () => {
    try {
      setLoading(true);
      const response = await Api.get(`quizzes/single/${quizId}`);
      setApiData(response.data);
      setSelectedQuestionsID(Object.fromEntries(response.data.questionIds.map((questionId) => [questionId, true])));
    } catch (error) {
      notify.error(error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCardData = async () => {
    try {
      const response = await Api.get(`${type === 'interview' ? 'ai-interview' : 'submissions'}/top-applicants/${quizId}`);
      setCandidateCardData(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    }
  };

  // Set edit mode based on id presence
  useEffect(() => {
    if (quizId) {
      // handelGet();
      handleGetData();
      handleCardData();
    }
  }, [quizId]);

  // Use these styles to make black overlay visible and not scrollable
  // Make the scroll in list pages only be smooth
  useEffect(() => {
    if (isShowReviewDrawer) {
      document.querySelector('html').classList.add('overflow-x-hidden');
    } else {
      document.querySelector('html').classList.remove('overflow-x-hidden');
    }
  }, [isShowReviewDrawer]);

  return (
    <div>
      {/* Header Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <Jumbotron />
        <Button label="Preview" onClick={() => setIsShowReviewDrawer(true)} customIcon="eye" iconWidth="22" iconHeight="22" tertiary />
      </div>

      {/* Candidate Info Card */}
      <Card className="mb-6 space-y-3  p-5 ">
        <div className="text-[#667085] flex w-full justify-between">
          <p>
            Created by {apiData.authorName}, {formatDate(apiData.createdAt)}{' '}
          </p>
          <div className="flex gap-2 items-center">
            <p>{apiData.updatedAt ? `Updated ${formatDistanceToNow(new Date(apiData.updatedAt), { addSuffix: true })}` : 'Not updated yet'}</p>
            <CustomIcon definedIcon="clockTwo" height="25" width="25" />
          </div>
        </div>
        <div className="pt-1  space-y-2">
          <div className="flex w-full items-center justify-between">
            <h1 className="font-semibold text-[20px]">{apiData.title} </h1>
            {/* <Button label="Interactive AI Interview" tertiary customIcon="blueStars" className="text-[#5A399E] border-none shadow-sm" /> */}
          </div>
          <p className="text-[#667085] text-base font-normal">{apiData.description}</p>
          <div className="flex pt-3 items-center gap-2">
            {apiData?.topicName?.map((topicName) => (
              <div key={topicName} className="w-fit border rounded-full p-1 px-3  text-[13px] font-medium">
                {topicName}
              </div>
            ))}
          </div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4  !mt-6">
            {getBlocksCards().map((block) => (
              <div key={block.header} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="text-sm text-[#535862]">{block.header}</p>
                  <p className="text-lg font-semibold">{block.subHeader || '—'}</p>
                </div>
                <CustomIcon definedIcon={block.icon} />
              </div>
            ))}
          </div>

          {apiData?.categoryName && (
            <div className="flex items-center gap-2">
              <p className="text-[#535862BF] dark:text-white">Categories covered:</p>
              <div className="flex flex-wrap items-center gap-2">
                {apiData?.categoryName?.map((category) => (
                  <p className="px-2 py-1 bg-[#F4F4F5] text-[#667085] text-[11px] font-medium rounded-md">{category}</p>
                ))}
              </div>
            </div>
          )}

          {apiData?.subCategoryName && (
            <div className="flex items-center gap-2">
              <p className="text-[#535862BF] dark:text-white">Subcategories covered:</p>
              <div className="flex flex-wrap items-center gap-2">
                {apiData?.subCategoryName?.map((subCategory) => (
                  <p className="px-2 py-1 text-xs border font-medium rounded-full">{subCategory}</p>
                ))}
              </div>
            </div>
          )}
        </div>
      </Card>

      <div className="space-y-4">
        <h2 className="text-[18px] font-semibold">Top Performers</h2>

        {candidateCardData.length > 0 ? (
          <div className="flex gap-6">
            {candidateCardData.map((data, index) => (
              <CandidateProfileCard key={index} data={data} />
            ))}
          </div>
        ) : (
          <NoDataFound
            noDataFound={{
              customIcon: 'applicant',
              message: 'No top performers found',
            }}
          />
        )}
        <div className="space-y-2">
          <h1 className="font-semibold text-[20px]">Assessment Distribution </h1>
          <p className="text-[#667085] text-base font-normal"> Manage links and track applicants progress</p>{' '}
        </div>
        <ScrollableTabs
          data={tabs}
          selectedTab={{
            activeTab: activeTab,
            setActiveTab: setActiveTab,
          }}
        />
        <div className="mt-5">{tabs[activeTab].data}</div>
      </div>
      {isShowReviewDrawer && (
        <ReviewDrawer
          onClose={() => setIsShowReviewDrawer(false)}
          selectedQuestionsID={selectedQuestionsID}
          setSelectedQuestionsID={setSelectedQuestionsID}
          anyQuestionHasEditMode={anyQuestionHasEditMode}
          setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
          canRemoveQuestion={false}
        />
      )}
    </div>
  );
};
