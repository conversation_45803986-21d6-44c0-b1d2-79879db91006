import { Navigate } from 'react-router-dom';
import { AssessmentReportMainLayout } from '../layouts/main';
import { AssessmentReportPage } from '../pages/list';
import { AssessmentReport } from '../pages/single';

export default [
  {
    path: 'assessment-report',
    element: <AssessmentReportMainLayout />,
    children: [
      {
        path: '',
        element: <Navigate to="/app/assessment-report/list" />,
      },

      {
        path: 'list',
        element: <AssessmentReportPage />,
        loader() {
          return {
            label: 'Generated Assessments',
            // icon: 'material-symbols:analytics',
            title: 'Assessment Report',
            subtitle: 'View and manage all your created assessments for benchmarking and evaluation',
          };
        },
      },

      {
        path: 'view/:type/:quizId',
        element: <AssessmentReport />,
        loader({}) {
          return {
            label: '',
            icon: '',
            title: 'Assessment Report',
            subtitle: `Easily review and manage all your assessments for streamlined evaluation and tracking.`,
          };
        },
      },
    ],
  },
];
