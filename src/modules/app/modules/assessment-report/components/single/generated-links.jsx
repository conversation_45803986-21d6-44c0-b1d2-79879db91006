// React
import { useContext, useEffect, useState } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';

// Flowbite
import { Datepicker } from 'flowbite-react';
// Date formate
import { addDays, format, isValid } from 'date-fns';
// Context
import { AppContext } from '/src/components/provider';
import { useParams } from 'react-router-dom';

// Core
import {
  Icon,
  CustomIcon,
  ToggleFilter,
  useFetchList,
  Button,
  TestDifficulty,
  TestSeniorityLevel,
  AvarageScore,
  StaticData,
  useScreenSize,
  Table,
  FormatDate,
  Form,
  useForm,
  useValidate,
  Select,
  useNotify,
} from '/src';

// Components
import { ComponentOverlayInterview } from './component-overlay-interview';

export const GeneratedLinks = () => {
  // User Data
  const { quizId, type } = useParams();
  const { userData } = useContext(AppContext);

  // Permissions
  const isPermitted = userData?.roles.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const isSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));

  // State
  const [selectedIds, setSelectedIds] = useState([]);
  const [selectedRow, setSelectedRow] = useState(null);

  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);

  // Hooks
  const { form, setFieldValue, setFormValue, resetForm } = useForm({ filter: null });
  const { notify } = useNotify();
  const { isRequired } = useValidate();
  const navigate = useNavigate();
  const screen = useScreenSize();
  const initialFilters = {
    seniortyLevel: {
      label: 'Seniorty Level',
      enum: 'QuizDifficulty',
    },
  };
  const filterFeedData = Object.keys(initialFilters);
  const { ready, loading, setLoading, list, count, search, pagination, filters, setFilters, refresh, handleDates } = useFetchList(
    `${type === 'interview' ? 'ai-interview' : 'submissions'}/generated-links`,
    {
      search: '',
      pagination: {
        page: 1,
        size: 20,
      },
      filters: initialFilters,
      id: quizId,
    }
  );

  // state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(10);
  const [switch2, setSwitch2] = useState(true);
  const [showOverlay, setShowOverlay] = useState(false);

  const formatDate = (customDate) => {
    const date = new Date(customDate || Date.now());
    if (!isValid(date)) {
      return 'Invalid date';
    }
    return format(date, 'dd MMMM , yyyy');
  };

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  // fake data
  const testData = [
    {
      id: 1,
      name: 'https:www.thepass.ai/test_99988.. ',
      createdDate: 'Created by Admin. 29 April 2025',
      usage: 100,
      activeStatus: 'Active',
      expireDate: '23 June,2025',
      completionRate: 60,
      averageScore: 20,
    },
    {
      id: 2,
      name: 'https:www.thepass.ai/test_99988.. ',
      createdDate: 'Created by Admin. 29 April 2025',
      usage: 20,
      activeStatus: 'Active',
      expireDate: '23 June,2025',
      completionRate: 80,
      averageScore: 60,
    },
    {
      id: 3,
      name: 'https:www.thepass.ai/test_99988.. ',
      createdDate: 'Created by Admin. 29 April 2025',
      usage: 80,
      activeStatus: 'Active',
      expireDate: '23 June,2025',
      completionRate: 60,
      averageScore: 10,
    },
    {
      id: 4,
      name: 'https:www.thepass.ai/test_99988.. ',
      createdDate: 'Created by Admin. 29 April 2025',
      usage: 20,
      activeStatus: 'Expired',
      completionRate: 10,
      averageScore: 90,
    },
    {
      id: 5,
      name: 'https:www.thepass.ai/test_99988.. ',
      createdDate: 'Created by Admin. 29 April 2025',
      usage: 5,
      activeStatus: 'Expired',
      completionRate: 60,
      averageScore: 50,
    },
    {
      id: 6,
      name: 'https:www.thepass.ai/test_99988.. ',
      createdDate: 'Created by Admin. 29 April 2025',
      usage: 100,
      activeStatus: 'Expired',
      completionRate: 60,
      averageScore: 90,
    },
  ];

  return (
    <>
      <Table
        ready={ready}
        loading={loading}
        title="Generated Links"
        searchPlaceholder="Search for links..."
        count={count}
        // search={search}
        filters={filters}
        setFilters={setFilters}
        // filterFeedData={filterFeedData}
        // drawerFilter={{
        //   filterCountNumber: filterCountNumber,
        //   isShowDrawerFilter: isShowDrawerFilter,
        //   setShowDrawerFilter: setShowDrawerFilter,
        // }}
        pagination={pagination}
        // rows={list}
        rows={list}
        backupRows={backupList}
        slots={{
          generatedLink: (_, row) => {
            return (
              <div>
                <div className="flex gap-2">
                  <p className="text-black text-[15px] dark:text-white font-medium truncate">{row?.assessmentUrl}</p>
                  <CustomIcon
                    definedIcon="copy"
                    className="cursor-pointer"
                    onClick={() => {
                      navigator.clipboard.writeText(row?.assessmentUrl);
                      notify('Link copied');
                    }}
                  />
                </div>
                <p className="text-[#656575] truncate">Created At {formatDate(row?.dueDate)}</p>
              </div>
            );
          },
          usage: (_, row) => {
            return <div className="capitalize font-medium text-sm text-[#535862] truncate">{row?.assignedCount} use</div>;
          },
          activeStatus: (_, row) => {
            return (
              <div className="space-y-1">
                <p
                  className={`w-fit flex items-center gap-1.5 px-1.5 py-0.5 border border-[#D5D7DA] rounded-lg ${
                    row?.expired === null ? 'bg-white text-[#414651]' : 'bg-[#f4f5f7] text-[#7A8B9F]'
                  }`}
                >
                  {row?.expired === null ? (
                    <span className="size-2 bg-[#17B26A] rounded-full" />
                  ) : (
                    <span className="size-2 bg-[#F05454] rounded-full" />
                  )}
                  {row?.expired === null ? 'Active' : 'Expired'}
                </p>
              </div>
            );
          },
          completionRate: (_, row) => {
            return (
              <div className="w-fit ">
                <AvarageScore score={row?.completionRate} label={row?.scoreLabel} />
              </div>
            );
          },
          averageScore: (_, row) => {
            return (
              <div className="w-fit">
                <AvarageScore score={row?.averageScore} label={row?.scoreLabel} />
              </div>
            );
          },
        }}
        columns={[
          {
            key: 'generatedLink',
            label: 'Generated Link',
            primary: true,
            width: '26%',
          },
          {
            key: 'usage',
            label: 'Usage',
            primary: true,
            width: '18%',
          },
          {
            key: 'activeStatus',
            label: 'Status',
            // primary: true,
            width: '19%',
          },
          {
            key: 'completionRate',
            label: 'Completion Rate',
            // primary: true,
            width: '15%',
          },
          {
            key: 'averageScore',
            label: 'Average Score',
            // primary: true,
            width: '17%',
          },
          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_, row) {
              return [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  // path: `/app/assessment-status/assessment-report/screening/${row?._id}`,
                  onClick: () => {
                    setShowOverlay(true);
                    setSelectedRow(row);
                  },
                },
                // {
                //   label: 'Assign',
                //   customIcon: 'assign',
                //   iconWidth: '22',
                //   iconHeight: '22',
                //   color: 'text-black dark:text-white',
                //   // onClick: () => {},
                //   dropDown: [
                //     {
                //       label: 'Interview',
                //       color: 'text-black dark:text-white',
                //       customIcon: 'interview',
                //       element: (
                //         <span
                //           className={`w-24 text-xs px-2 py-1 rounded-full shadow-md font-bold tracking-wide bg-magic-gradient bg-[length:200%]  text-white overflow-hidden`}
                //         >
                //           AI Magic ✨
                //         </span>
                //       ),
                //       onClick: () => {
                //         if (isSuperAdmin || userData?.features?.assignInterview > 0) {
                //           setAssignInterviewTestVisible(true);
                //           setApplicantDetails(row);
                //         } else {
                //           setNeedSubscription(true);
                //         }
                //       },
                //     },
                //   ],
                // },
              ];
            },
          },
        ]}
        // multiSelectedRow={{
        //   selectedIds: selectedIds,
        //   setSelectedIds: setSelectedIds,
        //   handleArchiveSelectedIds: handleArchiveSelectedIds,
        // }}
        noDataFound={{
          customIcon: 'applicant',
          message: 'No assessment created yet',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        // addButtonLabel=""
        // onClickAdd={() => {}}
        // actions={[]}
        hideJumbotron
        isScrollableTabsExists
      />

      {/* Need to add to table main buttons */}
      {/* <div className="flex justify-between items-center gap-x-2">
        <div className="flex gap-3 items-center p-3 ">
          <CustomIcon definedIcon="userWithShield" />
          <p className="font-medium  text-[14px]"> My Assigned Only</p>
          <ToggleSwitch checked={switch2} onChange={setSwitch2} />
        </div>

        <Form>
          <Select
            name="filter"
            lookup={[
              { value: 1, label: 'Status' },
              { value: 2, label: 'Last month' },
            ]}
            value={form.filter}
            onChange={setFieldValue('filter')}
            dropIcon={true}
            // validators={[isRequired()]}
          />
        </Form>
      </div> */}

      {showOverlay && (
        <ComponentOverlayInterview id={selectedRow.sourceLink || selectedRow._id} type={type} onClose={() => setShowOverlay(false)} />
      )}
    </>
  );
};
