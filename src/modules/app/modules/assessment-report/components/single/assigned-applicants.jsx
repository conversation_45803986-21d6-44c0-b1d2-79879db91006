// React
import { useContext, useEffect, useState } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';

// Flowbite
import { Datepicker, Tooltip } from 'flowbite-react';

import { useParams } from 'react-router-dom';
// Context
import { AppContext } from '/src/components/provider';

// Core
import {
  Icon,
  CustomIcon,
  ToggleFilter,
  useFetchList,
  Button,
  TestDifficulty,
  TestSeniorityLevel,
  AvarageScore,
  StaticData,
  useScreenSize,
  Table,
  FormatDate,
  Form,
  ResultStatusSubmission,
  useForm,
  EnumText,
  useValidate,
  Select,
  useNotify,
} from '/src';
import { ComponentOverlayInterview } from './component-overlay-interview';
import { ComponentInterviewRecord } from './interview-recored';

export const AssignedApplicants = () => {
  const { quizId, type } = useParams();

  // User Data
  const { userData } = useContext(AppContext);
  const { notify } = useNotify();

  // Permissions
  const isPermitted = userData?.roles.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const isSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));

  // State
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);
  const [showOverlay, setShowOverlay] = useState(false);
  const [showInterviewRecord, setShowInterviewRecord] = useState(false);
  const [applicantDetails, setApplicantDetails] = useState(false);

  // Hooks
  const { form, setFieldValue, setFormValue, resetForm } = useForm({ filter: null });
  const { isRequired } = useValidate();
  const navigate = useNavigate();
  const screen = useScreenSize();
  const initialFilters = {
    seniortyLevel: {
      label: 'Seniority Level',
      enum: 'QuizDifficulty',
    },
    activeStatus: {
      label: 'Status',
      enum: 'SubmissionStatus',
    },
    averageScore: {
      label: 'Score',
      enum: 'AverageScore',
    },
  };
  const filterFeedData = Object.keys(initialFilters);
  const { ready, loading, setLoading, list, count, search, pagination, filters, setFilters, refresh, handleDates } = useFetchList(
    `${type === 'interview' ? 'ai-interview' : 'submissions'}/assigned-applicants`,
    {
      search: '',
      pagination: {
        page: 1,
        size: 20,
      },
      filters: initialFilters,
      id: quizId,
    }
  );

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  return (
    <>
      <Table
        ready={ready}
        loading={loading}
        title="Assigned Applicants"
        searchPlaceholder="Search for applicants..."
        count={count}
        search={search}
        filters={filters}
        setFilters={setFilters}
        // filterFeedData={filterFeedData}
        // drawerFilter={{
        //   filterCountNumber: filterCountNumber,
        //   isShowDrawerFilter: isShowDrawerFilter,
        //   setShowDrawerFilter: setShowDrawerFilter,
        // }}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        slots={{
          name: (_, row) => {
            return <div className="truncate font-medium text-[#101828]  text-sm dark:text-white">{row?.applicantName}</div>;
          },
          email: (_, row) => {
            return (
              <div className="flex items-center gap-2">
                <div
                  className="font-medium text-gray-900 dark:text-white cursor-pointer truncate max-w-[85%]"
                  onClick={() => navigate(`/app/applicants/progress/${row?.applicantId}`)}
                >
                  {row.applicantEmail}
                </div>
                {row.applicantEmail && (
                  <Tooltip content="Copy Email" placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                    <span
                      onClick={() => {
                        navigator.clipboard.writeText(row.applicantEmail);
                        notify('Email copied');
                      }}
                      className="cursor-pointer"
                    >
                      <Icon
                        icon="ooui:copy-ltr"
                        className="text-gray-500 dark:text-gray-400 text-base hover:text-gray-700 dark:hover:text-gray-300"
                        width="16"
                      />
                    </span>
                  </Tooltip>
                )}
              </div>
            );
          },
          // role: (_, row) => {
          //   return <div className="border rounded-full w-fit p-1 px-3 text-sm font-semibold truncate">{row?.track?.name}</div>;
          // },
          seniorityLevel: (_, row) => {
            return (
              <div className="w-fit">
                <TestSeniorityLevel seniorityLevel={row?.seniorityLevel} />
              </div>
            );
          },
          activeStatus: (_, row) => {
            return (
              // <p
              //   className={`w-fit flex items-center gap-1.5 px-1.5 py-0.5 border border-[#D5D7DA] rounded-lg ${
              //     row?.activeStatus === 'Completed' ? 'bg-white text-[#414651]' : 'bg-[#f4f5f7] text-[#7A8B9F]'
              //   }`}
              // >
              //   {row?.activeStatus === 'Completed' ? (
              //     <span className="size-2 bg-[#17B26A] rounded-full" />
              //   ) : (
              //     <span className="size-2 bg-[#F05454] rounded-full" />
              //   )}
              //   <span>{row.activeStatus}</span>
              // </p>
              <div>
                <ResultStatusSubmission statusSubmission={row.status} />
              </div>
            );
          },
          duration: (_, row) => {
            return (
              <div className="  text-sm text-[#535862] truncate">
                <span className="font-medium mr-1">{row?.timeTaken}</span>
                <span className="font-normal">min</span>
              </div>
            );
          },
          averageScore: (_, row) => {
            return (
              <div className="w-fit">
                <AvarageScore score={row?.averageScore} label={row?.scoreLabel} />
              </div>
            );
          },
        }}
        columns={[
          {
            key: 'name',
            label: 'Name',
            primary: true,
            width: '22%',
          },
          {
            key: 'email',
            label: 'Email',
            primary: true,
            width: '23%',
          },
          // {
          //   key: 'role',
          //   label: 'Role',
          //   primary: true,
          //   width: '18%',
          // },
          {
            key: 'seniorityLevel',
            label: 'Seniority Level',
            primary: true,
            width: '18%',
          },
          {
            key: 'activeStatus',
            label: 'Status',
            // primary: true,
            width: '19%',
          },
          {
            key: 'duration',
            label: 'Duration',
            // primary: true,
            width: '10%',
          },
          {
            key: 'averageScore',
            label: 'Score',
            // primary: true,
            width: '17%',
          },
          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_, row) {
              const actions = [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  onClick: () => navigate(`/app/applicants/progress/${row.applicantId}`),
                },
              ];

              if (type === 'interview') {
                actions.push({
                  label: 'Interview Record',
                  customIcon: 'interview',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  onClick: () => {
                    setShowInterviewRecord(true);
                    setApplicantDetails(row);
                  },
                });
              }

              return actions;
            },
          },
        ]}
        noDataFound={{
          customIcon: 'applicant',
          message: 'No assessment created yet',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        // addButtonLabel=""
        // onClickAdd={() => {}}
        // actions={[]}
        hideJumbotron
        isScrollableTabsExists
      />

      {/* Need to add to table main buttons */}
      {/* <Form className="ml-auto w-fit">
        <Select
          name="filter"
          lookup={[
            { value: 1, label: 'Experience' },
            { value: 2, label: 'Status' },
            { value: 3, label: 'Score above 80%' },
            { value: 4, label: 'Score above 50%' },
          ]}
          value={form.filter}
          onChange={setFieldValue('filter')}
          dropIcon={true}
          // validators={[isRequired()]}
        />
      </Form> */}

      {showOverlay && <ComponentOverlayInterview onClose={() => setShowOverlay(false)} />}
      {showInterviewRecord && <ComponentInterviewRecord onClose={() => setShowInterviewRecord(false)} assessmentId={applicantDetails._id} />}
    </>
  );
};
