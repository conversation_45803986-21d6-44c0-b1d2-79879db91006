// React
import { useContext, useEffect, useState } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';
import { addDays, format, isValid } from 'date-fns';
import { Datepicker, Tooltip } from 'flowbite-react';

// Flowbite

// Context
import { AppContext } from '/src/components/provider';

// Components
import { CandidateProfileCard } from './candiadte-card';

import {
  Jumbotron,
  Button,
  CustomIcon,
  Icon,
  ScrollableTabs,
  useNotify,
  Card,
  ToggleFilter,
  ResultStatusSubmission,
  NoDataFound,
  TestDifficulty,
  TestSeniorityLevel,
  EnumText,
  AvarageScore,
  Dialog,
  useScreenSize,
  StaticData,
  useFetchList,
  Table,
  Form,
  useForm,
  Api,
  useValidate,
  Select,
} from '/src';

export const ComponentOverlayInterview = ({ onClose, id, type }) => {
  const getBlocksCards = () => {
    return [
      {
        header: 'Average Score',
        subHeader: apiDataPublic.averageScore,
        icon: 'average',
      },
      {
        header: 'Completion Rate',
        // Handle both questionIds and questions array for screening
        subHeader: apiDataPublic.completionRate,
        icon: 'nike',
      },
      {
        header: 'Usage',
        subHeader: apiDataPublic.tototalUsage,
        icon: 'userWithNike',
      },
    ];
  };

  // User Data
  const { userData } = useContext(AppContext);

  // Permissions
  const isPermitted = userData?.roles.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const isSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));

  // State
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);
  const [showOverlay, setShowOverlay] = useState(false);
  const [apiData, setApiData] = useState('');
  const [apiDataPublic, setApiDataPublic] = useState('');
  const [showInterviewRecord, setShowInterviewRecord] = useState(false);
  const [applicantDetails, setApplicantDetails] = useState(false);

  // Hooks
  const { form, setFieldValue, setFormValue, resetForm } = useForm({ filter: null });
  const { isRequired } = useValidate();
  const navigate = useNavigate();
  const screen = useScreenSize();
  const { notify } = useNotify();
  const initialFilters = {
    seniortyLevel: {
      label: 'Seniority Level',
      enum: 'QuizDifficulty',
    },
    activeStatus: {
      label: 'Status',
      enum: 'SubmissionStatus',
    },
    averageScore: {
      label: 'Score',
      enum: 'AverageScore',
    },
  };
  const filterFeedData = Object.keys(initialFilters);
  const { ready, loading, setLoading, list, count, search, pagination, filters, setFilters, refresh, handleDates } = useFetchList(
    `${type === 'interview' ? 'ai-interview' : 'submissions'}/assigned-applicants/by-link`,
    {
      search: '',
      pagination: {
        page: 1,
        size: 20,
      },
      filters: initialFilters,
      id: id,
    }
  );

  const handleGet = async () => {
    let AssessmentType;
    if (type == 'interview') {
      AssessmentType = 'ai-interview';
    } else {
      AssessmentType = 'submissions';
    }
    try {
      setLoading(true);
      const response = await Api.get(`${AssessmentType}/top-applicants/by-link/${id}`);
      setApiData(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (customDate) => {
    const date = new Date(customDate || Date.now());
    if (!isValid(date)) {
      return 'Invalid date';
    }
    return format(date, 'dd MMMM , yyyy');
  };

  const handleGetData = async () => {
    let AssessmentType;
    if (type == 'interview') {
      AssessmentType = 'ai-interview';
    } else {
      AssessmentType = 'submissions';
    }
    try {
      setLoading(true);
      const response = await Api.get(`${AssessmentType}/public/${id}`);
      setApiDataPublic(response.data);
    } catch (error) {
      notify.error(error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleGet();
    handleGetData();
  }, []);

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
      // handleGet();
    }
  }, [list]);

  return (
    <Dialog size="6xl" show popup modalHeader={''} onClose={onClose} overflowVisible={true}>
      <div className="space-y-4">
        <Card>
          {/* <div className="flex items-center justify-between"> */}
          {/* <h1 className="font-semibold text-[20px]">{apiDataPublic.title}</h1> */}
          {/* <div className="flex items-center gap-3">
              <Button label="Interactive AI Interview" tertiary customIcon="blueStars" className="text-[#5A399E] border-none" />
              <p className={`bg-white text-[#414651] w-fit flex items-center gap-1.5 px-1.5 py-0.5 border border-[#D5D7DA] rounded-lg`}>
                <span className="size-2 bg-[#17B26A] rounded-full" />
                {setApiDataPublic.status}
              </p>
            </div> */}
          {/* <Button label="Assign" customIcon="newUser" tertiary /> */}
          {/* </div> */}
          <p className="text-[#667085]">
            Created by {apiDataPublic.authorName} ,<span className="font-semibold">{formatDate(apiDataPublic.createdAt)}</span>
          </p>
          {/* blocks */}
          <div className="grid sm:grid-cols-1 lg:grid-cols-3 gap-4  !mt-6">
            {getBlocksCards().map((block) => (
              <div key={block.header} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="text-sm text-[#535862]">{block.header}</p>
                  <p className="text-lg font-semibold">{block.subHeader || '—'}</p>
                </div>
                <CustomIcon definedIcon={block.icon} />
              </div>
            ))}
          </div>

          <div className="pt-5 px-1 space-y-3">
            <h3 className="text-[#566577]">Share Link</h3>
            <div className="flex gap-6">
              <div className="flex justify-between grow items-center  space-y-0 rounded-lg ">
                <input
                  type="text"
                  placeholder="https://assessment.thepass.ai/test_a331717712621611661"
                  value={apiDataPublic.assessmentUrl}
                  className="w-full p-2 pl-10 dark:bg-gray-700 bg-gray-white text-[13.5px] bg-[#F8FAFC] text-gray-800 border border-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white truncate rounded-lg focus:ring-0 focus:border-gray-300 shadow-sm"
                />
              </div>
              <Button
                label="copy"
                customIcon="copy"
                tertiary
                className="bg-[#E2E8F0]"
                onClick={() => {
                  navigator.clipboard.writeText(
                    document.querySelector('input[placeholder="https://assessment.thepass.ai/test_a331717712621611661"]').value
                  );
                  notify('Link copied');
                }}
              />
            </div>
            <div className="pt-2 px-1   flex items-center align-middle gap-2">
              <CustomIcon definedIcon="calender" />
              <div className="flex items-center gap-2">
                <p className="">Valid until </p>
                <span className="text-[#3D3F44] font-semibold text-[15px]">{formatDate(apiDataPublic.dueDate)}</span>
              </div>
            </div>
          </div>
        </Card>

        <div className="space-y-6 px-4">
          <h2 className="text-[18px] font-semibold">Top Performers</h2>
          <div className="grid grid-cols-1   sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 mt-4 w-full">
            {apiData.length > 0 ? (
              <div className="flex gap-6">
                {apiData.map((data, index) => (
                  <CandidateProfileCard key={index} data={data} />
                ))}
              </div>
            ) : (
              <NoDataFound
                noDataFound={{
                  customIcon: 'applicant',
                  message: 'No top performers found',
                }}
              />
            )}
          </div>
        </div>

        <Table
          ready={ready}
          loading={loading}
          title="Assigned Applicants"
          searchPlaceholder="Search for applicants..."
          count={count}
          search={search}
          filters={filters}
          setFilters={setFilters}
          // filterFeedData={filterFeedData}
          // drawerFilter={{
          //   filterCountNumber: filterCountNumber,
          //   isShowDrawerFilter: isShowDrawerFilter,
          //   setShowDrawerFilter: setShowDrawerFilter,
          // }}
          pagination={pagination}
          rows={list}
          backupRows={backupList}
          slots={{
            name: (_, row) => {
              return <div className="truncate font-medium text-[#101828]  text-sm dark:text-white">{row?.applicantName}</div>;
            },
            email: (_, row) => {
              return (
                <div className="flex items-center gap-2">
                  <div
                    className="font-medium text-gray-900 dark:text-white cursor-pointer truncate max-w-[85%]"
                    onClick={() => navigate(`/app/applicants/progress/${row?.applicantId}`)}
                  >
                    {row.applicantEmail}
                  </div>
                  {row.applicantEmail && (
                    <Tooltip
                      content="Copy Email"
                      placement="bottom"
                      arrow={false}
                      className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                    >
                      <span
                        onClick={() => {
                          navigator.clipboard.writeText(row.applicantEmail);
                          notify('Email copied');
                        }}
                        className="cursor-pointer"
                      >
                        <Icon
                          icon="ooui:copy-ltr"
                          className="text-gray-500 dark:text-gray-400 text-base hover:text-gray-700 dark:hover:text-gray-300"
                          width="16"
                        />
                      </span>
                    </Tooltip>
                  )}
                </div>
              );
            },
            // role: (_, row) => {
            //   return <div className="border rounded-full w-fit p-1 px-3 text-sm font-semibold truncate">{row?.track?.name}</div>;
            // },
            seniorityLevel: (_, row) => {
              return (
                <div className="w-fit">
                  <TestSeniorityLevel seniorityLevel={row?.seniorityLevel} />
                </div>
              );
            },
            activeStatus: (_, row) => {
              return (
                // <p
                //   className={`w-fit flex items-center gap-1.5 px-1.5 py-0.5 border border-[#D5D7DA] rounded-lg ${
                //     row?.activeStatus === 'Completed' ? 'bg-white text-[#414651]' : 'bg-[#f4f5f7] text-[#7A8B9F]'
                //   }`}
                // >
                //   {row?.activeStatus === 'Completed' ? (
                //     <span className="size-2 bg-[#17B26A] rounded-full" />
                //   ) : (
                //     <span className="size-2 bg-[#F05454] rounded-full" />
                //   )}
                //   <span>{row.activeStatus}</span>
                // </p>
                <div>
                  <ResultStatusSubmission statusSubmission={row.status} />
                </div>
              );
            },
            duration: (_, row) => {
              return (
                <div className="  text-sm text-[#535862] truncate">
                  <span className="font-medium mr-1">{row?.timeTaken}</span>
                  <span className="font-normal">min</span>
                </div>
              );
            },
            averageScore: (_, row) => {
              return (
                <div className="w-fit">
                  <AvarageScore score={row?.averageScore} label={row?.scoreLabel} />
                </div>
              );
            },
          }}
          columns={[
            {
              key: 'name',
              label: 'Name',
              primary: true,
              width: '22%',
            },
            {
              key: 'email',
              label: 'Email',
              primary: true,
              width: '23%',
            },
            // {
            //   key: 'role',
            //   label: 'Role',
            //   primary: true,
            //   width: '18%',
            // },
            {
              key: 'seniorityLevel',
              label: 'Seniority Level',
              primary: true,
              width: '18%',
            },
            {
              key: 'activeStatus',
              label: 'Status',
              // primary: true,
              width: '19%',
            },
            {
              key: 'duration',
              label: 'Duration',
              // primary: true,
              width: '10%',
            },
            {
              key: 'averageScore',
              label: 'Score',
              // primary: true,
              width: '17%',
            },
            {
              key: 'actions',
              label: 'Actions',
              width: '10%',
              buttons(_, row) {
                return [
                  {
                    label: 'View',
                    customIcon: 'eye',
                    iconWidth: '22',
                    iconHeight: '22',
                    color: 'text-black dark:text-white',
                    onClick: () => navigate(`/app/applicants/progress/${row.applicantId}`),
                  },
                  {
                    label: 'Interview Record',
                    customIcon: 'interview',
                    iconWidth: '22',
                    iconHeight: '22',
                    color: 'text-black dark:text-white',
                    onClick: () => {
                      setShowInterviewRecord(true), setApplicantDetails(row);
                    },
                  },
                ];
              },
            },
          ]}
          noDataFound={{
            customIcon: 'applicant',
            message: 'No assessment created yet',
          }}
          noDataFoundIconWidth="60"
          noDataFoundIconHeight="60"
          showMoreMap={showMoreMap}
          setShowMoreMap={setShowMoreMap}
          // addButtonLabel=""
          // onClickAdd={() => {}}
          // actions={[]}
          hideJumbotron
          isScrollableTabsExists
        />
      </div>
    </Dialog>
  );
};
