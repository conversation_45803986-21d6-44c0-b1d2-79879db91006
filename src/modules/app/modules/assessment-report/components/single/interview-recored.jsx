import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Card, Icon, useNotify, Api } from '/src';
import { Modal } from 'flowbite-react';

export const ComponentInterviewRecord = (assessmentId) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [interviewRecords, setInterviewRecords] = useState([]);
  const { notify } = useNotify();

  // Fetch interview records from API
  const fetchInterviewRecords = async ({ assessmentId }) => {
    try {
      setIsLoading(true);
      const response = await Api.get(`ai-interview/list-uploads`, { assessmentId });

      const recordsWithVideos = response.data.map((record) => ({
        videoUrl: record,
      }));

      setInterviewRecords(recordsWithVideos);
      setShowModal(true);
    } catch (error) {
      notify.error(error?.response?.data?.message || 'Failed to fetch interview records');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchInterviewRecords(assessmentId);
  }, []);

  return (
    <>
      <Modal show={showModal} onClose={() => setShowModal(false)} size="4xl">
        <Modal.Header>Interview Recordings</Modal.Header>
        <Modal.Body>
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <Icon name="eos-icons:loading" className="text-4xl text-primaryPurple" />
            </div>
          ) : interviewRecords.length > 0 ? (
            <div className="space-y-6">
              {interviewRecords.map((record, index) => (
                <Card key={record._id || index} className="p-4">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-lg font-semibold">Recording {index + 1}</h3>
                  </div>

                  {record.videoUrl ? (
                    <div className="relative">
                      <div className="aspect-video">
                        <video
                          controls
                          className="w-full h-full rounded-lg border"
                          src={record.videoUrl}
                          type="video/webm"
                          crossOrigin="anonymous"
                          playsInline
                        >
                          <source src={record.videoUrl} type="video/webm" />
                          <source src={record.videoUrl} type="video/mp4" />
                          Your browser does not support the video tag.
                        </video>
                      </div>

                      <div className="mt-2 text-center">
                        <a
                          href={record.videoUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline flex items-center justify-center"
                        >
                          <Icon name="heroicons:arrow-top-right-on-square" className="mr-1" />
                          Open video in new tab
                        </a>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center p-8 bg-gray-100 dark:bg-gray-800 rounded-lg">
                      <Icon name="ri:video-off-line" className="text-4xl text-gray-400 mb-2" />
                      <p className="text-gray-500">No video recording available</p>
                    </div>
                  )}
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Icon name="ri:video-off-line" className="text-4xl mx-auto mb-3 text-gray-400" />
              <p className="text-gray-500">No interview recordings found for this applicant.</p>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setShowModal(false)}>Close</Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};
