// React
import { useContext, useEffect, useState } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';

// Flowbite
import { Datepicker } from 'flowbite-react';

// Context
import { AppContext } from '/src/components/provider';

// Core
import {
  Icon,
  CustomIcon,
  ToggleFilter,
  useFetchList,
  Button,
  TestDifficulty,
  TestSeniorityLevel,
  AvarageScore,
  StaticData,
  useScreenSize,
  Table,
  FormatDate,
} from '/src';
import { addDays, format, isValid } from 'date-fns';

export const TestList = () => {
  // User Data
  const { userData } = useContext(AppContext);

  // Permissions
  const isPermitted = userData?.roles.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const isSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));

  // State
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);

  // Hooks
  const navigate = useNavigate();
  const screen = useScreenSize();
  const initialFilters = {
    ...(userData.trackId
      ? {}
      : {
          category: {
            label: 'Category',
            lookup: 'category',
          },
        }),
    // subCategory: {
    //   label: 'Sub Category',
    //   lookup: 'subcategory',
    //   parentLookup: { key: 'category', fieldName: 'categoryId', fieldValue: null },
    // },
    difficulty: {
      label: 'Difficulty',
      enum: 'QuestionDifficulty',
    },
    seniortyLevel: {
      label: 'Seniorty Level',
      enum: 'QuizDifficulty',
    },
  };
  const filterFeedData = Object.keys(initialFilters);
  const { ready, loading, setLoading, list, count, search, pagination, filters, setFilters, refresh, handleDates } = useFetchList(
    'submissions/generated-tests',
    {
      search: '',
      pagination: {
        page: 1,
        size: 20,
      },
      filters: initialFilters,
    }
  );

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  return (
    <>
      <Table
        ready={ready}
        loading={loading}
        title="Test Reports"
        searchPlaceholder="Search for template name..."
        count={count}
        search={search}
        filters={filters}
        setFilters={setFilters}
        // filterFeedData={filterFeedData}
        // drawerFilter={{
        //   filterCountNumber: filterCountNumber,
        //   isShowDrawerFilter: isShowDrawerFilter,
        //   setShowDrawerFilter: setShowDrawerFilter,
        // }}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        slots={{
          name: (_, row) => {
            return (
              <div>
                <p className="text-[#101828] text-sm   dark:text-white font-medium truncate">{row?.title || '-'}</p>
                <p className="text-[#535862] font-normal truncate">
                  Created <FormatDate customDate={row?.startDate} />
                </p>
              </div>
            );
          },
          category: (_, row) => {
            return row?.category?.length > 0 ? (
              <div className="flex items-center gap-2">
                {row?.category?.slice(0, 2)?.map((category) => (
                  <p key={category?.categoryName} className="px-2 py-1 bg-[#F4F4F5] text-[#667085] text-[11px] font-medium rounded-md truncate">
                    {category?.categoryName}
                  </p>
                ))}
                {row?.category?.length > 2 && <p className="border rounded-full p-1 px-3 text-xs">+{row?.category?.length - 2}</p>}
              </div>
            ) : (
              <p className="px-2 py-1 bg-[#F4F4F5] text-[#667085] text-[11px] font-medium rounded-md truncate">{row?.category?.categoryName}</p>
            );
          },
          // subCategory: (_, row) => {
          //   return row?.subCategory?.length > 0
          //     ? row?.subCategory?.slice(0, 1)?.map((subCategory) => (
          //         <div key={subCategory?.subCategoryName} className="flex items-center gap-2">
          //           <p className="border rounded-full p-1 px-3 text-sm font-semibold truncate">{subCategory?.subCategoryName}</p>
          //           {category?.subCategoryName?.length > 1 && (
          //             <p className="border rounded-full p-1 px-3 text-sm font-semibold">+{category?.subCategoryName?.length - 1}</p>
          //           )}
          //         </div>
          //       ))
          //     : '—';
          // },
          seniorityLevel: (_, row) => {
            return (
              <div className="w-fit">
                <TestSeniorityLevel seniorityLevel={row?.seniorityLevel} />
              </div>
            );
          },
          difficulty: (_, row) => {
            return (
              <div className="w-fit">
                <TestDifficulty difficulty={row?.difficulty} />
              </div>
            );
          },
          duration: (_, row) => {
            return (
              <div className="  text-sm text-[#535862] truncate">
                <span className="font-medium mr-1">{row?.duration}</span>
                <span className="font-normal">min</span>
              </div>
            );
          },
          usage: (_, row) => {
            return <div className="capitalize font-medium text-sm text-[#535862] truncate">{row?.assignedCount}</div>;
          },
          averageScore: (_, row) => {
            return (
              <div className="w-fit ">
                <AvarageScore score={row?.averageScore} label={row?.scoreLabel} />
              </div>
            );
          },
        }}
        columns={[
          {
            key: 'name',
            label: 'Name',
            primary: true,
            width: '22%',
          },
          {
            key: 'category',
            label: 'Category',
            primary: true,
            width: '23%',
          },
          // {
          //   key: 'subCategory',
          //   label: 'Sub Category',
          //   primary: true,
          //   width: '23%',
          // },
          {
            key: 'seniorityLevel',
            label: 'Seniority Level',
            primary: true,
            width: '18%',
          },
          {
            key: 'difficulty',
            label: 'Difficulty',
            // primary: true,
            width: '15%',
          },
          {
            key: 'duration',
            label: 'Duration',
            // primary: true,
            width: '15%',
          },
          {
            key: 'usage',
            label: 'Usage',
            // primary: true,
            width: '10%',
          },
          {
            key: 'averageScore',
            label: 'Average Score',
            // primary: true,
            width: '18%',
          },
          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_, row) {
              return [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  path: `/app/assessment-report/view/test/${row?.quizId}/`,
                },
                // {
                //   label: 'New User',
                //   customIcon: 'newUser',
                //   iconWidth: '22',
                //   iconHeight: '22',
                //   color: 'text-black dark:text-white',
                //   // path: `/app/applicants/progress/`,
                // },
                // {
                //   label: 'Assign',
                //   customIcon: 'assign',
                //   iconWidth: '22',
                //   iconHeight: '22',
                //   color: 'text-black dark:text-white',
                //   // onClick: () => {},
                //   dropDown: [
                //     {
                //       label: 'Interview',
                //       color: 'text-black dark:text-white',
                //       customIcon: 'interview',
                //       element: (
                //         <span
                //           className={`w-24 text-xs px-2 py-1 rounded-full shadow-md font-bold tracking-wide bg-magic-gradient bg-[length:200%]  text-white overflow-hidden`}
                //         >
                //           AI Magic ✨
                //         </span>
                //       ),
                //       onClick: () => {
                //         if (isSuperAdmin || userData?.features?.assignInterview > 0) {
                //           setAssignInterviewTestVisible(true);
                //           setApplicantDetails(row);
                //         } else {
                //           setNeedSubscription(true);
                //         }
                //       },
                //     },
                //   ],
                // },
              ];
            },
          },
        ]}
        // multiSelectedRow={{
        //   selectedIds: selectedIds,
        //   setSelectedIds: setSelectedIds,
        //   handleArchiveSelectedIds: handleArchiveSelectedIds,
        // }}
        noDataFound={{
          customIcon: 'applicant',
          message: 'No assessment created yet',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        // addButtonLabel=""
        // onClickAdd={() => {}}
        // actions={[]}
        hideJumbotron
        isScrollableTabsExists
      />

      {/* Need to add to table main buttons */}
      {/* <div className="flex justify-between items-center gap-x-2">
        <div className="calendar flex flex-row justify-end items-center gap-2 text-gray-700 dark:text-gray-300 rounded-lg h-fit">
          <Datepicker
            className="inline-block w-full sm:w-44"
            // onSelectedDateChanged={() => {}}
            showTodayButton={false}
            showClearButton={false}
            // value=""
          />
        </div>

        <div className="flex gap-3">
          <Button label="Template" tertiary customIcon="template" />
          <Button label="List" icon="ix:list" />
        </div>
      </div> */}
    </>
  );
};
