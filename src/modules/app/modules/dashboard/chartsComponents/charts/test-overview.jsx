import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, <PERSON>Axis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { PlaceholderMessage } from '../../components/placeholder-message';

const TestChart = ({ data }) => {
  // Filter data to get only the keys (Intern, Fresh, etc.) that have values greater than 0
  const availableBars = [
    { key: 'Intern', color: '#b3cde0' }, // Pastel Blue
    { key: 'Fresh', color: '#c6e2c5' }, // Pastel Green
    { key: 'Junior', color: '#f9e3a3' }, // Pastel Yellow
    { key: 'MidLevel', color: '#f9c4a3' }, // Lighter Pastel Orange
    { key: 'Senior', color: '#f4a3b4' }, // Pastel Pink
  ].filter((bar) => data.some((item) => item[bar.key] > 0)); // Only include bars with data > 0

  const CustomTooltip = ({ active, payload }) => {
    if (!active || !payload || payload.length === 0) return null;

    return (
      <div className="p-3 rounded-md shadow-md border bg-white text-black dark:bg-gray-800 dark:text-white">
        <p className="font-semibold mb-1">{payload[0].payload.name}</p>
        {payload.map((entry, index) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {entry.value}
          </p>
        ))}
      </div>
    );
  };

  const CustomCursor = ({ x, y, width, height }) => {
    return (
      <rect
        x={x}
        y={y}
        width={width}
        height={height}
        className="fill-gray-300/50 dark:fill-gray-200/10"
      />
    );
  };

  return (
    <ResponsiveContainer width="100%" height={250}>
      <BarChart data={data} margin={{ top: 20, bottom: 5, left: -30 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" axisLine={false} tickLine={false} />
        <YAxis axisLine={false} tickLine={false} />
        <Tooltip cursor={<CustomCursor />} content={<CustomTooltip />} />
        <Legend />
        {availableBars.map((bar) => (
          <Bar key={bar.key} dataKey={bar.key} stackId="a" fill={bar.color} barSize={50} />
        ))}
      </BarChart>
    </ResponsiveContainer>
  );
};

export const TestOverview = ({ data, chartTitle }) => {
  const [selectedCategory, setSelectedCategory] = useState('');

  useEffect(() => {
    if (data && Object.keys(data).length > 0) {
      // TODO: Recheck this; changed from 0 to 1 as hotfix for null value 
      setSelectedCategory(Object.keys(data)[1]);
    }
  }, [data]);

  const categoriesAvailable = data && Object.keys(data).length > 0;

  return (
    <div className="flex flex-col bg-white rounded-lg shadow-sm border border-gray-200 h-full p-4 dark:bg-[#3E3D4B] dark:border-none dark:text-white">
      <div className="flex flex-col sm:flex-row sm:justify-between gap-x-3 gap-y-1 flex-wrap">
        <h2 className="w-fit text-lg font-semibold dark:text-white text-black">{chartTitle}</h2>
        <div className="mt-4 sm:mt-0">
          {categoriesAvailable && (
            <select
              id="category"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-48 truncate p-2 border rounded-lg text-black hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-none dark:hover:bg-gray-700"
            >
              <option disabled value="">
                Select Category
              </option>
              {Object.keys(data).map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          )}
        </div>
      </div>

      <div className="flex-grow mt-4">
        {categoriesAvailable && selectedCategory ? (
          data[selectedCategory]?.length > 0 ? (
            <TestChart data={data[selectedCategory]} />
          ) : (
            <PlaceholderMessage message="No data available for the selected category." />
          )
        ) : (
          <PlaceholderMessage message="No categories available." />
        )}
      </div>
    </div>
  );
};
