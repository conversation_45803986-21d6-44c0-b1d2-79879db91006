import React, { useState } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LabelList, Legend } from 'recharts';

// Define colors
const pastelPink = '#f87171'; // For low success rates
const pastelPurple = '#6ee7b7'; // For high success rates

//  dark shades for labels
const darkPink = 'white';
const darkPurple = 'white';

const theme = window.localStorage.getItem('theme');

export const TestPerformanceChart = ({ chartTitle, data }) => {
  const [selectedCategory, setSelectedCategory] = useState('Frontend');

  //  the color based on chartTitle
  const barColor = chartTitle.includes('High Success Rate') ? pastelPurple : pastelPink;
  const labelColor = barColor === pastelPurple ? darkPurple : darkPink;

  return (
    <div className="border rounded-lg shadow-sm p-4 bg-white dark:bg-[#3E3D4B] dark:text-white dark:border-none mb-1">
      <div className="flex justify-between ">
        <h3 className="text-lg font-semibold">{chartTitle}</h3>
        <div className="mb-4">
          <select
            id="category"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="p-2 border rounded-lg text-black hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-none dark:hover:bg-gray-700"
          >
            {Object.keys(data).map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>
      </div>
      <div className="flex grow">
        <ResponsiveContainer width="100%" height={250}>
          <BarChart data={data[selectedCategory]} layout="vertical" margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" axisLine={false} tickLine={false} />
            <YAxis type="category" dataKey="name" axisLine={false} tickLine={false} />
            <Tooltip
              itemStyle={{ color: barColor }}
              cursor={{ fill: `${theme === 'dark' ? '#181720' : '#F4F2FB'}` }}
              formatter={(value, name, props) => {
                const { payload } = props;
                return [
                  `Difficulty: ${payload.difficulty}`,
                  `${value} successful out of ${payload.totalApplicants} applicants`,
                  `Success Rate: ${payload.successRate}%`,
                ];
              }}
            />
            <Bar dataKey="successfulApplicants" fill={barColor} radius={[0, 15, 15, 0]} barSize={40}>
              <LabelList
                dataKey="successRate"
                position="right"
                formatter={(value) => `${value}%`}
                style={{ fill: `${theme === 'dark' ? 'white' : 'black'}`, fontSize: '12px', fontWeight: 'bold' }}
              />
              {/* <LabelList dataKey="difficulty" position="insideLeft" style={{ fill: labelColor }} /> */}
            </Bar>
            <Legend />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};
