import React, { useEffect, useState, useContext } from 'react';
import { useNotify, Api, CustomIcon, Button } from '/src';
import { Icon } from '/src';
import SubmissionsCharts from '../components/charts/submissions-charts';
import { ApplicantGrowthChart } from '../chartsComponents/charts/applicant-growth-chart';
import Categories from '../components/list/categories';
import Author from '../components/list/author';
import Questions from '../components/list/questions';
import Quizzes from '../components/list/quizzes';
import { DashboardPlaceholder } from '../components/dashboard-placeholder';
import { DashboardCard } from '../components/dashbaord-card';
import BlocksPlaceholder from '../components/blocks-placeholder';
import { AppContext } from '/src/components/provider';
import axios from 'axios';
import { Datepicker } from 'flowbite-react';
import { handleGetApplicants, handleGetUsers, handleGetTests, handleGetInterview, handleGetAssessments } from '../utils';
import { RoleDistribution } from '../chartsComponents/charts/role-distribution';
import { AssessmentStatusDonut } from '../chartsComponents/charts/assessment-status';
import { TestOverview } from '../chartsComponents/charts/test-overview';
import { TestPerformanceChart } from '../chartsComponents/charts/test-performance';
import { BsPersonVideo2 } from 'react-icons/bs';
import { FaUserShield } from 'react-icons/fa6';
import { HiUserGroup } from 'react-icons/hi';
import { HiClipboardDocumentList } from 'react-icons/hi2';
import { FaMagnifyingGlassChart } from 'react-icons/fa6';
import { PiExamDuotone } from 'react-icons/pi';
import { CurrentPlanSummary } from '../../organizations/components/profile/overview/current-plan-summary';
import { useNavigate } from 'react-router-dom';

const successTest = {
  Frontend: [
    { name: 'JavaScript', difficulty: 'Fresh', successfulApplicants: 40, totalApplicants: 120, successRate: 95 },
    { name: 'React', difficulty: 'Fresh', successfulApplicants: 30, totalApplicants: 95, successRate: 85 },
    { name: 'Vue.js', difficulty: 'Junior', successfulApplicants: 20, totalApplicants: 80, successRate: 80 },
    { name: 'Bootstrap', difficulty: 'Senior', successfulApplicants: 10, totalApplicants: 40, successRate: 50 },
  ],
  Backend: [
    { name: 'Node.js', difficulty: 'Fresh', successfulApplicants: 15, totalApplicants: 60, successRate: 70 },
    { name: 'Java', difficulty: 'Junior', successfulApplicants: 10, totalApplicants: 40, successRate: 60 },
    { name: 'PHP', difficulty: 'MidLevel', successfulApplicants: 20, totalApplicants: 55, successRate: 65 },
  ],
  Design: [
    { name: 'UI/UX', difficulty: 'Fresh', successfulApplicants: 12, totalApplicants: 40, successRate: 55 },
    { name: 'Graphic Design', difficulty: 'Junior', successfulApplicants: 7, totalApplicants: 30, successRate: 45 },
  ],
  DataScience: [
    { name: 'Python', difficulty: 'Senior', successfulApplicants: 20, totalApplicants: 50, successRate: 60 },
    { name: 'R', difficulty: 'MidLevel', successfulApplicants: 10, totalApplicants: 25, successRate: 40 },
  ],
};

// Data For Test Performance Low Rate

const lowTest = {
  Frontend: [
    { name: 'JavaScript', difficulty: 'Fresh', successfulApplicants: 10, totalApplicants: 120, successRate: 15 },
    { name: 'React', difficulty: 'Fresh', successfulApplicants: 4, totalApplicants: 95, successRate: 2 },
    { name: 'Vue.js', difficulty: 'Junior', successfulApplicants: 10, totalApplicants: 80, successRate: 3 },
    { name: 'Bootstrap', difficulty: 'Senior', successfulApplicants: 5, totalApplicants: 40, successRate: 5 },
  ],
  Backend: [
    { name: 'Node.js', difficulty: 'Fresh', successfulApplicants: 6, totalApplicants: 60, successRate: 7 },
    { name: 'Java', difficulty: 'Junior', successfulApplicants: 4, totalApplicants: 40, successRate: 6 },
    { name: 'PHP', difficulty: 'MidLevel', successfulApplicants: 2, totalApplicants: 55, successRate: 5 },
  ],
  Design: [
    { name: 'UI/UX', difficulty: 'Fresh', successfulApplicants: 4, totalApplicants: 40, successRate: 15 },
    { name: 'Graphic Design', difficulty: 'Junior', successfulApplicants: 7, totalApplicants: 30, successRate: 15 },
  ],
  DataScience: [
    { name: 'Python', difficulty: 'Senior', successfulApplicants: 2, totalApplicants: 50, successRate: 0 },
    { name: 'R', difficulty: 'MidLevel', successfulApplicants: 10, totalApplicants: 25, successRate: 5 },
  ],
};

export const StatisticsPage = () => {
  // User Data
  const { userData } = useContext(AppContext);

  // States
  const { notify } = useNotify();
  const [start, setStart] = useState(null);
  const [end, setEnd] = useState(null);
  const [questions, setQuestions] = useState();
  const [test, setTest] = useState();
  const [testByCategory, setTestByCategory] = useState();
  const [user, setUsers] = useState();
  const [applicant, setApplicant] = useState();
  const [interview, setInterview] = useState();
  const [interviewByCategory, setInterviewByCategory] = useState();
  const [submission, setSubmission] = useState();
  const [assessments, setAssessments] = useState();
  const [hasError, setHasError] = useState(false);

  // Tests Count
  // const testInProgressPercentage = submission?.inProgressPercentage;
  const testNotStartedPercentage = submission?.notStartedPercentage;
  const testSubmittedPercentage = submission?.submittedPercentage;

  // Interview Count
  const interviewInProgressPercentage = interview?.inProgressInterviewPercentage;
  const interviewNotStartedPercentage = interview?.notStartedInterviewPercentage;
  const interviewSubmittedPercentage = interview?.submittedInterviewPercentage;

  // Assessment Count
  const assessmentSubmittedPercentage = assessments?.assessmentsSubmittedPercentage || 0;
  const assessmentNotStartedPercentage = assessments?.assessmentsMissedPercentage || 0;

  const navigate = useNavigate();

  // Data for the Test Status
  const testData = [
    { name: 'Submitted', value: testSubmittedPercentage, color: '#86efac', track1: 15, track2: 10, track3: 15 }, // Pastel Green
    // { name: 'In Progress', value: testInProgressPercentage, color: '#60a5fa', track1: 15, track2: 10, track3: 15 }, // Pastel Blue
    { name: 'Not Started', value: testNotStartedPercentage, color: '#d4d4d8', track1: 5, track2: 8, track3: 17 }, // Pastel Purple
  ];

  const interviewData = [
    { name: 'Submitted', value: interviewSubmittedPercentage, color: '#86efac', track1: 15, track2: 10, track3: 15 }, // Pastel Green
    // { name: 'In Progress', value: interviewInProgressPercentage, color: '#60a5fa', track1: 15, track2: 10, track3: 15 }, // Pastel Blue
    { name: 'Not Started', value: interviewNotStartedPercentage, color: '#d4d4d8', track1: 5, track2: 8, track3: 17 }, // Pastel Purple
  ];

  const assessmentData = [
    { name: 'Submitted', value: assessmentSubmittedPercentage, color: '#86efac', track1: 15, track2: 10, track3: 15 }, // Pastel Green
    { name: 'Missed deadline', value: assessmentNotStartedPercentage, color: '#f87171', track1: 5, track2: 8, track3: 17 }, // Red color for missed deadline
  ];

  const ChartPlaceholder = ({ message }) => (
    <div className="flex items-center justify-center h-48 bg-gray-100 rounded-lg">
      <p className="text-gray-500">{message || 'No data available'}</p>
    </div>
  );

  const CardTitle = ({ icon, title, onClick }) => {
    return (
      <div className="min-h-12 flex flex-wrap justify-between gap-2 border-b dark:border-gray-700 px-2 pb-2">
        <div className="flex items-center gap-2">
          {icon && icon}
          <div className="flex items-center gap-3 font-semibold text-[#313D4F] dark:text-white">{title}</div>
        </div>
        {onClick && <Button onClick={onClick.onClick} label={onClick.title} tertiary size="sm" />}
      </div>
    );
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        let calcStart = Math.trunc(new Date(start).getTime() / (1000 * 60 * 60 * 24));
        let calcEnd = Math.trunc(new Date(end).getTime() / (1000 * 60 * 60 * 24));

        if (start && end) {
          if (calcStart <= calcEnd) {
            await handleGetApplicants({ setApplicant, notify, calcStart, calcEnd });
            await handleGetUsers({ setUsers, notify, calcStart, calcEnd });
            await handleGetTests({ setTest, setTestByCategory, notify, calcStart, calcEnd });
            await handleGetInterview({ setInterview, setInterviewByCategory, notify, calcStart, calcEnd });
            await handleGetAssessments({ setAssessments, notify, calcStart, calcEnd });
            setHasError(false);
          } else {
            setHasError(true);
          }
        } else {
          await handleGetApplicants({ setApplicant, notify, calcStart, calcEnd });
          await handleGetUsers({ setUsers, notify, calcStart, calcEnd });
          await handleGetTests({ setTest, setTestByCategory, notify, calcStart, calcEnd });
          await handleGetInterview({ setInterview, setSubmission, setInterviewByCategory, notify, calcStart, calcEnd });
          await handleGetAssessments({ setAssessments, notify, calcStart, calcEnd });
        }
      } catch (error) {
        console.error('Data fetching error:', error);
      }
    };

    fetchData();
  }, [start, end]);

  // if (!applicant && !user&& !test? && !interview) {
  //   return <DashboardPlaceholder />;
  // }

  return (
    <main>
      <div className="fixed bottom-4 right-4 flex items-center space-x-2 z-50">
        <div className="relative group">
          <div className="flex items-center justify-center w-12 h-12 bg-purple-500 text-white rounded-full shadow-lg cursor-pointer shadow-purple-600/50 hover:bg-purple-700 transition-transform transform hover:scale-110">
            <FaMagnifyingGlassChart className="w-6 h-6" />
          </div>
          <div className="absolute bottom-full -left-28 mb-3 hidden group-hover:flex flex-col items-center w-[150px]">
            <div className="relative z-50 p-3 text-sm leading-none text-white whitespace-nowrap bg-purple-500 shadow-lg rounded-md max-w-xs">
              Hover on each chart to know more about!
            </div>
          </div>
        </div>
      </div>

      <div className="p-4 bg-gray-100 rounded-lg bg-[url('/images/dashboard-statistics.png')] bg-cover bg-no-repeat bg-center">
        {/* <!-- Welcome Header --> */}
        <div className="mb-4">
          <h1 className="text-2xl font-semibold text-white">Welcome, {userData.name}</h1>
          <p className="text-base text-gray-300">Here is a quick overview of your platform statistics.</p>
        </div>

        {/* <!-- Statistics Boxes --> */}
        <div className="grid max-sm:grid-cols-1 sm:grid-cols-2 md:grid-cols-4  gap-4">
          <div className="bg-white dark:bg-[#3E3D4B] bg-opacity-90 rounded-lg p-4 h-24 flex items-center shadow-lg">
            <div className="w-12 h-10 flex items-center justify-center bg-purple-100 dark:bg-gray-700 rounded-full mr-4">
              <CustomIcon definedIcon="applicant" className="text-[#9061F9] dark:text-[#D1D5DB]" />
            </div>
            <div>
              <h2 className="text-base font-normal text-gray-500 dark:text-grayTextOnDarkMood">Total Applicants</h2>
              <h3 className="text-3xl font-semibold text-gray-900 dark:text-white">{applicant?.applicantsCount || 0}</h3>
            </div>
          </div>

          <div className="bg-white dark:bg-[#3E3D4B] bg-opacity-90 rounded-lg p-4 h-24 flex items-center shadow-lg">
            <div className="w-12 h-12 flex items-center justify-center bg-purple-100 dark:bg-gray-700 rounded-full mr-4">
              <CustomIcon definedIcon="users" className="text-[#9061F9] dark:text-[#D1D5DB]" />
            </div>
            <div>
              <h2 className="text-base font-normal text-gray-500 dark:text-grayTextOnDarkMood">Total Users</h2>
              <h3 className="text-3xl font-semibold text-gray-900 dark:text-white">{user?.totalUsers || 0}</h3>
            </div>
          </div>

          <div className="bg-white dark:bg-[#3E3D4B] bg-opacity-90 rounded-lg p-4 h-24 flex items-center shadow-lg">
            <div className="w-12 h-12 flex items-center justify-center bg-purple-100 dark:bg-gray-700 rounded-full mr-4">
              <CustomIcon definedIcon="tests" className="text-[#9061F9] dark:text-[#D1D5DB]" />
            </div>
            <div>
              <h2 className="text-base font-normal text-gray-500 dark:text-grayTextOnDarkMood">Total Tests</h2>
              <h3 className="text-3xl font-semibold text-gray-900 dark:text-white">{submission?.totalSubmissions || 0}</h3>
            </div>
          </div>

          <div className="bg-white dark:bg-[#3E3D4B] bg-opacity-90 rounded-lg p-4 h-24 flex items-center shadow-lg">
            <div className="w-12 h-12 flex items-center justify-center bg-purple-100 dark:bg-gray-700 rounded-full mr-4">
              <CustomIcon definedIcon="interview" className="text-[#9061F9] dark:text-[#D1D5DB]" />
            </div>
            <div>
              <h2 className="text-base font-normal text-gray-500 dark:text-grayTextOnDarkMood">Total Interviews</h2>
              <h3 className="text-3xl font-semibold text-gray-900 dark:text-white">{interview?.totalInterviews || 0}</h3>
            </div>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-5 gap-5 mt-4 ]">
        {/* First Row */}
        <div className="md:col-span-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg">
          <CardTitle
            title="Current Plan Summary"
            onClick={{
              title: 'Manage Plan',
              onClick: () => {
                navigate('/app/plans/list');
              },
            }}
          />
          <CurrentPlanSummary />
        </div>

        <div className="dark:border-gray-600  md:col-span-2">
          <RoleDistribution user={user} chartTitle="User Role Distribution" />
        </div>
        {/* Second Row */}
        <div className="dark:border-gray-600  md:col-span-3">
          <ApplicantGrowthChart data={applicant?.applicantsStatistics} chartTitle="Applicant Growth" />
        </div>

        {/* Third Row */}
        <div className="dark:border-gray-600  md:col-span-2">
          <AssessmentStatusDonut
            chartTitle="Assessment Status"
            data={assessmentData}
            submissionData={{
              submittedCount: assessments?.assessmentsSubmittedCount || 0,
              missedDeadlineCount: assessments?.assessmentsMissedCount || 0,
            }}
          />
        </div>

        <div className="dark:border-gray-600  md:col-span-5 ">
          <TestOverview chartTitle="Assessments by Category and Difficulty" data={testByCategory} ChartPlaceholder={ChartPlaceholder} />
        </div>
        {/* <div className="dark:border-gray-600  md:col-span-3 ">
          <TestOverview chartTitle="Interviews by Category and Difficulty" data={interviewByCategory} />
        </div> */}
      </div>
      {/* <div className="grid grid-cols-1 md:grid-cols-4 gap-5 mt-4 ">
        <div className="dark:border-gray-600  md:col-span-2 ">
          <TestPerformanceChart chartTitle="High Success Rate Tests" data={successTest} />
        </div>

        <div className="dark:border-gray-600  md:col-span-2 ">
          <TestPerformanceChart chartTitle="Low Success Rate Tests" data={lowTest} />
        </div>
      </div> */}
    </main>
  );
};
