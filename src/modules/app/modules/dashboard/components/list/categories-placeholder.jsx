import React from 'react';

export default function CategoriesPlaceholder() {
  return (
    <div className="border p-4 border-[#F4F4F4] bg-[#FFF] dark:bg-darkBackgroundCard rounded-lg dark:text-white dark:border-gray-600 shadow-[0px_4px_14px_0px_rgba(195,195,195,0.08)]">
      <div className="rounded-lg flex flex-col gap-3">
        <div className="w-10 h-10 bg-gray-200 rounded-full dark:bg-gray-700 animate-pulse"></div>
        <div className="w-40 h-4 my-[6px] bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse"></div>
        <div className="w-14 h-3 my-[10px] bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse"></div>
      </div>
    </div>
  );
}
