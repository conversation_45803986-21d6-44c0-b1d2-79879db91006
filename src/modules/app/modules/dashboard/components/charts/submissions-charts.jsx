import React from 'react';
import PieCharts from './pie-charts';

function SubmissionsCharts({ statistics }) {
  return (
    <>
      <div className="flex flex-col gap-2">
        <p className="p-1 text-[#374151] dark:text-white font-semibold">Tests</p>

        <div className="flex-grow grid grid-cols-1 sm:grid-cols-5 lg:gap-12 sm:gap-8 px-4 sm:px-8">
          <div className="col-span-2 flex justify-center">
            <PieCharts statistics={statistics} className="flex justify-center" />
          </div>
          <div className="transition-all ease-in duration-300 sm:col-span-3 text-base sm:text-xl flex items-center">
            <div className="w-full">
              <div className="py-2 text-black dark:text-white gap-6 flex justify-between items-center">
                <p className="flex items-center gap-4">
                  <span className="w-6 h-6 bg-[#58A8DC] rounded-full"></span>
                  <span>Not Started</span>
                </p>
                <span>{statistics.submissionsStatistic.notStartedCount}%</span>
              </div>
              <div className="py-2 text-black dark:text-white gap-6 flex justify-between items-center">
                <p className="flex items-center gap-4">
                  <span className="w-6 h-6 bg-[#FFA500] rounded-full"></span>
                  <span>In Progress</span>
                </p>
                <span>{statistics.submissionsStatistic.inProgressCount}%</span>
              </div>
              <div className="py-2 text-black dark:text-white gap-6 flex justify-between items-center">
                <p className="flex items-center gap-4">
                  <span className="w-6 h-6 bg-[#24C081] rounded-full"></span>
                  <span>Submitted</span>
                </p>
                <span>{statistics.submissionsStatistic.submittedCount}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default SubmissionsCharts;
