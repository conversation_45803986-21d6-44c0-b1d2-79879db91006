import React, { useEffect, useState } from 'react';
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, defaults } from 'chart.js/auto';

defaults.maintainAspectRatio = false

function BarChart({ statistics }) {
  const UserData = [
    {
      month: 'JAN',
    },
    {
      month: 'FEB',
    },
    {
      month: 'MAR',
    },
    {
      month: 'APR',
    },
    {
      month: 'MAY',
    },
    {
      month: 'JUN',
    },
    {
      month: 'JUL',
    },
    {
      month: 'AUG',
    },
    {
      month: 'SEP',
    },
    {
      month: 'OCT',
    },
    {
      month: 'NOV',
    },
    {
      month: 'DEC',
    },
  ];
  const userData = {
    labels: UserData.map((data) => data.month),
    datasets: [
      {
        label: 'Applicants',
        data: statistics.applicantsStatistics.map((stage) => stage.count),
        backgroundColor: ['rgba(27, 89, 248, 0.80)'],
        borderColor: ['rgb(54, 162, 235)'],
        borderWidth: 1,
        borderRadius: 100,
        barThickness: 10,
      },
    ],
  }

  const options = {
    scales: {
      x: {
        grid: {
          display: false, // Hide x-axis grid lines
        },
      },
      y: {
        grid: {
          display: false, // Hide y-axis grid lines
        },
      },
    },
    plugins: {
      legend: {
        display: false, // Hide the legend (labels above the chart)
      },
    },
  };

  useEffect(() => {
    userData
  }, [statistics])

  return (
    <div>
      <p className='p-1 mb-2 text-[#374151] dark:text-white font-semibold'>Applicants</p>
      <div className='sm:min-h-[250px] max-h-[250px] sm:pl-1 mx-auto  '>
        <Bar data={userData} options={options} />
      </div>
    </div>
  );
}

export default BarChart;
