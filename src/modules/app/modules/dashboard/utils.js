import { Api } from '/src';
// In case we need it
export const handleGetAssessments = async ({ setAssessments, notify, calcStart, calcEnd }) => {
  try {
    let startISO = new Date(calcStart).toISOString();
    let endISO = new Date(calcEnd).toISOString();
    if (startISO === '1970-01-01T00:00:00.000Z') {
      startISO = null;
    } else {
      startISO = startISO;
    }
    if (endISO === '1970-01-01T00:00:00.000Z') {
      endISO = null;
    } else {
      endISO = endISO;
    }

    const response = await Api.get('dashboard/assessment/statistics', {
      params: {
        start: startISO && endISO && startISO,
        end: startISO && endISO && endISO,
      },
    });

    setAssessments(response.data);
  } catch (error) {
    notify.error(error.response.data.message);
  }
};

export const handleGetApplicants = async ({ setApplicant, notify, calcStart, calcEnd }) => {
  try {
    let startISO = new Date(calcStart).toISOString();
    let endISO = new Date(calcEnd).toISOString();
    if (startISO === '1970-01-01T00:00:00.000Z') {
      startISO = null;
    } else {
      startISO = startISO;
    }
    if (endISO === '1970-01-01T00:00:00.000Z') {
      endISO = null;
    } else {
      endISO = endISO;
    }

    const response = await Api.get('/dashboard/list/applicants', {
      params: {
        start: startISO && endISO && startISO,
        end: startISO && endISO && endISO,
      },
    });

    setApplicant(response.data);
  } catch (error) {
    notify.error(error.response.data.message);
  }
};

// Users
export const handleGetUsers = async ({ setUsers, notify, calcStart, calcEnd }) => {
  try {
    let startISO = new Date(calcStart).toISOString();
    let endISO = new Date(calcEnd).toISOString();
    if (startISO === '1970-01-01T00:00:00.000Z') {
      startISO = null;
    } else {
      startISO = startISO;
    }
    if (endISO === '1970-01-01T00:00:00.000Z') {
      endISO = null;
    } else {
      endISO = endISO;
    }

    const response = await Api.get('/dashboard/list/users', {
      params: {
        start: startISO && endISO && startISO,
        end: startISO && endISO && endISO,
      },
    });
    setUsers(response.data);
  } catch (error) {
    notify.error(error.response.data.message);
  }
};

// Tests
export const handleGetTests = async ({ setTest, setTestByCategory, notify, calcStart, calcEnd }) => {
  try {
    let startISO = new Date(calcStart).toISOString();
    let endISO = new Date(calcEnd).toISOString();
    if (startISO === '1970-01-01T00:00:00.000Z') {
      startISO = null;
    } else {
      startISO = startISO;
    }
    if (endISO === '1970-01-01T00:00:00.000Z') {
      endISO = null;
    } else {
      endISO = endISO;
    }

    const response = await Api.get('/dashboard/list/quizzes', {
      params: {
        start: startISO && endISO && startISO,
        end: startISO && endISO && endISO,
      },
    });

    const mapQuizzesToCategory = (quizzesByCategory) => {
      const result = {};

      quizzesByCategory.forEach((category) => {
        const categoryName = category._id;
        const subcategories = category.subcategories.map((subcategory) => {
          let Fresh = 0,
            Junior = 0,
            MidLevel = 0,
            Senior = 0;

          // Iterate over each quiz in the subcategory and assign based on difficulty
          subcategory.quizzes.forEach((quiz) => {
            const difficulty = quiz.difficulty;
            if (difficulty === 1) {
              Fresh += 1;
            } else if (difficulty === 2) {
              Junior += 1;
            } else if (difficulty === 3) {
              MidLevel += 1;
            } else if (difficulty >= 4) {
              Senior += 1;
            }
          });

          // Create the object for this subcategory
          return {
            name: subcategory.subcategoryName,
            Fresh: Fresh,
            Junior: Junior,
            MidLevel: MidLevel,
            Senior: Senior,
          };
        });

        result[categoryName] = subcategories;
      });

      return result;
    };

    setTestByCategory(mapQuizzesToCategory(response.data?.quizzesByCategory));
    // @TODO: make sure that interviewsByCategory is correct after endpoint is ready
    setTest(response.data);
  } catch (error) {
    notify.error(error.response.data.message);
  }
};

// Interviews
export const handleGetInterview = async ({ setInterview, setInterviewByCategory, setSubmission, notify, calcStart, calcEnd }) => {
  try {
    let startISO = new Date(calcStart).toISOString();
    let endISO = new Date(calcEnd).toISOString();
    if (startISO === '1970-01-01T00:00:00.000Z') {
      startISO = null;
    } else {
      startISO = startISO;
    }
    if (endISO === '1970-01-01T00:00:00.000Z') {
      endISO = null;
    } else {
      endISO = endISO;
    }

    const response = await Api.get('/dashboard/list/assessments', {
      params: {
        start: startISO && endISO && startISO,
        end: startISO && endISO && endISO,
      },
    });

    const mapInterviewToCategory = (interviewByCategory) => {
      const result = {};

      interviewByCategory?.forEach((category) => {
        const categoryName = category._id;
        const subcategories = category.subcategories.map((subcategory) => {
          let Fresh = 0,
            Junior = 0,
            MidLevel = 0,
            Senior = 0;

          // Iterate over each quiz in the subcategory and assign based on difficulty
          subcategory.interviews.forEach((quiz) => {
            const difficulty = quiz.difficulty;
            if (difficulty === 1) {
              Fresh += 1;
            } else if (difficulty === 2) {
              Junior += 1;
            } else if (difficulty === 3) {
              MidLevel += 1;
            } else if (difficulty >= 4) {
              Senior += 1;
            }
          });

          // Create the object for this subcategory
          return {
            name: subcategory.subcategoryName,
            Fresh: Fresh,
            Junior: Junior,
            MidLevel: MidLevel,
            Senior: Senior,
          };
        });

        result[categoryName] = subcategories;
      });

      return result;
    };

    setInterviewByCategory(mapInterviewToCategory(response.data.interviewStatistic.interviewByCategory));

    setInterview(response.data.interviewStatistic);
    setSubmission(response.data.submissionsStatistic);
  } catch (error) {
    notify.error(error.response.data.message);
  }
};
