import React, { useEffect, useState, useContext, useRef } from 'react';
import { useLocation } from 'react-router-dom';
// UI
import { Dialog, Form, TextInput, Button, useForm, useNotify, Api, useValidate, Icon, Regex } from '/src';

import { AppContext } from '/src/components/provider';

export const ProfileEditPage = ({ onClose }) => {
  // State
  const { notify } = useNotify();
  const { updateUser } = useContext(AppContext);
  const location = useLocation();

  const [confirmPassword, setConfirmPassword] = useState('');
  const { isRequired, validateRegex, minLength, maxLength, validatePasswordRegex, isNotSpaces } = useValidate();
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showError, setShowError] = useState(false);

  // Form
  const { form, setFieldValue, setFormValue } = useForm({
    name: '',
    email: '',
    password: '',
    profilePicture: '',
  });

  // File input reference
  const fileInputRef = useRef(null);
  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get('auth/profile');

      // Just set the form value with API data
      setFormValue(response.data);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  const handleSubmit = async () => {
    if (form.password && confirmPassword !== form.password) {
      setShowError(true);
    } else {
      try {
        // Create a copy of the form data to submit, but exclude the profilePicture
        const { profilePicture, ...formData } = form;

        // Only send the necessary data to the API (excluding profilePicture)
        const response = await Api.put('auth/profile/single', formData);
        if (response.data.access_token) {
          localStorage.setItem('userData', JSON.stringify(response.data));
          updateUser(response.data);

          notify('Profile edited successfully!');
          onClose();
          if (location.pathname.includes('users')) {
            window.location.reload();
          }
        }
      } catch (error) {
        notify.error(error.response.data.message);
      }
    }
  };

  const handleShow = (type) => {
    if (type == 'newPass') setShowNewPassword(!showNewPassword);
    else setShowConfirmPassword(!showConfirmPassword);
  };

  // On mount
  useEffect(() => {
    handleGet();
  }, []);

  useEffect(() => {
    if (confirmPassword === form.password) setShowError(false);
  }, [confirmPassword]);

  return (
    <Dialog size="lg" show popup modalHeader="Edit Profile" onClose={onClose}>
      <Form className="space-y-4" onSubmit={handleSubmit}>
        <div className="grid gap-4">
          {/* Profile Picture */}
          <div className="flex justify-center mb-4">
            <div className="relative">
              <div className="w-24 h-24 rounded-full overflow-hidden border-2 border-gray-200">
                {form.profilePicture ? (
                  <img
                    src={form.profilePicture}
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                    <Icon
                      icon="mdi:account"
                      width="40"
                      className="text-gray-400"
                    />
                  </div>
                )}
              </div>

              {/* Upload Icon with Two-Level Background */}
              <div
                className="absolute bottom-0 right-0 rounded-full p-1 bg-primaryPurple bg-opacity-30 cursor-pointer hover:scale-110 transition-transform duration-200 shadow-md"
                onClick={() => fileInputRef.current.click()}
                title="Change profile picture"
              >
                <div className="rounded-full p-1.5 bg-primaryPurple text-white">
                  <svg width="14" height="12" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M6.66797 12.3333L10.0013 9M10.0013 9L13.3346 12.3333M10.0013 9V16.5M16.668 12.9524C17.6859 12.1117 18.3346 10.8399 18.3346 9.41667C18.3346 6.88536 16.2826 4.83333 13.7513 4.83333C13.5692 4.83333 13.3989 4.73833 13.3064 4.58145C12.2197 2.73736 10.2133 1.5 7.91797 1.5C4.46619 1.5 1.66797 4.29822 1.66797 7.75C1.66797 9.47175 2.36417 11.0309 3.49043 12.1613"
                      stroke="#ffffff"
                      stroke-width="1.66667"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </div>

              {/* Hidden File Input */}
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files[0];
                  if (file) {
                    // Create a local URL for the selected image
                    const imageUrl = URL.createObjectURL(file);

                    // Update the form state with the new image URL
                    setFieldValue('profilePicture')(imageUrl);
                  }
                }}
              />
            </div>
          </div>

          <div className="flex flex-col gap-6">
            <TextInput
              label="Full Name"
              name="name"
              placeholder="User Name"
              value={form.name}
              onChange={setFieldValue('name')}
              validators={[isRequired()]}
              requiredLabel
            />

            <TextInput
              label="Email"
              name="email"
              placeholder="Email"
              value={form.email}
              onChange={setFieldValue('email')}
              validators={[isRequired()]}
              requiredLabel
            />

            <div className="flex w-full">
              <div className="w-full">
                <TextInput
                  label="New Password"
                  name="newPassword"
                  placeholder="New Password"
                  autoComplete="new-password"
                  value={form.password}
                  type={showNewPassword ? 'text' : 'password'}
                  onChange={setFieldValue('password')}
                  validators={form.password ? [isRequired(), isNotSpaces(), minLength(8), maxLength(50), validatePasswordRegex(Regex.password)] : []}
                />
              </div>

              <div className="mt-8" onClick={() => handleShow('newPass')}>
                <Icon
                  className="ml-3 p-5 w-8 h-8 rounded-md cursor-pointer bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400"
                  width="25"
                  icon={!showNewPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                />
              </div>
            </div>

            <div className="flex w-full">
              <div className="w-full">
                <TextInput
                  label="Confirm Password"
                  name="confirmPassword"
                  placeholder="Confirm Password"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(value) => setConfirmPassword(value)}
                  disabled={!form.password}
                />
              </div>
              <div className="mt-8" onClick={() => handleShow()}>
                <Icon
                  className="ml-3 p-5 w-8 h-8 rounded-md cursor-pointer bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400"
                  width="25"
                  icon={!showConfirmPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                />
              </div>
            </div>
            {(confirmPassword && confirmPassword !== form.password) || showError ? (
              <label className="text-red-500 text-sm">Confirm password doesn't match the password</label>
            ) : null}
          </div>
        </div>
        <Button className="w-full" type="submit" label={'Update'} />
      </Form>
    </Dialog>
  );
};
