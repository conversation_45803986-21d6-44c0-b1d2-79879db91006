// React
import { useContext, useEffect, useState } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';

// Context
import { AppContext } from '/src/components/provider';

// Core
import {
  Icon,
  useFetchList,
  TestSeniorityLevel,
  useScreenSize,
  useValidate,
  Table,
  CustomIcon,
  FormatDate,
  TestDifficulty,
  AvarageScore,
} from '/src';

// Components

// Flowbite

export const PromotionDetails = () => {
  // User Data
  const { userData } = useContext(AppContext);

  // Permissions
  const isPermitted = userData?.roles.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const isSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));

  // State
  const [selectAll, setSelectAll] = useState(false);
  const [startDate, setStartDate] = useState(new Date());
  const [dueDate, setDueDate] = useState(() => {
    const result = new Date(startDate);
    result.setDate(result.getDate() + 1);
    return result;
  });
  const [showNote, setShowNote] = useState(true);
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);

  // Hooks
  const screen = useScreenSize();
  const { isRequired, validateRegex, minLength, maxLength, countryCodeNumberValid } = useValidate();
  const initialFilters = {};
  const filterFeedData = Object.keys(initialFilters);
  const { ready, loading, setLoading, list, count, filters, setFilters, search, pagination, refresh } = useFetchList('applicants/list', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: initialFilters,
  });

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  // TODO: table needs to be responsive
  const headerData = ['Sale Name', 'Duration', 'Users', 'Actions'];

  const fakeData = [
    {
      id: Math.random(),
      users: 3,
      name: 'Eid Sale',
      duration: '3 Days',
      engagement: 2,
      lastActiveStatus: 1,
      date: 'Apr 2, 2025 12:00AM - Apr 3 ,2025 3:00PM ',
      actions: (
        <div>
          <div className="flex align-middle items-center gap-8">
            <CustomIcon definedIcon="copy" stroke="#535862" width="20" height="20" />
            <CustomIcon definedIcon="download" stroke="#535862" width="20" height="20" />
            <CustomIcon definedIcon="trash" stroke="#535862" width="20" height="20" />
          </div>
        </div>
      ),
    },
    {
      id: Math.random(),
      name: 'Ramadan Sale',
      duration: '3 Days',
      engagement: 2,
      lastActiveStatus: 1,
      date: 'Apr 2, 2025 12:00AM - Apr 3 ,2025 3:00PM ',
      users: 0,
      actions: (
        <div>
          <div className="flex align-middle items-center gap-8">
            <CustomIcon definedIcon="copy" stroke="#535862" width="20" height="20" />
            <CustomIcon definedIcon="download" stroke="#535862" width="20" height="20" />
            <CustomIcon definedIcon="trash" stroke="#535862" width="20" height="20" />
          </div>
        </div>
      ),
    },
    {
      id: Math.random(),
      name: 'Easter Sale',
      duration: '3 Days',
      lastActiveStatus: 1,
      engagement: 2,
      date: 'Apr 2, 2025 12:00AM - Apr 3 ,2025 3:00PM ',
      users: 1,
      actions: (
        <div>
          <div className="flex align-middle items-center gap-8">
            <CustomIcon definedIcon="copy" width="20" stroke="#535862" height="20" />
            <CustomIcon definedIcon="download" stroke="#535862" width="20" height="20" />
            <CustomIcon definedIcon="trash" stroke="#535862" width="20" height="20" />
          </div>
        </div>
      ),
    },
  ];

  const handleEnagement = (engagement) => {
    if (engagement === 1) {
      return {
        text: 'active',
        color: 'text-green-700',
        background: 'bg-green-100',
        border: 'border-green-300',
      };
    }
  };

  return (
    <>
      <Table
        ready={ready}
        loading={loading}
        title="Promotion List"
        searchPlaceholder="Search for promotions..."
        count={count}
        search={search}
        filters={filters}
        setFilters={setFilters}
        // filterFeedData={filterFeedData}
        // drawerFilter={{
        //   filterCountNumber: filterCountNumber,
        //   isShowDrawerFilter: isShowDrawerFilter,
        //   setShowDrawerFilter: setShowDrawerFilter,
        // }}
        pagination={pagination}
        rows={fakeData}
        backupRows={backupList}
        slots={{
          saleName: (_, row) => {
            return <div className="whitespace-nowrap text-[14px] font-normal text-[#535862] dark:text-white">{row?.name}</div>;
          },
          duration: (_, row) => {
            return (
              <div className="flex flex-col gap-2">
                <div className="flex gap-3 items-center">
                  <span className="text-sm font-normal text-[#535862]">{row?.duration}</span>
                  <div
                    className={`${handleEnagement(row?.lastActiveStatus).background} ${handleEnagement(row?.lastActiveStatus).border} ${
                      handleEnagement(row?.lastActiveStatus).color
                    } rounded-full w-fit px-2 py-0.5 border capitalize text-sm`}
                  >
                    {handleEnagement(row?.lastActiveStatus).text}
                  </div>
                </div>
                <div>
                  <p className="text-sm font-normal text-[#535862]">{row?.date}</p>
                </div>
              </div>
            );
          },
          users: (_, row) => {
            return <div className="capitalize font-semibold text-base text-[#667085] truncate">{row?.users}</div>;
          },
          averageScore: (_, row) => {
            return (
              <div className="w-fit">
                <AvarageScore score={row?.averageScore} />
              </div>
            );
          },
          actinos: (_, row) => {
            return <div className="capitalize font-semibold text-base text-[#667085] truncate">{row?.actions}</div>;
          },
        }}
        columns={[
          {
            key: 'saleName',
            label: 'Sale Name',
            primary: true,
            width: '22%',
          },
          {
            key: 'duration',
            label: 'Duration',
            // primary: true,
            width: '15%',
          },
          {
            key: 'users',
            label: 'Users',
            // primary: true,
            width: '10%',
          },

          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            // buttons(_, row) {
            //   return [
            //     {
            //       label: 'View',
            //       customIcon: 'eye',
            //       iconWidth: '22',
            //       iconHeight: '22',
            //       color: 'text-black dark:text-white',
            //       path: `/app/assessment-status/assessment-report/screening/${row?._id}`,
            //     },
            //     {
            //       label: 'New User',
            //       customIcon: 'newUser',
            //       iconWidth: '22',
            //       iconHeight: '22',
            //       color: 'text-black dark:text-white',
            //       // path: `/app/applicants/progress/`,
            //     },
            //     // {
            //     //   label: 'Assign',
            //     //   customIcon: 'assign',
            //     //   iconWidth: '22',
            //     //   iconHeight: '22',
            //     //   color: 'text-black dark:text-white',
            //     //   // onClick: () => {},
            //     //   dropDown: [
            //     //     {
            //     //       label: 'Interview',
            //     //       color: 'text-black dark:text-white',
            //     //       customIcon: 'interview',
            //     //       element: (
            //     //         <span
            //     //           className={`w-24 text-xs px-2 py-1 rounded-full shadow-md font-bold tracking-wide bg-magic-gradient bg-[length:200%]  text-white overflow-hidden`}
            //     //         >
            //     //           AI Magic ✨
            //     //         </span>
            //     //       ),
            //     //       onClick: () => {
            //     //         if (isSuperAdmin || userData?.features?.assignInterview > 0) {
            //     //           setAssignInterviewTestVisible(true);
            //     //           setApplicantDetails(row);
            //     //         } else {
            //     //           setNeedSubscription(true);
            //     //         }
            //     //       },
            //     //     },
            //     //   ],
            //     // },
            //   ];
            // },
          },
        ]}
        // multiSelectedRow={{
        //   selectedIds: selectedIds,
        //   setSelectedIds: setSelectedIds,
        //   handleArchiveSelectedIds: handleArchiveSelectedIds,
        // }}
        noDataFound={{
          customIcon: 'applicant',
          message: 'No assessment created yet',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        // addButtonLabel=""
        // onClickAdd={() => {}}
        // actions={[]}
        hideJumbotron
        isScrollableTabsExists
      />
    </>
  );
};
