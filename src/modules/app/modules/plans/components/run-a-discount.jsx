import { useState } from 'react';
import { Api, useNotify, Select, TextInput, Drawer, Checkbox, Form, useForm, useValidate, NoDataFound, CustomIcon } from '/src';
import { Datepicker, ToggleSwitch } from 'flowbite-react';

export const RunAdiscount = ({ setIsRunAdiscountVisible }) => {
  // State to track multiple discount forms
  const [discountForms, setDiscountForms] = useState([{ id: 1 }]);

  const onClose = () => {
    setIsRunAdiscountVisible(false);
  };

  // Function to add another discount form
  const addAnotherDiscount = () => {
    const newId = discountForms.length + 1;
    setDiscountForms([...discountForms, { id: newId }]);
  };

  // Function to remove a specific discount form
  const removeDiscountForm = (idToRemove) => {
    if (discountForms.length > 1) {
      setDiscountForms(discountForms.filter((form) => form.id !== idToRemove));
    }
  };
  return (
    <Drawer split className="justify-between" onClose={onClose}>
      <Drawer.SingleView>
        <Drawer.Header onClose={onClose} headerLabel="Set up a Discount" />
        <div className="overflow-auto space-y-3">
          {discountForms.map((form, index) => (
            <div key={form.id} className="mb-8">
              {index > 0 && (
                <div className="flex justify-between items-center mb-4 border-t pt-4">
                  <h3 className="font-medium">Discount #{form.id}</h3>
                  <button onClick={() => removeDiscountForm(form.id)} className="text-red-500 text-sm">
                    <CustomIcon definedIcon="trash" />
                  </button>
                </div>
              )}

              <Form className="grid grid-cols-2 gap-4 w-full space-y-5 my-1 border p-6 rounded-md items-center">
                <TextInput label="Discount Amount" name={`DiscountAmount_${form.id}`} placeholder="....." requiredLabel className="w-full" />

                <div className="grid grid-cols-2 gap-3 w-full">
                  <Select
                    label="Discount Type"
                    name={`DiscountType_${form.id}`}
                    dropIcon={true}
                    placeholder="Percentage off"
                    className="w-full"
                    requiredLabel
                  />
                  <Select name={`SeniorityLevel_${form.id}`} placeholder="20" className="w-full" requiredLabel />
                </div>

                <div className="flex flex-col w-full col-span-2">
                  <label className="text-[#707F8F] text-base font-medium">Start Duration</label>
                  <div className="grid grid-cols-2 gap-4 w-full">
                    <div className="w-full">
                      <Datepicker
                        className="w-full"
                        theme={{
                          root: {
                            base: 'w-full',
                          },
                        }}
                        showTodayButton={false}
                        showClearButton={false}
                      />
                    </div>
                    <div className="w-full">
                      <Datepicker
                        className="w-full"
                        theme={{
                          root: {
                            base: 'w-full',
                          },
                        }}
                        showTodayButton={false}
                        showClearButton={false}
                      />
                    </div>
                  </div>
                </div>

                <div className="flex flex-col space-y-7 col-span-2 w-full">
                  <div className="flex gap-4">
                    <span className="text-[#707F8F] font-medium text-base">Apply to All Plans</span>
                    <ToggleSwitch checked={true} color="purple" sizing="md" />
                  </div>
                  <TextInput placeholder="Select plans" disabled requiredLabel className="w-full" />
                </div>

                <div className="flex gap-4">
                  <Checkbox name={`willSendEmail_${form.id}`} label="Send email to organizations" value={true} />
                </div>
              </Form>
            </div>
          ))}

          <div className="flex mt-7 p-3">
            <span onClick={addAnotherDiscount} className="text-purple-600 cursor-pointer font-medium flex items-center">
              + Add Another Discount
            </span>
          </div>
        </div>

        <Drawer.Footer>
          <Drawer.Footer.Button label="Cancel" tertiary onClick={onClose} />
          <Drawer.Footer.Button label={'confirm'} mainButton />
        </Drawer.Footer>
      </Drawer.SingleView>
    </Drawer>
  );
};
