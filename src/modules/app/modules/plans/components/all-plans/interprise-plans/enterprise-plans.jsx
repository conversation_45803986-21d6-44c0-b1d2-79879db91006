import { Button, useFetchList } from 'src';

import { Avatar, AvatarGroup, AvatarGroupCounter } from 'flowbite-react';
const avatarCount = 11;

export const EnterprisePlans = () => {
  const { ready, loading, setLoading, list, pagination } = useFetchList('plans/list/vip', {
    pagination: {
      page: 1,
      size: 10,
    },
  });

  const correctIcon = () => {
    return (
      <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="path-1-inside-1_15449_16866" fill="white">
          <path d="M0 14C0 6.26801 6.26801 0 14 0C21.732 0 28 6.26801 28 14C28 21.732 21.732 28 14 28C6.26801 28 0 21.732 0 14Z" />
        </mask>
        <path
          d="M14 27C6.8203 27 1 21.1797 1 14H-1C-1 22.2843 5.71573 29 14 29V27ZM27 14C27 21.1797 21.1797 27 14 27V29C22.2843 29 29 22.2843 29 14H27ZM14 1C21.1797 1 27 6.8203 27 14H29C29 5.71573 22.2843 -1 14 -1V1ZM14 -1C5.71573 -1 -1 5.71573 -1 14H1C1 6.8203 6.8203 1 14 1V-1Z"
          fill="url(#paint0_linear_15449_16866)"
          mask="url(#path-1-inside-1_15449_16866)"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M19.385 8.45837C19.9392 7.88997 20.8877 7.84389 21.5034 8.35545C22.1192 8.86701 22.1691 9.74249 21.6149 10.3109L12.6149 19.5416C12.04 20.1313 11.047 20.1554 10.4393 19.5945L6.43934 15.9021C5.85355 15.3614 5.85355 14.4847 6.43934 13.944C7.02513 13.4033 7.97487 13.4033 8.56066 13.944L11.4427 16.6044L19.385 8.45837Z"
          fill="#149041"
        />
        <defs>
          <linearGradient id="paint0_linear_15449_16866" x1="14" y1="0" x2="14" y2="28" gradientUnits="userSpaceOnUse">
            <stop stop-color="#F4F4F5" />
            <stop offset="1" stop-color="#C8D6E5" />
          </linearGradient>
        </defs>
      </svg>
    );
  };

  const wrongIcon = () => {
    return (
      <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="path-1-inside-1_15449_18096" fill="white">
          <path d="M0 14C0 6.26801 6.26801 0 14 0C21.732 0 28 6.26801 28 14C28 21.732 21.732 28 14 28C6.26801 28 0 21.732 0 14Z" />
        </mask>
        <path
          d="M14 27C6.8203 27 1 21.1797 1 14H-1C-1 22.2843 5.71573 29 14 29V27ZM27 14C27 21.1797 21.1797 27 14 27V29C22.2843 29 29 22.2843 29 14H27ZM14 1C21.1797 1 27 6.8203 27 14H29C29 5.71573 22.2843 -1 14 -1V1ZM14 -1C5.71573 -1 -1 5.71573 -1 14H1C1 6.8203 6.8203 1 14 1V-1Z"
          fill="url(#paint0_linear_15449_18096)"
          mask="url(#path-1-inside-1_15449_18096)"
        />
        <path
          d="M15.5142 14.0009L20.4142 9.1009C20.5967 8.91461 20.6984 8.6638 20.6971 8.40297C20.6958 8.14214 20.5916 7.89237 20.4071 7.70793C20.2227 7.5235 19.9729 7.4193 19.7121 7.41798C19.4513 7.41666 19.2005 7.51833 19.0142 7.7009L14.1142 12.6009L9.21418 7.7009C9.02789 7.51833 8.77708 7.41666 8.51625 7.41798C8.25542 7.4193 8.00565 7.5235 7.82121 7.70793C7.63678 7.89237 7.53258 8.14214 7.53126 8.40297C7.52995 8.6638 7.63162 8.91461 7.81418 9.1009L12.7142 14.0009L7.81418 18.9009C7.63162 19.0872 7.52995 19.338 7.53126 19.5988C7.53258 19.8597 7.63678 20.1094 7.82121 20.2939C8.00565 20.4783 8.25542 20.5825 8.51625 20.5838C8.77708 20.5851 9.02789 20.4835 9.21418 20.3009L14.1142 15.4009L19.0142 20.3009C19.2005 20.4835 19.4513 20.5851 19.7121 20.5838C19.9729 20.5825 20.2227 20.4783 20.4071 20.2939C20.5916 20.1094 20.6958 19.8597 20.6971 19.5988C20.6984 19.338 20.5967 19.0872 20.4142 18.9009L15.5142 14.0009Z"
          fill="#982828"
        />
        <defs>
          <linearGradient id="paint0_linear_15449_18096" x1="14" y1="0" x2="14" y2="28" gradientUnits="userSpaceOnUse">
            <stop stop-color="#F4F4F5" />
            <stop offset="1" stop-color="#C8D6E5" />
          </linearGradient>
        </defs>
      </svg>
    );
  };

  const formatKey = (key) => {
    return key
      .replace(/([A-Z]+)([A-Z][a-z])/g, '$1 $2')
      .replace(/([a-z\d])([A-Z])/g, '$1 $2')
      .replace(/^./, (str) => str.toUpperCase());
  };

  return (
    <div className="space-y-6 my-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6">
        {list?.items?.map((item) => (
          <div
            className={`flex space-y-5 flex-col gap-4 bg-gradient-to-b from-white to-[#F7EFFF]/80 shadow-lg rounded-lg p-6 min-w-96`}
            key={item.id}
          >
            <p className="text-xl font-semibold text-[#5E2EC3]">{item?.name}</p>

            <span className="text-[28px] font-bold text-[#1A1A1A]">
              ${item.price}
              <p className="text-sm text-gray-500">
                {item.durationInDays === 30 && '/ per month'}
                {item.durationInDays === 365 && '/ per year'}
              </p>
            </span>
            <div className="flex justify-center text-lg">
              <Button tertiary label="Edit Subscription Details" labelSize="text-base" size="lg" className="w-full font-semibold !text-lg" />
            </div>

            <div className="flex gap-3 items-center">
              <img src="https://placehold.co/400/transparent/fff" className="w-10 rounded-full border" alt="Logo" />
              <div className="font-medium">
                <p className="capitalize">{item.organizationName}</p>
                <p className="text-gray-600">{item.organizationEmail}</p>
              </div>
            </div>

            {/* this code may be needed */}
            {/* <AvatarGroup className="p-2">
              [...Array(avatarCount)].map((_, index) => (
              <Avatar key={index} rounded />
              ))}
              <Avatar.Counter className="bg-[#F3F4F6] text-[#535862] text-[13px] font-semibold text-center" total={5} href="#" />
            </AvatarGroup> */}

            <hr class="border-t border-dashed border-gray-300" />
            <div className="space-y-3">
              {item?.features &&
                Object.entries(item.features)
                  .sort(([keyA, valueA], [keyB, valueB]) => {
                    const aActive = typeof valueA === 'number' ? valueA > 0 : !!valueA;
                    const bActive = typeof valueB === 'number' ? valueB > 0 : !!valueB;
                    if (aActive && !bActive) return -1;
                    if (!aActive && bActive) return 1;
                    return 0;
                  })
                  .map(([key, value]) => {
                    const formattedKey = formatKey(key);
                    const isActive = typeof value === 'number' ? value > 0 : !!value;

                    return (
                      <div className="flex p-2 items-center gap-2 ">
                        <div className="flex gap-3 items-center ">
                          {isActive ? correctIcon() : wrongIcon()}
                          <p className="text-[#1A1A1A] text-lg font-medium">
                            {typeof value === 'number' && value > 0 && value} {formattedKey}
                            {typeof value === 'string' && `: ${value.charAt(0).toUpperCase() + value.slice(1)}`}
                          </p>
                        </div>
                      </div>
                    );
                  })}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
