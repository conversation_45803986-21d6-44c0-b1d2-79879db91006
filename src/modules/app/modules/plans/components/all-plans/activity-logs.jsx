// import { Steps } from 'rsuite';
// import { CustomIcon } from '/src';
// const styles = {
//   width: '500px',
//   display: 'inline-table',
//   verticalAlign: 'top',
// };
// export const ActivityLogs = () => {
//   return (
//     <Steps current={1} vertical style={styles}>
//       <Steps.Item
//         title={
//           <div>
//             <span className="font-bold">george</span>
//             <span className="font-semibold">sdfjosdjs</span>
//           </div>
//         }
//         icon={<CustomIcon definedIcon="arrowDownRight" className=" rounded  text-white" />}
//         description="Description"
//       />

//       <Steps.Item icon={<CustomIcon definedIcon="trash" className=" rounded  text-white" />} title="In Progress" description="ay hamada " />
//       <Steps.Item title="Waiting" description="Description" />
//       <Steps.Item title="Waiting" description="Description" />
//     </Steps>
//   );
// };

// React
import { useState } from 'react';

// Flowbite
import { Datepicker, Select as SelectFlowbite } from 'flowbite-react';

// Core
import { Icon, CustomIcon } from '/src';

// Components

export const ActivityLogs = () => {
  // Search
  const [search, setSearch] = useState('');

  return (
    <div className="space-y-4 mt-6">
      {/* <h2 className="flex items-center gap-2 text-base dark:text-white">
        Activity Logs
        <span className="bg-[#f9f5ff] dark:bg-[#1f1a2e] text-primaryPurple text-sm font-medium rounded-full px-2.5 border">0</span>
      </h2> */}

      <div className="flex justify-between items-center gap-2">
        {/* Search bar */}
        <div className="flex justify-between items-center grow space-y-0 rounded-lg relative">
          <Icon icon="carbon:search" width="20" className="size-5 text-gray-500 dark:text-gray-400 absolute left-3 pointer-events-none" />
          <input
            type="text"
            placeholder="Search..."
            className="w-full p-2 pl-10 dark:bg-gray-700 bg-gray-white text-[13.5px] text-gray-800 border border-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white truncate rounded-lg focus:ring-0 focus:border-gray-300 shadow-sm"
            value={search}
            onInput={(e) => setSearch(e.target.value)}
          />
        </div>

        {/* <SelectFlowbite id="countries" required className="w-72">
          <option>All Companies</option>
          <option>First</option>
          <option>Second</option>
          <option>Third</option>
        </SelectFlowbite> */}

        <div className="calendar flex flex-row justify-end items-center gap-2 text-gray-700 dark:text-gray-300 rounded-lg h-fit">
          <Datepicker
            className="inline-block w-full sm:w-44"
            // onSelectedDateChanged={() => {}}
            showTodayButton={false}
            showClearButton={false}
            // value=""
          />
        </div>
      </div>

      {/* <div className="space-y-8">
        <div className="space-y-4">
          <h2 className="text-[#566577] dark:text-white font-medium">Today</h2>
          <div className="space-y-8 text-sm">
            <div className="flex items-center gap-5 text-[#566577] dark:text-white relative">
              <CustomIcon definedIcon="arrowDownRight" />
              <div>
                <p>
                  Organization <span className="text-black font-medium underline">SmartHire</span> Downgraded from{' '}
                  <span className="text-black font-medium underline">Enterprise</span> Plan To{' '}
                  <span className="text-black font-medium underline">Basic Plan</span>
                </p>
                <p>2 hours ago</p>
              </div>
              <p className="w-0.5 h-8 bg-[#E7E9EB] absolute top-full left-4 translate-x-1/2 -z-10" />
            </div>

            <div className="flex items-center gap-5 text-[#566577] dark:text-white relative">
              <CustomIcon definedIcon="arrowUpRight" />
              <div>
                <p className="dark:text-white">
                  Organization <span className="text-black font-medium underline"> Martena Weber </span> Upgraded from{' '}
                  <span className="text-black font-medium underline">Basic Plan</span> To{' '}
                  <span className="text-black font-medium underline">Pro Plan</span>
                </p>
                <p>4 hours ago</p>
              </div>
              <p className="w-0.5 h-8 bg-[#E7E9EB] absolute top-full left-4 translate-x-1/2 -z-10" />
            </div>

            <div className="flex items-center gap-5 text-[#566577] dark:text-white relative">
              <CustomIcon definedIcon="envelope" />
              <div>
                <p>
                  Organization <span className="text-black font-medium underline">Martena Weber</span> Changed Billing Address To{' '}
                  <span className="text-black font-medium underline">Martena <EMAIL></span>
                </p>
                <p>8 hours ago</p>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-[#566577] dark:text-white font-medium">Yesterday</h2>
          <div className="space-y-8 text-sm">
            <div className="flex items-center gap-5 text-[#566577] dark:text-white relative">
              <CustomIcon definedIcon="arrowDownRight" />
              <div>
                <p>
                  Organization <span className="text-black font-medium underline">SmartHire</span> Downgraded from{' '}
                  <span className="text-black font-medium underline">Enterprise</span> Plan To{' '}
                  <span className="text-black font-medium underline">Basic Plan</span>
                </p>
                <p>2 hours ago</p>
              </div>
              <p className="w-0.5 h-8 bg-[#E7E9EB] absolute top-full left-4 translate-x-1/2 -z-10" />
            </div>

            <div className="flex items-center gap-5 text-[#566577] dark:text-white relative">
              <CustomIcon definedIcon="arrowUpRight" />
              <div>
                <p className="dark:text-white">
                  Organization <span className="text-black font-medium underline"> Martena Weber </span> Upgraded from{' '}
                  <span className="text-black font-medium underline">Basic Plan</span> To{' '}
                  <span className="text-black font-medium underline">Pro Plan</span>
                </p>
                <p>4 hours ago</p>
              </div>
              <p className="w-0.5 h-8 bg-[#E7E9EB] absolute top-full left-4 translate-x-1/2 -z-10" />
            </div>

            <div className="flex items-center gap-5 text-[#566577] dark:text-white relative">
              <CustomIcon definedIcon="envelope" />
              <div>
                <p>
                  Organization <span className="text-black font-medium underline">Martena Weber</span> Changed Billing Address To{' '}
                  <span className="text-black font-medium underline">Martena <EMAIL></span>
                </p>
                <p>8 hours ago</p>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-[#566577] dark:text-white font-medium">April 20,2025</h2>
          <div className="space-y-8 text-sm">
            <div className="flex items-center gap-5 text-[#566577] dark:text-white relative">
              <CustomIcon definedIcon="arrowDownRight" />
              <div>
                <p>
                  Organization <span className="text-black font-medium underline">SmartHire</span> Downgraded from{' '}
                  <span className="text-black font-medium underline">Enterprise</span> Plan To{' '}
                  <span className="text-black font-medium underline">Basic Plan</span>
                </p>
                <p>2 hours ago</p>
              </div>
              <p className="w-0.5 h-8 bg-[#E7E9EB] absolute top-full left-4 translate-x-1/2 -z-10" />
            </div>

            <div className="flex items-center gap-5 text-[#566577] dark:text-white relative">
              <CustomIcon definedIcon="arrowUpRight" />
              <div>
                <p className="dark:text-white">
                  Organization <span className="text-black font-medium underline"> Martena Weber </span> Upgraded from{' '}
                  <span className="text-black font-medium underline">Basic Plan</span> To{' '}
                  <span className="text-black font-medium underline">Pro Plan</span>
                </p>
                <p>4 hours ago</p>
              </div>
              <p className="w-0.5 h-8 bg-[#E7E9EB] absolute top-full left-4 translate-x-1/2 -z-10" />
            </div>

            <div className="flex items-center gap-5 text-[#566577] dark:text-white relative">
              <CustomIcon definedIcon="envelope" />
              <div>
                <p>
                  Organization <span className="text-black font-medium underline">Martena Weber</span> Changed Billing Address To{' '}
                  <span className="text-black font-medium underline">Martena <EMAIL></span>
                </p>
                <p>8 hours ago</p>
              </div>
            </div>
          </div>
        </div>
      </div> */}
    </div>
  );
};
