import { useState } from 'react';
import { Api, useNotify, Select, TextInput, Drawer, Checkbox, Form, useForm, useValidate, NoDataFound, CustomIcon } from '/src';
import { Input, InputGroup, Whisper, Tooltip } from 'rsuite';
import { Datepicker, ToggleSwitch } from 'flowbite-react';

export const CreatePlan = ({ setIsCreatePlanVisible }) => {
  const styles = {
    width: 300,
    marginBottom: 10,
  };
  const onClose = () => {
    setIsCreatePlanVisible(false);
  };

  return (
    <Drawer split className="justify-between" onClose={onClose}>
      <Drawer.SingleView>
        <Drawer.Header
          onClose={onClose}
          headerLabel={
            <div className="flex p-2 space-y-4 flex-col">
              <div className="flex gap-3 items-center ">
                <img src="https://placehold.co/400/transparent/fff" className="w-10 rounded-full border" alt="Logo" />
                <div className="font-medium ">
                  <p className="capitalize text-[#834CFF] text-[23px] font-medium">The Pass</p>
                </div>
              </div>
              <div className="text-[24px]  font-semibold">
                <p>Create a Plan</p>
              </div>
            </div>
          }
        />
        <div className="overflow-auto ">
          <Form className="grid grid-cols-2 gap-4    rounded-md items-center">
            <TextInput label="Plan Name " requiredLabel className="w-full" />

            <TextInput label="Plan Type  " requiredLabel className="w-full" />

            <div className="flex py-2 flex-col">
              <label className="text-[#707F8F] text-base font-medium">Monthly price</label>
              <InputGroup inside>
                <InputGroup.Addon>USD</InputGroup.Addon>
                <Input />
              </InputGroup>
            </div>

            <div className="flex py-2 flex-col">
              <label className="text-[#707F8F] text-base font-medium">Yearly Price *</label>
              <InputGroup inside>
                <InputGroup.Addon>USD </InputGroup.Addon>
                <Input />
              </InputGroup>
            </div>

            <div className="flex flex-col py-2 col-span-2 w-full">
              <div className="flex gap-4">
                <span className="text-[#707F8F] font-medium text-base">Add a Usage Limit</span>
                <ToggleSwitch checked={true} color="purple" sizing="md" />
              </div>
            </div>
            <div className="col-span-2 py-2 w-full">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 w-full">
                <TextInput label="AI Interviews" requiredLabel placeholder="3" className="w-full" />
                <TextInput label="Tests " requiredLabel placeholder="3" className="w-full" />
                <TextInput label="Users " requiredLabel placeholder="3" className="w-full" />
              </div>
            </div>

            <div className="flex  gap-4">
              <span className="text-[#707F8F] font-medium text-base">This Plan has Trial Period</span>
              <ToggleSwitch checked={true} color="purple" sizing="md" />
            </div>

            <div className="col-span-2 w-full pt-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-4 w-full">
                <TextInput requiredLabel placeholder="3" className="w-full" />
                <TextInput requiredLabel placeholder="Day" className="w-full" />
              </div>
            </div>

            <div className="flex flex-col py-3 gap-4">
              <Checkbox label="Add custom branding" value={true} />
              <Checkbox label="Test multiple applicants with single link" value={false} />
              <Checkbox label="Access to given and correct answers" value={true} />
            </div>
          </Form>
        </div>

        <Drawer.Footer>
          <Drawer.Footer.Button label="Cancel" tertiary onClick={onClose} />
          <Drawer.Footer.Button label={'Create'} mainButton />
        </Drawer.Footer>
      </Drawer.SingleView>
    </Drawer>
  );
};
