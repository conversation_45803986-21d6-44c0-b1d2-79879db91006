// Core
import { useState } from 'react';
import { Jumbotron, Button, Icon, CustomIcon, ScrollableTabs } from '/src';
import { Link, useLocation } from 'react-router-dom';
import { BasePlans } from '../components/all-plans/base-plans/base-plans';
import { AllPlans } from '../components/all-plans';
import { PromotionDetails } from '../components/promotion-details';
import { ActivityLogs } from '../components/all-plans/activity-logs';
import { RunAdiscount } from '../components/run-a-discount';
import { CreatePlan } from '../components/create-plan';

export const PlansListPage = () => {
  const [activeTab, setActiveTab] = useState(0);
  // const [isRunAdiscountVisible, setIsRunAdiscountVisible] = useState(false);
  // const [isCreatePlanVisible, setIsCreatePlanVisible] = useState(false);

  const tabs = [
    { title: 'Plan List', data: <AllPlans /> },
    // { title: 'Promotion Details ', data: <PromotionDetails /> },
    // { title: 'Activity Logs', data: <ActivityLogs /> },
  ];

  return (
    <div>
      <div className="flex flex-wrap justify-between sm:items-center gap-4">
        <Jumbotron />
        {/* <div className="flex gap-3 flex-wrap  ">
          <Button
            type="button"
            onClick={() => setIsRunAdiscountVisible(true)}
            customIcon="discount"
            label="Run a Discount"
            className="w-fit min-w-20"
            tertiary
          />

          <Button type="button" className="w-fit min-w-20">
            <div className="sm:mr-2 h-5 w-5">
              <Icon icon="mdi:add" width="22" />
            </div>
            <p onClick={() => setIsCreatePlanVisible(true)} className="text-nowrap">
              Create Plan
            </p>
          </Button>
        </div> */}
      </div>

      <ScrollableTabs
        data={tabs}
        selectedTab={{
          activeTab: activeTab,
          setActiveTab: setActiveTab,
        }}
      />

      {tabs[activeTab].data}
      {/* 
      {isCreatePlanVisible && <CreatePlan setIsCreatePlanVisible={setIsCreatePlanVisible} />}
      {isRunAdiscountVisible && <RunAdiscount setIsRunAdiscountVisible={setIsRunAdiscountVisible} />} */}
    </div>
  );
};
