import { Navigate } from 'react-router-dom';

import { PlansMainLayout } from '../layouts/main';

import { PlansListPage } from '../pages/list';

export default [
  {
    path: 'plans',
    element: <PlansMainLayout />,
    children: [
      {
        path: '',
        element: <Navigate to="/app/plans/list" />,
      },

      {
        path: 'list',
        element: <PlansListPage />,
        loader() {
          return {
            label: 'Plans Management',
            icon: 'material-symbols:person-raised-hand-rounded',
            title: 'Subscription Plans ',
            subtitle: 'Monitor, manage, and create plans that scale with your goals.',
          };
        },
      },
    ],
  },
];
