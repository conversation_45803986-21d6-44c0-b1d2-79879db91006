import React, { useState, useEffect, useContext } from 'react';
import { Dropdown } from 'flowbite-react';
import { AppContext } from '/src/components/provider';

import { useDarkMode } from '/src/hooks/dark-mode';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthUtils } from '/src/hooks/auth-utils';
import { ProfileEditPage } from '../modules/profile/pages/edit-dialog';

import { Icon, Api, Logo } from '/src';
import { useNotify } from '../../../hooks';

export const AppHeader = ({ isDrawerVisible, setIsDrawerVisible }) => {
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [trackName, setTrackName] = useState('');

  // Hooks
  const { switchDarkMode, isDark } = useDarkMode();
  const { logout } = useAuthUtils();
  const navigate = useNavigate();
  const { userData, isSidebarVisible, setSidebarVisiblity } = useContext(AppContext);
  const location = useLocation();
  const currentUrl = location.pathname;
  const { notify } = useNotify();

  const fetchTrackName = async () => {
    if (userData?.trackId) {
      try {
        const response = await Api.get(`lookups/category/single/${userData.trackId}`);
        setTrackName(response.data.name);
      } catch (error) {
        notify.error(error.response.data.message);
      }
    }
  };

  useEffect(() => {
    fetchTrackName();
  }, [userData]);

  const renderRole = () => {
    if (userData?.roles.some((role) => ['super-admin'].includes(role))) {
      return (
        <div className="bg-[#F3E8FFC2] text-[#7E22CE] px-3 py-1 text-xs w-fit rounded-full">
          <p>Super Admin</p>
        </div>
      );
    } else if (userData?.roles.some((role) => ['admin'].includes(role))) {
      return (
        <div className="bg-[#F3E8FFC2] text-[#7E22CE] px-3 py-1 text-xs w-fit rounded-full">
          <p>Admin</p>
        </div>
      );
    } else if (userData?.roles.some((role) => ['content-creator'].includes(role))) {
      return (
        <div className="bg-[#FCE7F3C2] text-[#BE185D] px-3 py-1 text-xs w-fit rounded-full">
          <p>Content Creator</p>
        </div>
      );
    } else if (userData?.roles.some((role) => ['hr'].includes(role))) {
      return (
        <div className="bg-[#E3F2FDC2] text-[#3B82F6] px-3 py-1 text-xs w-fit rounded-full">
          <p>HR</p>
        </div>
      );
    }
  };

  useEffect(() => {
    setSidebarVisiblity(isDrawerVisible);
  }, [isDrawerVisible]);

  return (
    <div className={`${currentUrl.includes('pdf') ? 'hidden' : 'block'} print:hidden`}>
      <nav className="bg-white border-b border-[#f4f4f4] px-4 py-2.5 dark:bg-darkBackgroundCard dark:border-[#374151] fixed left-0 right-0 top-0 z-[60]">
        <div className="flex flex-wrap justify-between items-center">
          <div className="flex justify-between items-center ">
            {/* <button
              onClick={() => setIsDrawerVisible(!isDrawerVisible)}
              className="p-2 mr-2 text-gray-600 rounded-lg cursor-pointer hover:text-gray-900 hover:bg-gray-100 focus:bg-gray-100 dark:focus:bg-gray-700 focus:ring-2 focus:ring-gray-100 dark:focus:ring-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
            >
              <Icon icon="streamline:interface-setting-menu-1-button-parallel-horizontal-lines-menu-navigation-three-hamburger" />
            </button> */}

            <Icon
              onClick={() => setIsDrawerVisible(!isDrawerVisible)}
              icon={isDrawerVisible ? 'material-symbols:keyboard-double-arrow-left-rounded' : 'material-symbols:keyboard-double-arrow-right-rounded'}
              className={`text-[#8D5BF8] rounded-xl cursor-pointer`}
              width={30}
            />

            <div className="flex items-center justify-between cursor-pointer" onClick={() => navigate('/')}>
              <Logo className="h-8 sm:h-10" />
            </div>
          </div>
          <div className="flex items-center gap-4">
            {/* <button
              type="button"
              className="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 flex items-center justify-center"
              onClick={switchDarkMode}
            >
              <Icon icon={isDark ? 'ic:outline-light-mode' : 'ic:outline-dark-mode'} width="20px" />
            </button> */}

            <Dropdown
              renderTrigger={() => (
                <div className="w-8 h-8 flex items-center justify-center rounded-full cursor-pointer bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400">
                  <Icon width="20" icon="mdi:user" />
                </div>
              )}
              className="rounded-xl dark:bg-darkBackgroundCard"
            >
              <div className="min-w-56">
                <Dropdown.Header>
                  <div className="flex gap-3 items-start">
                    <div className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400">
                      <Icon width="20" icon="mdi:user" />
                    </div>
                    <div className="flex flex-wrap gap-2 items-center pl-3 max-w-[230px]">
                      <span className="block text-base max-w-[200px] break-words">{userData.name}</span>
                      {renderRole()}
                      <p className="text-[#667085] dark:text-gray-300 text-xs">{trackName}</p>
                    </div>
                  </div>
                </Dropdown.Header>
                <Dropdown.Item onClick={() => setCreateDialogVisibility(true)}>
                  <div className="flex ml-1 gap-5">
                    <Icon width="20" icon="lucide:user" />
                    <span>Edit profile </span>
                  </div>
                </Dropdown.Item>
                <Dropdown.Item onClick={logout}>
                  <div className="flex ml-1 gap-5">
                    <Icon width="20" icon="mynaui:logout" />
                    <span>Logout</span>
                  </div>
                </Dropdown.Item>
              </div>
            </Dropdown>
          </div>
        </div>
      </nav>
      {isCreateDialogVisible && <ProfileEditPage onClose={() => setCreateDialogVisibility(false)} />}
    </div>
  );
};
