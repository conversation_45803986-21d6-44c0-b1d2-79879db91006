// React
import { useContext, useEffect, useState } from 'react';
import { useLocation, Link, useNavigate } from 'react-router-dom';
import * as LucideIcons from 'lucide-react';
import { MessageCircleQuestion, MoonStar } from 'lucide-react';

// Core
import { Icon, useScreenSize, SidebarFilterPage, useDarkMode, useAuthUtils, Logo } from '/src';

// Context
import { AppContext } from '/src/components/provider';

// Flowbite
import { Dropdown } from 'flowbite-react';

// Components
import { ProfileEditPage } from '../modules/profile/pages/edit-dialog';

export const AppSidebar = ({ isDrawerVisible, setIsDrawerVisible }) => {
  // Hooks
  const location = useLocation();
  const screen = useScreenSize();
  const { userData, sidebarFilter, setSidebarFilter, sidebarSearch, setSidebarSearch } = useContext(AppContext);
  const { switchDarkMode, isDark } = useDarkMode();
  const { logout } = useAuthUtils();
  const navigate = useNavigate();

  const currentUrl = location.pathname;
  const isReport = currentUrl.includes('pdf');

  // Permissions
  const isPermittedSuperAdmin = userData?.roles.some((role) => ['super-admin'].includes(role));
  const isPermittedAdmin = userData?.roles?.some((role) => ['admin'].includes(role));
  const isPermittedContentCreator = userData?.roles?.some((role) => ['content-creator'].includes(role));
  const isPermittedHr = userData?.roles?.some((role) => ['hr'].includes(role));

  // State
  const [expandedItems, setExpandedItems] = useState({ Administration: true });
  const [trackName, setTrackName] = useState('');
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [search, setSearch] = useState('');

  // Computed
  const getActiveClasses = (itemPath) => {
    if (location.pathname.includes(itemPath)) {
      return 'bg-gray-100 dark:bg-gray-700 dark:text-white';
    }
    return '';
  };

  const toggleExpand = (itemLabel) => {
    setExpandedItems({
      ...expandedItems,
      [itemLabel]: !expandedItems[itemLabel],
    });
  };

  // Items
  const menuItems = [
    ...(isPermittedSuperAdmin
      ? [
          {
            label: 'Administration',
            icon: 'mdi:account-cog',
            children: [
              { label: 'Dashboard', path: '/app/dashboard-superadmin/statistics', lucide: 'ChartNoAxesColumnIncreasing' },
              { label: 'Organizations', path: '/app/organizations', lucide: 'Network' },
              { label: 'Individuals', path: '/app/users', lucide: 'Users' },
              { label: 'Plans', path: '/app/plans', lucide: 'ClipboardList' },
            ],
          },
        ]
      : []),

    ...(isPermittedAdmin
      ? [
          {
            label: 'Administration',
            icon: 'mdi:account-cog',
            children: [
              { label: 'Dashboard', path: '/app/dashboard', lucide: 'ChartNoAxesColumnIncreasing' },
              { label: 'Plans', path: '/app/plans', lucide: 'ClipboardList' },
              { label: 'Avatars', path: '/app/avatars', lucide: 'Smile' },
              { label: 'Team', path: '/app/users', lucide: 'Users' },
            ],
          },
        ]
      : []),

    {
      label: 'Assessments',
      path: '/assessment-templates',
      icon: 'mdi:clipboard-check-outline',
      children: [
        ...(isPermittedSuperAdmin || isPermittedAdmin || isPermittedHr
          ? [{ label: 'Screenings', path: '/app/assessment-templates/list/screening', lucide: 'FileSearch' }]
          : []),
        ...(isPermittedSuperAdmin || isPermittedAdmin || isPermittedContentCreator
          ? [
              { label: 'Tests', path: '/app/assessment-templates/list/test', lucide: 'FileCheck2' },
              { label: 'Interviews', path: '/app/assessment-templates/list/interview', lucide: 'FileVideo2' },
            ]
          : []),
      ],
    },
    ...(isPermittedSuperAdmin || isPermittedAdmin || isPermittedHr
      ? [
          {
            label: 'Reports',
            icon: 'mdi:account-box-outline',
            children: [
              { label: 'Applicants Report', path: '/app/applicants', lucide: 'IdCard' },
              { label: 'Assessment Report', path: '/app/assessment-report', lucide: 'FileChartColumn' },
            ],
          },
        ]
      : []),
    ...(isPermittedSuperAdmin || isPermittedAdmin || isPermittedContentCreator
      ? [
          {
            label: 'Question Bank',
            icon: 'mdi:help-circle-outline',
            children: [{ label: 'Question', path: '/app/questions', lucide: 'FileQuestion' }],
          },
        ]
      : []),
  ];

  const renderRole = () => {
    if (userData?.roles.some((role) => ['super-admin'].includes(role))) {
      return (
        <div className="bg-[#F3E8FFC2] text-[#7E22CE] px-3 py-1 text-xs w-fit rounded-full">
          <p>Super Admin</p>
        </div>
      );
    } else if (userData?.roles.some((role) => ['admin'].includes(role))) {
      return (
        <div className="bg-[#F3E8FFC2] text-[#7E22CE] px-3 py-1 text-xs w-fit rounded-full">
          <p>Admin</p>
        </div>
      );
    } else if (userData?.roles.some((role) => ['content-creator'].includes(role))) {
      return (
        <div className="bg-[#FCE7F3C2] text-[#BE185D] px-3 py-1 text-xs w-fit rounded-full">
          <p>Content Creator</p>
        </div>
      );
    } else if (userData?.roles.some((role) => ['hr'].includes(role))) {
      return (
        <div className="bg-[#E3F2FDC2] text-[#3B82F6] px-3 py-1 text-xs w-fit rounded-full">
          <p>HR</p>
        </div>
      );
    }
  };

  // const clearSidebarFilter = () => {
  //   setSidebarSearch(null);
  //   setSidebarFilter(null);
  // };

  // useEffect(() => {
  //   clearSidebarFilter();
  // }, location);

  // Use these styles to make black overlay visible and not scrollable
  // Make the scroll in list pages only be smooth
  /*
  useEffect(() => {
    if (showFilter) {
      document.querySelector('html').classList.add('overflow-x-hidden');
    } else {
      document.querySelector('html').classList.remove('overflow-x-hidden');
    }
  }, [showFilter]);
  */

  return (
    <>
      <aside
        id="drawer-navigation"
        className={`w-[220px] fixed top-0 left-0 z-[55] h-screen transition-transform bg-white border-r border-gray-200 dark:bg-darkBackgroundCard dark:border-[#374151] ${
          isReport && 'hidden'
        } ${isDrawerVisible ? '-translate-x-0' : '-translate-x-full'}`}
        aria-label="Sidenav"
      >
        <div className="flex flex-col justify-between gap-3 bg-white overflow-y-auto pt-[70px] pb-5 px-2 h-full  dark:bg-darkBackgroundCard">
          <ul className="">
            {/* <div className="flex justify-between items-center grow py-2 space-y-0 rounded-lg relative">
              <Icon icon="carbon:search" width="20" className="size-5 text-gray-500 dark:text-gray-400 absolute left-3 pointer-events-none" />
              <input
                type="text"
                placeholder="Search..."
                className="w-full p-2 pl-10 dark:bg-gray-700 bg-gray-white text-[13.5px] text-gray-800 border border-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white truncate rounded-lg focus:ring-0 focus:border-gray-300 shadow-sm"
                value={search}
                onInput={(e) => setSearch(e.target.value)}
              />
            </div> */}

            {menuItems
              .filter((menu) => menu?.label.toLowerCase().includes(search.toLowerCase()))
              .map((item, index) => (
                <li key={index} className={item.type === 'header' ? 'text-xs font-semibold text-gray-500 py-2' : ''}>
                  {item.type === 'header' ? (
                    item.label
                  ) : item.children ? (
                    <div>
                      <button
                        onClick={() => {
                          toggleExpand(item.label);
                          // clearSidebarFilter();
                        }}
                        className="flex justify-between items-center w-full p-2 text-base font-medium text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group"
                      >
                        {/* <Icon
                          icon={item.icon}
                          className="text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                          width="20"
                        /> */}
                        {isDrawerVisible && <span className="text-[#83899F] text-base flex-1 text-left whitespace-nowrap">{item.label}</span>}
                        <Icon icon={expandedItems[item.label] ? 'mdi:chevron-up' : 'mdi:chevron-down'} className="text-gray-500" width="18" />
                      </button>

                      {expandedItems[item.label] && (
                        <ul className="py-1 space-y-1  pl-1 ml-3 border-l">
                          {item.children.map((child, childIndex) => {
                            const Lucide = child?.lucide && LucideIcons[child?.lucide];
                            return (
                              <li key={childIndex}>
                                <Link
                                  to={child.path}
                                  className={`flex items-center p-1 text-sm font-normal  text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 ${getActiveClasses(
                                    child.path
                                  )}`}
                                  onClick={() => {
                                    // clearSidebarFilter();
                                    screen.lt.xl() && setIsDrawerVisible(false);
                                  }}
                                >
                                  {child?.lucide && <Lucide width="18" />}

                                  {child.icon && (
                                    <Icon
                                      icon={child.icon}
                                      className="text-gray-500 transition duration-75  dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                      width="18"
                                    />
                                  )}

                                  {isDrawerVisible && (
                                    <span
                                      className={`ml-3 text-sm font-medium text-[#0A1B39] dark:text-white ${
                                        location.pathname.includes(child.path) && 'font-semibold'
                                      }`}
                                    >
                                      {child.label}
                                    </span>
                                  )}
                                </Link>
                              </li>
                            );
                          })}
                        </ul>
                      )}
                    </div>
                  ) : (
                    <Link
                      to={item.path}
                      className={`flex items-center p-2 text-base font-normal text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 ${getActiveClasses(
                        item.path
                      )}`}
                      // onClick={() => {
                      //   clearSidebarFilter();
                      // }}
                    >
                      <Icon
                        icon={item.icon}
                        className="text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                        width="18"
                      />
                      {isDrawerVisible && <span className="ml-3">{item.label}</span>}
                    </Link>
                  )}
                </li>
              ))}
          </ul>
        </div>

        {/* {(sidebarSearch || sidebarFilter) && <SidebarFilterPage />} */}
      </aside>

      {isCreateDialogVisible && <ProfileEditPage onClose={() => setCreateDialogVisibility(false)} />}
    </>
  );
};
