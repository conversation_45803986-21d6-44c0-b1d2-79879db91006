@tailwind base;
@tailwind components;
@tailwind utilities;

/* Target the list in the markdown-editor */
.wmde-markdown ul {
  list-style-type: disc;
}

.wmde-markdown ol {
  list-style-type: decimal;
}
/* 
  Remove the blue line in markdown-editor  
  markdown-editor has a tag that have inline style that can't be removed 
*/
.w-md-editor-area .w-md-editor-text {
  min-height: 100% !important;
}

/*
  @TODO: This should be fixed and replaced by classnames
  Because Datepicker don't accept classnames
*/
@media (max-width: 370px) {
  .calendar-left > div:last-of-type {
    left: -30px;
  }
}
@media screen and (min-width: 640px) {
  .calendar div.relative div:last-of-type {
    right: 0;
  }
}
.dark .calendar input {
  background-color: #22212a !important;
}

/* Targting markdown edtior in adding question */
.space-y-2 .container {
  max-width: 100%;
}

/* This will make any dialog in the center of the page in the small screen */
/* because its a flowbite bug */
/* need to enhacne in iphone mobile */
div[role='dialog'] {
  height: auto !important;
}
/* 
  @TODO: To be replaced when creating our main components instead of flowbite-react
*/
button[role='switch'] div::after {
  background: white;
  border-radius: 50%;
  transition-duration: 0.1s;
}

/* width */
.custom-scroll::-webkit-scrollbar {
  width: 8px;
}

/* Track */
.custom-scroll::-webkit-scrollbar-track {
  background: transparent;
}

/* Handle */
.dark .custom-scroll::-webkit-scrollbar-thumb {
  background: #22212a;
  border-radius: 999px;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background: #ceced4;
  border-radius: 999px;
}

/* Handle on hover */
.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: #555;
}

@media print {
  @page {
    size: A4;
    margin: 0;
  }
  /* a[href]:after {
    content: none !important;
  } */
  #print-pdf {
    print-color-adjust: exact;
  }
  html {
    height: 100%;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden;
  }
  body {
    /* page-break-after: always; */
    height: 100vh;
    margin: 48px 20px 24px !important;
    padding: 0 !important;
    overflow: hidden;
  }
  header,
  footer {
    display: none;
  }
  .print-footer {
    display: block;
    position: fixed;
    bottom: 24px;
    width: 100%;
  }

  .print-footer img {
    width: 29px;
  }
}

/* Target the dropdown styles*/
@media screen and (min-width: 640px) {
  div[role='menu'] {
    left: -36px !important;
  }
}

/* @TODO: To remove background color when browser automatically fill the input */
:root {
  --input-bg-light: #fff;
  --input-text-light: #000;
  --input-bg-dark: #374151;
  --input-text-dark: #fff;
}
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 30px var(--input-bg-light) inset !important;
  -webkit-text-fill-color: var(--input-text-light) !important;
}
.dark input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 30px var(--input-bg-dark) inset !important;
  -webkit-text-fill-color: var(--input-text-dark) !important;
}
/* Todo end */

@keyframes fadeInTest {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.typing-word {
  display: inline-block;
  animation: fadeIn 0.3s ease-in-out;
}

.rs-picker-popup.rs-picker-popup-date {
  z-index: 111;
}

.rs-input::placeholder {
  font-size: 14px;
  font-weight: 400;
  color: #6b7280;
}

.rs-input {
  background-color: #f9fafb;
}

.custom-tooltip-spacing {
  transform: translateX(190px);
}

button[aria-selected='true'] {
  padding-bottom: 15px;
  border-bottom: 1px solid #a169fb;
  background-color: transparent !important;
}

button[aria-selected='false'] {
  background-color: transparent !important;
}

.custom-tab-border {
  position: relative;
}

.custom-tab-border::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0%;
  height: 2px;
  background-color: #a169fb;
  transition: all 0.2s ease;
}

.custom-tab-border:hover::before {
  width: 100%;
  left: 0;
}

/* Phone number styling */
/* Input */
.country-code-container .country-code-input {
  background-color: #f9fafb;
  border-color: #d1d5db;
  width: 100%;
  height: 42px;
  padding-left: 70px;
}
.dark .country-code-container .country-code-input {
  background-color: #374151;
  color: white;
  border-color: #4b5563;
}
.country-code-container .country-code-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button */
.country-code-container .country-code-button {
  padding-right: 11px;
  padding-left: 10px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
}
.dark .country-code-container .country-code-button {
  background-color: #4b5563;
  border: 1px solid #4b5563;
}
.country-code-container .country-code-button:hover {
  background-color: #e5e7eb;
}
.dark .country-code-container .country-code-button:hover {
  background-color: #4b5563;
}
.country-code-container .country-code-button > div,
.country-code-container .country-code-button > div:hover {
  background-color: transparent;
}
.country-code-container .country-code-button > div:disabled {
  cursor: not-allowed;
}
.country-code-container .country-code-button .selected-flag .flag {
  transform: scale(1.5);
}
.dark .country-code-container .country-code-button .selected-flag .flag .arrow {
  border-top-color: white;
}
.country-code-container .country-code-input:disabled ~ .country-code-button .selected-flag .flag .arrow {
  display: none;
}
.country-code-container .country-code-input:disabled ~ .country-code-button .selected-flag {
  cursor: not-allowed;
}

/* Button List */
.country-code-container .country-list {
  max-width: 230px;
  max-height: 200px;
  overflow-y: auto;
  position: absolute;
  left: 10px;
  z-index: 1050;
  border: 1px solid #d1d5db;
  border-radius: 8px;
}
.country-code-container .country-list .country {
  color: black;
}
.dark .country-code-container .country-list .country {
  background-color: #374151;
  color: white;
  border: 1px solid #696f7a;
}
.dark .country-code-container .country-list .country:hover {
  background-color: #4b5563;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s ease;
}
.dark .country-code-container .country-list .country.highlight {
  background-color: #4b5563;
}
/* End of Phone number styling */

/* this is for editing rich-text when read-only  */
.custom-read-only-input .w-md-editor-preview div p {
  color: #6b7280;
}

/* Light and Dark Logo */
.dark-logo {
  display: none;
}
.dark .light-logo {
  display: none;
}
.dark .dark-logo {
  display: block;
}
