// React
import React, { StrictMode } from 'react';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import routes from '/src/routes';

// Deps
import 'iconify-icon';

// Componrnts
import { AppProvider } from '/src/components/provider';
import UserSSEListener from './components/UserSSEListener';

export const App = () => {
  // Route
  const router = createBrowserRouter(routes);

  return (
    <StrictMode>
      <AppProvider>
        <UserSSEListener />
        <RouterProvider router={router} />
      </AppProvider>
    </StrictMode>
  );
};
