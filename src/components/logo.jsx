import React from 'react';

export const Logo = ({ className, icon }) => {
  return (
    <div>
      {icon ? (
        <div>
          <img src="/images/Thepass-1.ico" className={className} />
        </div>
      ) : (
        <div>
          <img src="/images/Thepass-1.svg" className={`${className} light-logo`} alt="Logo" />
          <img src="/images/Thepass-2.svg" className={`${className} dark-logo`} alt="Logo" />
        </div>
      )}
    </div>
  );
};
