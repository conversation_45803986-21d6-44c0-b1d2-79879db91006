// Core
import { Icon, CustomIcon } from '/src';

export const Steps = ({ step }) => {
  return (
    <div className="space-y-4">
      <h2 className="text-[#566577] dark:text-white font-medium">{step.mainLabel}</h2>
      <div className="space-y-8 text-sm">
        {step.data.map((singleData, index) => (
          <div key={singleData.description} className="flex items-center gap-5 text-[#566577] dark:text-white relative">
            <CustomIcon definedIcon={singleData.icon} />
            <div>
              {singleData.header}
              <p>{singleData.description}</p>
            </div>
            {step.data.length - 1 !== index && <p className="w-0.5 h-8 bg-[#E7E9EB] absolute top-full left-4 translate-x-1/2 -z-10" />}
          </div>
        ))}
      </div>
    </div>
  );
};
