import React from 'react';
import { Modal } from 'flowbite-react';

export const Dialog = ({ children, title, subtitle, modalHeader, subModalHeader, overflowVisible, ...props }) => {
  return (
    <Modal {...props} dismissible className="z-[70]">
      <div className={`pt-2 px-2 xssm:px-4 max-h-screen ${overflowVisible ? 'overflow-y-auto' : 'overflow-x-hidden overflow-y-auto'}`}>
        <Modal.Header>
          <div className="flex flex-wrap items-center gap-1">
            <p className="text-[#374151] dark:text-white text-lg font-semibold">{modalHeader}</p>
            {subModalHeader && subModalHeader}
          </div>
        </Modal.Header>
        {modalHeader && <hr className="mx-2" />}
        <Modal.Body className="overflow-visible w-full p-2 pb-6 pt-4">
          <div className="relative">
            <div>
              {!!title && <h3 className="text-2xl font-semibold text-[#000000] dark:text-white">{title}</h3>}

              {!!subtitle && <p className="text-gray-400">{subtitle}</p>}
            </div>

            {children}
          </div>
        </Modal.Body>
      </div>
    </Modal>
  );
};
