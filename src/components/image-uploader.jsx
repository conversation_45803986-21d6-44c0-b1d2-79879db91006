import React, { useRef, useState, useEffect } from 'react';
import { useNotify, Button } from '/src';

export const ImageUploader = ({ label, value, onChange }) => {
  const fileInputRef = useRef(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);

  const { notify } = useNotify();

  // Update imagePreview if value is provided externally
  useEffect(() => {
    if (value) {
      setImagePreview(value);
    }
  }, [value]);

  const handleButtonClick = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setSelectedFile(file);
      const imageUrl = URL.createObjectURL(file);
      setImagePreview(imageUrl);

      // Call parent onChange if provided
      if (onChange) {
        onChange(imageUrl);
      }

      uploadImage(file);
    }
  };

  // This function would handle the actual upload to a server
  const uploadImage = async (file) => {
    // Commented out for now, but would be implemented when API is ready
    // const formData = new FormData();
    // formData.append('image', file);
    // try {
    //   const response = await Api.post('...', {formData});
    // } catch (error) {
    //   console.error('Upload failed:', error);
    //   notify.error(error.response.data.message);
    // }
  };

  return (
    <div className="items-center gap-4">
      <div className="space-y-2 mt-2">
        <p className="text-sm text-[#414651]">{label}</p>
        {imagePreview ? (
          <div className="relative">
            <img src={imagePreview} alt="Selected" className="object-cover rounded-md shadow" />
            <div>
              <div
                className="absolute bottom-2 right-2 rounded-full p-1 bg-primaryPurple bg-opacity-30 cursor-pointer hover:scale-110 transition-transform duration-200 shadow-md"
                onClick={handleButtonClick}
                title="Replace image"
              >
                <div className="rounded-full p-1.5 bg-primaryPurple text-white">
                  <svg width="18" height="16" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M6.66797 12.3333L10.0013 9M10.0013 9L13.3346 12.3333M10.0013 9V16.5M16.668 12.9524C17.6859 12.1117 18.3346 10.8399 18.3346 9.41667C18.3346 6.88536 16.2826 4.83333 13.7513 4.83333C13.5692 4.83333 13.3989 4.73833 13.3064 4.58145C12.2197 2.73736 10.2133 1.5 7.91797 1.5C4.46619 1.5 1.66797 4.29822 1.66797 7.75C1.66797 9.47175 2.36417 11.0309 3.49043 12.1613"
                      stroke="#ffffff"
                      stroke-width="1.66667"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div
            className="flex flex-col border border-[#E9EAEB] rounded-2xl w-full justify-center items-center space-y-3 py-7 cursor-pointer"
            onClick={handleButtonClick}
          >
            <svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="3" y="3" width="40" height="40" rx="20" fill="#F5F5F5" />
              <rect x="3" y="3" width="40" height="40" rx="20" stroke="#FAFAFA" stroke-width="6" />
              <path
                d="M19.668 26.3333L23.0013 23M23.0013 23L26.3346 26.3333M23.0013 23V30.5M29.668 26.9524C30.6859 26.1117 31.3346 24.8399 31.3346 23.4167C31.3346 20.8854 29.2826 18.8333 26.7513 18.8333C26.5692 18.8333 26.3989 18.7383 26.3064 18.5814C25.2197 16.7374 23.2133 15.5 20.918 15.5C17.4662 15.5 14.668 18.2982 14.668 21.75C14.668 23.4718 15.3642 25.0309 16.4904 26.1613"
                stroke="#535862"
                stroke-width="1.66667"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <p className="text-[#535862] text-sm">
              <span className="text-[#8D5BF8]">Click to upload</span> or drag and drop SVG, PNG
            </p>
          </div>
        )}
      </div>

      <input type="file" accept="image/*" ref={fileInputRef} className="hidden" onChange={handleFileChange} />

      {/* {imagePreview && } */}
    </div>
  );
};

// Default props
ImageUploader.defaultProps = {
  value: '',
  onChange: () => {},
};

export default ImageUploader;
