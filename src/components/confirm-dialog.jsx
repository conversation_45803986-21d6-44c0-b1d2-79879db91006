import { Modal } from 'flowbite-react';

import { Button } from '/src';

export const ConfirmDialog = ({ options, onClose }) => {
  return (
    <Modal show size="md" popup onClose={onClose} className="z-[80]">
      <Modal.Header />

      <Modal.Body>
        <div className="text-center">
          <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">{options.message}</h3>
          <div className="space-y-2">
            <Button
              className="w-full"
              label={options.confirmLabel || "Yes, I'm sure"}
              // danger
              onClick={options.onConfirm}
            />
            <Button className="w-full" tertiary label={options.cancelLabel || 'No, cancel'} onClick={onClose} />
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};
