// React
import React, { createContext, useEffect, useState } from 'react';
import { useDarkMode } from '/src/hooks/dark-mode';

// Deps
import 'iconify-icon';

// Componrnts
import { AppToast } from '/src/components/toast';
import { ConfirmDialog } from '/src/components/confirm-dialog';

export const AppContext = createContext({
  userData: JSON.parse(localStorage.getItem('userData')) || null,
  setViewOnly: () => {},
  setSidebarVisiblity: () => {},
});

export const AppProvider = ({ children }) => {
  const { isDark, switchDarkMode } = useDarkMode();

  //   State
  const [toastOptions, notify] = useState(null);
  const [confirmDialogOptions, showConfirm] = useState(null);
  const [userData, setUserData] = useState(() => {
    return JSON.parse(localStorage.getItem('userData')) || null;
  });
  const [hideDialogOption, hideDialogConfirm] = useState(null);
  const [isViewOnly, setViewOnly] = useState(false);
  // const [sidebarFilter, setSidebarFilter] = useState(null);
  // const [sidebarSearch, setSidebarSearch] = useState(null);
  const [isSidebarVisible, setSidebarVisiblity] = useState(false);

  const onClose = () => {
    showConfirm(null);
    hideDialogOption && hideDialogOption.onClose();
  };

  // Get user data
  const updateUser = (data) => {
    if (data) {
      setUserData(data);
    }
  };

  //   App Context
  const appContext = {
    notify,
    showConfirm,
    isDark,
    switchDarkMode,
    updateUser,
    userData,
    hideDialogConfirm,
    isViewOnly,
    setViewOnly,
    // sidebarFilter,
    // setSidebarFilter,
    // sidebarSearch,
    // setSidebarSearch,
    isSidebarVisible,
    setSidebarVisiblity,
  };

  useEffect(() => {
    // Set full height
    document.body.classList.add('h-full', 'overflow-x-hidden', 'bg-white', 'dark:bg-darkGrayBackground');
    document.documentElement.setAttribute('data-color-mode', 'light');
    // Add 'overflow-x-hidden' to 'html' to avoid any scrollable background page
    document.querySelector('html').classList.add('h-full');
    document.getElementById('root').classList.add('h-full');
  }, []);

  return (
    <AppContext.Provider value={appContext}>
      {/* Handle Notifications */}
      {toastOptions && <AppToast options={toastOptions} onDismiss={() => notify(null)} />}
      {confirmDialogOptions && <ConfirmDialog options={confirmDialogOptions} onClose={onClose} />}

      {children}
    </AppContext.Provider>
  );
};
