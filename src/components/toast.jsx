import React, { useEffect } from "react";
import { createPortal } from "react-dom";
import { Toast } from "flowbite-react";

import { Icon } from "/src";

export const AppToast = ({ options, onDismiss }) => {
  // Computes
  const getClassNames = () => {
    if (options.type === "warning") {
      return "bg-orange-100 text-orange-500 dark:bg-orange-700 dark:text-orange-200";
    }

    if (options.type === "error") {
      return "bg-red-100 text-red-500 dark:bg-red-800 dark:text-red-200";
    }

    return "bg-green-100 text-green-500 dark:bg-green-800 dark:text-green-200";
  };
  const getIconNames = () => {
    if (options.type === "warning") {
      return "material-symbols:warning-outline-rounded";
    }

    if (options.type === "error") {
      return "material-symbols:error-circle-rounded-outline-sharp";
    }

    return "material-symbols:check-circle-outline";
  };

  useEffect(() => {
    setTimeout(() => {
      onDismiss();
    }, 3000);
  }, []);

  return createPortal(
    <Toast className="fixed top-20 w-[90%] sm:w-full left-1/2 -translate-x-1/2 z-[100]">
      <div
        className={`inline-flex h-8 w-8 shrink-0 items-center justify-center rounded-lg ${getClassNames()}`}
      >
        <Icon icon={getIconNames()} />
      </div>
      <div className="ml-3 text-sm font-normal">{options.message}</div>
      <Toast.Toggle onDismiss={onDismiss} />
    </Toast>,
    document.body
  );
};

// Props
AppToast.defaultProps = {
  options: {
    message: "Success !",
    type: "success",
  },
  onDismiss: () => {},
};
