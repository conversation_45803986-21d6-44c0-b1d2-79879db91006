import React, { forwardRef, useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { Label, TextInput as Input, ListGroup, Badge, Tooltip } from 'flowbite-react';
import ClickAwayListener from 'react-click-away-listener';
import { asField } from '../hocs/field';

import { Icon, useScreenSize, useLookups, useConfirmDialog, Button, TextInput, Api, useNotify } from '/src';

export const MultiSelect = asField(
  forwardRef(
    (
      {
        name,
        label,
        subLabel = '',
        placeholder,
        options,
        value,
        disabled,
        disabledMessage,
        onChange,
        onSearch,
        filterOnly,
        optionLabelKey,
        optionValueKey,
        minSelection,
        maxSelection,
        breakDown,
        lookup,
        params,
        cached = false,
        validatorsScroll,
        errorMessage,
        requiredLabel,
        showSingleClear,
        handleSingleClear,
        readOnly,
        customSize = 'sm',
        customWeight = 'medium',
        creationOptions,
        // @TODO: Confirmation warning message
        // confirmOptions = false,
      },
      ref
    ) => {
      // State
      const [keyword, setKeyword] = useState('');
      const [listVisibility, setListVisibility] = useState(false);

      const [isCreating, setIsCreating] = useState(false);
      const [customValue, setCustomValue] = useState('');
      const [errorCustomMessage, setErrorCustomMessage] = useState('');
      const [isLoading, setIsLoading] = useState(false);

      // Hooks
      const { lookups, loading, refresh } = useLookups(lookup, { cached, params });
      const screen = useScreenSize();
      const { showConfirm, hideConfirm } = useConfirmDialog();
      const { notify } = useNotify();

      // Reference
      const inputRef = useRef(null);

      const customTheme = {
        field: {
          input: {
            colors: {
              gray: `${
                readOnly ? 'cursor-not-allowed dark:bg-gray-800 text-gray-500' : 'dark:bg-gray-700'
              } block w-full border disabled:cursor-not-allowed   disabled:opacity-50 bg-gray-50 border-gray-300 text-gray-900 dark:border-gray-600 dark:text-white dark:placeholder-gray-400 p-2.5 text-sm rounded-lg focus:ring-0 focus:border-gray-300`,
              failure:
                'border-red-500 bg-white-500 text-gray-900 dark:text-white placeholder-gray-400 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-[#374151] dark:focus:border-red-500 dark:focus:ring-red-500',
            },
          },
        },
      };

      const tooltipCustomTheme = {
        target: 'w-auto',
      };

      const filteredLookup = () => {
        if (onSearch) {
          return lookups;
        }
        return (
          lookups
            // Remove selected values
            .filter((option) => !value?.includes(option[optionValueKey]))
            // Search the results
            .filter((option) => option[optionLabelKey]?.toLowerCase().includes(keyword?.toLowerCase()))
        );
      };

      const getOptionLabel = (id) => {
        return lookups.find((option) => option[optionValueKey] == id)?.name || lookups.find((option) => option[optionValueKey] == id)?.label;
      };

      // Actions
      const updateInput = (updatedValue = value) => {
        setKeyword('');
      };
      const handleSelect = (selectedValue) => () => {
        const nextValue = [...value, selectedValue];
        if (nextValue.length >= minSelection && nextValue.length <= maxSelection) {
          onChange(nextValue);
          setListVisibility(false);

          if (!filterOnly) {
            updateInput(nextValue);
          } else {
            updateInput([]);
          }
        }
      };

      const handleSubmit = async (e) => {
        e.stopPropagation();
        try {
          setIsLoading(true);
          if (!customValue) {
            setErrorCustomMessage('This field is required');
            return;
          }
          const response = await Api.post(creationOptions.url, { ...params, [creationOptions?.fieldName]: customValue });
          if (response) {
            await refresh();
            notify(`${label || 'Option'} created successfully`);
            setCustomValue('');
            setKeyword('');
            setIsCreating(false);
          }
        } catch (error) {
          notify.error(error.response?.data?.message || 'Failed to create option');
        } finally {
          setIsLoading(false);
        }
      };

      const handleReset = () => {
        onChange([]);
        setKeyword('');
        setListVisibility(true);

        if (onSearch) {
          onSearch('');
        }
      };
      const handleClickAway = () => {
        setListVisibility(false);
        updateInput();
      };

      const handleRemoveItem = (id) => {
        // // @TODO: Confirmation warning message
        // const element = () => {
        const nextUpdate = value.filter((item) => item !== id);
        onChange(nextUpdate);
        // };

        // // @TODO: Confirmation warning message
        // if (confirmOptions?.isConfirmationDialog) {
        //   showConfirm(confirmOptions.confirmTextNotChangeInputs(), {
        //     onConfirm() {
        //       element();
        //       hideConfirm();
        //       confirmOptions.doAfterConfirmation();
        //     },
        //   });
        // } else {
        //   element();
        // }
      };

      const onFocus = () => {
        // // @TODO: Confirmation warning message
        // const element = () => {
        setListVisibility(true);
        // };

        // // @TODO: Confirmation warning message
        // if (confirmOptions?.isConfirmationDialog) {
        //   inputRef.current.blur();
        //   showConfirm(confirmOptions.confirmTextNotChangeInputs(), {
        //     onConfirm() {
        //       element();
        //       hideConfirm();
        //       confirmOptions.doAfterConfirmation();
        //     },
        //   });
        // } else {
        //   element();
        // }
      };

      // Lifecycle
      useEffect(() => {
        if (value) {
          updateInput();
        }
      }, [value]);

      const element = (
        <Input
          ref={inputRef}
          theme={customTheme}
          className="overflow-x-auto w-full"
          id={name}
          placeholder={!(value?.length === maxSelection) && placeholder}
          value={keyword}
          disabled={disabled}
          autoComplete="off"
          onInput={(e) => !readOnly && setKeyword(e.target.value)}
          onFocus={onFocus}
          color={errorMessage ? 'failure' : 'gray'}
          helperText={errorMessage ? <>{errorMessage}</> : undefined}
          scrolltoerror={validatorsScroll && errorMessage && document.getElementById(name).scrollIntoView({ behavior: 'smooth', block: 'end' })}
          readOnly={readOnly}
        />
      );
      return (
        <div className="relative flex flex-col gap-2">
          <div className="block">
            <Label htmlFor={name}>
              <div className="flex w-full justify-between">
                <div>
                  <span className={`text-inputLabel dark:text-inputDarkLabel text-${customSize} font-${customWeight}`}>{label}</span>{' '}
                  <span className="text-inputSubLabel dark:text-inputLabel">{subLabel}</span>
                  {requiredLabel && <span className="text-red-600 dark:text-red-800"> *</span>}
                </div>
                {showSingleClear && (
                  <p className="text-[#9061F9] cursor-pointer text-sm select-none" onClick={handleSingleClear}>
                    Clear
                  </p>
                )}
              </div>
            </Label>
          </div>

          <ClickAwayListener onClickAway={handleClickAway}>
            {disabled && disabledMessage ? (
              <Tooltip content={disabledMessage} arrow={false} placement="bottom" theme={tooltipCustomTheme}>
                {element}
              </Tooltip>
            ) : (
              <div>
                <div className="relative">{element}</div>

                {!readOnly && listVisibility && !loading && (
                  <ListGroup className="absolute left-0 right-0 z-10 max-h-[222px] overflow-x-hidden overflow-y-scroll">
                    {filteredLookup()?.length > 0 ? (
                      filteredLookup().map((option) => (
                        <ListGroup.Item key={option[optionValueKey]} onClick={handleSelect(option[optionValueKey])}>
                          <p className="truncate">{option[optionLabelKey]}</p>
                        </ListGroup.Item>
                      ))
                    ) : (
                      <div className="p-2 px-4 border-b border-b-white/10">
                        <p className="py-1 cursor-default">No results found...</p>
                      </div>
                    )}
                    {/* Add the new UI components here */}
                    {creationOptions && !isCreating && filteredLookup().length < 4 && (
                      <div
                        className="text-white py-2 m-2 rounded-md text-center cursor-pointer bg-[#702ede] hover:opacity-90"
                        onClick={(e) => {
                          e.preventDefault(); // Prevent default behavior
                          e.stopPropagation(); // Stop the event from bubbling up
                          setIsCreating(true);
                        }}
                      >
                        Create new +
                      </div>
                    )}

                    {creationOptions && isCreating && (
                      <ListGroup.Item className="my-1 [&_div]:w-full ">
                        <div className="text-start">
                          <div className="flex gap-2">
                            <TextInput
                              className="text-white"
                              name="name"
                              placeholder={`Enter new ${label.toLowerCase() || 'option'}`}
                              value={customValue}
                              onChange={(event) => {
                                setCustomValue(event);
                                if (event === '' || creationOptions?.validation?.test(event)) {
                                  setErrorCustomMessage('');
                                } else {
                                  setErrorCustomMessage('Please enter a valid text');
                                }
                              }}
                            />

                            <Button
                              label="Create"
                              disabled={!customValue.trim() || !!errorCustomMessage}
                              loading={isLoading}
                              gradientMonochrome="purple"
                              onClick={handleSubmit}
                            />
                          </div>
                          {errorCustomMessage && <p className="mt-2 ms-1 text-sm text-red-600 dark:text-red-500">{errorCustomMessage}</p>}
                        </div>
                      </ListGroup.Item>
                    )}
                  </ListGroup>
                )}
              </div>
            )}
          </ClickAwayListener>
          {value?.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {value?.map((id, index) => (
                <div
                  key={index}
                  className={`flex rounded-xl justify-between border border-gray-200 dark:border-gray-600 min-w-fit px-4 py-2 text-sm ${
                    readOnly ? 'text-gray-500 cursor-not-allowed' : 'text-black  dark:text-white'
                  } `}
                >
                  <p className="line-clamp-1">{getOptionLabel(id)}</p>

                  {!readOnly && !disabled && (
                    <div className="flex" onClick={() => handleRemoveItem(id)}>
                      <Icon icon="material-symbols-light:close" width={20} className="text-[#667085] dark:text-gray-200 cursor-pointer pl-2" />
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }
  )
);

// Types
MultiSelect.propTypes = {
  name: PropTypes.string.isRequired,
  label: PropTypes.string,
  options: PropTypes.array,
  onChange: PropTypes.func,
  filterOnly: PropTypes.bool,
  value: PropTypes.array,
  minSelection: PropTypes.number, // Minimum selection allowed
  maxSelection: PropTypes.number, // Maximum selection allowed
  creationOptions: PropTypes.object,
};

MultiSelect.defaultProps = {
  label: '',
  value: [],
  optionLabelKey: 'label',
  optionValueKey: 'value',
  placeholder: 'Search...',
  options: [],
  filterOnly: false,
  onChange() {},
  minSelection: 0,
  maxSelection: Infinity,
  creationOptions: null,
};
