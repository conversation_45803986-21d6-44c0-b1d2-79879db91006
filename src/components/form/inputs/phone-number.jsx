/* NB: Validation using "libphonenumber-js" */

// React Phone Number
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';

// Hocs
import { asField } from '../hocs/field';

export const PhoneNumberInput = asField(({ label, requiredLabel, value, onChange, errorMessage, ...props }) => {
  return (
    <div className="space-y-2">
      <p className="text-inputLabel dark:text-inputDarkLabel text-sm font-medium">
        {label} {requiredLabel && <span className="text-red-600">*</span>}
      </p>

      <PhoneInput
        country="eg"
        value={value}
        onChange={onChange}
        containerClass="country-code-container"
        inputClass="country-code-input"
        buttonClass="country-code-button"
        {...props}
      />

      {errorMessage && <p className="text-sm text-[#e02424] dark:text-[#f05252]">{errorMessage}</p>}
    </div>
  );
});
