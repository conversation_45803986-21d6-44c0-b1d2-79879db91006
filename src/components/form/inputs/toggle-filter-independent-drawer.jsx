// Core
import { Drawer } from '/src';

export const ToggleFilterIndependentDrawer = ({ drawerFilter }) => {
  // Methods
  const onCloseDrawerFilter = () => drawerFilter.setShowDrawerFilter((prev) => !prev);

  return (
    <Drawer className="w-screen max-w-[340px]" onClose={onCloseDrawerFilter}>
      <Drawer.SingleView className="!px-0 !py-1 !space-y-0">
        <Drawer.Header
          onClose={onCloseDrawerFilter}
          headerLabel={<span className="text-base self-center text-[#111827] dark:text-white font-medium">Filters</span>}
          className="px-3 pb-2"
          headerChild={
            drawerFilter.drawerClearAll &&
            drawerFilter.isAnyFilterApplied() && (
              <p
                className="no-underline font-medium h-fit pt-1 text-[13px] cursor-pointer text-[#7D2EF0]"
                onClick={() => drawerFilter.drawerClearAll()}
              >
                Clear All
              </p>
            )
          }
          children={
            drawerFilter.count !== null &&
            drawerFilter.isAnyFilterApplied() && (
              <p className="text-sm">
                <span className="font-medium">{drawerFilter.count}</span> results found
              </p>
            )
          }
        />
        <Drawer.Body className="flex flex-col">{drawerFilter?.element}</Drawer.Body>
      </Drawer.SingleView>
    </Drawer>
  );
};
