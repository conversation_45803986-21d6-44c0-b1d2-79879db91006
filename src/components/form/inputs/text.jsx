import React from 'react';
import PropTypes from 'prop-types';
import { Label, TextInput as Input, Tooltip } from 'flowbite-react';
import { Icon, StaticData } from '/src/';
import { asField } from '../hocs/field';

export const TextInput = asField(
  ({ name, requiredLabel, label, subLabel, onChange, validatorsScroll, errorMessage, autoComplete, labelTooltip, ...props }) => {
    // Methods

    const handleChange = (e) => {
      onChange(e.target.value);
    };
    const customTheme = {
      field: {
        input: {
          colors: {
            gray: `${
              props.readOnly ? 'cursor-not-allowed  dark:bg-gray-800 text-gray-500' : ' dark:bg-gray-700 text-gray-800'
            } block w-full border bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 border-gray-300  dark:border-gray-600 dark:text-white dark:placeholder-gray-400 p-2.5 text-sm rounded-lg focus:ring-0 focus:border-gray-300`,
            failure:
              'border-red-500 bg-white-500 text-gray-900 dark:text-white placeholder-gray-400 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-[#374151] dark:focus:border-red-500 dark:focus:ring-red-500',
          },
        },
      },
    };

    return (
      <div>
        {label && (
          <div className="mb-2 flex items-center gap-2">
            <Label htmlFor={name}>
              <span className="text-inputLabel dark:text-inputDarkLabel">{label}</span>{' '}
              <span className="text-inputSubLabel dark:text-inputLabel">{subLabel}</span>
              {requiredLabel && <span className="text-red-600 dark:text-red-800"> *</span>}
            </Label>
            {labelTooltip && (
              <Tooltip theme={StaticData.customTooltipTheme} content={labelTooltip} style="auto" className="border border-white">
                <Icon icon="solar:info-circle-outline" className="text-gray-600 dark:text-gray-200" width="18" />
              </Tooltip>
            )}
          </div>
        )}
        <Input
          theme={customTheme}
          id={name}
          onChange={handleChange}
          {...props}
          rightIcon={props.rightIcon}
          autoComplete={!!autoComplete ? autoComplete : 'off'}
          color={errorMessage ? 'failure' : 'gray'}
          helperText={errorMessage ? <>{errorMessage}</> : undefined}
          scrolltoerror={validatorsScroll && errorMessage && document.getElementById(name).scrollIntoView({ behavior: 'smooth', block: 'end' })}
        />
      </div>
    );
  }
);

// Types
TextInput.propTypes = {
  name: PropTypes.string.isRequired,
  label: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  validatorsScroll: PropTypes.bool,
  onChange: PropTypes.func,
};

TextInput.defaultProps = {
  label: '',
  value: '',

  onChange() {},
};
