// React
import React from 'react';

// Flowbite
import { Datepicker as FlowbiteDatepicker } from 'flowbite-react';

// UI
import { Icon } from '/src';

export const DatePicker = (index) => {
  return (
    <div key={index} className="calendar flex flex-row justify-end items-center gap-2 text-gray-700 dark:text-gray-300 rounded-lg h-fit">
      <div className="flex flex-col sm:flex-row w-full gap-2 sm:gap-4">
        <FlowbiteDatepicker
          className="inline-block w-full sm:w-44 calendar-left"
          onSelectedDateChanged={(date) => setStart(date)}
          showTodayButton={false}
          showClearButton={false}
          // value={start ? `From: ${new Date(start).toDateString().substring(4)}` : 'From: -- / -- / ----'}
          value="From: -- / -- / ----"
        />
        <FlowbiteDatepicker
          className="inline-block w-full sm:w-44 calendar-left"
          onSelectedDateChanged={(date) => setEnd(date)}
          showTodayButton={false}
          showClearButton={false}
          // value={end ? `To: ${new Date(end).toDateString().substring(4)}` : 'To: -- / -- / ----'}
          value="To: -- / -- / ----"
        />
      </div>
      <div
        className={`mx-auto sm:mx-0 ${
          // start && end ? 'bg-[#8484E1]' : 'bg-white dark:bg-[#374151]'
          false ? 'bg-[#8484E1]' : 'bg-white dark:bg-[#374151]'
        } w-10 h-10 rounded-lg flex justify-center items-center cursor-pointer border border-gray-300 dark:border-gray-600 px-2`}
        // onClick={handleClear}
      >
        <Icon icon="f7:delete-left" width={26} />
      </div>
    </div>
  );
};
