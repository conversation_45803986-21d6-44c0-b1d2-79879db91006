import React from 'react';
import PropTypes from 'prop-types';
import { Label, RangeSlider as Input } from 'flowbite-react';

export const RangeSlider = ({ name, label, onChange, ...props }) => (
  <div>
    <div className='mb-1 block'>
      <Label htmlFor={name} value={label} />
    </div>
    <Input id={name} onChange={(e) => onChange(e.target.value)} {...props} />
  </div>
);

// Types
RangeSlider.propTypes = {
  name: PropTypes.string.isRequired,
  label: PropTypes.string,
  onChange: PropTypes.func,
};

RangeSlider.defaultProps = {
  label: '',
  onChange() {},
};
