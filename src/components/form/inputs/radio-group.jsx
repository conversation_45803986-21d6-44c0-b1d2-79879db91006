import React from 'react';
import { Radio, useLookups } from '/src';
import PropTypes from 'prop-types';

export const RadioGroup = ({
  name,
  label,
  value,
  onChange,
  lookup,
  params,
  cached = false,
  className,
  requiredLabel,
  showSingleClear,
  handleSingleClear,
  customSize = 'sm',
}) => {
  const { lookups } = useLookups(lookup, { cached, params });
  return (
    <>
      <div className="space-y-2 w-full ">
        {label && (
          <p className={`text-sm font-medium text-gray-900 dark:text-gray-300 ${className}`}>
            <div className={`flex justify-between gap-2 items-center ${!customSize && 'px-2'}`}>
              <div>
                <span className={`${customSize && 'text-[13px] font-semibold text-inputLabel dark:text-inputDarkLabel'}`}>{label}</span>{' '}
                {requiredLabel && <span className="text-red-600 dark:text-red-800"> *</span>}
              </div>
              <div>
                {showSingleClear && (
                  <p className="text-[#9061F9] cursor-pointer text-sm select-none" onClick={handleSingleClear}>
                    Clear
                  </p>
                )}
              </div>
            </div>
          </p>
        )}

        <div className={`flex ${customSize ? 'flex flex-col gap-2' : 'flex-col gap-3 sm:flex-row sm:gap-5'} `}>
          {lookups.map((option, index) => (
            <Radio
              name={`${name}-${index}`}
              key={option.label}
              label={option.label}
              value={value}
              selectionValue={option.value}
              onChange={onChange}
              customSize={customSize}
            />
          ))}
        </div>
      </div>
    </>
  );
};

// Types
RadioGroup.propTypes = {
  options: PropTypes.array,
  name: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.bool]),
  label: PropTypes.string,
  onChange: PropTypes.func,
};

RadioGroup.defaultProps = {
  options: [],
  value: null,
  label: null,
  onChange() {},
};
