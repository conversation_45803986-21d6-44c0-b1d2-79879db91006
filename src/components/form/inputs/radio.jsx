import React from 'react';
import PropTypes from 'prop-types';
import { Radio as FlowbiteRadio, Label, Tooltip } from 'flowbite-react';
import { Icon, StaticData } from '/src/';

export const Radio = ({
  name,
  selectionValue,
  value,
  label,
  labelTooltip,
  labelTooltipStyles,
  onChange,
  fullWidth,
  pointer,
  isCustomLabel,
  applicantTestView,
  customSize = 'sm',
  ...props
}) => {
  const handleChange = (e) => {
    onChange(e.target.value);
  };

  return (
    <div className="flex items-center gap-2">
      <FlowbiteRadio id={name} name={name} value={selectionValue} checked={selectionValue === value} onChange={handleChange} {...props} />
      <Label htmlFor={name} className={`${fullWidth && 'w-full'} ${pointer && 'cursor-pointer'} overflow-hidden break-words`}>
        {isCustomLabel ? (
          <div className="border border-[#DEDEDE] py-5 pl-4 pr-1 rounded-lg dark:border-gray-500 ">
            {/* NB: applicantTestView is for submission of applicant only will be black and white */}
            <span className={`${applicantTestView ? 'text-[#374151] dark:text-white' : 'text-inputSubLabel'} font-medium`}> {label} </span>
          </div>
        ) : (
          <span className={`${applicantTestView ? 'text-[#374151] dark:text-white' : 'text-inputSubLabel'} text-${customSize}`}> {label} </span>
        )}
      </Label>
      {labelTooltip && (
        <Tooltip theme={StaticData.customTooltipTheme} content={labelTooltip} style="auto" className={`border border-white ${labelTooltipStyles}`}>
          <Icon icon="solar:info-circle-outline" className="text-gray-600 dark:text-gray-200" width="18" />
        </Tooltip>
      )}
    </div>
  );
};

// Types
Radio.propTypes = {
  name: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.bool]),
  selectionValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.bool]),
  label: PropTypes.string,
  onChange: PropTypes.func,
  fullWidth: PropTypes.bool,
  pointer: PropTypes.bool,
  isCustomLabel: PropTypes.bool,
};

Radio.defaultProps = {
  value: null,
  label: null,
  onChange() {},
};
