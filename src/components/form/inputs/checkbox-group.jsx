import React from 'react';
import PropTypes from 'prop-types';

import { Checkbox, useLookups } from '/src';

export const CheckboxGroup = ({ label, options, nameProp, onChange, lookup, params, cached = false }) => {
  const { lookups } = useLookups(lookup, { cached, params });

  const handleChange = (key) => (value) => {
    const limitMultichoiceAnswer = () => {
      const counter = lookups.filter((option) => option['value']).length;
      if (counter < 2) {
        return true;
      } else if (!value) {
        return true;
      }
      return false;
    };
    limitMultichoiceAnswer() && onChange(key)(value);
  };

  return (
    <div className="space-y-2">
      {label && <p className="text-sm font-medium text-gray-900 dark:text-gray-300">{label}</p>}
      <div className="flex flex-col gap-3 sm:flex-row sm:gap-5">
        {lookups.map((option) => (
          <Checkbox key={option.label} onChange={handleChange(option.name)} {...option} name={option[nameProp]} />
        ))}
      </div>
    </div>
  );
};

// Types
CheckboxGroup.propTypes = {
  options: PropTypes.array,
  label: PropTypes.string,
  nameProp: PropTypes.string,
  onChange: PropTypes.func,
};

CheckboxGroup.defaultProps = {
  options: [],
  label: null,
  nameProp: 'name',
  onChange() {},
};
