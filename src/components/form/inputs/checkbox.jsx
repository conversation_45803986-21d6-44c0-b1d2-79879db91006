import React from 'react';
import PropTypes from 'prop-types';
import { Checkbox as FlowbiteCheckbox, Label } from 'flowbite-react';

export const Checkbox = ({ name, value, label, onChange, fullWidth, preventSendingMail, isCustomLabel, ...props }) => {
  const handleChange = (e) => {
    onChange(e.target.checked);
  };

  return (
    <div className="flex items-center gap-2">
      <FlowbiteCheckbox id={name} checked={value} onChange={handleChange} {...props} />
      <Label htmlFor={name} className={`${fullWidth && 'w-full'} ${preventSendingMail ? 'cursor-not-allowed opacity-50' : ''}`}>
        {isCustomLabel ? (
          <div className="border border-[#DEDEDE] py-5 pl-4 pr-1 rounded-lg dark:border-gray-500">
            <span className="text-[#4B5563]dark:text-inputSubLabel font-medium"> {label} </span>
          </div>
        ) : (
          <span className="text-inputSubLabel"> {label} </span>
        )}
      </Label>
    </div>
  );
};

// Types
Checkbox.propTypes = {
  name: PropTypes.string.isRequired,
  value: PropTypes.bool,
  label: PropTypes.string,
  onChange: PropTypes.func,
  fullWidth: PropTypes.bool,
  preventSendingMail: PropTypes.bool,
  isCustomLabel: PropTypes.bool,
};

Checkbox.defaultProps = {
  value: false,
  label: null,
  onChange() {},
};
