import React, { useContext, useEffect, useState } from 'react';
import ClickAwayListener from 'react-click-away-listener';

import { FormContext } from '../';
import { useValidate } from '../../../hooks';

export const asField =
  (Field) =>
    ({ name, value, validators, ...props }) => {
      // Providers
      const formProvider = useContext(FormContext);

      // State
      const [errorMessage, setErrorMessage] = useState(null);

      // Hooks
      const { validate } = useValidate();

      // Register myself
      useEffect(() => {
        if (value) {
          setErrorMessage("")
        }
        formProvider.registerField(name, {
          runValidations() {
            const error = validate(value, validators);
            // Update UI
            setErrorMessage(error);

            return !error;
          },
        });
      }, [value]);

      return (

        <Field name={name} value={value} errorMessage={errorMessage}  {...props} />

      );
    };
