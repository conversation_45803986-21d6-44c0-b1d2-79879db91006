import React, { createContext, useEffect } from 'react';
import { useImmer as useState } from 'use-immer';
import PropTypes from 'prop-types';

// Inputs
export * from './inputs';

export const FormContext = createContext({});

export const Form = ({ children, onSubmit, ...props }) => {
  const [fields, setFields] = useState({});

  // Provider
  const provider = {
    // Methods
    registerField(name, field) {
      setFields((draft) => {
        draft[name] = field;
      });
    },
  };

  // Methods
  const handleSubmit = (e) => {
    e.preventDefault();

    const result = Object.values(fields).map((field) => {
      return field.runValidations();
    });

    const isAllFieldsValid = result.every((isValid) => isValid);

    // Make sure that all validations passed
    if (isAllFieldsValid) {
      onSubmit(e);
    }
  };

  return (
    <FormContext.Provider value={provider}>
      <form onSubmit={handleSubmit} {...props}>
        {children}
      </form>
    </FormContext.Provider>
  );
};

// Types
Form.propTypes = {
  onSubmit: PropTypes.func,
  className: PropTypes.string,
};

Form.defaultProps = {
  className: '',
  onSubmit() {},
};
