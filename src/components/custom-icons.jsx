/* 
  Document ->
    - This component contains defined icons ready for use.
    - className & width & height are optional.
    - definedIcon property is required.
      -options: [
        - dashboard
        - users
        - tests
        - questions
        - screening
        - applicant
        - interview
        - archive
        - export
        - assign
        - more
      ]

    - Usage (e.g):
      <CustomIcon definedIcon="<option>" className='...' width='...' height='...' />
*/

export const CustomIcon = ({ definedIcon, className, stroke, width, height, onClick }) => {
  const CustomIcon = {
    dashboard: (
      <svg
        className={className}
        width={width ? width : '22'}
        height={height ? height : '20'}
        viewBox="0 0 22 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.45531 9.80559H6.26286C6.65014 9.80559 6.96598 10.1214 6.96598 10.5087V15.2741C6.96598 15.6614 6.65014 15.9773 6.26286 15.9773H4.45531C4.06803 15.9773 3.75219 15.6614 3.75219 15.2741V10.5087C3.75219 10.1215 4.06803 9.80559 4.45531 9.80559ZM21.0021 18.1867H1.54273V1.27134C1.54273 0.970453 1.2988 0.726562 0.997906 0.726562C0.697016 0.726562 0.453125 0.970453 0.453125 1.27134V18.7315C0.453125 19.0324 0.697016 19.2764 0.997906 19.2764H21.002C21.3029 19.2764 21.5468 19.0324 21.5468 18.7315C21.5469 18.4306 21.3029 18.1867 21.0021 18.1867ZM10.5925 6.00187H12.4001C12.7873 6.00187 13.1032 6.31772 13.1032 6.705V15.2742C13.1032 15.6615 12.7873 15.9773 12.4001 15.9773H10.5925C10.2052 15.9773 9.88939 15.6615 9.88939 15.2742V6.705C9.88939 6.31772 10.2052 6.00187 10.5925 6.00187ZM16.7297 2.4113H18.5373C18.9245 2.4113 19.2404 2.72714 19.2404 3.11442V15.2742C19.2404 15.6615 18.9245 15.9773 18.5373 15.9773H16.7297C16.3424 15.9773 16.0266 15.6615 16.0266 15.2742V3.11442C16.0266 2.72714 16.3424 2.4113 16.7297 2.4113Z"
          className="fill-current"
        />
      </svg>
    ),
    users: (
      <svg
        className={className}
        width={width ? width : '24'}
        height={height ? height : '30'}
        viewBox="0 0 24 30"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M19.1064 16.3448C19.1623 16.2809 19.2041 16.2059 19.2291 16.1248C19.2541 16.0436 19.2616 15.9581 19.2513 15.8739L18.8227 12.378C18.7574 11.8631 18.5647 11.3726 18.2622 10.951C17.9596 10.5293 17.5567 10.1897 17.0898 9.96291L15.2785 9.06932C15.2362 9.05089 15.1915 9.03868 15.1457 9.0331C15.1115 9.03018 15.0772 9.03018 15.043 9.0331H14.8196L14.717 9.0814L14.6023 9.15989C13.8174 9.98102 13.0627 10.3674 12.3623 10.3071C12.0224 10.2551 11.6963 10.1359 11.4029 9.95661C11.1095 9.77728 10.8548 9.54133 10.6536 9.26253L10.5993 9.20819L10.4906 9.11159L10.4 9.06328L10.2853 9.01498H10.0619C10.0202 9.02144 9.97954 9.03364 9.94114 9.05121C9.91707 9.04847 9.89276 9.04847 9.86869 9.05121L8.05737 9.94479C7.59132 10.1719 7.18932 10.5117 6.88779 10.9334C6.58626 11.3551 6.39474 11.8454 6.33058 12.3599L5.9019 15.904C5.89239 15.9887 5.90092 16.0745 5.92694 16.1557C5.95296 16.2368 5.99586 16.3116 6.05284 16.375C6.10942 16.4394 6.17907 16.491 6.25714 16.5264C6.33522 16.5618 6.41994 16.5802 6.50567 16.5803H18.6536C18.7417 16.5765 18.8279 16.5534 18.9061 16.5127C18.9843 16.4721 19.0527 16.4147 19.1064 16.3448ZM7.18793 15.3425L7.53812 12.5229C7.57447 12.2039 7.69097 11.8993 7.87678 11.6375C8.06259 11.3757 8.31165 11.1652 8.60077 11.0255L9.98944 10.3433C10.5553 11.0098 11.35 11.4406 12.2174 11.5508C12.3038 11.5567 12.3906 11.5567 12.477 11.5508C13.4823 11.5014 14.4306 11.0694 15.1276 10.3433L16.5283 11.0255C16.8175 11.1657 17.0669 11.3763 17.2537 11.6379C17.4404 11.8995 17.5584 12.2038 17.597 12.5229L17.9472 15.3425H7.18793ZM8.78793 4.86102C8.79307 5.87986 9.18944 6.85778 9.89511 7.59267C10.6008 8.32757 11.5618 8.76326 12.5796 8.8097C14.6747 8.8097 16.3774 6.95008 16.3774 4.66178C16.3774 2.37347 14.6747 0.507812 12.5796 0.507812C10.4845 0.507812 8.7819 2.36744 8.7819 4.66178C8.77904 4.70198 8.77904 4.74233 8.7819 4.78253V4.81272C8.7819 4.84291 8.78793 4.84291 8.78793 4.86102ZM12.5796 7.60215C11.9635 7.57202 11.3767 7.33035 10.918 6.9179C10.4594 6.50545 10.157 5.94745 10.0619 5.338C10.8105 5.17312 11.4748 4.74453 11.9336 4.13045C12.3424 4.54661 12.836 4.86985 13.3809 5.07822C13.9258 5.28659 14.5092 5.37519 15.0913 5.338C14.9954 5.94601 14.6935 6.50264 14.2364 6.91477C13.7792 7.32691 13.1943 7.56956 12.5796 7.60215ZM12.5796 1.71536C13.2211 1.75048 13.8291 2.01278 14.2949 2.45528C14.7606 2.89779 15.0536 3.49161 15.1215 4.13045C14.5855 4.18136 14.046 4.0797 13.5653 3.8372C13.0845 3.5947 12.6822 3.22123 12.4045 2.75989C12.3499 2.65717 12.2668 2.57241 12.1652 2.5158C12.0636 2.4592 11.9478 2.43315 11.8317 2.44081C11.7156 2.44846 11.6042 2.48948 11.5109 2.55894C11.4176 2.6284 11.3463 2.72334 11.3057 2.83234C11.0551 3.39066 10.6019 3.83304 10.0377 4.07008C10.1189 3.44214 10.4177 2.86256 10.8822 2.43228C11.3466 2.00201 11.9473 1.7483 12.5796 1.71536ZM14.0045 29.4889H22.9042C22.9929 29.4902 23.0808 29.472 23.1616 29.4354C23.2425 29.3989 23.3143 29.345 23.3719 29.2775C23.4296 29.2101 23.4717 29.1308 23.4952 29.0452C23.5187 28.9596 23.523 28.87 23.5079 28.7825L23.2 26.9712C23.1513 26.5649 23.0014 26.1774 22.7641 25.844C22.5267 25.5107 22.2095 25.2422 21.8415 25.0633L20.32 24.3569C20.2159 24.3048 20.099 24.2839 19.9833 24.2968C19.8676 24.3097 19.7581 24.3557 19.6679 24.4293C19.5101 24.5946 19.3203 24.7262 19.1102 24.8161C18.9001 24.906 18.6739 24.9523 18.4453 24.9523C18.2167 24.9523 17.9905 24.906 17.7804 24.8161C17.5702 24.7262 17.3805 24.5946 17.2227 24.4293C17.1325 24.3557 17.023 24.3097 16.9073 24.2968C16.7916 24.2839 16.6747 24.3048 16.5706 24.3569L15.0793 25.0814C14.7147 25.2562 14.3998 25.5197 14.1635 25.8477C13.9272 26.1758 13.777 26.558 13.7268 26.9591L13.4068 28.7705C13.393 28.8572 13.3983 28.9458 13.4223 29.0303C13.4462 29.1148 13.4884 29.193 13.5457 29.2595C13.6006 29.3291 13.6702 29.3857 13.7495 29.4254C13.8288 29.465 13.9159 29.4867 14.0045 29.4889ZM14.9162 27.1342C14.9431 26.9284 15.0206 26.7324 15.1418 26.5638C15.2629 26.3952 15.424 26.2593 15.6106 26.1682L16.7759 25.5644C17.2595 25.937 17.8529 26.139 18.4634 26.139C19.0739 26.139 19.6673 25.937 20.151 25.5644L21.3283 26.1682C21.5193 26.263 21.6831 26.4046 21.8046 26.5799C21.926 26.7552 22.001 26.9583 22.0226 27.1705L22.2159 28.2874H14.717L14.9162 27.1342ZM18.4543 24.0489C19.268 24.0129 20.0343 23.6559 20.5854 23.0562C21.1364 22.4564 21.4275 21.6628 21.3947 20.8489C21.4275 20.0351 21.1364 19.2415 20.5854 18.6417C20.0343 18.042 19.268 17.685 18.4543 17.6489C17.6402 17.685 16.8733 18.0418 16.3213 18.6414C15.7692 19.2409 15.4768 20.0345 15.5079 20.8489C15.4768 21.6633 15.7692 22.457 16.3213 23.0565C16.8733 23.6561 17.6402 24.0129 18.4543 24.0489ZM18.4543 18.8565C18.9473 18.8936 19.4057 19.1237 19.7301 19.4967C20.0546 19.8697 20.2188 20.3556 20.1872 20.8489C20.2188 21.3423 20.0546 21.8282 19.7301 22.2012C19.4057 22.5742 18.9473 22.8043 18.4543 22.8414C17.9603 22.8058 17.5004 22.5764 17.1747 22.2032C16.849 21.83 16.6839 21.3433 16.7155 20.8489C16.6839 20.3546 16.849 19.8679 17.1747 19.4947C17.5004 19.1215 17.9603 18.8921 18.4543 18.8565ZM0.504161 28.7825C0.489068 28.87 0.49343 28.9596 0.516937 29.0452C0.540443 29.1308 0.582516 29.2101 0.640163 29.2775C0.697809 29.345 0.769611 29.3989 0.850461 29.4354C0.93131 29.472 1.01922 29.4902 1.10793 29.4889H9.99548C10.0832 29.4882 10.1696 29.4684 10.2489 29.4308C10.3281 29.3933 10.3982 29.339 10.4544 29.2716C10.5117 29.2051 10.5538 29.1269 10.5778 29.0424C10.6018 28.9579 10.607 28.8692 10.5932 28.7825L10.2793 26.9712C10.2306 26.5657 10.0814 26.1788 9.84522 25.8456C9.60903 25.5124 9.29333 25.2435 8.9268 25.0633L7.41133 24.3569C7.30795 24.3061 7.1922 24.2859 7.07773 24.2987C6.96326 24.3116 6.85485 24.3569 6.76529 24.4293C6.60691 24.5948 6.41666 24.7265 6.20602 24.8165C5.99537 24.9064 5.76869 24.9528 5.53963 24.9528C5.31058 24.9528 5.08389 24.9064 4.87325 24.8165C4.6626 24.7265 4.47235 24.5948 4.31397 24.4293C4.22442 24.3569 4.11601 24.3116 4.00154 24.2987C3.88707 24.2859 3.77131 24.3061 3.66793 24.3569L2.17058 25.0814C1.80824 25.2577 1.49579 25.5219 1.26168 25.8499C1.02756 26.1778 0.879232 26.5592 0.830199 26.9591L0.504161 28.7825ZM2.03775 27.1342C2.05999 26.9309 2.1323 26.7362 2.2482 26.5676C2.3641 26.399 2.51998 26.2618 2.7019 26.1682L3.84907 25.5644C4.33009 25.9297 4.9175 26.1275 5.52152 26.1275C6.12554 26.1275 6.71295 25.9297 7.19397 25.5644L8.37133 26.1682C8.56231 26.263 8.72617 26.4046 8.84759 26.5799C8.96901 26.7552 9.04403 26.9583 9.06567 27.1705L9.28303 28.2814H1.81435L2.03775 27.1342ZM2.60529 20.8489C2.57255 21.6628 2.86358 22.4564 3.41466 23.0562C3.96574 23.6559 4.73199 24.0129 5.54567 24.0489C6.35987 24.0129 7.12671 23.6561 7.67875 23.0565C8.23079 22.457 8.52319 21.6633 8.49209 20.8489C8.52319 20.0345 8.23079 19.2409 7.67875 18.6414C7.12671 18.0418 6.35987 17.685 5.54567 17.6489C4.73199 17.685 3.96574 18.042 3.41466 18.6417C2.86358 19.2415 2.57255 20.0351 2.60529 20.8489ZM7.28454 20.8489C7.31608 21.3433 7.15104 21.83 6.82535 22.2032C6.49966 22.5764 6.03971 22.8058 5.54567 22.8414C5.05271 22.8043 4.59429 22.5742 4.26988 22.2012C3.94547 21.8282 3.78124 21.3423 3.81284 20.8489C3.78124 20.3556 3.94547 19.8697 4.26988 19.4967C4.59429 19.1237 5.05271 18.8936 5.54567 18.8565C6.03971 18.8921 6.49966 19.1215 6.82535 19.4947C7.15104 19.8679 7.31608 20.3546 7.28454 20.8489ZM14.1132 22.7569C14.2233 22.8539 14.3649 22.9076 14.5117 22.9078C14.5974 22.9077 14.6822 22.8893 14.7602 22.8539C14.8383 22.8186 14.908 22.7669 14.9645 22.7025C15.0701 22.5824 15.1236 22.4252 15.1134 22.2656C15.1033 22.106 15.0302 21.957 14.9102 21.8512L12.803 20.0097V17.6671C12.803 17.5069 12.7394 17.3534 12.6262 17.2401C12.513 17.1269 12.3594 17.0633 12.1993 17.0633C12.0391 17.0633 11.8856 17.1269 11.7723 17.2401C11.6591 17.3534 11.5955 17.5069 11.5955 17.6671V20.0278L9.71775 21.8754C9.60529 21.9885 9.54217 22.1415 9.54217 22.301C9.54217 22.4605 9.60529 22.6136 9.71775 22.7267C9.77366 22.7837 9.84035 22.8291 9.91393 22.8602C9.98752 22.8913 10.0665 22.9075 10.1464 22.9078C10.304 22.9088 10.4556 22.8481 10.5691 22.7388L12.2294 21.1086L14.1132 22.7569Z"
          className="fill-current"
        />
      </svg>
    ),
    tests: (
      <svg
        className={className}
        width={width ? width : '22'}
        height={height ? height : '22'}
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7 4.5C7 4.36739 7.05268 4.24021 7.14645 4.14645C7.24021 4.05268 7.36739 4 7.5 4H11.5C11.6326 4 11.7598 4.05268 11.8536 4.14645C11.9473 4.24021 12 4.36739 12 4.5C12 4.63261 11.9473 4.75979 11.8536 4.85355C11.7598 4.94732 11.6326 5 11.5 5H7.5C7.36739 5 7.24021 4.94732 7.14645 4.85355C7.05268 4.75979 7 4.63261 7 4.5ZM7.5 6C7.36739 6 7.24021 6.05268 7.14645 6.14645C7.05268 6.24021 7 6.36739 7 6.5C7 6.63261 7.05268 6.75979 7.14645 6.85355C7.24021 6.94732 7.36739 7 7.5 7H11.5C11.6326 7 11.7598 6.94732 11.8536 6.85355C11.9473 6.75979 12 6.63261 12 6.5C12 6.36739 11.9473 6.24021 11.8536 6.14645C11.7598 6.05268 11.6326 6 11.5 6H7.5ZM7 11C7 10.8674 7.05268 10.7402 7.14645 10.6464C7.24021 10.5527 7.36739 10.5 7.5 10.5H11.5C11.6326 10.5 11.7598 10.5527 11.8536 10.6464C11.9473 10.7402 12 10.8674 12 11C12 11.1326 11.9473 11.2598 11.8536 11.3536C11.7598 11.4473 11.6326 11.5 11.5 11.5H7.5C7.36739 11.5 7.24021 11.4473 7.14645 11.3536C7.05268 11.2598 7 11.1326 7 11ZM7.5 12.5C7.36739 12.5 7.24021 12.5527 7.14645 12.6464C7.05268 12.7402 7 12.8674 7 13C7 13.1326 7.05268 13.2598 7.14645 13.3536C7.24021 13.4473 7.36739 13.5 7.5 13.5H11.5C11.6326 13.5 11.7598 13.4473 11.8536 13.3536C11.9473 13.2598 12 13.1326 12 13C12 12.8674 11.9473 12.7402 11.8536 12.6464C11.7598 12.5527 11.6326 12.5 11.5 12.5H7.5Z"
          className="fill-current"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M2 10.5C2 10.3674 2.05268 10.2402 2.14645 10.1464C2.24021 10.0527 2.36739 10 2.5 10H5C5.13261 10 5.25979 10.0527 5.35355 10.1464C5.44732 10.2402 5.5 10.3674 5.5 10.5V13C5.5 13.1326 5.44732 13.2598 5.35355 13.3536C5.25979 13.4473 5.13261 13.5 5 13.5H2.5C2.36739 13.5 2.24021 13.4473 2.14645 13.3536C2.05268 13.2598 2 13.1326 2 13V10.5ZM3 11V12.5H4.5V11H3Z"
          className="fill-current"
        />
        <path
          d="M5.85352 4.85517C5.9446 4.76087 5.995 4.63457 5.99386 4.50347C5.99272 4.37237 5.94013 4.24696 5.84743 4.15426C5.75473 4.06155 5.62932 4.00897 5.49822 4.00783C5.36712 4.00669 5.24082 4.05709 5.14652 4.14817L3.50002 5.79467L2.85352 5.14817C2.75922 5.05709 2.63292 5.00669 2.50182 5.00783C2.37072 5.00897 2.24532 5.06156 2.15261 5.15426C2.05991 5.24696 2.00732 5.37237 2.00618 5.50347C2.00504 5.63457 2.05544 5.76087 2.14652 5.85517L3.50002 7.20867L5.85352 4.85517Z"
          className="fill-current"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M2 0C1.46957 0 0.960859 0.210714 0.585786 0.585786C0.210714 0.960859 0 1.46957 0 2V16C0 16.5304 0.210714 17.0391 0.585786 17.4142C0.960859 17.7893 1.46957 18 2 18H12C12.5304 18 13.0391 17.7893 13.4142 17.4142C13.7893 17.0391 14 16.5304 14 16V2C14 1.46957 13.7893 0.960859 13.4142 0.585786C13.0391 0.210714 12.5304 0 12 0H2ZM1 2C1 1.73478 1.10536 1.48043 1.29289 1.29289C1.48043 1.10536 1.73478 1 2 1H12C12.2652 1 12.5196 1.10536 12.7071 1.29289C12.8946 1.48043 13 1.73478 13 2V16C13 16.2652 12.8946 16.5196 12.7071 16.7071C12.5196 16.8946 12.2652 17 12 17H2C1.73478 17 1.48043 16.8946 1.29289 16.7071C1.10536 16.5196 1 16.2652 1 16V2ZM15 5C15 4.60218 15.158 4.22064 15.4393 3.93934C15.7206 3.65804 16.1022 3.5 16.5 3.5C16.8978 3.5 17.2794 3.65804 17.5607 3.93934C17.842 4.22064 18 4.60218 18 5V15.1515L16.5 17.4015L15 15.1515V5ZM16.5 4.5C16.3674 4.5 16.2402 4.55268 16.1464 4.64645C16.0527 4.74021 16 4.86739 16 5V6H17V5C17 4.86739 16.9473 4.74021 16.8536 4.64645C16.7598 4.55268 16.6326 4.5 16.5 4.5ZM16.5 15.5985L16 14.8485V7H17V14.8485L16.5 15.5985Z"
          className="fill-current"
        />
      </svg>
    ),
    questions: (
      <svg
        className={className}
        width={width ? width : '22'}
        height={height ? height : '22'}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12.0752 14.4C12.5792 14.4 12.8312 14.04 12.9062 13.4595C12.9662 12.6585 13.2032 12.237 14.1767 11.5695C15.1862 10.857 15.7502 9.9345 15.7502 8.5905C15.7502 6.603 14.3702 5.25 12.3572 5.25C10.8272 5.25 9.66916 5.988 9.20716 7.185C9.06673 7.51179 8.99622 7.86434 9.00016 8.22C9.00016 8.8095 9.30466 9.18 9.81766 9.18C10.2257 9.18 10.5002 8.9595 10.6637 8.415C10.9007 7.527 11.4512 7.0425 12.2747 7.0425C13.1897 7.0425 13.8197 7.7115 13.8197 8.6685C13.8197 9.513 13.5077 9.996 12.5867 10.656C11.6582 11.3055 11.1977 12.027 11.1977 13.116V13.2825C11.1977 13.9245 11.5097 14.4 12.0752 14.4Z"
          className="fill-current"
        />
        <path
          d="M15.41 3.76829L14.0285 2.35229L15.101 1.30529L16.034 2.26079L17.369 2.24429C17.9469 2.23745 18.5203 2.34623 19.0555 2.56423C19.5907 2.78222 20.077 3.10503 20.4856 3.51368C20.8943 3.92233 21.2171 4.40856 21.4351 4.94379C21.6531 5.47901 21.7618 6.05242 21.755 6.63029L21.74 7.96529L22.694 8.89829C23.1072 9.30205 23.4355 9.78432 23.6597 10.3168C23.8838 10.8492 23.9993 11.4211 23.9993 11.9988C23.9993 12.5765 23.8838 13.1484 23.6597 13.6808C23.4355 14.2133 23.1072 14.6955 22.694 15.0993L21.7385 16.0323L21.755 17.3673C21.7618 17.9452 21.6531 18.5186 21.4351 19.0538C21.2171 19.589 20.8943 20.0753 20.4856 20.4839C20.077 20.8926 19.5907 21.2154 19.0555 21.4334C18.5203 21.6514 17.9469 21.7601 17.369 21.7533L16.034 21.7383L15.101 22.6923C14.6972 23.1055 14.215 23.4338 13.6825 23.658C13.1501 23.8821 12.5782 23.9976 12.0005 23.9976C11.4228 23.9976 10.8509 23.8821 10.3185 23.658C9.78603 23.4338 9.30376 23.1055 8.9 22.6923L7.967 21.7368L6.632 21.7533C6.05413 21.7601 5.48072 21.6514 4.94549 21.4334C4.41027 21.2154 3.92404 20.8926 3.51539 20.4839C3.10674 20.0753 2.78393 19.589 2.56593 19.0538C2.34794 18.5186 2.23916 17.9452 2.246 17.3673L2.261 16.0323L1.307 15.0993C0.893812 14.6955 0.565493 14.2133 0.341336 13.6808C0.11718 13.1484 0.00170898 12.5765 0.00170898 11.9988C0.00170898 11.4211 0.11718 10.8492 0.341336 10.3168C0.565493 9.78432 0.893812 9.30205 1.307 8.89829L2.2625 7.96529L2.246 6.63029C2.23916 6.05242 2.34794 5.47901 2.56593 4.94379C2.78393 4.40856 3.10674 3.92233 3.51539 3.51368C3.92404 3.10503 4.41027 2.78222 4.94549 2.56423C5.48072 2.34623 6.05413 2.23745 6.632 2.24429L7.967 2.25929L8.9 1.30529C9.30376 0.892103 9.78603 0.563784 10.3185 0.339627C10.8509 0.115471 11.4228 0 12.0005 0C12.5782 0 13.1501 0.115471 13.6825 0.339627C14.215 0.563784 14.6972 0.892103 15.101 1.30529L14.0285 2.35229C13.7644 2.08197 13.449 1.86717 13.1007 1.72052C12.7525 1.57386 12.3784 1.49831 12.0005 1.49831C11.6226 1.49831 11.2485 1.57386 10.9003 1.72052C10.552 1.86717 10.2366 2.08197 9.9725 2.35229L8.5925 3.76829L6.6125 3.74429C6.23477 3.74007 5.86 3.81138 5.51021 3.95403C5.16043 4.09669 4.84268 4.30781 4.57564 4.57499C4.30859 4.84218 4.09764 5.16004 3.95517 5.5099C3.8127 5.85976 3.74158 6.23456 3.746 6.61229L3.77 8.58929L2.354 9.97079C2.08368 10.2349 1.86888 10.5503 1.72222 10.8986C1.57557 11.2468 1.50002 11.6209 1.50002 11.9988C1.50002 12.3767 1.57557 12.7508 1.72222 13.099C1.86888 13.4473 2.08368 13.7627 2.354 14.0268L3.77 15.4068L3.746 17.3868C3.74178 17.7645 3.81309 18.1393 3.95574 18.4891C4.0984 18.8389 4.30952 19.1566 4.5767 19.4237C4.84389 19.6907 5.16175 19.9017 5.51161 20.0441C5.86147 20.1866 6.23627 20.2577 6.614 20.2533L8.591 20.2293L9.9725 21.6453C10.2366 21.9156 10.552 22.1304 10.9003 22.2771C11.2485 22.4237 11.6226 22.4993 12.0005 22.4993C12.3784 22.4993 12.7525 22.4237 13.1007 22.2771C13.449 22.1304 13.7644 21.9156 14.0285 21.6453L15.4085 20.2293L17.3885 20.2533C17.7662 20.2575 18.141 20.1862 18.4908 20.0436C18.8406 19.9009 19.1583 19.6898 19.4254 19.4226C19.6924 19.1554 19.9034 18.8376 20.0458 18.4877C20.1883 18.1378 20.2594 17.763 20.255 17.3853L20.231 15.4083L21.647 14.0268C21.9173 13.7627 22.1321 13.4473 22.2788 13.099C22.4254 12.7508 22.501 12.3767 22.501 11.9988C22.501 11.6209 22.4254 11.2468 22.2788 10.8986C22.1321 10.5503 21.9173 10.2349 21.647 9.97079L20.231 8.59079L20.255 6.61079C20.2592 6.23306 20.1879 5.85829 20.0453 5.50851C19.9026 5.15872 19.6915 4.84097 19.4243 4.57393C19.1571 4.30688 18.8393 4.09593 18.4894 3.95346C18.1395 3.81099 17.7647 3.73987 17.387 3.74429L15.41 3.76829Z"
          className="fill-current"
        />
        <path
          d="M10.5017 16.5C10.5017 16.1022 10.6597 15.7206 10.941 15.4393C11.2224 15.158 11.6039 15 12.0017 15C12.3995 15 12.7811 15.158 13.0624 15.4393C13.3437 15.7206 13.5017 16.1022 13.5017 16.5C13.5017 16.8978 13.3437 17.2794 13.0624 17.5607C12.7811 17.842 12.3995 18 12.0017 18C11.6039 18 11.2224 17.842 10.941 17.5607C10.6597 17.2794 10.5017 16.8978 10.5017 16.5Z"
          className="fill-current"
        />
      </svg>
    ),
    screening: (
      <svg
        className={className}
        width={width ? width : '30'}
        height={height ? height : '30'}
        viewBox="0 0 30 30"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M27.5813 29.9999C27.5485 29.9999 27.511 29.9999 27.4781 29.9953C26.8781 29.9671 26.311 29.6812 25.9266 29.2171L22.5938 25.1765C22.3594 24.8906 22.3969 24.4687 22.6828 24.2296C22.9688 23.9953 23.3906 24.0328 23.6297 24.3187L26.9625 28.3593C27.1078 28.5374 27.3141 28.6406 27.5438 28.6499C27.7688 28.664 27.9891 28.5796 28.1485 28.4156C28.3078 28.2515 28.3922 28.0406 28.3828 27.8109C28.3735 27.5812 28.2703 27.3749 28.0922 27.2296L24.0516 23.9062C23.7656 23.6718 23.7235 23.2453 23.9625 22.9593C24.1969 22.6734 24.6235 22.6312 24.9094 22.8703L28.95 26.2031C29.4141 26.5874 29.7 27.1499 29.7281 27.7546C29.7563 28.3546 29.5313 28.9453 29.1047 29.3718C28.6969 29.7749 28.1485 29.9999 27.5813 29.9999Z"
          className="fill-current"
        />
        <path
          d="M20.1656 26.3328C18.5906 26.3328 17.1094 25.7187 15.9984 24.6078C14.8828 23.4922 14.2734 22.0156 14.2734 20.4406C14.2734 18.8656 14.8875 17.3844 15.9984 16.2734C18.2953 13.9766 22.0359 13.9766 24.3375 16.2734C26.6344 18.5703 26.6344 22.3109 24.3375 24.6125C23.2219 25.7187 21.7406 26.3328 20.1656 26.3328ZM20.1656 15.8844C18.9984 15.8844 17.8359 16.3297 16.9453 17.2156C15.1688 18.9922 15.1688 21.8797 16.9453 23.6562C18.7219 25.4328 21.6094 25.4328 23.3859 23.6562C25.1625 21.8797 25.1625 18.9922 23.3859 17.2156C22.5 16.3297 21.3328 15.8844 20.1656 15.8844Z"
          className="fill-current"
        />
        <path
          d="M19.1484 15.9836C18.9656 15.9836 18.7828 15.9086 18.6468 15.7633C17.564 14.5586 15.5109 14.1789 14.9062 14.0945C14.6812 14.057 14.5547 14.0477 14.5406 14.043C13.6219 14.0055 12.9187 13.6961 12.4734 13.1336C12.0469 12.5945 11.9062 11.8305 12.075 10.9961C12.15 10.6305 12.5015 10.3961 12.8672 10.4711C13.2328 10.5414 13.4672 10.8977 13.3922 11.2633C13.35 11.4695 13.2797 11.9898 13.5281 12.3086C13.7156 12.5477 14.0859 12.6836 14.6297 12.707C14.6625 12.707 14.8265 12.7258 15.1031 12.768C16.0125 12.8992 18.2953 13.368 19.6406 14.868C19.889 15.1445 19.8656 15.5711 19.589 15.8148C19.4672 15.9273 19.3078 15.9836 19.1484 15.9836ZM16.5844 24.9086H0.942163C0.571851 24.9086 0.271851 24.6086 0.271851 24.2383V17.6289C0.271851 13.8602 4.55623 12.9508 5.87341 12.7633C6.1406 12.7211 6.30466 12.707 6.30466 12.707C6.87654 12.6789 7.24685 12.5477 7.43435 12.3086C7.67341 12.0039 7.63123 11.5164 7.57966 11.2539C7.57029 11.2117 7.57029 11.1742 7.57029 11.132C7.57029 10.7617 7.87029 10.457 8.2406 10.457C8.58279 10.457 8.86404 10.7102 8.90623 11.0383C9.05623 11.882 8.91091 12.6086 8.48435 13.143C8.04373 13.6961 7.3406 14.0008 6.39373 14.043C6.4031 14.043 6.27654 14.057 6.07029 14.0898C5.0156 14.2398 1.61248 14.9383 1.61248 17.6242V23.5633H16.5797C16.95 23.5633 17.25 23.8633 17.25 24.2336C17.2547 24.6086 16.9547 24.9086 16.5844 24.9086Z"
          className="fill-current"
        />
        <path
          d="M10.4859 24.9093C10.2141 24.9093 9.97032 24.7452 9.86719 24.4968L5.34844 13.6874C5.20313 13.3452 5.36719 12.9515 5.70938 12.8108C6.05157 12.6655 6.44532 12.8296 6.58594 13.1718L10.4859 22.4999L14.3859 13.1718C14.5266 12.8296 14.925 12.6655 15.2625 12.8108C15.6047 12.9562 15.7688 13.3452 15.6234 13.6874L11.1047 24.4968C11.0016 24.7452 10.7578 24.9093 10.4859 24.9093Z"
          className="fill-current"
        />
        <path
          d="M11.9109 21.4934C11.5828 21.4934 11.2922 21.2496 11.2453 20.9168L10.3922 14.8793C10.3406 14.5137 10.5984 14.1715 10.9641 14.1199C11.3297 14.0684 11.6719 14.3215 11.7234 14.6918L12.5766 20.7293C12.6281 21.0949 12.3703 21.4371 12.0047 21.4887C11.9766 21.4934 11.9437 21.4934 11.9109 21.4934Z"
          className="fill-current"
        />
        <path
          d="M9.06092 21.4933C9.02811 21.4933 8.99999 21.4933 8.96717 21.4886C8.60155 21.437 8.34374 21.0948 8.3953 20.7292L9.24842 14.6917C9.29999 14.3261 9.64218 14.0683 10.0078 14.1198C10.3734 14.1714 10.6312 14.5136 10.5797 14.8792L9.72655 20.9167C9.67499 21.2495 9.38905 21.4933 9.06092 21.4933Z"
          className="fill-current"
        />
        <path
          d="M11.7844 15.4547H9.18749C8.81718 15.4547 8.51718 15.1547 8.51718 14.7844C8.51718 14.4141 8.81718 14.1141 9.18749 14.1141H11.7844C12.1547 14.1141 12.4547 14.4141 12.4547 14.7844C12.4547 15.1547 12.1547 15.4547 11.7844 15.4547ZM10.4859 12.6094C7.29843 12.6094 5.08125 8.99531 5.08125 5.75156C5.07656 2.41875 7.35468 0 10.4859 0C13.6172 0 15.8906 2.41875 15.8906 5.75156C15.8906 8.99531 13.6734 12.6094 10.4859 12.6094ZM10.4859 1.34531C8.13281 1.34531 6.42187 3.20156 6.42187 5.75156C6.42187 8.5875 8.39531 11.2641 10.4859 11.2641C12.5766 11.2641 14.55 8.58281 14.55 5.75156C14.55 3.19688 12.8391 1.34531 10.4859 1.34531Z"
          className="fill-current"
        />
      </svg>
    ),
    applicant: (
      <svg
        className={className}
        width={width ? width : '22'}
        height={height ? height : '24'}
        viewBox="0 0 22 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.5 0.21875H4.5C3.96128 0.21875 3.44462 0.432756 3.06369 0.813689C2.68276 1.19462 2.46875 1.71128 2.46875 2.25V9.15625C2.46875 9.69497 2.68276 10.2116 3.06369 10.5926C3.44462 10.9735 3.96128 11.1875 4.5 11.1875H17.5C18.0387 11.1875 18.5554 10.9735 18.9363 10.5926C19.3172 10.2116 19.5312 9.69497 19.5312 9.15625V2.25C19.5312 1.71128 19.3172 1.19462 18.9363 0.813689C18.5554 0.432756 18.0387 0.21875 17.5 0.21875ZM5.71875 10.375V9.15625C5.71875 8.83302 5.84715 8.52302 6.07571 8.29446C6.30427 8.0659 6.61427 7.9375 6.9375 7.9375H9.375C9.69823 7.9375 10.0082 8.0659 10.2368 8.29446C10.4653 8.52302 10.5938 8.83302 10.5938 9.15625V10.375H5.71875ZM18.7188 9.15625C18.7188 9.47948 18.5903 9.78948 18.3618 10.018C18.1332 10.2466 17.8232 10.375 17.5 10.375H11.4062V9.15625C11.4062 8.61753 11.1922 8.10087 10.8113 7.71994C10.4304 7.33901 9.91372 7.125 9.375 7.125H6.9375C6.39878 7.125 5.88212 7.33901 5.50119 7.71994C5.12026 8.10087 4.90625 8.61753 4.90625 9.15625V10.375H4.5C4.17677 10.375 3.86677 10.2466 3.63821 10.018C3.40965 9.78948 3.28125 9.47948 3.28125 9.15625V2.25C3.28125 1.92677 3.40965 1.61677 3.63821 1.38821C3.86677 1.15965 4.17677 1.03125 4.5 1.03125H17.5C17.8232 1.03125 18.1332 1.15965 18.3618 1.38821C18.5903 1.61677 18.7188 1.92677 18.7188 2.25V9.15625ZM4.5 18.9062H2.0625C1.52378 18.9062 1.00712 19.1203 0.626189 19.5012C0.245256 19.8821 0.03125 20.3988 0.03125 20.9375V23.375C0.03125 23.4827 0.0740513 23.5861 0.150238 23.6623C0.226425 23.7384 0.329756 23.7812 0.4375 23.7812H6.125C6.23274 23.7812 6.33608 23.7384 6.41226 23.6623C6.48845 23.5861 6.53125 23.4827 6.53125 23.375V20.9375C6.53125 20.3988 6.31724 19.8821 5.93631 19.5012C5.55538 19.1203 5.03872 18.9062 4.5 18.9062ZM5.71875 22.9688H0.84375V20.9375C0.84375 20.6143 0.972154 20.3043 1.20071 20.0757C1.42927 19.8472 1.73927 19.7188 2.0625 19.7188H4.5C4.82323 19.7188 5.13323 19.8472 5.36179 20.0757C5.59035 20.3043 5.71875 20.6143 5.71875 20.9375V22.9688ZM1.25 16.0625C1.25 16.4642 1.36913 16.857 1.59233 17.191C1.81552 17.525 2.13276 17.7854 2.50392 17.9391C2.87509 18.0929 3.2835 18.1331 3.67753 18.0547C4.07155 17.9763 4.43349 17.7829 4.71756 17.4988C5.00164 17.2147 5.19509 16.8528 5.27347 16.4588C5.35185 16.0648 5.31162 15.6563 5.15788 15.2852C5.00414 14.914 4.74379 14.5968 4.40975 14.3736C4.07571 14.1504 3.68299 14.0312 3.28125 14.0312C2.74253 14.0312 2.22587 14.2453 1.84494 14.6262C1.46401 15.0071 1.25 15.5238 1.25 16.0625ZM3.28125 14.8438C3.5223 14.8438 3.75793 14.9152 3.95835 15.0491C4.15877 15.1831 4.31498 15.3734 4.40723 15.5961C4.49947 15.8188 4.52361 16.0639 4.47658 16.3003C4.42956 16.5367 4.31348 16.7538 4.14304 16.9243C3.97259 17.0947 3.75543 17.2108 3.51902 17.2578C3.2826 17.3049 3.03755 17.2807 2.81485 17.1885C2.59216 17.0962 2.40181 16.94 2.2679 16.7396C2.13398 16.5392 2.0625 16.3035 2.0625 16.0625C2.0625 15.7393 2.1909 15.4293 2.41946 15.2007C2.64802 14.9722 2.95802 14.8438 3.28125 14.8438ZM19.9375 18.9062H17.5C16.9613 18.9062 16.4446 19.1203 16.0637 19.5012C15.6828 19.8821 15.4688 20.3988 15.4688 20.9375V23.375C15.4688 23.4827 15.5116 23.5861 15.5877 23.6623C15.6639 23.7384 15.7673 23.7812 15.875 23.7812H21.5625C21.6702 23.7812 21.7736 23.7384 21.8498 23.6623C21.9259 23.5861 21.9688 23.4827 21.9688 23.375V20.9375C21.9688 20.3988 21.7547 19.8821 21.3738 19.5012C20.9929 19.1203 20.4762 18.9062 19.9375 18.9062ZM21.1562 22.9688H16.2812V20.9375C16.2812 20.6143 16.4097 20.3043 16.6382 20.0757C16.8668 19.8472 17.1768 19.7188 17.5 19.7188H19.9375C20.2607 19.7188 20.5707 19.8472 20.7993 20.0757C21.0278 20.3043 21.1562 20.6143 21.1562 20.9375V22.9688ZM16.6875 16.0625C16.6875 16.4642 16.8066 16.857 17.0298 17.191C17.253 17.525 17.5703 17.7854 17.9414 17.9391C18.3126 18.0929 18.721 18.1331 19.115 18.0547C19.5091 17.9763 19.871 17.7829 20.1551 17.4988C20.4391 17.2147 20.6326 16.8528 20.711 16.4588C20.7893 16.0648 20.7491 15.6563 20.5954 15.2852C20.4416 14.914 20.1813 14.5968 19.8473 14.3736C19.5132 14.1504 19.1205 14.0312 18.7188 14.0312C18.18 14.0312 17.6634 14.2453 17.2824 14.6262C16.9015 15.0071 16.6875 15.5238 16.6875 16.0625ZM19.9375 16.0625C19.9375 16.3035 19.866 16.5392 19.7321 16.7396C19.5982 16.94 19.4078 17.0962 19.1851 17.1885C18.9624 17.2807 18.7174 17.3049 18.481 17.2578C18.2446 17.2108 18.0274 17.0947 17.857 16.9243C17.6865 16.7538 17.5704 16.5367 17.5234 16.3003C17.4764 16.0639 17.5005 15.8188 17.5928 15.5961C17.685 15.3734 17.8412 15.1831 18.0416 15.0491C18.2421 14.9152 18.4777 14.8438 18.7188 14.8438C19.042 14.8438 19.352 14.9722 19.5805 15.2007C19.8091 15.4293 19.9375 15.7393 19.9375 16.0625ZM12.2188 18.9062H9.78125C9.24253 18.9062 8.72587 19.1203 8.34494 19.5012C7.96401 19.8821 7.75 20.3988 7.75 20.9375V23.375C7.75 23.4827 7.7928 23.5861 7.86899 23.6623C7.94517 23.7384 8.04851 23.7812 8.15625 23.7812H13.8438C13.9515 23.7812 14.0548 23.7384 14.131 23.6623C14.2072 23.5861 14.25 23.4827 14.25 23.375V20.9375C14.25 20.3988 14.036 19.8821 13.6551 19.5012C13.2741 19.1203 12.7575 18.9062 12.2188 18.9062ZM13.4375 22.9688H8.5625V20.9375C8.5625 20.6143 8.6909 20.3043 8.91946 20.0757C9.14802 19.8472 9.45802 19.7188 9.78125 19.7188H12.2188C12.542 19.7188 12.852 19.8472 13.0805 20.0757C13.3091 20.3043 13.4375 20.6143 13.4375 20.9375V22.9688ZM8.96875 16.0625C8.96875 16.4642 9.08788 16.857 9.31108 17.191C9.53427 17.525 9.85151 17.7854 10.2227 17.9391C10.5938 18.0929 11.0023 18.1331 11.3963 18.0547C11.7903 17.9763 12.1522 17.7829 12.4363 17.4988C12.7204 17.2147 12.9138 16.8528 12.9922 16.4588C13.0706 16.0648 13.0304 15.6563 12.8766 15.2852C12.7229 14.914 12.4625 14.5968 12.1285 14.3736C11.7945 14.1504 11.4017 14.0312 11 14.0312C10.4613 14.0312 9.94462 14.2453 9.56369 14.6262C9.18276 15.0071 8.96875 15.5238 8.96875 16.0625ZM12.2188 16.0625C12.2188 16.3035 12.1473 16.5392 12.0134 16.7396C11.8794 16.94 11.6891 17.0962 11.4664 17.1885C11.2437 17.2807 10.9986 17.3049 10.7622 17.2578C10.5258 17.2108 10.3087 17.0947 10.1382 16.9243C9.96777 16.7538 9.85169 16.5367 9.80467 16.3003C9.75764 16.0639 9.78178 15.8188 9.87402 15.5961C9.96627 15.3734 10.1225 15.1831 10.3229 15.0491C10.5233 14.9152 10.759 14.8438 11 14.8438C11.3232 14.8438 11.6332 14.9722 11.8618 15.2007C12.0903 15.4293 12.2188 15.7393 12.2188 16.0625Z"
          className="fill-current"
        />
        <path
          d="M8.15625 2.25C7.75451 2.25 7.36179 2.36913 7.02775 2.59233C6.69371 2.81552 6.43336 3.13276 6.27962 3.50392C6.12588 3.87509 6.08565 4.2835 6.16403 4.67753C6.24241 5.07155 6.43587 5.43349 6.71994 5.71756C7.00402 6.00164 7.36595 6.19509 7.75997 6.27347C8.154 6.35185 8.56241 6.31162 8.93358 6.15788C9.30474 6.00414 9.62198 5.74379 9.84517 5.40975C10.0684 5.07571 10.1875 4.68299 10.1875 4.28125C10.1875 3.74253 9.9735 3.22587 9.59256 2.84494C9.21163 2.46401 8.69497 2.25 8.15625 2.25ZM8.15625 5.5C7.91521 5.5 7.67957 5.42852 7.47915 5.2946C7.27873 5.16069 7.12252 4.97034 7.03027 4.74765C6.93803 4.52495 6.91389 4.2799 6.96092 4.04348C7.00794 3.80707 7.12402 3.58991 7.29446 3.41946C7.46491 3.24902 7.68207 3.13294 7.91848 3.08592C8.1549 3.03889 8.39995 3.06303 8.62265 3.15527C8.84534 3.24752 9.03569 3.40373 9.16961 3.60415C9.30352 3.80457 9.375 4.0402 9.375 4.28125C9.375 4.60448 9.2466 4.91448 9.01804 5.14304C8.78948 5.3716 8.47948 5.5 8.15625 5.5ZM12.2188 3.46875H17.0938V4.28125H12.2188V3.46875ZM12.2188 6.71875H17.0938V7.53125H12.2188V6.71875ZM12.2188 5.09375H15.4688V5.90625H12.2188V5.09375Z"
          className="fill-current"
        />
      </svg>
    ),
    interview: (
      <svg
        className={className}
        width={width ? width : '23'}
        height={height ? height : '19'}
        viewBox="0 0 23 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M4.71327 7.12908L4.46727 7.69508C4.42882 7.78725 4.36396 7.86598 4.28086 7.92135C4.19776 7.97673 4.10013 8.00628 4.00027 8.00628C3.9004 8.00628 3.80277 7.97673 3.71967 7.92135C3.63657 7.86598 3.57171 7.78725 3.53327 7.69508L3.28727 7.12908C2.85475 6.1278 2.06265 5.325 1.06727 4.87908L0.308266 4.54008C0.216185 4.49772 0.13818 4.42985 0.0834994 4.34451C0.0288189 4.25917 -0.000244141 4.15994 -0.000244141 4.05858C-0.000244141 3.95722 0.0288189 3.85799 0.0834994 3.77265C0.13818 3.68731 0.216185 3.61944 0.308266 3.57708L1.02527 3.25808C2.04567 2.79946 2.85132 1.96696 3.27627 0.932081L3.52927 0.321081C3.56643 0.226435 3.63124 0.145179 3.71525 0.0879062C3.79927 0.0306333 3.89859 0 4.00027 0C4.10195 0 4.20127 0.0306333 4.28528 0.0879062C4.3693 0.145179 4.43411 0.226435 4.47127 0.321081L4.72427 0.931081C5.14876 1.96616 5.95405 2.79901 6.97427 3.25808L7.69227 3.57808C7.78407 3.62056 7.8618 3.68842 7.91627 3.77365C7.97075 3.85889 7.99969 3.95793 7.99969 4.05908C7.99969 4.16023 7.97075 4.25928 7.91627 4.34451C7.8618 4.42974 7.78407 4.4976 7.69227 4.54008L6.93227 4.87808C5.93708 5.32445 5.14534 6.12761 4.71327 7.12908ZM1.00027 17.0011V10.0011H3.00027V16.0011H15.0003V4.00108H10.0003V2.00108H16.0003C16.2655 2.00108 16.5198 2.10644 16.7074 2.29397C16.8949 2.48151 17.0003 2.73586 17.0003 3.00108V7.20108L22.2133 3.55108C22.2882 3.49854 22.3762 3.46758 22.4675 3.46158C22.5589 3.45559 22.6501 3.47478 22.7313 3.51708C22.8125 3.55937 22.8805 3.62314 22.9279 3.70143C22.9753 3.77973 23.0004 3.86954 23.0003 3.96108V16.0411C23.0004 16.1326 22.9753 16.2224 22.9279 16.3007C22.8805 16.379 22.8125 16.4428 22.7313 16.4851C22.6501 16.5274 22.5589 16.5466 22.4675 16.5406C22.3762 16.5346 22.2882 16.5036 22.2133 16.4511L17.0003 12.8011V17.0011C17.0003 17.2663 16.8949 17.5207 16.7074 17.7082C16.5198 17.8957 16.2655 18.0011 16.0003 18.0011H2.00027C1.73505 18.0011 1.4807 17.8957 1.29316 17.7082C1.10562 17.5207 1.00027 17.2663 1.00027 17.0011ZM17.0003 10.3601L21.0003 13.1601V6.84108L17.0003 9.64108V10.3601Z"
          className="fill-current"
        />
      </svg>
    ),
    archive: (
      <svg
        className={className}
        width={width ? width : '18'}
        height={height ? height : '18'}
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M15.6667 7.33464C15.6667 6.16797 15.6667 5.58464 15.4392 5.1388C15.2396 4.74672 14.9211 4.42788 14.5292 4.22797C14.0834 4.0013 13.5 4.0013 12.3334 4.0013H5.66669C4.50002 4.0013 3.91669 4.0013 3.47085 4.22797C3.07865 4.42774 2.75979 4.7466 2.56002 5.1388C2.33335 5.58464 2.33335 6.16797 2.33335 7.33464M14 4.0013C14 2.42964 14 1.64464 13.5117 1.1563C13.0234 0.667969 12.2384 0.667969 10.6667 0.667969H7.33335C5.76169 0.667969 4.97669 0.667969 4.48835 1.1563C4.00002 1.64464 4.00002 2.42964 4.00002 4.0013M0.666687 12.3346C0.666687 10.3855 0.666687 9.4113 1.11335 8.69964C1.34665 8.3287 1.66054 8.0151 2.03169 7.78214C2.74335 7.33464 3.71669 7.33464 5.66669 7.33464H12.3334C14.2825 7.33464 15.2567 7.33464 15.9684 7.78214C16.3394 8.01486 16.6533 8.32817 16.8867 8.6988C17.3334 9.41214 17.3334 10.3863 17.3334 12.3346C17.3334 14.283 17.3334 15.258 16.8859 15.9696C16.6531 16.3407 16.3398 16.6546 15.9692 16.888C15.2559 17.3346 14.2817 17.3346 12.3334 17.3346H5.66669C3.71752 17.3346 2.74335 17.3346 2.03169 16.8871C1.66063 16.6544 1.34675 16.3411 1.11335 15.9705C0.666687 15.2571 0.666687 14.283 0.666687 12.3346Z"
          stroke="currentColor"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M11.5 10.668C11.5 11.11 11.3244 11.5339 11.0118 11.8465C10.6993 12.159 10.2754 12.3346 9.83333 12.3346H8.16667C7.72464 12.3346 7.30072 12.159 6.98816 11.8465C6.6756 11.5339 6.5 11.11 6.5 10.668"
          stroke="currentColor"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    export: (
      <svg
        className={className}
        width={width ? width : '20'}
        height={height ? height : '20'}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.91669 9.79297L10 11.8763L12.0834 9.79297M10 3.54297V11.369"
          stroke="currentColor"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M16.6666 13.957L16.4821 15.0644C16.3481 15.868 15.6528 16.457 14.8381 16.457H5.16185C4.34713 16.457 3.65181 15.868 3.51787 15.0644L3.33331 13.957"
          stroke="currentColor"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    assign: (
      <svg
        className={className}
        width={width ? width : '18'}
        height={height ? height : '20'}
        viewBox="0 0 18 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M2.14802 17.332C1.73735 17.332 1.3878 17.1878 1.09935 16.8994C0.810909 16.6109 0.666687 16.2617 0.666687 15.8516V4.14578C0.666687 3.73634 0.810909 3.38709 1.09935 3.09803C1.3878 2.80959 1.73735 2.66536 2.14802 2.66536H6.45635C6.37263 2.19725 6.48447 1.77467 6.79185 1.39761C7.09863 1.02056 7.50135 0.832031 8.00002 0.832031C8.5103 0.832031 8.91913 1.02056 9.22652 1.39761C9.53391 1.77467 9.63994 2.19725 9.5446 2.66536H13.8529C14.2624 2.66536 14.6116 2.80959 14.9007 3.09803C15.1891 3.38648 15.3334 3.73603 15.3334 4.1467V9.60086C15.1726 9.54525 15.0186 9.50003 14.8714 9.4652C14.7247 9.43098 14.5731 9.39675 14.4167 9.36253V4.1467C14.4167 4.00553 14.358 3.87598 14.2407 3.75803C14.1234 3.64009 13.9938 3.58142 13.852 3.58203H2.14802C2.00685 3.58203 1.8773 3.6407 1.75935 3.75803C1.64141 3.87536 1.58274 4.00492 1.58335 4.1467V15.8516C1.58335 15.9922 1.64202 16.1214 1.75935 16.2394C1.87669 16.3573 2.00594 16.416 2.1471 16.4154H7.34094C7.36294 16.5865 7.39135 16.7457 7.42619 16.8929C7.46102 17.0396 7.50594 17.186 7.56094 17.332H2.14802ZM1.58335 15.4987V16.4154V3.58203V9.36253V9.29378V15.4987ZM3.87502 13.9129H7.50502C7.53863 13.7558 7.58446 13.6039 7.64252 13.4573L7.82585 12.9953H3.87502V13.9129ZM3.87502 10.457H9.90669C10.208 10.2456 10.5047 10.0665 10.7968 9.91986C11.0889 9.77259 11.4024 9.65709 11.7373 9.57336V9.54036H3.87502V10.457ZM3.87502 7.0012H12.125V6.08453H3.87502V7.0012ZM8.00002 3.07145C8.19863 3.07145 8.36302 3.00667 8.49319 2.87711C8.62335 2.74756 8.68813 2.58317 8.68752 2.38395C8.68691 2.18473 8.62183 2.02064 8.49227 1.8917C8.36271 1.76275 8.19863 1.69736 8.00002 1.69553C7.80141 1.6937 7.63733 1.75878 7.50777 1.89078C7.37822 2.02278 7.31313 2.18686 7.31252 2.38303C7.31191 2.5792 7.37699 2.74359 7.50777 2.8762C7.63855 3.00881 7.80263 3.0745 8.00002 3.07145ZM13.5 19.2717C12.4789 19.2717 11.6126 18.9157 10.9013 18.2038C10.1893 17.4924 9.83335 16.6262 9.83335 15.605C9.83335 14.5839 10.1893 13.7173 10.9013 13.0054C11.6132 12.2934 12.4795 11.9378 13.5 11.9384C14.5206 11.939 15.3871 12.2946 16.0997 13.0054C16.8122 13.7161 17.1679 14.5826 17.1667 15.605C17.1667 16.6256 16.811 17.4918 16.0997 18.2038C15.3871 18.9157 14.5206 19.2717 13.5 19.2717ZM13.148 18.2487H13.8529V15.957H16.1446V15.2512H13.8529V12.9595H13.1471V15.2512H10.8554V15.957H13.1471L13.148 18.2487Z"
          className="fill-current"
        />
      </svg>
    ),
    more: (
      <svg
        className={className}
        width={width ? width : '23'}
        height={height ? height : '23'}
        viewBox="0 0 26 26"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M13.3248 17.2814C12.4618 17.275 11.7571 17.9694 11.7507 18.8323C11.7443 19.6952 12.4386 20.4 13.3016 20.4064C14.1645 20.4128 14.8692 19.7184 14.8756 18.8555C14.882 17.9926 14.1877 17.2879 13.3248 17.2814Z"
          className="fill-current"
        />
        <path
          d="M13.3712 11.0314C12.5082 11.025 11.8035 11.7194 11.7971 12.5823C11.7907 13.4452 12.485 14.15 13.3479 14.1564C14.2109 14.1628 14.9156 13.4684 14.922 12.6055C14.9284 11.7426 14.2341 11.0379 13.3712 11.0314Z"
          className="fill-current"
        />
        <path
          d="M13.4178 4.78145C12.5549 4.77504 11.8501 5.46938 11.8437 6.3323C11.8373 7.19522 12.5317 7.89995 13.3946 7.90636C14.2575 7.91277 14.9622 7.21843 14.9686 6.35551C14.975 5.49259 14.2807 4.78786 13.4178 4.78145Z"
          className="fill-current"
        />
      </svg>
    ),
    eye: (
      <svg
        className={className}
        width={width ? width : '24'}
        height={height ? height : '25'}
        viewBox="0 0 24 25"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M14.9341 12.5938C14.9341 10.9368 13.5969 9.59375 11.9473 9.59375C10.2976 9.59375 8.96045 10.9368 8.96045 12.5938C8.96045 14.2507 10.2976 15.5938 11.9473 15.5938C13.5969 15.5938 14.9341 14.2507 14.9341 12.5938Z"
          stroke="currentColor"
          strokeWidth="1.3"
        />
        <path
          d="M11.9473 5.59375C16.1108 5.59375 19.6772 9.60696 21.1639 11.556C21.636 12.1751 21.636 13.0124 21.1639 13.6315C19.6772 15.5805 16.1108 19.5938 11.9473 19.5938C7.78395 19.5938 4.21743 15.5805 2.73084 13.6315C2.25866 13.0124 2.25867 12.1751 2.73084 11.556C4.21743 9.60696 7.78395 5.59375 11.9473 5.59375Z"
          stroke="currentColor"
          strokeLinejoin="round"
          strokeWidth="1.3"
        />
      </svg>
    ),
    edit: (
      <svg
        className={className}
        width={width ? width : '18'}
        height={height ? height : '18'}
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8.52941 17H16.5294M14.1765 7.58824L17 4.76471L13.2353 1L10.4118 3.82353M14.1765 7.58824L4.76471 17H1V13.2353L10.4118 3.82353M14.1765 7.58824L10.4118 3.82353"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    lampGuidance: (
      <svg width={width || 30} height={height || 30} viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M3.66589 20.1654C3.66589 25.0277 5.59744 29.6908 9.0356 33.129C12.4738 36.5672 17.1369 38.4987 21.9992 38.4987C26.8615 38.4987 31.5247 36.5672 34.9629 33.129C38.401 29.6908 40.3326 25.0277 40.3326 20.1654C40.3326 15.3031 38.401 10.6399 34.9629 7.20174C31.5247 3.76357 26.8615 1.83203 21.9992 1.83203C17.1369 1.83203 12.4738 3.76357 9.0356 7.20174C5.59744 10.6399 3.66589 15.3031 3.66589 20.1654Z"
          fill="#FFF59D"
        />
        <path
          d="M33.9144 20.174C33.9144 13.1157 27.8644 7.52399 20.6227 8.34899C15.1227 8.99065 10.7227 13.3907 10.1727 18.8907C9.71438 23.1073 11.456 26.8657 14.3894 29.249C15.6727 30.349 16.4977 31.9073 16.4977 33.649V33.924H27.4977V33.8323C27.4977 32.1823 28.231 30.5323 29.5144 29.4323C32.1727 27.2323 33.9144 23.9323 33.9144 20.174Z"
          fill="#FBC02D"
        />
        <path
          d="M28.0496 18.5162L25.2996 16.6829C25.0246 16.4996 24.5662 16.4996 24.2912 16.6829L21.9996 18.1496L19.7996 16.6829C19.5246 16.4996 19.0662 16.4996 18.7912 16.6829L16.0412 18.5162C15.8579 18.6996 15.6746 18.8829 15.6746 19.1579C15.6746 19.4329 15.6746 19.7079 15.8579 19.8912L19.3412 24.1996V33.9162H21.1746V23.8329C21.1746 23.6496 21.0829 23.4662 20.9912 23.2829L17.9662 19.5246L19.3412 18.6079L21.5412 20.0746C21.8162 20.2579 22.2746 20.2579 22.5496 20.0746L24.7496 18.6079L26.1246 19.5246L23.0996 23.2829C23.0079 23.4662 22.9162 23.6496 22.9162 23.8329V33.9162H24.7496V24.1996L28.2329 19.8912C28.4162 19.7079 28.5079 19.4329 28.4162 19.1579C28.3246 18.8829 28.2329 18.6079 28.0496 18.5162Z"
          fill="#FFF59D"
        />
        <path
          d="M19.25 40.332C19.25 41.0614 19.5397 41.7608 20.0555 42.2766C20.5712 42.7923 21.2707 43.082 22 43.082C22.7293 43.082 23.4288 42.7923 23.9445 42.2766C24.4603 41.7608 24.75 41.0614 24.75 40.332C24.75 39.6027 24.4603 38.9032 23.9445 38.3875C23.4288 37.8718 22.7293 37.582 22 37.582C21.2707 37.582 20.5712 37.8718 20.0555 38.3875C19.5397 38.9032 19.25 39.6027 19.25 40.332Z"
          fill="#5C6BC0"
        />
        <path d="M23.8333 41.25H20.1667C18.15 41.25 16.5 39.6 16.5 37.5833V33H27.5V37.5833C27.5 39.6 25.85 41.25 23.8333 41.25Z" fill="#9FA8DA" />
        <path
          d="M27.5 37.5846L16.8667 39.0513C17.1417 39.693 17.6917 40.3346 18.3333 40.7013L26.95 39.5096C27.3167 38.9596 27.5 38.318 27.5 37.5846ZM16.5 35.4763V37.3096L27.5 35.7513V33.918L16.5 35.4763Z"
          fill="#5C6BC0"
        />
      </svg>
    ),
    settings: (
      <svg
        className={className}
        width={width ? width : '20'}
        height={height ? height : '20'}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8.33333 3.33333C8.33333 2.8731 8.70642 2.5 9.16667 2.5H10.8333C11.2936 2.5 11.6667 2.8731 11.6667 3.33333V3.80732C11.6667 4.16382 11.9059 4.47354 12.2352 4.61023C12.5646 4.74698 12.9481 4.69487 13.2002 4.44269L13.5355 4.10742C13.8609 3.78198 14.3886 3.78198 14.714 4.10742L15.8925 5.28593C16.218 5.61137 16.218 6.13901 15.8925 6.46445L15.5573 6.79967C15.3051 7.05186 15.253 7.43539 15.3897 7.76478C15.5264 8.09406 15.8362 8.33333 16.1927 8.33333H16.6667C17.1269 8.33333 17.5 8.70642 17.5 9.16667V10.8333C17.5 11.2936 17.1269 11.6667 16.6667 11.6667H16.1927C15.8362 11.6667 15.5265 11.9059 15.3897 12.2352C15.253 12.5646 15.3051 12.9481 15.5573 13.2002L15.8925 13.5355C16.218 13.8609 16.218 14.3886 15.8925 14.714L14.714 15.8925C14.3886 16.218 13.8609 16.218 13.5355 15.8925L13.2002 15.5573C12.9481 15.3051 12.5646 15.253 12.2352 15.3897C11.9059 15.5264 11.6667 15.8362 11.6667 16.1927V16.6667C11.6667 17.1269 11.2936 17.5 10.8333 17.5H9.16667C8.70642 17.5 8.33333 17.1269 8.33333 16.6667V16.1927C8.33333 15.8362 8.09406 15.5264 7.76478 15.3897C7.43539 15.253 7.05186 15.3051 6.79967 15.5573L6.46443 15.8925C6.13899 16.218 5.61136 16.218 5.28593 15.8925L4.10741 14.714C3.78198 14.3886 3.78198 13.8609 4.10741 13.5355L4.44269 13.2002C4.69487 12.9481 4.74698 12.5646 4.61023 12.2352C4.47354 11.9059 4.16382 11.6667 3.80732 11.6667H3.33333C2.8731 11.6667 2.5 11.2936 2.5 10.8333V9.16667C2.5 8.70642 2.8731 8.33333 3.33333 8.33333H3.80731C4.16382 8.33333 4.47354 8.09407 4.61024 7.7648C4.747 7.43542 4.69488 7.05191 4.44269 6.79972L4.10742 6.46445C3.78198 6.13901 3.78198 5.61138 4.10742 5.28594L5.28593 4.10742C5.61137 3.78199 6.13901 3.78199 6.46445 4.10742L6.79972 4.44269C7.0519 4.69488 7.43542 4.747 7.7648 4.61024C8.09407 4.47354 8.33333 4.16382 8.33333 3.8073V3.33333Z"
          stroke="currentColor"
        />
        <path
          d="M11.6673 10.0007C11.6673 10.9212 10.9212 11.6673 10.0007 11.6673C9.08015 11.6673 8.33398 10.9212 8.33398 10.0007C8.33398 9.08015 9.08015 8.33398 10.0007 8.33398C10.9212 8.33398 11.6673 9.08015 11.6673 10.0007Z"
          stroke="currentColor"
        />
      </svg>
    ),
    infoItalic: (
      <svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.5 0.5C11.6421 0.5 15 3.85787 15 8C15 12.1421 11.6421 15.5 7.5 15.5C3.35787 15.5 0 12.1421 0 8C0 3.85787 3.35787 0.5 7.5 0.5ZM9.24841 6.02459C9.02552 5.79364 8.71852 5.71252 8.39508 5.71634C7.96363 5.72148 7.50291 5.87778 7.17336 6.0313C6.38475 6.39868 5.77965 7.04923 5.27568 7.74468C5.18493 7.86987 5.13757 8.0177 5.29724 8.13294C5.4334 8.23122 5.53145 8.14328 5.61357 8.05562L5.61896 8.04984C5.62522 8.0431 5.63139 8.0364 5.63748 8.0298C5.72962 7.93002 5.81868 7.82668 5.90773 7.72326L5.93 7.6974L5.95227 7.67155C6.2197 7.3615 6.49177 7.05589 6.85184 6.84855C7.0659 6.72528 7.22013 6.91566 7.18709 7.12951C7.16728 7.25773 7.09986 7.37852 7.05271 7.50251C6.88521 7.94284 6.71523 8.38225 6.54598 8.82195C6.36051 9.30398 6.17562 9.7862 5.99577 10.2704L5.98015 10.3124L5.96454 10.3544C5.80587 10.7813 5.64955 11.205 5.54615 11.6505C5.46353 12.0066 5.35231 12.4388 5.51211 12.7887C5.60415 12.9903 5.79938 13.1288 6.01654 13.1603C6.31273 13.2033 6.63299 13.2082 6.92281 13.1423C7.07294 13.1082 7.22062 13.0636 7.36477 13.0094C7.80731 12.843 8.21028 12.5864 8.57321 12.2848C8.94243 11.9771 9.26789 11.6131 9.56466 11.2356C9.66005 11.1143 9.77563 10.9801 9.80812 10.8247C9.83866 10.679 9.70855 10.4708 9.53995 10.5554C9.45093 10.6001 9.38426 10.7106 9.3194 10.7841C9.23799 10.8763 9.15499 10.9671 9.07107 11.0569C8.9032 11.2365 8.73112 11.4121 8.5583 11.5868C8.45252 11.6938 8.32104 11.7842 8.18595 11.8506C8.01727 11.9335 7.88294 11.8416 7.9012 11.6548C7.91795 11.4833 7.95991 11.3099 8.01874 11.1475C8.25551 10.4938 8.49679 9.8418 8.7362 9.18906C8.88586 8.78114 9.03477 8.37296 9.1815 7.96394C9.31864 7.58169 9.43552 7.20251 9.48489 6.79753C9.51893 6.51855 9.44661 6.22998 9.24841 6.02459ZM9.74623 2.88788C9.03262 2.61024 8.18191 3.08407 8.03862 3.83911C7.9351 4.3843 8.14281 4.86434 8.56985 5.06688C9.39614 5.45876 10.3846 4.83287 10.3846 3.91779C10.3847 3.41 10.1611 3.04931 9.74623 2.88788Z"
          fill="#6B7280"
        />
      </svg>
    ),
    hourglass: (
      <svg className="mx-auto" width="80" height="80" viewBox="0 0 66 66" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="5" y="5" width="56" height="56" rx="28" fill="#F0E7FF" />
        <rect x="5" y="5" width="56" height="56" rx="28" stroke="#F8F4FF" strokeWidth="10" />
        <path
          d="M36.5729 35.918H29.4223C28.1688 36.9946 27.6641 38.6951 27.6641 39.8006V42.5846H38.3307V39.8006C38.3307 38.6951 37.826 36.9946 36.5729 35.918Z"
          fill="#C0A2FF"
        />
        <path
          d="M41.4436 22.584C41.4436 23.0424 41.0434 23.4173 40.5547 23.4173H25.4436C24.9549 23.4173 24.5547 23.0424 24.5547 22.584V22.1673C24.5547 21.7089 24.9549 21.334 25.4436 21.334H40.5547C41.0434 21.334 41.4436 21.7089 41.4436 22.1673V22.584Z"
          fill="#783EF5"
        />
        <path
          d="M41.4436 43.834C41.4436 44.2922 41.0434 44.6673 40.5547 44.6673H25.4436C24.9549 44.6673 24.5547 44.2922 24.5547 43.834V43.4173C24.5547 42.9592 24.9549 42.584 25.4436 42.584H40.5547C41.0434 42.584 41.4436 42.9592 41.4436 43.4173V43.834Z"
          fill="#783EF5"
        />
        <path
          d="M30.7765 32.168C27.5443 31.3725 26.332 28.1907 26.332 26.202C26.332 24.2135 26.332 23.418 26.332 23.418"
          stroke="#524D4D"
          strokeWidth="0.5"
          stroke-miterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M26.332 42.584C26.332 42.584 26.332 41.7885 26.332 39.8C26.332 37.8114 27.5443 34.6295 30.7765 33.834"
          stroke="#524D4D"
          strokeWidth="0.5"
          stroke-miterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M30.7773 33.8346C31.2218 33.7215 31.6662 33.4619 31.6662 33.0013C31.6662 32.5411 31.2218 32.2811 30.7773 32.168"
          stroke="#524D4D"
          strokeWidth="0.5"
          stroke-miterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M35.2227 32.168C38.4549 31.3725 39.6671 28.1907 39.6671 26.202C39.6671 24.2135 39.6671 23.418 39.6671 23.418"
          stroke="#524D4D"
          strokeWidth="0.5"
          stroke-miterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M39.6671 42.584C39.6671 42.584 39.6671 41.7885 39.6671 39.8C39.6671 37.8114 38.4549 34.6295 35.2227 33.834"
          stroke="#524D4D"
          strokeWidth="0.5"
          stroke-miterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M35.2209 33.8346C34.7765 33.7215 34.332 33.4619 34.332 33.0013C34.332 32.5411 34.7765 32.2811 35.2209 32.168"
          stroke="#524D4D"
          strokeWidth="0.5"
          stroke-miterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M41.4436 22.584C41.4436 23.0424 41.0434 23.4173 40.5547 23.4173H25.4436C24.9549 23.4173 24.5547 23.0424 24.5547 22.584V22.1673C24.5547 21.7089 24.9549 21.334 25.4436 21.334H40.5547C41.0434 21.334 41.4436 21.7089 41.4436 22.1673V22.584Z"
          stroke="#524D4D"
          strokeWidth="0.5"
          stroke-miterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M41.4436 43.834C41.4436 44.2922 41.0434 44.6673 40.5547 44.6673H25.4436C24.9549 44.6673 24.5547 44.2922 24.5547 43.834V43.4173C24.5547 42.9592 24.9549 42.584 25.4436 42.584H40.5547C41.0434 42.584 41.4436 42.9592 41.4436 43.4173V43.834Z"
          stroke="#524D4D"
          strokeWidth="0.5"
          stroke-miterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M29.8867 35.918H36.1089" stroke="#524D4D" strokeWidth="0.5" stroke-miterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
    ),
    customize: (
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M0.5 5.5H15.5M3.83333 0.5V2.16667M12.1667 0.5V2.16667M3 8H4.66667M7.16667 8H8.83333M11.3333 8H13M3 10.5H4.66667M7.16667 10.5H8.83333M11.3333 10.5H13M3 13H4.66667M7.16667 13H8.83333M11.3333 13H13M3.16667 15.5H12.8333C13.7667 15.5 14.2335 15.5 14.59 15.3183C14.9036 15.1586 15.1586 14.9036 15.3183 14.59C15.5 14.2335 15.5 13.7667 15.5 12.8333V4.83333C15.5 3.89991 15.5 3.4332 15.3183 3.07668C15.1586 2.76307 14.9036 2.50811 14.59 2.34832C14.2335 2.16667 13.7667 2.16667 12.8333 2.16667H3.16667C2.23325 2.16667 1.76653 2.16667 1.41002 2.34832C1.09641 2.50811 0.841442 2.76307 0.681658 3.07668C0.5 3.4332 0.5 3.89991 0.5 4.83333V12.8333C0.5 13.7667 0.5 14.2335 0.681658 14.59C0.841442 14.9036 1.09641 15.1586 1.41002 15.3183C1.76653 15.5 2.23324 15.5 3.16667 15.5Z"
          stroke="#3D3F44"
          strokeLinecap="round"
        />
      </svg>
    ),
    clock: (
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M8 4.11111V8L9.94444 6.83333M15 8C15 11.866 11.866 15 8 15C4.13401 15 1 11.866 1 8C1 4.13401 4.13401 1 8 1C11.866 1 15 4.13401 15 8Z"
          stroke="#6B7280"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    clockTwo: (
      <svg width={width ? width : '17'} height={height ? height : '18'} viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M6.99844 13.0984C5.89086 13.0984 4.80816 12.77 3.88725 12.1547C2.96633 11.5393 2.24857 10.6647 1.82471 9.64147C1.40086 8.6182 1.28997 7.49223 1.50604 6.40593C1.72212 5.31964 2.25547 4.32182 3.03864 3.53864C3.82182 2.75547 4.81964 2.22212 5.90593 2.00604C6.99223 1.78997 8.1182 1.90086 9.14147 2.32471C10.1647 2.74857 11.0393 3.46633 11.6547 4.38725C12.27 5.30816 12.5984 6.39086 12.5984 7.49844C12.5984 8.98365 12.0084 10.408 10.9582 11.4582C9.90803 12.5084 8.48365 13.0984 6.99844 13.0984ZM6.99844 2.83177C6.07546 2.83177 5.17321 3.10547 4.40578 3.61825C3.63835 4.13103 3.04021 4.85986 2.687 5.71258C2.33379 6.56531 2.24138 7.50362 2.42144 8.40886C2.60151 9.31411 3.04596 10.1456 3.69861 10.7983C4.35125 11.4509 5.18277 11.8954 6.08802 12.0754C6.99326 12.2555 7.93157 12.1631 8.7843 11.8099C9.63702 11.4567 10.3659 10.8585 10.8786 10.0911C11.3914 9.32367 11.6651 8.42142 11.6651 7.49844C11.6651 6.26076 11.1734 5.07378 10.2983 4.19861C9.4231 3.32344 8.23612 2.83177 6.99844 2.83177Z"
          fill="#667085"
        />
        <path
          d="M9.33125 10.0659C9.21559 10.0656 9.10417 10.0223 9.01858 9.94455L6.68525 7.84455C6.63691 7.80092 6.59823 7.74766 6.57171 7.68818C6.54519 7.62871 6.5314 7.56434 6.53125 7.49922V5.16589C6.53125 5.04212 6.58042 4.92342 6.66793 4.8359C6.75545 4.74839 6.87415 4.69922 6.99792 4.69922C7.12168 4.69922 7.24038 4.74839 7.3279 4.8359C7.41542 4.92342 7.46458 5.04212 7.46458 5.16589V7.28922L9.64392 9.24922C9.71527 9.31214 9.76569 9.39537 9.78842 9.48775C9.81115 9.58013 9.8051 9.67725 9.77108 9.76609C9.73707 9.85494 9.67671 9.93126 9.5981 9.98485C9.51949 10.0384 9.42638 10.0667 9.33125 10.0659Z"
          fill="#667085"
        />
      </svg>
    ),
    questionsCircle: (
      <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.9987 1.33203C4.59295 1.33203 1.83203 4.09294 1.83203 7.4987C1.83203 10.9044 4.59295 13.6654 7.9987 13.6654C11.4044 13.6654 14.1654 10.9044 14.1654 7.4987C14.1654 4.09294 11.4044 1.33203 7.9987 1.33203ZM0.832031 7.4987C0.832031 3.54066 4.04066 0.332031 7.9987 0.332031C11.9568 0.332031 15.1654 3.54066 15.1654 7.4987C15.1654 11.4568 11.9568 14.6654 7.9987 14.6654C4.04066 14.6654 0.832031 11.4568 0.832031 7.4987ZM7.9987 4.66536C7.5845 4.66536 7.2487 5.00115 7.2487 5.41536C7.2487 5.6915 7.02483 5.91536 6.7487 5.91536C6.47256 5.91536 6.2487 5.6915 6.2487 5.41536C6.2487 4.44886 7.03223 3.66536 7.9987 3.66536C8.96517 3.66536 9.7487 4.44886 9.7487 5.41536C9.7487 6.05759 9.40243 6.61843 8.8889 6.9223C8.7577 6.9999 8.64943 7.08463 8.5789 7.16776C8.51037 7.24843 8.4987 7.3011 8.4987 7.33203V8.16536C8.4987 8.4415 8.27483 8.66537 7.9987 8.66537C7.72257 8.66537 7.4987 8.4415 7.4987 8.16536V7.33203C7.4987 6.99476 7.64637 6.7211 7.81643 6.5207C7.98443 6.3227 8.19397 6.1715 8.3797 6.06162C8.60163 5.93029 8.7487 5.68973 8.7487 5.41536C8.7487 5.00115 8.4129 4.66536 7.9987 4.66536ZM7.9987 10.832C8.3669 10.832 8.66537 10.5336 8.66537 10.1654C8.66537 9.79716 8.3669 9.4987 7.9987 9.4987C7.6305 9.4987 7.33203 9.79716 7.33203 10.1654C7.33203 10.5336 7.6305 10.832 7.9987 10.832Z"
          fill="#6B7280"
        />
      </svg>
    ),
    book: (
      <svg width="16" height="13" viewBox="0 0 16 13" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M8 5.3V12.5M8 5.3C8 3.61984 8 2.77977 7.673 2.13803C7.38538 1.57354 6.92645 1.1146 6.36197 0.826977C5.72023 0.5 4.88016 0.5 3.2 0.5H2.45C2.02996 0.5 1.81994 0.5 1.65951 0.581743C1.51839 0.653653 1.40365 0.768388 1.33174 0.909508C1.25 1.06994 1.25 1.27996 1.25 1.7V9.8C1.25 10.2201 1.25 10.4301 1.33174 10.5905C1.40365 10.7317 1.51839 10.8463 1.65951 10.9183C1.81994 11 2.02996 11 2.45 11H4.66001C5.06515 11 5.26772 11 5.46361 11.035C5.63746 11.0659 5.80708 11.1172 5.96893 11.188C6.15128 11.2675 6.31983 11.3799 6.6569 11.6047L8 12.5M8 5.3C8 3.61984 8 2.77977 8.327 2.13803C8.61462 1.57354 9.07355 1.1146 9.638 0.826977C10.2798 0.5 11.1198 0.5 12.8 0.5H13.55C13.9701 0.5 14.1801 0.5 14.3405 0.581743C14.4817 0.653653 14.5963 0.768388 14.6683 0.909508C14.75 1.06994 14.75 1.27996 14.75 1.7V9.8C14.75 10.2201 14.75 10.4301 14.6683 10.5905C14.5963 10.7317 14.4817 10.8463 14.3405 10.9183C14.1801 11 13.9701 11 13.55 11H11.34C10.9348 11 10.7322 11 10.5364 11.035C10.3625 11.0659 10.1929 11.1172 10.0311 11.188C9.84875 11.2675 9.68015 11.3799 9.3431 11.6047L8 12.5"
          stroke="#6B7280"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    arrowUp: (
      <svg width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.8246 6.38035C12.0585 6.13741 12.0585 5.74406 11.8246 5.50174L6.86647 0.363942C6.39818 -0.121314 5.63848 -0.121314 5.17019 0.363942L0.175476 5.53894C-0.0559712 5.77939 -0.0589703 6.16785 0.16948 6.4114C0.402726 6.66056 0.78707 6.66294 1.02391 6.41814L5.59411 1.68172C5.82855 1.43878 6.20811 1.43878 6.44255 1.68172L10.9768 6.38035C11.2106 6.62329 11.5908 6.62329 11.8246 6.38035Z"
          fill="#98A2B3"
        />
      </svg>
    ),
    arrowDown: (
      <svg width={width ? width : '13'} height={height ? height : '8'} viewBox="0 0 13 8" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0.675385 0.918653C0.441538 1.16158 0.441538 1.55496 0.675385 1.79727L5.63352 6.93477C6.10182 7.42063 6.86151 7.42063 7.3298 6.93477L12.3245 1.76006C12.556 1.51961 12.559 1.13115 12.3305 0.887599C12.0973 0.639079 11.7129 0.636066 11.4761 0.88086L6.90589 5.61699C6.67144 5.85992 6.29189 5.85992 6.05744 5.61699L1.52323 0.918653C1.28938 0.675719 0.909831 0.675719 0.675385 0.918653Z"
          fill="#98A2B3"
        />
      </svg>
    ),
    info: (
      <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.3333 7.5C13.3333 10.7216 10.7216 13.3333 7.5 13.3333C4.27834 13.3333 1.66667 10.7216 1.66667 7.5C1.66667 4.27834 4.27834 1.66667 7.5 1.66667C10.7216 1.66667 13.3333 4.27834 13.3333 7.5ZM14.5 7.5C14.5 11.366 11.366 14.5 7.5 14.5C3.63401 14.5 0.5 11.366 0.5 7.5C0.5 3.63401 3.63401 0.5 7.5 0.5C11.366 0.5 14.5 3.63401 14.5 7.5ZM6.91667 8.66667V4.58333H8.08333V8.66667H6.91667ZM6.91667 10.4167V9.25H8.08333V10.4167H6.91667Z"
          fill="#C72716"
        />
      </svg>
    ),
    arrowRight: (
      <svg
        className={className}
        stroke={stroke ? stroke : '#C72716'}
        width="5"
        height="9"
        viewBox="0 0 5 9"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M1.16675 1.16797L4.50008 4.5013L1.16675 7.83464" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
    ),
    warning: (
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clipPath="url(#clip0_14456_4004)">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M0.9375 6C0.9375 3.20406 3.20406 0.9375 6 0.9375H12C14.7959 0.9375 17.0625 3.20406 17.0625 6V12C17.0625 14.7959 14.7959 17.0625 12 17.0625H6C3.20406 17.0625 0.9375 14.7959 0.9375 12V6ZM6 2.0625C3.82538 2.0625 2.0625 3.82538 2.0625 6V12C2.0625 14.1746 3.82538 15.9375 6 15.9375H12C14.1746 15.9375 15.9375 14.1746 15.9375 12V6C15.9375 3.82538 14.1746 2.0625 12 2.0625H6Z"
            fill="#667085"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M9 4.6875C9.31065 4.6875 9.5625 4.93934 9.5625 5.25V10.5785C9.5625 10.8892 9.31065 11.141 9 11.141C8.68935 11.141 8.4375 10.8892 8.4375 10.5785V5.25C8.4375 4.93934 8.68935 4.6875 9 4.6875Z"
            fill="#667085"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M8.25 12.75C8.25 12.3358 8.58578 12 9 12H9.0075C9.42173 12 9.7575 12.3358 9.7575 12.75C9.7575 13.1642 9.42173 13.5 9.0075 13.5H9C8.58578 13.5 8.25 13.1642 8.25 12.75Z"
            fill="#667085"
          />
        </g>
        <defs>
          <clipPath id="clip0_14456_4004">
            <rect width="18" height="18" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    warningLarge: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        width={width ? width : '23'}
        height={height ? height : '19'}
        fill="none"
        viewBox="0 0 256 256"
      >
        <path
          className="fill-current"
          d="M235.07 189.09L147.61 37.22a22.75 22.75 0 0 0-39.22 0L20.93 189.09a21.53 21.53 0 0 0 0 21.72A22.35 22.35 0 0 0 40.55 222h174.9a22.35 22.35 0 0 0 19.6-11.19a21.53 21.53 0 0 0 .02-21.72m-10.41 15.71a10.46 10.46 0 0 1-9.21 5.2H40.55a10.46 10.46 0 0 1-9.21-5.2a9.51 9.51 0 0 1 0-9.72l87.45-151.87a10.75 10.75 0 0 1 18.42 0l87.46 151.87a9.51 9.51 0 0 1-.01 9.72M122 144v-40a6 6 0 0 1 12 0v40a6 6 0 0 1-12 0m16 36a10 10 0 1 1-10-10a10 10 0 0 1 10 10"
        />
      </svg>
    ),
    en: (
      <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M20.3125 3H4.6875C3.82456 3 3.125 3.67157 3.125 4.5V19.5C3.125 20.3284 3.82456 21 4.6875 21H20.3125C21.1754 21 21.875 20.3284 21.875 19.5V4.5C21.875 3.67157 21.1754 3 20.3125 3Z"
          stroke="#667085"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10.9382 8.5H6.77148V15.5H10.6777M6.77148 12H10.6777M13.5423 9.5V15.5V12.25C13.5423 11.6533 13.7892 11.081 14.2288 10.659C14.6683 10.2371 15.2645 10 15.8861 10C16.5077 10 17.1038 10.2371 17.5433 10.659C17.9829 11.081 18.2298 11.6533 18.2298 12.25V15.5"
          stroke="#667085"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    discount: (
      <svg width="30" height="31" viewBox="0 0 30 31" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M12.3461 5.80505C12.6967 5.50631 12.8719 5.35694 13.0401 5.24419C14.2246 4.44984 15.7717 4.44987 16.9562 5.24419C17.1243 5.35694 17.2996 5.50631 17.6502 5.80505C17.7898 5.92406 17.8596 5.98357 17.9308 6.03823C18.4208 6.41435 19.0012 6.65477 19.6136 6.73523C19.7026 6.74693 19.794 6.75424 19.9769 6.76882C20.436 6.80548 20.6656 6.82378 20.8642 6.86294C22.2635 7.13885 23.3574 8.2328 23.6332 9.63204C23.6726 9.83069 23.6909 10.0602 23.7274 10.5194C23.742 10.7023 23.7492 10.7937 23.761 10.8827C23.8416 11.4951 24.0818 12.0755 24.458 12.5655C24.5126 12.6366 24.5721 12.7065 24.6912 12.8461C24.99 13.1967 25.1393 13.3719 25.252 13.5401C26.0463 14.7246 26.0463 16.2717 25.252 17.4562C25.1393 17.6243 24.99 17.7996 24.6912 18.1502C24.5721 18.2898 24.5126 18.3596 24.458 18.4308C24.0818 18.9208 23.8416 19.5012 23.761 20.1136C23.7492 20.2026 23.742 20.294 23.7274 20.4769C23.6909 20.936 23.6726 21.1656 23.6332 21.3642C23.3574 22.7635 22.2635 23.8574 20.8642 24.1332C20.6656 24.1726 20.436 24.1909 19.9769 24.2274C19.794 24.242 19.7026 24.2492 19.6136 24.261C19.0012 24.3416 18.4208 24.5818 17.9308 24.958C17.8596 25.0126 17.7898 25.0721 17.6502 25.1912C17.2996 25.49 17.1243 25.6393 16.9562 25.752C15.7717 26.5463 14.2246 26.5463 13.0401 25.752C12.8719 25.6393 12.6967 25.49 12.3461 25.1912C12.2065 25.0721 12.1366 25.0126 12.0655 24.958C11.5755 24.5818 10.9951 24.3416 10.3827 24.261C10.2937 24.2492 10.2023 24.242 10.0194 24.2274C9.56024 24.1909 9.33069 24.1726 9.13204 24.1332C7.7328 23.8574 6.63885 22.7635 6.36294 21.3642C6.32378 21.1656 6.30548 20.936 6.26882 20.4769C6.25424 20.294 6.24693 20.2026 6.23523 20.1136C6.15477 19.5012 5.91435 18.9208 5.53823 18.4308C5.48357 18.3596 5.42406 18.2898 5.30505 18.1502C5.00631 17.7996 4.85694 17.6243 4.74419 17.4562C3.94987 16.2717 3.94984 14.7246 4.74419 13.5401C4.85694 13.3719 5.00631 13.1967 5.30505 12.8461C5.42406 12.7065 5.48357 12.6366 5.53823 12.5655C5.91435 12.0755 6.15477 11.4951 6.23523 10.8827C6.24693 10.7937 6.25424 10.7023 6.26882 10.5194C6.30548 10.0602 6.32378 9.83069 6.36294 9.63204C6.63885 8.2328 7.7328 7.13885 9.13204 6.86294C9.33069 6.82378 9.56024 6.80548 10.0194 6.76882C10.2023 6.75424 10.2937 6.74693 10.3827 6.73523C10.9951 6.65477 11.5755 6.41435 12.0655 6.03823C12.1366 5.98357 12.2065 5.92406 12.3461 5.80505Z"
          stroke="black"
        />
        <path d="M12.1875 18.3125L17.8125 12.6875" stroke="black" strokeLinecap="round" />
        <path
          d="M12.6562 14.3281C13.3035 14.3281 13.8281 13.8035 13.8281 13.1562C13.8281 12.509 13.3035 11.9844 12.6562 11.9844C12.009 11.9844 11.4844 12.509 11.4844 13.1562C11.4844 13.8035 12.009 14.3281 12.6562 14.3281Z"
          fill="black"
        />
        <path
          d="M17.3438 19.0156C17.991 19.0156 18.5156 18.491 18.5156 17.8438C18.5156 17.1965 17.991 16.6719 17.3438 16.6719C16.6965 16.6719 16.1719 17.1965 16.1719 17.8438C16.1719 18.491 16.6965 19.0156 17.3438 19.0156Z"
          fill="black"
        />
      </svg>
    ),
    copy: (
      <svg
        className={className}
        width={width ? width : '22'}
        height={height ? height : '22'}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M4.16406 12.5013C3.38749 12.5013 2.99921 12.5013 2.69292 12.3744C2.28454 12.2053 1.96009 11.8808 1.79093 11.4724C1.66406 11.1662 1.66406 10.7779 1.66406 10.0013V4.33464C1.66406 3.40121 1.66406 2.9345 1.84572 2.57798C2.00551 2.26438 2.26047 2.00941 2.57408 1.84962C2.9306 1.66797 3.39731 1.66797 4.33073 1.66797H9.9974C10.774 1.66797 11.1622 1.66797 11.4685 1.79484C11.8769 1.96399 12.2014 2.28845 12.3705 2.69683C12.4974 3.00311 12.4974 3.3914 12.4974 4.16797M10.1641 18.3346H15.6641C16.5975 18.3346 17.0642 18.3346 17.4207 18.153C17.7343 17.9932 17.9893 17.7382 18.1491 17.4246C18.3307 17.0681 18.3307 16.6014 18.3307 15.668V10.168C18.3307 9.23455 18.3307 8.76784 18.1491 8.41132C17.9893 8.09771 17.7343 7.84275 17.4207 7.68296C17.0642 7.5013 16.5975 7.5013 15.6641 7.5013H10.1641C9.23064 7.5013 8.76393 7.5013 8.40741 7.68296C8.09381 7.84275 7.83884 8.09771 7.67905 8.41132C7.4974 8.76784 7.4974 9.23455 7.4974 10.168V15.668C7.4974 16.6014 7.4974 17.0681 7.67905 17.4246C7.83884 17.7382 8.09381 17.9932 8.40741 18.153C8.76393 18.3346 9.23064 18.3346 10.1641 18.3346Z"
          stroke={stroke ? stroke : '#535862'}
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    download: (
      <svg
        className={className}
        width={width ? width : '22'}
        height={height ? height : '22'}
        viewBox="0 0 20 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.66406 13.1667L9.9974 16.5M9.9974 16.5L13.3307 13.1667M9.9974 16.5V9M16.6641 12.9524C17.682 12.1117 18.3307 10.8399 18.3307 9.41667C18.3307 6.88536 16.2787 4.83333 13.7474 4.83333C13.5653 4.83333 13.3949 4.73833 13.3025 4.58145C12.2158 2.73736 10.2094 1.5 7.91406 1.5C4.46228 1.5 1.66406 4.29822 1.66406 7.75C1.66406 9.47175 2.36027 11.0309 3.48652 12.1613"
          stroke={stroke ? stroke : '#535862'}
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    trash: (
      <svg
        className={className}
        width={width ? width : '22'}
        height={height ? height : '22'}
        viewBox="0 0 18 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12.3333 5.0013V4.33464C12.3333 3.40121 12.3333 2.9345 12.1517 2.57798C11.9919 2.26438 11.7369 2.00941 11.4233 1.84962C11.0668 1.66797 10.6001 1.66797 9.66667 1.66797H8.33333C7.39991 1.66797 6.9332 1.66797 6.57668 1.84962C6.26308 2.00941 6.00811 2.26438 5.84832 2.57798C5.66667 2.9345 5.66667 3.40121 5.66667 4.33464V5.0013M7.33333 9.58464V13.7513M10.6667 9.58464V13.7513M1.5 5.0013H16.5M14.8333 5.0013V14.3346C14.8333 15.7348 14.8333 16.4348 14.5608 16.9696C14.3212 17.44 13.9387 17.8225 13.4683 18.0622C12.9335 18.3346 12.2335 18.3346 10.8333 18.3346H7.16667C5.76654 18.3346 5.06647 18.3346 4.53169 18.0622C4.06129 17.8225 3.67883 17.44 3.43915 16.9696C3.16667 16.4348 3.16667 15.7348 3.16667 14.3346V5.0013"
          stroke={stroke ? stroke : '#535862'}
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    organization: (
      <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clipPath="url(#clip0_15439_14724)">
          <path
            d="M4.42189 11.6412C5.08343 11.6443 5.40304 12.4505 5.2565 13.0251C5.11997 13.5608 3.94227 13.5732 3.49227 13.467L3.77958 13.6878C3.64535 13.4185 3.57766 12.8497 3.62343 12.5532C3.67766 12.1993 4.07997 12.062 4.38881 11.9516C4.96727 11.7451 4.71804 10.8158 4.13304 11.0247C3.7192 11.1724 3.26227 11.3616 2.95381 11.6885C2.60766 12.0555 2.6465 12.5666 2.6965 13.0312C2.7392 13.4285 2.73497 14.1101 3.12189 14.3455C3.54381 14.6028 4.33381 14.4351 4.79304 14.3751C5.81227 14.2424 6.32381 13.5497 6.22381 12.5316C6.12766 11.5439 5.47381 10.6847 4.42189 10.6797C3.80304 10.6766 3.80266 11.6382 4.42189 11.6412ZM16.1907 11.9751C16.2619 11.7374 17.16 11.6935 17.3134 11.8493C17.3819 11.9185 17.4442 12.2532 17.4627 12.3462C17.5134 12.5962 17.5319 12.9016 17.3565 13.1124C17.0896 13.4332 16.6877 13.3374 16.3846 13.1362C16.0346 12.9039 16.0846 12.5424 16.1134 12.1632C16.1607 11.5462 15.1988 11.5493 15.1519 12.1632C15.0815 13.0855 15.3596 13.7624 16.2396 14.1412C17.0242 14.4793 17.9661 14.1651 18.3119 13.3612C18.5096 12.9024 18.4773 12.397 18.3477 11.9232C18.2446 11.5485 18.1257 11.0593 17.7257 10.9047C16.968 10.612 15.5307 10.8251 15.2634 11.7193C15.0861 12.3135 16.0134 12.5674 16.1907 11.9751Z"
            fill="#6B7280"
          />
          <path
            d="M17.1441 10.7788C16.9757 9.87535 16.5311 8.97112 16.0657 8.18343C15.6241 7.43651 14.8434 7.01804 14.0887 6.65035C13.5349 6.38035 13.0464 7.20958 13.6034 7.48074C14.3164 7.82766 15.0134 8.17535 15.3591 8.92804C15.6837 9.63381 16.0726 10.2607 16.2168 11.0346C16.3299 11.6423 17.2572 11.3846 17.1441 10.7788ZM4.39798 11.6211L5.13106 9.92689C5.22721 9.70497 5.30144 9.45535 5.42798 9.24804C5.61413 8.94304 5.84452 8.81651 6.09798 8.57574C6.32375 8.36151 6.39952 8.08266 6.59644 7.86227C6.90798 7.51381 7.50567 7.44189 7.86106 7.10997C8.31413 6.68689 7.63336 6.00804 7.18106 6.42997C6.83298 6.75535 6.30567 6.75997 5.96375 7.10881C5.64413 7.43535 5.44452 7.80381 5.08683 8.10497C4.29567 8.77189 3.9649 10.218 3.5676 11.1357C3.32452 11.698 4.15298 12.1884 4.39836 11.6211H4.39798Z"
            fill="#6B7280"
          />
          <path
            d="M7.58957 8.77631C8.78304 8.8917 9.97881 8.96977 11.178 8.99016C11.855 9.0017 13.0446 9.22477 13.6296 8.78593C14.1592 8.389 14.0969 7.48054 14.1134 6.90631C14.1353 6.14939 14.1357 5.33516 13.6003 4.74093C12.5065 3.529 9.93496 4.054 8.56842 4.32439C6.84534 4.66554 6.92804 7.02708 7.02534 8.3717C7.06957 8.98516 8.0315 8.99016 7.98688 8.3717C7.95073 7.87016 7.90496 7.35977 7.94842 6.85708C8.04457 5.74631 8.43573 5.39208 9.52611 5.1667C10.7 4.92439 12.8023 4.57477 13.1477 6.1117C13.2119 6.39708 13.2634 7.75708 13.0703 7.95093C12.8053 8.21631 11.5365 8.03477 11.178 8.02862C9.97952 8.00498 8.78242 7.93364 7.58957 7.81477C6.97419 7.75516 6.97804 8.71708 7.58957 8.77631ZM12.2792 2.739C12.4577 2.24939 12.708 1.854 12.4984 1.32708C12.3369 0.921312 11.9788 0.65862 11.5857 0.49862C10.6665 0.124389 9.65881 0.074389 9.06804 0.990927C8.57188 1.76016 8.7315 2.86862 9.56727 3.33516C10.3165 3.75323 11.465 3.749 12.1273 3.19785C12.603 2.8017 11.9196 2.12477 11.4473 2.51785C11.165 2.75247 10.5657 2.67554 10.2357 2.56593C9.7338 2.40054 9.60111 1.89093 9.91534 1.46016C10.2077 1.05862 10.7088 1.19977 11.1146 1.34708C11.3153 1.42016 11.6277 1.54708 11.6238 1.81862C11.6211 2.004 11.4184 2.30093 11.3519 2.48323C11.14 3.06554 12.0688 3.31708 12.2792 2.739ZM14.3303 18.6663C14.4053 18.0909 14.4761 17.5144 14.5561 16.9394C14.7077 15.8525 15.5127 15.7486 16.4553 15.7044C17.2369 15.6675 18.4927 15.5052 18.9692 16.3632C19.1942 16.7678 19.1219 17.4405 19.1557 17.8921C19.2134 18.674 19.2365 18.6975 18.4557 18.6229C18.0669 18.5855 17.7557 18.5132 17.3592 18.5548C16.6723 18.6267 16.1277 18.7917 15.413 18.694C14.803 18.6105 14.1957 18.4605 13.5915 18.3432C12.9873 18.2255 12.7284 19.1521 13.3361 19.2702C14.4153 19.4802 15.57 19.8402 16.6792 19.6717C17.3561 19.5686 17.9292 19.5348 18.6196 19.6355C19.0103 19.6929 19.4327 19.8075 19.8215 19.6863C19.9769 19.6378 20.1203 19.5159 20.1573 19.3505C20.363 18.4302 20.2003 16.7109 19.7826 15.8217C19.3653 14.9344 18.5473 14.7732 17.6442 14.7371C16.6765 14.6978 14.7961 14.5002 14.0646 15.3194C13.3877 16.0771 13.4896 17.7409 13.3684 18.6663C13.2888 19.2778 14.2515 19.2721 14.3303 18.6663ZM7.69996 18.0182C7.84188 17.1459 7.80188 16.204 7.29611 15.439C6.87919 14.8082 6.35496 14.7998 5.64534 14.7855C3.83957 14.7494 1.26573 14.1679 0.889959 16.5732C0.789959 17.2159 0.596882 18.5355 0.992652 19.1148C1.4015 19.7128 2.54381 19.7594 3.18496 19.7548C4.26457 19.7471 5.52727 19.6855 6.59265 19.5194C7.32073 19.4055 7.71265 18.9952 7.78304 18.2763C7.84342 17.6609 6.8815 17.6648 6.8215 18.2763C6.78111 18.6898 4.17727 18.7328 3.73304 18.7667C2.57381 18.8559 1.73842 18.8759 1.79804 17.5194C1.84034 16.5455 1.92227 15.9678 2.9415 15.7079C3.45419 15.5771 4.21265 15.7263 4.74419 15.734C5.12496 15.7398 5.76342 15.614 6.09611 15.7536C6.86073 16.0752 6.89188 17.0309 6.77304 17.7628C6.67457 18.3678 7.60111 18.6282 7.69996 18.0182Z"
            fill="#6B7280"
          />
          <path
            d="M7.09583 18.6376C9.14276 19.2364 11.2574 19.4226 13.3024 18.723C13.8847 18.5237 13.6347 17.5945 13.047 17.7957C11.1516 18.4441 9.24583 18.2649 7.35122 17.7103C6.75622 17.536 6.50199 18.4641 7.09583 18.6372V18.6376ZM14.1808 12.893C14.1662 10.7349 12.4885 9.59143 10.4601 9.55528C8.30776 9.51682 6.67929 11.363 6.84852 13.5322C7.01776 15.691 8.85314 16.5368 10.7716 16.7676C12.6901 16.9984 14.3247 14.6976 14.1116 12.903C14.0397 12.296 13.077 12.2887 13.1501 12.903C13.2778 13.9764 12.3978 15.7095 11.177 15.8553C9.95891 16.0007 8.36429 15.218 7.95776 14.073C7.38852 12.4691 8.53122 10.5614 10.2589 10.5114C11.8066 10.4672 13.2078 11.1807 13.2193 12.893C13.2235 13.5114 14.1851 13.513 14.1808 12.893Z"
            fill="#6B7280"
          />
          <path
            d="M11.9977 12.5771C12.3558 11.0371 10.3623 10.2486 9.49041 11.5371C9.10964 12.0998 9.21887 12.7628 9.6881 13.2409C9.95118 13.509 10.2843 13.6528 10.602 13.8409C10.7481 13.914 10.8827 14.004 11.0077 14.1101C11.0716 14.0567 11.0512 14.0544 10.9462 14.1025C10.8654 14.164 10.1439 14.259 10.1685 14.0563C10.242 13.4432 9.28003 13.4486 9.20695 14.0563C9.05234 15.3432 11.2608 15.4171 11.8581 14.6363C12.2485 14.1263 11.8781 13.5436 11.4343 13.2244C11.1585 13.0255 10.8139 12.9205 10.5485 12.7121C10.3004 12.5171 10.06 12.2905 10.3723 11.9725C10.6385 11.7005 11.18 11.8494 11.0704 12.3213C10.9304 12.9236 11.8577 13.1805 11.9977 12.5771Z"
            fill="#6B7280"
          />
          <path
            d="M10.0312 15.0763V15.2048C10.0312 15.8232 10.9928 15.824 10.9928 15.2048V15.0763C10.9928 14.4579 10.0312 14.4567 10.0312 15.0763ZM10.512 11.4225C11.1312 11.4225 11.132 10.4609 10.512 10.4609C9.89356 10.4609 9.89279 11.4225 10.512 11.4225Z"
            fill="#6B7280"
          />
        </g>
        <defs>
          <clipPath id="clip0_15439_14724">
            <rect width="20" height="20" fill="white" transform="translate(0.5)" />
          </clipPath>
        </defs>
      </svg>
    ),
    cloudArrowDown: (
      <svg width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M6.66797 13.1667L10.0013 16.5M10.0013 16.5L13.3346 13.1667M10.0013 16.5V9M16.668 12.9524C17.6859 12.1117 18.3346 10.8399 18.3346 9.41667C18.3346 6.88536 16.2826 4.83333 13.7513 4.83333C13.5692 4.83333 13.3989 4.73833 13.3064 4.58145C12.2197 2.73736 10.2133 1.5 7.91797 1.5C4.46619 1.5 1.66797 4.29822 1.66797 7.75C1.66797 9.47175 2.36417 11.0309 3.49043 12.1613"
          stroke="#535862"
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    arrowDownRight: (
      <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="36" height="36" rx="16" fill="#F4F2FC" />
        <path
          d="M23.8315 14.6649C23.6105 14.6649 23.3986 14.7527 23.2423 14.9089C23.086 15.0652 22.9982 15.2772 22.9982 15.4982V21.8232L12.7565 11.5732C12.5996 11.4163 12.3868 11.3281 12.1649 11.3281C11.9429 11.3281 11.7301 11.4163 11.5732 11.5732C11.4163 11.7301 11.3281 11.9429 11.3281 12.1649C11.3281 12.3868 11.4163 12.5996 11.5732 12.7565L21.8232 22.9982H15.5815C15.3605 22.9982 15.1486 23.086 14.9923 23.2423C14.836 23.3986 14.7482 23.6105 14.7482 23.8315C14.7482 24.0525 14.836 24.2645 14.9923 24.4208C15.1486 24.5771 15.3605 24.6649 15.5815 24.6649H23.8315C24.0525 24.6649 24.2645 24.5771 24.4208 24.4208C24.5771 24.2645 24.6649 24.0525 24.6649 23.8315V15.4982C24.6649 15.2772 24.5771 15.0652 24.4208 14.9089C24.2645 14.7527 24.0525 14.6649 23.8315 14.6649Z"
          fill="#8A3FFC"
        />
      </svg>
    ),
    arrowUpRight: (
      <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="36" height="36" rx="18" fill="#F4F2FC" />
        <path
          d="M24.5628 10.5H15.1912C14.9427 10.5 14.7043 10.5987 14.5286 10.7745C14.3528 10.9502 14.2541 11.1886 14.2541 11.4372C14.2541 11.6857 14.3528 11.9241 14.5286 12.0998C14.7043 12.2756 14.9427 12.3743 15.1912 12.3743H22.3043L10.7772 23.892C10.6894 23.9791 10.6197 24.0828 10.5721 24.197C10.5245 24.3112 10.5 24.4337 10.5 24.5574C10.5 24.6811 10.5245 24.8036 10.5721 24.9178C10.6197 25.032 10.6894 25.1357 10.7772 25.2228C10.8643 25.3106 10.968 25.3803 11.0822 25.4279C11.1964 25.4755 11.3189 25.5 11.4426 25.5C11.5663 25.5 11.6888 25.4755 11.803 25.4279C11.9172 25.3803 12.0209 25.3106 12.108 25.2228L23.6257 13.6957V20.715C23.6257 20.9636 23.7244 21.202 23.9002 21.3777C24.0759 21.5535 24.3143 21.6522 24.5628 21.6522C24.8114 21.6522 25.0498 21.5535 25.2255 21.3777C25.4013 21.202 25.5 20.9636 25.5 20.715V11.4372C25.5 11.1886 25.4013 10.9502 25.2255 10.7745C25.0498 10.5987 24.8114 10.5 24.5628 10.5Z"
          fill="#8A3FFC"
        />
      </svg>
    ),
    percent: (
      <svg
        className={className}
        width={width ? width : '12'}
        height={height ? height : '12'}
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M13.8294 0.98697L0.984375 13.832L2.16287 15.0105L15.0079 2.16547L13.8294 0.98697Z" fill="#7E3AF2" />
        <path
          d="M3.83333 7.16667C5.67417 7.16667 7.16667 5.67417 7.16667 3.83333C7.16667 1.9925 5.67417 0.5 3.83333 0.5C1.9925 0.5 0.5 1.9925 0.5 3.83333C0.5 5.67417 1.9925 7.16667 3.83333 7.16667ZM3.83333 2.16667C4.7525 2.16667 5.5 2.91417 5.5 3.83333C5.5 4.7525 4.7525 5.5 3.83333 5.5C2.91417 5.5 2.16667 4.7525 2.16667 3.83333C2.16667 2.91417 2.91417 2.16667 3.83333 2.16667Z"
          fill="#7E3AF2"
        />
        <path
          d="M12.1693 15.4987C14.0101 15.4987 15.5026 14.0062 15.5026 12.1654C15.5026 10.3245 14.0101 8.83203 12.1693 8.83203C10.3284 8.83203 8.83594 10.3245 8.83594 12.1654C8.83594 14.0062 10.3284 15.4987 12.1693 15.4987ZM12.1693 10.4987C13.0884 10.4987 13.8359 11.2462 13.8359 12.1654C13.8359 13.0845 13.0884 13.832 12.1693 13.832C11.2501 13.832 10.5026 13.0845 10.5026 12.1654C10.5026 11.2462 11.2501 10.4987 12.1693 10.4987Z"
          fill="#7E3AF2"
        />
      </svg>
    ),

    envelope: (
      <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="36" height="36" rx="18" fill="#F4F2FC" />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.6443 12.3083C9.66797 13.2847 9.66797 14.856 9.66797 17.9987C9.66797 21.1414 9.66797 22.7127 10.6443 23.6891C11.6206 24.6654 13.1919 24.6654 16.3346 24.6654H19.668C22.8107 24.6654 24.382 24.6654 25.3583 23.6891C26.3346 22.7127 26.3346 21.1414 26.3346 17.9987C26.3346 14.856 26.3346 13.2847 25.3583 12.3083C24.382 11.332 22.8107 11.332 19.668 11.332H16.3346C13.1919 11.332 11.6206 11.332 10.6443 12.3083ZM23.4814 14.2652C23.7024 14.5304 23.6666 14.9245 23.4014 15.1455L21.571 16.6708C20.8324 17.2864 20.2337 17.7853 19.7053 18.1251C19.1549 18.4791 18.6189 18.7028 18.0013 18.7028C17.3837 18.7028 16.8477 18.4791 16.2973 18.1251C15.7689 17.7853 15.1702 17.2864 14.4316 16.6708L12.6012 15.1455C12.336 14.9245 12.3002 14.5304 12.5212 14.2652C12.7421 14.0001 13.1362 13.9642 13.4014 14.1852L15.2005 15.6845C15.978 16.3323 16.5177 16.7807 16.9735 17.0738C17.4146 17.3575 17.7137 17.4528 18.0013 17.4528C18.2889 17.4528 18.588 17.3575 19.0292 17.0738C19.4849 16.7807 20.0246 16.3323 20.8021 15.6845L22.6012 14.1852C22.8664 13.9642 23.2605 14.0001 23.4814 14.2652Z"
          fill="#8A3FFC"
        />
      </svg>
    ),
    greenRightSign: (
      <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="path-1-inside-1_15498_8707" fill="white">
          <path d="M0 14C0 6.26801 6.26801 0 14 0C21.732 0 28 6.26801 28 14C28 21.732 21.732 28 14 28C6.26801 28 0 21.732 0 14Z" />
        </mask>
        <path
          d="M14 27C6.8203 27 1 21.1797 1 14H-1C-1 22.2843 5.71573 29 14 29V27ZM27 14C27 21.1797 21.1797 27 14 27V29C22.2843 29 29 22.2843 29 14H27ZM14 1C21.1797 1 27 6.8203 27 14H29C29 5.71573 22.2843 -1 14 -1V1ZM14 -1C5.71573 -1 -1 5.71573 -1 14H1C1 6.8203 6.8203 1 14 1V-1Z"
          fill="url(#paint0_linear_15498_8707)"
          mask="url(#path-1-inside-1_15498_8707)"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M19.385 8.45837C19.9392 7.88997 20.8877 7.84389 21.5034 8.35545C22.1192 8.86701 22.1691 9.74249 21.6149 10.3109L12.6149 19.5416C12.04 20.1313 11.047 20.1554 10.4393 19.5945L6.43934 15.9021C5.85355 15.3614 5.85355 14.4847 6.43934 13.944C7.02513 13.4033 7.97487 13.4033 8.56066 13.944L11.4427 16.6044L19.385 8.45837Z"
          fill="#149041"
        />
        <defs>
          <linearGradient id="paint0_linear_15498_8707" x1="14" y1="0" x2="14" y2="28" gradientUnits="userSpaceOnUse">
            <stop stop-color="#F4F4F5" />
            <stop offset="1" stop-color="#C8D6E5" />
          </linearGradient>
        </defs>
      </svg>
    ),
    redWrongSign: (
      <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="path-1-inside-1_15498_8724" fill="white">
          <path d="M0 14C0 6.26801 6.26801 0 14 0C21.732 0 28 6.26801 28 14C28 21.732 21.732 28 14 28C6.26801 28 0 21.732 0 14Z" />
        </mask>
        <path
          d="M14 27C6.8203 27 1 21.1797 1 14H-1C-1 22.2843 5.71573 29 14 29V27ZM27 14C27 21.1797 21.1797 27 14 27V29C22.2843 29 29 22.2843 29 14H27ZM14 1C21.1797 1 27 6.8203 27 14H29C29 5.71573 22.2843 -1 14 -1V1ZM14 -1C5.71573 -1 -1 5.71573 -1 14H1C1 6.8203 6.8203 1 14 1V-1Z"
          fill="url(#paint0_linear_15498_8724)"
          mask="url(#path-1-inside-1_15498_8724)"
        />
        <path
          d="M15.5142 14.0009L20.4142 9.1009C20.5967 8.91461 20.6984 8.6638 20.6971 8.40297C20.6958 8.14214 20.5916 7.89237 20.4071 7.70793C20.2227 7.5235 19.9729 7.4193 19.7121 7.41798C19.4513 7.41666 19.2005 7.51833 19.0142 7.7009L14.1142 12.6009L9.21418 7.7009C9.02789 7.51833 8.77708 7.41666 8.51625 7.41798C8.25542 7.4193 8.00565 7.5235 7.82121 7.70793C7.63678 7.89237 7.53258 8.14214 7.53126 8.40297C7.52995 8.6638 7.63162 8.91461 7.81418 9.1009L12.7142 14.0009L7.81418 18.9009C7.63162 19.0872 7.52995 19.338 7.53126 19.5988C7.53258 19.8597 7.63678 20.1094 7.82121 20.2939C8.00565 20.4783 8.25542 20.5825 8.51625 20.5838C8.77708 20.5851 9.02789 20.4835 9.21418 20.3009L14.1142 15.4009L19.0142 20.3009C19.2005 20.4835 19.4513 20.5851 19.7121 20.5838C19.9729 20.5825 20.2227 20.4783 20.4071 20.2939C20.5916 20.1094 20.6958 19.8597 20.6971 19.5988C20.6984 19.338 20.5967 19.0872 20.4142 18.9009L15.5142 14.0009Z"
          fill="#982828"
        />
        <defs>
          <linearGradient id="paint0_linear_15498_8724" x1="14" y1="0" x2="14" y2="28" gradientUnits="userSpaceOnUse">
            <stop stop-color="#F4F4F5" />
            <stop offset="1" stop-color="#C8D6E5" />
          </linearGradient>
        </defs>
      </svg>
    ),

    imoChatInterviewAssign: (
      <svg width="61" height="60" viewBox="0 0 61 60" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="0.5" width="60" height="60" rx="30" fill="#EFEAFC" />
        <path d="M25.5 30H25.5112M30.4888 30H30.5M35.4888 30H35.5" stroke="#A379FC" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" />
        <path
          d="M30.5 42.5C37.4035 42.5 43 36.9035 43 30C43 23.0964 37.4035 17.5 30.5 17.5C23.5964 17.5 18 23.0964 18 30C18 31.9996 18.4695 33.8895 19.3043 35.5656C19.5262 36.011 19.6 36.5201 19.4714 37.0007L18.7269 39.7834C18.4037 40.9912 19.5088 42.0962 20.7167 41.7731L23.4992 41.0286C23.9799 40.9 24.489 40.9739 24.9344 41.1956C26.6105 42.0305 28.5004 42.5 30.5 42.5Z"
          stroke="#A379FC"
          strokeWidth="2"
        />
      </svg>
    ),
    interactiveInterviewAssign: (
      <svg width="61" height="61" viewBox="0 0 61 61" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="0.5" y="0.5" width="60" height="60" rx="30" fill="#EFEAFC" />
        <g clipPath="url(#clip0_16142_9154)">
          <path
            d="M41.3168 17.3555H16.482C15.9405 17.3555 15.5 17.796 15.5 18.3374V24.4195C15.5 24.9609 15.9405 25.4014 16.482 25.4014H41.3168C41.8583 25.4014 42.2988 24.9609 42.2988 24.4195V18.3374C42.2988 17.796 41.8583 17.3555 41.3168 17.3555ZM22.0889 24.4511H16.482C16.4646 24.4511 16.4503 24.4369 16.4503 24.4194V18.3374C16.4503 18.3199 16.4645 18.3057 16.482 18.3057H22.0889V24.4511ZM41.3485 24.4195C41.3485 24.4369 41.3343 24.4512 41.3168 24.4512H23.0391V18.3057H41.3168C41.3342 18.3057 41.3485 18.3199 41.3485 18.3374V24.4195Z"
            fill="#A379FC"
          />
          <path
            d="M33.2085 19.8867H24.5923C24.3299 19.8867 24.1172 20.0995 24.1172 20.3619C24.1172 20.6242 24.3299 20.837 24.5923 20.837H33.2085C33.4709 20.837 33.6836 20.6242 33.6836 20.3619C33.6836 20.0995 33.4709 19.8867 33.2085 19.8867Z"
            fill="#A379FC"
          />
          <path
            d="M39.2915 21.9141H29.1548C28.8923 21.9141 28.6797 22.1268 28.6797 22.3892C28.6797 22.6516 28.8924 22.8643 29.1548 22.8643H39.2915C39.554 22.8643 39.7667 22.6516 39.7667 22.3892C39.7667 22.1268 39.5539 21.9141 39.2915 21.9141Z"
            fill="#A379FC"
          />
          <path
            d="M27.1266 21.9141H24.5923C24.3299 21.9141 24.1172 22.1268 24.1172 22.3892C24.1172 22.6516 24.3299 22.8643 24.5923 22.8643H27.1266C27.3891 22.8643 27.6018 22.6516 27.6018 22.3892C27.6018 22.1268 27.389 21.9141 27.1266 21.9141Z"
            fill="#A379FC"
          />
          <path
            d="M19.2735 18.875C18.3128 18.875 17.5312 19.6566 17.5312 20.6172C17.5312 20.8797 17.744 21.0924 18.0064 21.0924C18.2688 21.0924 18.4815 20.8796 18.4815 20.6172C18.4815 20.1805 18.8368 19.8253 19.2735 19.8253C19.7102 19.8253 20.0654 20.1806 20.0654 20.6172C20.0654 21.0539 19.7102 21.4092 19.2735 21.4092C19.011 21.4092 18.7983 21.6219 18.7983 21.8843C18.7983 22.1467 19.0111 22.3595 19.2735 22.3595C20.2342 22.3595 21.0157 21.5779 21.0157 20.6172C21.0157 19.6565 20.2342 18.875 19.2735 18.875Z"
            fill="#A379FC"
          />
          <path
            d="M19.2745 22.9297H19.2695C19.0071 22.9297 18.7969 23.1424 18.7969 23.4048C18.7969 23.6672 19.0121 23.88 19.2746 23.88C19.537 23.88 19.7497 23.6672 19.7497 23.4048C19.7497 23.1424 19.5369 22.9297 19.2745 22.9297Z"
            fill="#A379FC"
          />
          <path
            d="M33.2085 29.0117H24.5923C24.3299 29.0117 24.1172 29.2245 24.1172 29.4869C24.1172 29.7492 24.3299 29.962 24.5923 29.962H33.2085C33.4709 29.962 33.6836 29.7492 33.6836 29.4869C33.6836 29.2245 33.4709 29.0117 33.2085 29.0117Z"
            fill="#A379FC"
          />
          <path
            d="M37.771 31.0391H29.1548C28.8924 31.0391 28.6797 31.2518 28.6797 31.5142C28.6797 31.7766 28.8924 31.9893 29.1548 31.9893H37.771C38.0335 31.9893 38.2462 31.7766 38.2462 31.5142C38.2462 31.2518 38.0334 31.0391 37.771 31.0391Z"
            fill="#A379FC"
          />
          <path
            d="M27.1266 31.0391H24.5923C24.3299 31.0391 24.1172 31.2518 24.1172 31.5142C24.1172 31.7766 24.3299 31.9893 24.5923 31.9893H27.1266C27.3891 31.9893 27.6018 31.7766 27.6018 31.5142C27.6018 31.2518 27.389 31.0391 27.1266 31.0391Z"
            fill="#A379FC"
          />
          <path
            d="M19.2735 27.9961C18.3128 27.9961 17.5312 28.7776 17.5312 29.7383C17.5312 30.0008 17.744 30.2135 18.0064 30.2135C18.2688 30.2135 18.4815 30.0007 18.4815 29.7383C18.4815 29.3016 18.8368 28.9464 19.2735 28.9464C19.7102 28.9464 20.0654 29.3016 20.0654 29.7383C20.0654 30.175 19.7102 30.5303 19.2735 30.5303C19.011 30.5303 18.7983 30.743 18.7983 31.0054C18.7983 31.2678 19.0111 31.4805 19.2735 31.4805C20.2342 31.4805 21.0157 30.699 21.0157 29.7383C21.0157 28.7776 20.2342 27.9961 19.2735 27.9961Z"
            fill="#A379FC"
          />
          <path
            d="M19.2745 32.0508H19.2695C19.0071 32.0508 18.7969 32.2635 18.7969 32.5259C18.7969 32.7883 19.0121 33.0011 19.2746 33.0011C19.537 33.0011 19.7497 32.7883 19.7497 32.5259C19.7497 32.2635 19.5369 32.0508 19.2745 32.0508Z"
            fill="#A379FC"
          />
          <path
            d="M33.2085 38.1328H24.5923C24.3299 38.1328 24.1172 38.3456 24.1172 38.6079C24.1172 38.8703 24.3299 39.0831 24.5923 39.0831H33.2085C33.4709 39.0831 33.6836 38.8703 33.6836 38.6079C33.6836 38.3456 33.4709 38.1328 33.2085 38.1328Z"
            fill="#A379FC"
          />
          <path
            d="M38.2779 40.1602H29.1548C28.8924 40.1602 28.6797 40.3729 28.6797 40.6353C28.6797 40.8977 28.8924 41.1104 29.1548 41.1104H38.2779C38.5403 41.1104 38.753 40.8977 38.753 40.6353C38.753 40.3729 38.5403 40.1602 38.2779 40.1602Z"
            fill="#A379FC"
          />
          <path
            d="M27.1266 40.1602H24.5923C24.3299 40.1602 24.1172 40.3729 24.1172 40.6353C24.1172 40.8977 24.3299 41.1104 24.5923 41.1104H27.1266C27.3891 41.1104 27.6018 40.8977 27.6018 40.6353C27.6018 40.3729 27.389 40.1602 27.1266 40.1602Z"
            fill="#A379FC"
          />
          <path
            d="M19.2735 37.1211C18.3128 37.1211 17.5312 37.9026 17.5312 38.8633C17.5312 39.1258 17.744 39.3385 18.0064 39.3385C18.2688 39.3385 18.4815 39.1257 18.4815 38.8633C18.4815 38.4266 18.8368 38.0714 19.2735 38.0714C19.7102 38.0714 20.0654 38.4266 20.0654 38.8633C20.0654 39.3 19.7102 39.6553 19.2735 39.6553C19.011 39.6553 18.7983 39.868 18.7983 40.1304C18.7983 40.3928 19.0111 40.6055 19.2735 40.6055C20.2342 40.6055 21.0157 39.824 21.0157 38.8633C21.0157 37.9027 20.2342 37.1211 19.2735 37.1211Z"
            fill="#A379FC"
          />
          <path
            d="M19.2745 41.1758H19.2695C19.0071 41.1758 18.7969 41.3885 18.7969 41.6509C18.7969 41.9133 19.0121 42.1261 19.2746 42.1261C19.537 42.1261 19.7497 41.9133 19.7497 41.6509C19.7497 41.3885 19.5369 41.1758 19.2745 41.1758Z"
            fill="#A379FC"
          />
          <path
            d="M45.4798 36.8789C45.5303 36.7114 45.4846 36.5295 45.3608 36.4058L42.2988 33.3437V27.4585C42.2988 26.9171 41.8583 26.4766 41.3168 26.4766H16.482C15.9405 26.4766 15.5 26.9171 15.5 27.4585V33.5406C15.5 34.082 15.9405 34.5225 16.482 34.5225H39.3212V35.5996H16.482C15.9405 35.5996 15.5 36.0401 15.5 36.5816V42.6636C15.5 43.2051 15.9405 43.6456 16.482 43.6456H41.3168C41.8583 43.6456 42.2988 43.2051 42.2988 42.6636V41.2075L42.742 42.2775C42.7902 42.394 42.8827 42.4865 42.9992 42.5347C43.0574 42.5588 43.1192 42.5709 43.181 42.5709C43.2427 42.5709 43.3046 42.5588 43.3628 42.5347L45.0677 41.8285C45.3102 41.728 45.4253 41.4501 45.3249 41.2077L43.7802 37.4785L45.119 37.2075C45.2907 37.1728 45.4293 37.0465 45.4798 36.8789ZM22.0889 33.5723H16.482C16.4646 33.5723 16.4503 33.5581 16.4503 33.5406V27.4585C16.4503 27.4411 16.4645 27.4268 16.482 27.4268H22.0889V33.5723ZM23.0391 33.5723V27.4269H41.3168C41.3343 27.4269 41.3485 27.4411 41.3485 27.4586V32.3935L40.1323 31.1773C39.9964 31.0414 39.792 31.0008 39.6145 31.0743C39.4369 31.1478 39.3212 31.3212 39.3212 31.5133V33.5723L23.0391 33.5723ZM22.0889 42.6953H16.482C16.4646 42.6953 16.4503 42.6811 16.4503 42.6636V36.5816C16.4503 36.5641 16.4645 36.5499 16.482 36.5499H22.0889V42.6953ZM41.3485 42.6636C41.3485 42.681 41.3343 42.6953 41.3168 42.6953H23.0391V36.5499H39.3212V38.8947C39.3212 39.0692 39.4169 39.2298 39.5705 39.3128C39.7241 39.3957 39.9108 39.3878 40.0568 39.2921L41.1962 38.5454L41.3485 38.9132V42.6636ZM43.0262 36.6615C42.887 36.6897 42.7677 36.7786 42.701 36.904C42.6342 37.0294 42.6271 37.1779 42.6814 37.3091L44.2651 41.1324L43.4382 41.4749L41.8545 37.6517C41.8004 37.5209 41.6908 37.4211 41.5555 37.3795C41.5096 37.3653 41.4625 37.3584 41.4157 37.3584C41.3241 37.3584 41.2334 37.3848 41.1552 37.4361L40.2716 38.0153V32.6604L44.063 36.4518L43.0262 36.6615Z"
            fill="#A379FC"
          />
        </g>
        <defs>
          <clipPath id="clip0_16142_9154">
            <rect width="30" height="30" fill="white" transform="translate(15.5 15.5)" />
          </clipPath>
        </defs>
      </svg>
    ),
    questionMarkBorder: (
      <svg width="15" height="17" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M7.17004 0.5C7.8547 0.5 8.50212 0.811744 8.92904 1.347L10.759 3.64131C11.0769 4.03985 11.25 4.53452 11.25 5.0443V10.25C11.25 11.4926 10.2426 12.5 9 12.5H3C1.75736 12.5 0.75 11.4926 0.75 10.25V2.75C0.75 1.50736 1.75736 0.5 3 0.5H7.17004ZM7.17004 1.25H3C2.19668 1.25 1.54085 1.88149 1.50184 2.67513L1.5 2.75V10.25C1.5 11.0533 2.13149 11.7092 2.92513 11.7482L3 11.75H9C9.80332 11.75 10.4592 11.1185 10.4982 10.3249L10.5 10.25V5.0443C10.5 4.73534 10.4046 4.43471 10.2281 4.18298L10.1727 4.10897L8.34271 1.81467C8.07588 1.48013 7.67986 1.27656 7.2553 1.25242L7.17004 1.25ZM6 8.5625C6.28995 8.5625 6.525 8.79755 6.525 9.0875C6.525 9.37745 6.28995 9.6125 6 9.6125C5.71005 9.6125 5.475 9.37745 5.475 9.0875C5.475 8.79755 5.71005 8.5625 6 8.5625ZM6 4.25C6.82843 4.25 7.5 4.92157 7.5 5.75C7.5 6.44884 7.0221 7.03605 6.37529 7.20267L6.375 7.625C6.375 7.83211 6.20711 8 6 8C5.79289 8 5.625 7.83211 5.625 7.625V6.875C5.625 6.66789 5.79289 6.5 6 6.5C6.41421 6.5 6.75 6.16421 6.75 5.75C6.75 5.33579 6.41421 5 6 5C5.58579 5 5.25 5.33579 5.25 5.75C5.25 5.95711 5.08211 6.125 4.875 6.125C4.66789 6.125 4.5 5.95711 4.5 5.75C4.5 4.92157 5.17157 4.25 6 4.25Z"
          fill="#667085"
        />
      </svg>
    ),

    interActiveAiInterviewStars: (
      <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clipPath="url(#clip0_16142_9564)">
          <g mask="url(#mask0_16142_9564)">
            <path
              d="M19.1054 9.41788L14.4217 7.68419L12.6727 1.95209C12.6397 1.84442 12.573 1.75019 12.4824 1.68325C12.3919 1.6163 12.2822 1.58018 12.1696 1.58018C12.057 1.58018 11.9473 1.6163 11.8567 1.68325C11.7662 1.75019 11.6995 1.84442 11.6664 1.95209L9.918 7.68419L5.23379 9.41788C5.13307 9.45527 5.0462 9.52258 4.98485 9.61079C4.92351 9.69899 4.89062 9.80386 4.89062 9.9113C4.89062 10.0187 4.92351 10.1236 4.98485 10.2118C5.0462 10.3 5.13307 10.3673 5.23379 10.4047L9.9159 12.1379L11.6648 18.0447C11.697 18.1536 11.7636 18.2492 11.8545 18.3172C11.9455 18.3851 12.056 18.4219 12.1696 18.4219C12.2831 18.4219 12.3937 18.3851 12.4846 18.3172C12.5756 18.2492 12.6421 18.1536 12.6743 18.0447L14.4238 12.1379L19.1059 10.4047C19.2071 10.3677 19.2944 10.3005 19.3561 10.2122C19.4178 10.124 19.4509 10.0188 19.4508 9.91112C19.4507 9.8034 19.4176 9.69831 19.3558 9.61008C19.294 9.52186 19.2066 9.45476 19.1054 9.41788Z"
              fill="#AA8ED6"
            />
            <path
              d="M8.577 15.2087L7.35963 14.7582L6.88595 13.0214C6.85564 12.9099 6.7895 12.8115 6.69771 12.7413C6.60592 12.6711 6.49359 12.6331 6.37805 12.6331C6.26251 12.6331 6.15018 12.6711 6.05839 12.7413C5.9666 12.8115 5.90046 12.9099 5.87016 13.0214L5.39647 14.7582L4.1791 15.2087C4.07838 15.2461 3.99151 15.3135 3.93016 15.4017C3.86882 15.4899 3.83594 15.5947 3.83594 15.7022C3.83594 15.8096 3.86882 15.9145 3.93016 16.0027C3.99151 16.0909 4.07838 16.1582 4.1791 16.1956L5.39016 16.6445L5.86752 18.5509C5.89605 18.6647 5.96177 18.7657 6.05427 18.8378C6.14677 18.91 6.26073 18.9492 6.37805 18.9492C6.49537 18.9492 6.60933 18.91 6.70183 18.8378C6.79433 18.7657 6.86005 18.6647 6.88858 18.5509L7.36595 16.6445L8.577 16.1956C8.67772 16.1582 8.76459 16.0909 8.82594 16.0027C8.88728 15.9145 8.92016 15.8096 8.92016 15.7022C8.92016 15.5947 8.88728 15.4899 8.82594 15.4017C8.76459 15.3135 8.67772 15.2461 8.577 15.2087Z"
              fill="#FCAB40"
            />
            <path
              d="M6.29646 3.80625L5.05172 3.34572L4.5912 2.10098C4.55391 2.00008 4.4866 1.91303 4.39833 1.85155C4.31007 1.79006 4.20508 1.7571 4.09751 1.7571C3.98994 1.7571 3.88496 1.79006 3.79669 1.85155C3.70842 1.91303 3.64111 2.00008 3.60383 2.10098L3.14277 3.34572L1.89856 3.80625C1.79766 3.84353 1.71061 3.91084 1.64913 3.99911C1.58765 4.08737 1.55469 4.19236 1.55469 4.29993C1.55469 4.4075 1.58765 4.51248 1.64913 4.60075C1.71061 4.68902 1.79766 4.75633 1.89856 4.79361L3.14277 5.25414L3.60383 6.49888C3.64089 6.6 3.70811 6.68731 3.7964 6.74899C3.8847 6.81067 3.98981 6.84375 4.09751 6.84375C4.20522 6.84375 4.31032 6.81067 4.39862 6.74899C4.48691 6.68731 4.55414 6.6 4.5912 6.49888L5.05172 5.25414L6.29646 4.79361C6.39736 4.75633 6.48441 4.68902 6.54589 4.60075C6.60737 4.51248 6.64033 4.4075 6.64033 4.29993C6.64033 4.19236 6.60737 4.08737 6.54589 3.99911C6.48441 3.91084 6.39736 3.84353 6.29646 3.80625Z"
              fill="#5DADEC"
            />
          </g>
        </g>
        <defs>
          <clipPath id="clip0_16142_9564">
            <rect width="20" height="20" fill="white" transform="translate(0.5)" />
          </clipPath>
        </defs>
      </svg>
    ),
    manInSuit: (
      <svg width="45" height="45" viewBox="0 0 45 45" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="0.5" y="0.5" width="44" height="44" rx="22" fill="#EDE9FE" />
        <rect x="0.5" y="0.5" width="44" height="44" rx="22" stroke="#E1E4E8" />
        <path
          d="M22.2031 25.5508C22.2031 25.6285 22.234 25.703 22.2889 25.7579C22.3439 25.8129 22.4184 25.8438 22.4961 25.8438C22.5738 25.8438 22.6483 25.8129 22.7033 25.7579C22.7582 25.703 22.7891 25.6285 22.7891 25.5508C22.7891 25.4731 22.7582 25.3986 22.7033 25.3436C22.6483 25.2887 22.5738 25.2578 22.4961 25.2578C22.4184 25.2578 22.3439 25.2887 22.2889 25.3436C22.234 25.3986 22.2031 25.4731 22.2031 25.5508Z"
          fill="#702DFF"
        />
        <path
          d="M22.1953 26.7461C22.1953 26.8238 22.2262 26.8983 22.2811 26.9533C22.3361 27.0082 22.4106 27.0391 22.4883 27.0391C22.566 27.0391 22.6405 27.0082 22.6954 26.9533C22.7504 26.8983 22.7812 26.8238 22.7812 26.7461C22.7812 26.6684 22.7504 26.5939 22.6954 26.5389C22.6405 26.484 22.566 26.4531 22.4883 26.4531C22.4106 26.4531 22.3361 26.484 22.2811 26.5389C22.2262 26.5939 22.1953 26.6684 22.1953 26.7461Z"
          fill="#702DFF"
        />
        <path
          d="M22.2188 27.9414C22.2188 28.0191 22.2496 28.0936 22.3046 28.1486C22.3595 28.2035 22.434 28.2344 22.5117 28.2344C22.5894 28.2344 22.6639 28.2035 22.7189 28.1486C22.7738 28.0936 22.8047 28.0191 22.8047 27.9414C22.8047 27.8637 22.7738 27.7892 22.7189 27.7342C22.6639 27.6793 22.5894 27.6484 22.5117 27.6484C22.434 27.6484 22.3595 27.6793 22.3046 27.7342C22.2496 27.7892 22.2188 27.8637 22.2188 27.9414Z"
          fill="#702DFF"
        />
        <path
          d="M22.5 11.332C23.7334 11.332 24.8936 11.8125 25.7666 12.6855C26.6396 13.5586 27.1201 14.7188 27.1201 15.9521C27.1201 17.1855 26.6396 18.3457 25.7666 19.2188C24.8936 20.0918 23.7334 20.5723 22.5 20.5723C21.2666 20.5723 20.1064 20.0918 19.2334 19.2188C18.3604 18.3457 17.8799 17.1855 17.8799 15.9521C17.8799 14.7188 18.3604 13.5586 19.2334 12.6855C20.1064 11.8125 21.2666 11.332 22.5 11.332ZM22.5 10.043C19.2363 10.043 16.5938 12.6885 16.5938 15.9492C16.5938 19.2129 19.2393 21.8555 22.5 21.8555C25.7637 21.8555 28.4062 19.21 28.4062 15.9492C28.4062 12.6885 25.7607 10.043 22.5 10.043ZM22.5088 22.8369L9.96094 27.4629V34.9541H35.0391V27.4629L22.5088 22.8369ZM33.75 33.665H11.25V28.377L22.5088 24.2051L33.75 28.377V33.665Z"
          fill="#9566F8"
        />
        <path
          d="M23.7754 24.6475L23.7344 24.6328V29.3174C23.7344 29.6602 23.5937 29.9707 23.3711 30.1963C23.1455 30.4219 22.835 30.5596 22.4922 30.5596C21.8066 30.5596 21.25 30.0029 21.25 29.3174V24.6475L21.209 24.6621L20.7812 24.8145V29.3203C20.7812 30.2637 21.5488 31.0313 22.4922 31.0313C23.4355 31.0313 24.2031 30.2637 24.2031 29.3203V24.7998L23.7754 24.6475Z"
          fill="#702DFF"
        />
      </svg>
    ),
    questionInBorder: (
      <svg width="46" height="45" viewBox="0 0 46 45" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="1.16406" y="0.5" width="44" height="44" rx="22" fill="#EDE9FE" />
        <rect x="1.16406" y="0.5" width="44" height="44" rx="22" stroke="#E1E4E8" />
        <g clipPath="url(#clip0_16142_9607)">
          <path
            d="M25.6016 10C27.028 10 28.3768 10.6495 29.2662 11.7646L33.0787 16.5444C33.7409 17.3747 34.1016 18.4053 34.1016 19.4673V30.3125C34.1016 32.9013 32.0029 35 29.4141 35H16.9141C14.3252 35 12.2266 32.9013 12.2266 30.3125V14.6875C12.2266 12.0987 14.3252 10 16.9141 10H25.6016ZM25.6016 11.5625H16.9141C15.2405 11.5625 13.8742 12.8781 13.7929 14.5315L13.7891 14.6875V30.3125C13.7891 31.9861 15.1047 33.3524 16.7581 33.4337L16.9141 33.4375H29.4141C31.0877 33.4375 32.454 32.1219 32.5352 30.4685L32.5391 30.3125V19.4673C32.5391 18.8236 32.3404 18.1973 31.9725 17.6729L31.8571 17.5187L28.0447 12.7389C27.4888 12.0419 26.6638 11.6178 25.7793 11.5675L25.6016 11.5625ZM23.1641 26.7969C23.7681 26.7969 24.2578 27.2866 24.2578 27.8906C24.2578 28.4947 23.7681 28.9844 23.1641 28.9844C22.56 28.9844 22.0703 28.4947 22.0703 27.8906C22.0703 27.2866 22.56 26.7969 23.1641 26.7969ZM23.1641 17.8125C24.89 17.8125 26.2891 19.2116 26.2891 20.9375C26.2891 22.3934 25.2934 23.6168 23.9459 23.9639L23.9453 24.8438C23.9453 25.2752 23.5955 25.625 23.1641 25.625C22.7326 25.625 22.3828 25.2752 22.3828 24.8438V23.2812C22.3828 22.8498 22.7326 22.5 23.1641 22.5C24.027 22.5 24.7266 21.8004 24.7266 20.9375C24.7266 20.0746 24.027 19.375 23.1641 19.375C22.3011 19.375 21.6016 20.0746 21.6016 20.9375C21.6016 21.369 21.2518 21.7188 20.8203 21.7188C20.3888 21.7188 20.0391 21.369 20.0391 20.9375C20.0391 19.2116 21.4382 17.8125 23.1641 17.8125Z"
            fill="#A379FC"
          />
        </g>
        <defs>
          <clipPath id="clip0_16142_9607">
            <rect width="25" height="25" fill="white" transform="translate(10.6641 10)" />
          </clipPath>
        </defs>
      </svg>
    ),
    clockThree: (
      <svg width="46" height="45" viewBox="0 0 46 45" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="0.835938" y="0.5" width="44" height="44" rx="22" fill="#EDE9FE" />
        <rect x="0.835938" y="0.5" width="44" height="44" rx="22" stroke="#E1E4E8" />
        <path
          d="M22.8359 33.75C29.0492 33.75 34.0859 28.7133 34.0859 22.5C34.0859 16.2868 29.0492 11.25 22.8359 11.25C16.6227 11.25 11.5859 16.2868 11.5859 22.5C11.5859 28.7133 16.6227 33.75 22.8359 33.75Z"
          stroke="#A379FC"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M22.8359 15V22.5" stroke="#A379FC" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M28.1359 27.8L22.8359 22.5" stroke="#A379FC" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
    ),
    charts: (
      <svg width="45" height="45" viewBox="0 0 45 45" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="0.5" y="0.5" width="44" height="44" rx="22" fill="#EDE9FE" />
        <rect x="0.5" y="0.5" width="44" height="44" rx="22" stroke="#E1E4E8" />
        <path
          d="M16.875 25.3125V28.125M20.625 22.5V28.125M24.375 19.6875V28.125M28.125 16.875V28.125M15 32.8125H30C30.7459 32.8125 31.4613 32.5162 31.9887 31.9887C32.5162 31.4613 32.8125 30.7459 32.8125 30V15C32.8125 14.2541 32.5162 13.5387 31.9887 13.0113C31.4613 12.4838 30.7459 12.1875 30 12.1875H15C14.2541 12.1875 13.5387 12.4838 13.0113 13.0113C12.4838 13.5387 12.1875 14.2541 12.1875 15V30C12.1875 30.7459 12.4838 31.4613 13.0113 31.9887C13.5387 32.5162 14.2541 32.8125 15 32.8125Z"
          stroke="#A379FC"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    stars: (
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M7.70093 1.97548C7.77913 1.65868 8.22951 1.65868 8.30771 1.97548L8.94637 4.56252C9.2802 5.9148 10.336 6.97063 11.6883 7.30446L14.2754 7.94312C14.5921 8.02132 14.5921 8.4717 14.2754 8.5499L11.6883 9.18855C10.336 9.52238 9.2802 10.5782 8.94637 11.9305L8.30771 14.5176C8.22951 14.8343 7.77913 14.8343 7.70093 14.5176L7.06227 11.9305C6.72845 10.5782 5.6726 9.52238 4.32034 9.18855L1.73329 8.5499C1.41649 8.4717 1.41649 8.02132 1.73329 7.94312L4.32034 7.30446C5.67262 6.97063 6.72845 5.91479 7.06227 4.56252L7.70093 1.97548ZM2.85262 1.36099C2.89173 1.20259 3.11691 1.20259 3.15602 1.36099L3.20524 1.56038C3.38607 2.29285 3.95798 2.86476 4.69045 3.04559L4.88984 3.0948C5.04824 3.13391 5.04824 3.3591 4.88984 3.39821L4.69045 3.44743C3.95798 3.62826 3.38607 4.20016 3.20524 4.93263L3.15602 5.13202C3.11691 5.29043 2.89173 5.29043 2.85262 5.13202L2.8034 4.93263C2.62257 4.20016 2.05066 3.62826 1.3182 3.44743L1.1188 3.39821C0.960398 3.3591 0.960398 3.13391 1.1188 3.0948L1.3182 3.04559C2.05066 2.86476 2.62257 2.29285 2.8034 1.56038L2.85262 1.36099Z"
          stroke="black"
        />
      </svg>
    ),
    random: (
      <svg width="15" height="15" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0.281218 2.78889C0.206773 2.78889 0.135376 2.76056 0.0827358 2.71011C0.0300952 2.65966 0.000521453 2.59124 0.000521453 2.51989C0.000521453 2.44855 0.0300952 2.38012 0.0827358 2.32968C0.135376 2.27923 0.206773 2.2509 0.281218 2.2509C1.38783 2.2509 2.42557 2.6774 3.19983 3.4519C3.22532 3.47744 3.2453 3.50755 3.25865 3.54049C3.272 3.57344 3.27844 3.60857 3.27762 3.6439C3.2768 3.67923 3.26872 3.71407 3.25385 3.74641C3.23898 3.77875 3.21761 3.80797 3.19096 3.83239C3.1643 3.85682 3.13289 3.87598 3.09851 3.88877C3.06414 3.90157 3.02747 3.90774 2.9906 3.90695C2.95373 3.90616 2.91738 3.89842 2.88364 3.88417C2.84989 3.86992 2.8194 3.84944 2.79391 3.82389C2.47068 3.49653 2.08109 3.23588 1.64902 3.0579C1.21695 2.87993 0.751507 2.78838 0.281218 2.78889ZM7.81878 2.78889C5.90191 2.78889 4.34191 4.27739 4.32939 6.11239C4.33043 6.12039 4.33044 6.1279 4.33044 6.1349C4.33044 8.2769 2.51322 10.0179 0.280697 10.0179C0.206252 10.0179 0.134855 9.98956 0.0822144 9.93911C0.0295737 9.88867 0 9.82024 0 9.7489C0 9.67755 0.0295737 9.60913 0.0822144 9.55868C0.134855 9.50823 0.206252 9.4799 0.280697 9.4799C1.20197 9.479 2.08554 9.12923 2.73893 8.50682C3.39232 7.8844 3.76257 7.03976 3.76904 6.1569C3.768 6.1499 3.768 6.1424 3.768 6.1344C3.768 3.9934 5.58522 2.2514 7.81878 2.2514H10.9174L9.51026 0.904903C9.46316 0.853355 9.43828 0.786566 9.44065 0.71809C9.44302 0.649613 9.47246 0.58456 9.52301 0.536113C9.57357 0.487666 9.64145 0.459449 9.7129 0.45718C9.78436 0.45491 9.85404 0.478758 9.90782 0.523891L11.7939 2.33039C11.8385 2.37701 11.8646 2.4372 11.8675 2.50039C11.8696 2.51889 11.8654 2.5334 11.8612 2.5529C11.8591 2.5764 11.8654 2.60089 11.855 2.62339C11.8445 2.64689 11.8231 2.65939 11.808 2.67889C11.8002 2.68839 11.8023 2.7009 11.7939 2.7109L9.90782 4.5164C9.85496 4.56666 9.78351 4.59486 9.70904 4.59486C9.63458 4.59486 9.56313 4.56666 9.51026 4.5164C9.45788 4.4659 9.42848 4.3976 9.42848 4.3264C9.42848 4.2552 9.45788 4.18689 9.51026 4.13639L10.9174 2.78889H7.81878ZM11.8612 9.7169C11.8591 9.6934 11.8654 9.6689 11.855 9.6454C11.8445 9.6219 11.8231 9.60939 11.808 9.59089C11.7997 9.58089 11.8017 9.56739 11.7939 9.55889L9.79252 7.6409C9.73965 7.59064 9.66821 7.56243 9.59374 7.56243C9.51927 7.56243 9.44783 7.59064 9.39496 7.6409C9.34252 7.69156 9.31309 7.76004 9.31309 7.8314C9.31309 7.90276 9.34252 7.97123 9.39496 8.02189L10.9174 9.4799L7.81878 9.48089C7.3494 9.4818 6.88477 9.39087 6.45334 9.21368C6.02191 9.03648 5.63277 8.77675 5.30974 8.45039C5.2842 8.42495 5.25368 8.40459 5.21992 8.39047C5.18616 8.37635 5.14982 8.36875 5.11299 8.3681C5.07616 8.36745 5.03956 8.37376 5.00528 8.38668C4.971 8.39961 4.93972 8.41888 4.91322 8.4434C4.88653 8.46774 4.86512 8.49689 4.8502 8.52917C4.83528 8.56145 4.82715 8.59623 4.82628 8.63153C4.82541 8.66682 4.83181 8.70194 4.84511 8.73486C4.85842 8.76778 4.87837 8.79787 4.90383 8.82339C5.27941 9.20192 5.73153 9.50316 6.23261 9.7087C6.73369 9.91425 7.27321 10.0198 7.81826 10.0189L10.9169 10.0179L9.50974 11.3654C9.46205 11.4168 9.43669 11.4838 9.43885 11.5525C9.44101 11.6213 9.47052 11.6867 9.52135 11.7352C9.57218 11.7838 9.64046 11.8119 9.71222 11.8138C9.78398 11.8157 9.85377 11.7912 9.9073 11.7454L11.7934 9.9389C11.8382 9.89238 11.8642 9.83213 11.867 9.7689C11.8696 9.7499 11.8654 9.7354 11.8612 9.7169Z"
          fill="black"
        />
      </svg>
    ),
    newFolder: (
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          opacity="0.5"
          d="M18.3307 11.457V9.62195C18.3307 7.42831 18.3307 6.33149 17.6895 5.61856C17.6306 5.55298 17.5681 5.49056 17.5026 5.43158C16.7896 4.79036 15.6928 4.79036 13.4991 4.79036H13.1877C12.2263 4.79036 11.7456 4.79036 11.2977 4.66268C11.0516 4.59253 10.8144 4.49429 10.5908 4.36989C10.1838 4.14342 9.8439 3.80351 9.16406 3.1237L8.70548 2.66516C8.47765 2.43731 8.36373 2.32337 8.24401 2.22412C7.72783 1.79623 7.0946 1.53394 6.42704 1.47151C6.2722 1.45703 6.11108 1.45703 5.78885 1.45703C5.05341 1.45703 4.68569 1.45703 4.37939 1.51482C3.03099 1.76923 1.97626 2.82396 1.72185 4.17236C1.66406 4.47866 1.66406 4.84638 1.66406 5.58182V11.457C1.66406 14.5997 1.66406 16.1711 2.64037 17.1474C3.61669 18.1237 5.18803 18.1237 8.33073 18.1237H11.6641C14.8067 18.1237 16.3781 18.1237 17.3544 17.1474C18.3307 16.1711 18.3307 14.5997 18.3307 11.457Z"
          fill="#DADEE7"
        />
        <path
          d="M10 8C10.4519 8 10.8182 8.36633 10.8182 8.81818V10.1818H12.1818C12.6337 10.1818 13 10.5481 13 11C13 11.4519 12.6337 11.8182 12.1818 11.8182H10.8182V13.1818C10.8182 13.6337 10.4519 14 10 14C9.54815 14 9.18182 13.6337 9.18182 13.1818V11.8182H7.81818C7.36632 11.8182 7 11.4519 7 11C7 10.5481 7.36632 10.1818 7.81818 10.1818H9.18182V8.81818C9.18182 8.36633 9.54815 8 10 8Z"
          fill="#667085"
        />
      </svg>
    ),
    circledAdd: (
      <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M9.5 0.5625C7.83122 0.5625 6.19992 1.05735 4.81238 1.98448C3.42484 2.9116 2.34338 4.22936 1.70477 5.77111C1.06616 7.31286 0.899065 9.00936 1.22463 10.6461C1.55019 12.2828 2.35378 13.7862 3.53379 14.9662C4.7138 16.1462 6.21721 16.9498 7.85393 17.2754C9.49064 17.6009 11.1871 17.4338 12.7289 16.7952C14.2706 16.1566 15.5884 15.0752 16.5155 13.6876C17.4427 12.3001 17.9375 10.6688 17.9375 9C17.935 6.76301 17.0452 4.61837 15.4634 3.03658C13.8816 1.45479 11.737 0.565031 9.5 0.5625ZM9.5 16.3125C8.05373 16.3125 6.63993 15.8836 5.4374 15.0801C4.23486 14.2766 3.2976 13.1346 2.74413 11.7984C2.19067 10.4622 2.04586 8.99189 2.32801 7.5734C2.61017 6.15492 3.30661 4.85195 4.32929 3.82928C5.35196 2.80661 6.65492 2.11016 8.07341 1.82801C9.49189 1.54585 10.9622 1.69067 12.2984 2.24413C13.6346 2.7976 14.7766 3.73486 15.5801 4.93739C16.3836 6.13993 16.8125 7.55372 16.8125 9C16.8103 10.9387 16.0391 12.7974 14.6683 14.1683C13.2974 15.5391 11.4387 16.3103 9.5 16.3125Z"
          fill="white"
        />
        <path
          d="M12.3125 8.4375H10.0625V6.1875C10.0625 6.03832 10.0032 5.89524 9.89775 5.78975C9.79226 5.68426 9.64918 5.625 9.5 5.625C9.35082 5.625 9.20774 5.68426 9.10225 5.78975C8.99676 5.89524 8.9375 6.03832 8.9375 6.1875V8.4375H6.6875C6.53832 8.4375 6.39524 8.49676 6.28975 8.60225C6.18426 8.70774 6.125 8.85082 6.125 9C6.125 9.14918 6.18426 9.29226 6.28975 9.39775C6.39524 9.50324 6.53832 9.5625 6.6875 9.5625H8.9375V11.8125C8.9375 11.9617 8.99676 12.1048 9.10225 12.2102C9.20774 12.3157 9.35082 12.375 9.5 12.375C9.64918 12.375 9.79226 12.3157 9.89775 12.2102C10.0032 12.1048 10.0625 11.9617 10.0625 11.8125V9.5625H12.3125C12.4617 9.5625 12.6048 9.50324 12.7102 9.39775C12.8157 9.29226 12.875 9.14918 12.875 9C12.875 8.85082 12.8157 8.70774 12.7102 8.60225C12.6048 8.49676 12.4617 8.4375 12.3125 8.4375Z"
          fill="white"
        />
      </svg>
    ),
    template: (
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M1.33594 10.4987V11.9989C1.33594 12.9323 1.33594 13.3988 1.5176 13.7553C1.67738 14.0689 1.93216 14.3241 2.24577 14.4839C2.60194 14.6654 3.06843 14.6654 4.00003 14.6654H8.0026M1.33594 10.4987V5.4987M1.33594 10.4987H8.0026M8.0026 14.6654V10.4987M8.0026 14.6654H12.0056C12.9372 14.6654 13.403 14.6654 13.7592 14.4839C14.0728 14.3241 14.328 14.0689 14.4878 13.7553C14.6693 13.3991 14.6693 12.9333 14.6693 12.0017V10.4987M1.33594 5.4987V3.99886C1.33594 3.06544 1.33594 2.59838 1.5176 2.24186C1.67738 1.92826 1.93216 1.67347 2.24577 1.51369C2.60229 1.33203 3.06935 1.33203 4.00277 1.33203H8.0026M1.33594 5.4987H8.0026M8.0026 10.4987V5.4987M8.0026 10.4987H14.6693M8.0026 1.33203H12.0028C12.9362 1.33203 13.4027 1.33203 13.7592 1.51369C14.0728 1.67347 14.328 1.92826 14.4878 2.24186C14.6693 2.59803 14.6693 3.06452 14.6693 3.99612V5.4987M8.0026 1.33203V5.4987M8.0026 5.4987H14.6693M14.6693 5.4987V10.4987"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    newUser: (
      <svg width="25" height="25" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4.66667 6.0013H2M3.33333 4.66797V7.33464" stroke="#702DFF" strokeLinecap="round" strokeLinejoin="round" />
        <path
          d="M5.69531 3.33333C6.14036 2.83575 6.70286 2.45748 7.33155 2.23298C7.96023 2.00848 8.63511 1.94489 9.29466 2.04802C9.95421 2.15115 10.5775 2.41771 11.1076 2.8234C11.6378 3.2291 12.0579 3.76102 12.3299 4.3707C12.6018 4.98038 12.7168 5.64842 12.6644 6.31393C12.612 6.97944 12.3939 7.62125 12.0299 8.18087C11.6659 8.74048 11.1677 9.20011 10.5806 9.51785C9.9935 9.83559 9.33621 10.0013 8.66865 9.99999C8.10722 9.99886 7.55233 9.87955 7.04005 9.64983C6.52777 9.42011 6.06959 9.08512 5.69531 8.66666"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M6.0426 9.10156C5.23462 9.47549 4.55047 10.0728 4.07088 10.8228C3.59129 11.5729 3.33628 12.4446 3.33594 13.3349C3.33594 13.5117 3.40618 13.6813 3.5312 13.8063C3.65622 13.9313 3.82579 14.0016 4.0026 14.0016H13.3359C13.5127 14.0016 13.6823 13.9313 13.8073 13.8063C13.9324 13.6813 14.0026 13.5117 14.0026 13.3349C14.0017 12.4439 13.7457 11.5717 13.2649 10.8215C12.784 10.0714 12.0985 9.47452 11.2893 9.10156"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    nextArrow: (
      <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M1.16406 7.0013H12.8307M12.8307 7.0013L6.9974 1.16797M12.8307 7.0013L6.9974 12.8346"
          stroke="#414651"
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    previousArrow: (
      <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M12.8307 7.0013H1.16406M1.16406 7.0013L6.9974 12.8346M1.16406 7.0013L6.9974 1.16797"
          stroke="#414651"
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    candidateIconSvg: (
      <svg width="85" height="82" viewBox="0 0 76 82" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="15.5312" y="22" width="59" height="59" rx="29.5" fill="#EDE9FE" />
        <rect x="15.5312" y="22" width="59" height="59" rx="29.5" stroke="#E1E4E8" />
        <path
          d="M43.9027 46.3182V56.5H42.2621L37.4645 49.5646H37.38V56.5H35.5355V46.3182H37.1861L41.9787 53.2585H42.0682V46.3182H43.9027ZM47.22 56.5H45.2512L48.8358 46.3182H51.1127L54.7022 56.5H52.7335L50.014 48.4062H49.9345L47.22 56.5ZM47.2846 52.5078H52.6539V53.9893H47.2846V52.5078Z"
          fill="#8D5BF8"
        />
        {/* <path
          d="M30.5455 15.1805C27.3858 10.7024 25.1686 5.57064 22.9885 0.5C23.0191 2.80632 23.0483 5.11263 23.0789 7.41895C23.0988 8.98186 23.1108 10.5964 22.5206 12.0384C21.9304 13.4803 20.5732 14.7157 19.0472 14.6315C18.219 14.5866 17.4573 14.1721 16.7501 13.7304C12.6293 11.1604 9.35527 7.42031 6.14502 3.73591C6.14236 4.32438 6.4388 4.86664 6.72991 5.37493C9.29146 9.85709 12.1867 14.4697 12.3608 19.6572C12.3954 20.6792 12.2678 21.8208 11.514 22.4922C10.8427 23.0902 9.87235 23.1405 8.98305 23.135C6.29655 23.1201 3.61272 22.8673 0.96875 22.3821C2.85901 23.9695 4.84098 25.4413 6.90006 26.7895C8.75974 28.0059 10.9345 29.6802 13.0494 30.3679C15.1244 31.042 16.8804 28.9749 18.4024 27.802C20.5399 26.1549 22.6761 24.5077 24.8136 22.8605C26.2599 21.7461 28.6234 19.6694 30.192 18.271C31.068 17.4882 31.2275 16.1468 30.5455 15.1805Z"
          fill="#EE8E3C"
        /> */}
      </svg>
    ),
    filters: (
      <svg width="18" height="12" viewBox="0 0 18 12" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 6H14M1.5 1H16.5M6.5 11H11.5" stroke="#344054" strokeWidth="1.67" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
    ),
    userWithShield: (
      <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M0.5 19C0.5 15.4735 3.43367 12.5561 7.25 12.0709M18.3567 13.2038C18.28 13.2079 18.2027 13.21 18.125 13.21C16.9725 13.21 15.9212 12.7524 15.125 12C14.3288 12.7524 13.2775 13.2099 12.125 13.2099C12.0473 13.2099 11.9701 13.2078 11.8933 13.2037C11.7997 13.5853 11.75 13.9855 11.75 14.3979C11.75 16.6121 13.1841 18.4725 15.125 19C17.0659 18.4725 18.5 16.6121 18.5 14.3979C18.5 13.9855 18.4503 13.5853 18.3567 13.2038ZM12.875 5C12.875 7.20914 10.8602 9 8.375 9C5.88972 9 3.875 7.20914 3.875 5C3.875 2.79086 5.88972 1 8.375 1C10.8602 1 12.875 2.79086 12.875 5Z"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    blueStars: (
      <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M18.1054 8.41788L13.4217 6.68419L11.6727 0.952089C11.6397 0.844421 11.573 0.750191 11.4824 0.683247C11.3919 0.616302 11.2822 0.580175 11.1696 0.580175C11.057 0.580175 10.9473 0.616302 10.8567 0.683247C10.7662 0.750191 10.6995 0.844421 10.6664 0.952089L8.918 6.68419L4.23379 8.41788C4.13307 8.45527 4.0462 8.52258 3.98485 8.61079C3.92351 8.69899 3.89062 8.80386 3.89062 8.9113C3.89062 9.01874 3.92351 9.1236 3.98485 9.21181C4.0462 9.30001 4.13307 9.36733 4.23379 9.40472L8.9159 11.1379L10.6648 17.0447C10.697 17.1536 10.7636 17.2492 10.8545 17.3172C10.9455 17.3851 11.056 17.4219 11.1696 17.4219C11.2831 17.4219 11.3937 17.3851 11.4846 17.3172C11.5756 17.2492 11.6421 17.1536 11.6743 17.0447L13.4238 11.1379L18.1059 9.40472C18.2071 9.36773 18.2944 9.30054 18.3561 9.21225C18.4178 9.12395 18.4509 9.01883 18.4508 8.91112C18.4507 8.8034 18.4176 8.69831 18.3558 8.61008C18.294 8.52186 18.2066 8.45476 18.1054 8.41788Z"
          fill="#AA8ED6"
        />
        <path
          d="M7.577 14.2087L6.35963 13.7582L5.88595 12.0214C5.85564 11.9099 5.7895 11.8115 5.69771 11.7413C5.60592 11.6711 5.49359 11.6331 5.37805 11.6331C5.26251 11.6331 5.15018 11.6711 5.05839 11.7413C4.9666 11.8115 4.90046 11.9099 4.87016 12.0214L4.39647 13.7582L3.1791 14.2087C3.07838 14.2461 2.99151 14.3135 2.93016 14.4017C2.86882 14.4899 2.83594 14.5947 2.83594 14.7022C2.83594 14.8096 2.86882 14.9145 2.93016 15.0027C2.99151 15.0909 3.07838 15.1582 3.1791 15.1956L4.39016 15.6445L4.86752 17.5509C4.89605 17.6647 4.96177 17.7657 5.05427 17.8378C5.14677 17.91 5.26073 17.9492 5.37805 17.9492C5.49537 17.9492 5.60933 17.91 5.70183 17.8378C5.79433 17.7657 5.86005 17.6647 5.88858 17.5509L6.36595 15.6445L7.577 15.1956C7.67772 15.1582 7.76459 15.0909 7.82594 15.0027C7.88728 14.9145 7.92016 14.8096 7.92016 14.7022C7.92016 14.5947 7.88728 14.4899 7.82594 14.4017C7.76459 14.3135 7.67772 14.2461 7.577 14.2087Z"
          fill="#FCAB40"
        />
        <path
          d="M5.29646 2.80625L4.05172 2.34572L3.5912 1.10098C3.55391 1.00008 3.4866 0.913029 3.39833 0.851547C3.31007 0.790065 3.20508 0.757105 3.09751 0.757105C2.98994 0.757105 2.88496 0.790065 2.79669 0.851547C2.70842 0.913029 2.64111 1.00008 2.60383 1.10098L2.14277 2.34572L0.898564 2.80625C0.797664 2.84353 0.710612 2.91084 0.64913 2.99911C0.587648 3.08737 0.554688 3.19236 0.554688 3.29993C0.554688 3.4075 0.587648 3.51248 0.64913 3.60075C0.710612 3.68902 0.797664 3.75633 0.898564 3.79361L2.14277 4.25414L2.60383 5.49888C2.64089 5.6 2.70811 5.68731 2.7964 5.74899C2.8847 5.81067 2.98981 5.84375 3.09751 5.84375C3.20522 5.84375 3.31032 5.81067 3.39862 5.74899C3.48691 5.68731 3.55414 5.6 3.5912 5.49888L4.05172 4.25414L5.29646 3.79361C5.39736 3.75633 5.48441 3.68902 5.54589 3.60075C5.60737 3.51248 5.64033 3.4075 5.64033 3.29993C5.64033 3.19236 5.60737 3.08737 5.54589 2.99911C5.48441 2.91084 5.39736 2.84353 5.29646 2.80625Z"
          fill="#5DADEC"
        />
      </svg>
    ),
    average: (
      <svg width="48" height="45" viewBox="0 0 48 45" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="0.515625" y="0.5" width="46.2655" height="44" rx="22" fill="#EDE9FE" />
        <rect x="0.515625" y="0.5" width="46.2655" height="44" rx="22" stroke="#E1E4E8" />
        <g clipPath="url(#clip0_16761_9662)">
          <path
            d="M29.4623 22.7752L16.5869 22.9999M22.9337 17.4404C23.065 17.4381 23.1918 17.4881 23.2862 17.5793C23.3807 17.6705 23.435 17.7955 23.4373 17.9267C23.4396 18.058 23.3896 18.1848 23.2984 18.2792C23.2072 18.3737 23.0823 18.428 22.951 18.4303C22.886 18.4314 22.8214 18.4197 22.7609 18.3959C22.7004 18.3721 22.6452 18.3366 22.5985 18.2914C22.5517 18.2463 22.5143 18.1924 22.4884 18.1327C22.4625 18.0731 22.4485 18.009 22.4474 17.944C22.4463 17.879 22.458 17.8144 22.4818 17.7539C22.5056 17.6935 22.5411 17.6382 22.5863 17.5915C22.6314 17.5447 22.6854 17.5073 22.745 17.4814C22.8046 17.4555 22.8687 17.4416 22.9337 17.4404ZM23.1065 27.3392C23.2378 27.3369 23.3646 27.3868 23.459 27.478C23.5534 27.5692 23.6078 27.6942 23.6101 27.8255C23.6124 27.9568 23.5624 28.0836 23.4712 28.178C23.38 28.2724 23.255 28.3268 23.1238 28.3291C23.0588 28.3302 22.9942 28.3185 22.9337 28.2947C22.8732 28.2709 22.818 28.2354 22.7713 28.1902C22.7245 28.1451 22.6871 28.0911 22.6612 28.0315C22.6353 27.9719 22.6213 27.9078 22.6202 27.8428C22.6191 27.7778 22.6307 27.7132 22.6546 27.6527C22.6784 27.5922 22.7139 27.537 22.7591 27.4903C22.8042 27.4435 22.8581 27.4061 22.9177 27.3802C22.9774 27.3542 23.0415 27.3403 23.1065 27.3392Z"
            stroke="#7633E7"
            strokeWidth="0.989558"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </g>
        <defs>
          <clipPath id="clip0_16761_9662">
            <rect width="16" height="16" fill="white" transform="translate(15.6484 14.5)" />
          </clipPath>
        </defs>
      </svg>
    ),
    nike: (
      <svg width="48" height="45" viewBox="0 0 48 45" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="0.515625" y="0.5" width="46.2655" height="44" rx="22" fill="#E4ECFF" />
        <rect x="0.515625" y="0.5" width="46.2655" height="44" rx="22" stroke="#E1E4E8" />
        <rect x="1.64844" y="0.5" width="44" height="44" rx="22" fill="#EDE9FE" />
        <rect x="1.64844" y="0.5" width="44" height="44" rx="22" stroke="#E1E4E8" />
        <path
          d="M32.8085 16.2773L21.2327 27.8531L15.6569 22.2773C15.6012 22.2216 15.5351 22.1774 15.4623 22.1472C15.3895 22.1171 15.3115 22.1016 15.2327 22.1016C15.1539 22.1016 15.0759 22.1171 15.0031 22.1472C14.9304 22.1774 14.8642 22.2216 14.8085 22.2773C14.7528 22.333 14.7086 22.3991 14.6785 22.4719C14.6483 22.5447 14.6328 22.6227 14.6328 22.7015C14.6328 22.7803 14.6483 22.8583 14.6785 22.931C14.7086 23.0038 14.7528 23.07 14.8085 23.1257L20.8085 29.1257C20.8641 29.1816 20.9302 29.226 21.003 29.2562C21.0758 29.2865 21.1539 29.3021 21.2327 29.3021C21.3116 29.3021 21.3896 29.2865 21.4624 29.2562C21.5352 29.226 21.6013 29.1816 21.6569 29.1257L33.6569 17.1257C33.7694 17.0132 33.8326 16.8606 33.8326 16.7015C33.8326 16.5424 33.7694 16.3898 33.6569 16.2773C33.5444 16.1648 33.3918 16.1016 33.2327 16.1016C33.0736 16.1016 32.921 16.1648 32.8085 16.2773Z"
          fill="#702DFF"
        />
      </svg>
    ),
    userWithNike: (
      <svg width="48" height="45" viewBox="0 0 48 45" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="0.515625" y="0.5" width="46.2655" height="44" rx="22" fill="#E4ECFF" />
        <rect x="0.515625" y="0.5" width="46.2655" height="44" rx="22" stroke="#E1E4E8" />
        <rect x="1.64844" y="0.5" width="44" height="44" rx="22" fill="#EDE9FE" />
        <rect x="1.64844" y="0.5" width="44" height="44" rx="22" stroke="#E1E4E8" />
        <g clipPath="url(#clip0_16761_9695)">
          <path
            d="M23.6465 21.8873C24.5484 21.8885 25.4305 21.6222 26.181 21.122C26.9315 20.6218 27.5168 19.9102 27.8627 19.0773C28.2087 18.2443 28.2999 17.3275 28.1247 16.4428C27.9495 15.558 27.5157 14.7451 26.8784 14.107C26.2411 13.4688 25.4288 13.034 24.5443 12.8576C23.6598 12.6812 22.7428 12.7711 21.9094 13.116C21.0761 13.4608 20.3637 14.0452 19.8625 14.795C19.3613 15.5448 19.0938 16.4265 19.0938 17.3284C19.0937 18.5365 19.5732 19.6951 20.4269 20.5499C21.2805 21.4047 22.4385 21.8857 23.6465 21.8873ZM23.6465 13.9856C24.307 13.9844 24.9529 14.1793 25.5026 14.5454C26.0522 14.9116 26.4808 15.4326 26.7341 16.0426C26.9874 16.6525 27.054 17.3239 26.9255 17.9718C26.7969 18.6196 26.479 19.2147 26.012 19.6817C25.545 20.1487 24.9499 20.4666 24.3021 20.5952C23.6543 20.7237 22.9829 20.6571 22.3729 20.4038C21.763 20.1505 21.2419 19.7219 20.8758 19.1722C20.5096 18.6226 20.3148 17.9766 20.316 17.3162C20.3176 16.4334 20.669 15.5872 21.2933 14.9629C21.9175 14.3387 22.7637 13.9873 23.6465 13.9856Z"
            fill="#702DFF"
          />
          <path
            d="M16.3164 30.9888V27.2488C17.2598 26.2586 18.3991 25.4756 19.6616 24.9496C20.9241 24.4235 22.2822 24.166 23.6497 24.1933C25.5213 24.1681 27.3634 24.6609 28.9725 25.6172L29.7975 24.6944C27.9579 23.5357 25.8237 22.9311 23.6497 22.9527C22.0591 22.9122 20.4791 23.2231 19.0225 23.8632C17.5658 24.5033 16.2683 25.457 15.2225 26.6561C15.1409 26.7611 15.0959 26.8898 15.0941 27.0227V30.9888C15.0859 31.3217 15.2099 31.6443 15.439 31.886C15.668 32.1276 15.9835 32.2687 16.3164 32.2783H23.888L22.7269 31.0561L16.3164 30.9888Z"
            fill="#702DFF"
          />
          <path
            d="M30.9781 30.9889V31.0562H29.0531L27.9531 32.2784H30.9781C31.3067 32.2688 31.6184 32.1311 31.8468 31.8948C32.0752 31.6584 32.2021 31.3421 32.2003 31.0134V27.5117L30.9781 28.8745V30.9889Z"
            fill="#702DFF"
          />
          <path
            d="M33.8896 22.8773C33.7687 22.7697 33.6102 22.7145 33.4487 22.7236C33.2871 22.7328 33.1358 22.8056 33.0279 22.9262L25.9268 30.8706L22.749 27.4545C22.6969 27.3935 22.6332 27.3434 22.5617 27.307C22.4901 27.2706 22.4121 27.2487 22.3321 27.2424C22.2521 27.2362 22.1717 27.2458 22.0953 27.2706C22.019 27.2955 21.9484 27.3351 21.8874 27.3873C21.8276 27.4417 21.7793 27.5073 21.745 27.5805C21.7108 27.6536 21.6915 27.7329 21.688 27.8135C21.6846 27.8942 21.6973 27.9748 21.7252 28.0506C21.7531 28.1264 21.7958 28.1959 21.8507 28.2551L25.939 32.6551L33.9385 23.7206C34.0405 23.6008 34.0923 23.4463 34.0832 23.2892C34.0741 23.1321 34.0048 22.9845 33.8896 22.8773Z"
            fill="#702DFF"
          />
        </g>
        <defs>
          <clipPath id="clip0_16761_9695">
            <rect width="22" height="22" fill="white" transform="translate(12.6484 11.5)" />
          </clipPath>
        </defs>
      </svg>
    ),
    calender: (
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clipPath="url(#clip0_16761_9724)">
          <path
            d="M15.1962 17.87H2.8071C2.09829 17.8685 1.41895 17.5863 0.917744 17.0851C0.416541 16.5839 0.134307 15.9045 0.132812 15.1957V4.40085C0.134174 3.69156 0.416718 3.01174 0.918505 2.51044C1.42029 2.00913 2.10038 1.72724 2.80967 1.72656H15.1988C15.9081 1.72792 16.5879 2.01047 17.0892 2.51225C17.5905 3.01404 17.8724 3.69413 17.8731 4.40342V15.1931C17.8723 15.9028 17.59 16.5832 17.0882 17.0851C16.5863 17.5869 15.9059 17.8692 15.1962 17.87ZM2.8071 3.00971C2.43827 3.01011 2.08466 3.15681 1.82386 3.41761C1.56306 3.67841 1.41636 4.03202 1.41596 4.40085V15.1931C1.41718 15.5615 1.56422 15.9143 1.82492 16.1746C2.08562 16.4348 2.43876 16.5812 2.8071 16.5817H15.1962C15.5642 16.5806 15.9167 16.434 16.1769 16.1738C16.4371 15.9136 16.5837 15.5611 16.5848 15.1931V4.40085C16.5839 4.03287 16.4373 3.68023 16.1771 3.42003C15.9169 3.15983 15.5642 3.01323 15.1962 3.01228L2.8071 3.00971Z"
            fill="#535862"
          />
          <path
            d="M17.2274 8.91462H0.791295C0.620798 8.91462 0.457285 8.84689 0.336726 8.72633C0.216167 8.60577 0.148438 8.44226 0.148438 8.27176C0.148438 8.10127 0.216167 7.93775 0.336726 7.81719C0.457285 7.69664 0.620798 7.62891 0.791295 7.62891H17.2274C17.3978 7.62891 17.5614 7.69664 17.6819 7.81719C17.8025 7.93775 17.8702 8.10127 17.8702 8.27176C17.8702 8.44226 17.8025 8.60577 17.6819 8.72633C17.5614 8.84689 17.3978 8.91462 17.2274 8.91462Z"
            fill="#535862"
          />
          <path
            d="M4.86161 4.70862C4.77717 4.70869 4.69354 4.69211 4.61552 4.65982C4.53749 4.62754 4.4666 4.58019 4.40689 4.52048C4.34718 4.46077 4.29983 4.38988 4.26755 4.31185C4.23526 4.23383 4.21868 4.1502 4.21875 4.06576V0.771763C4.21875 0.601267 4.28648 0.437754 4.40704 0.317195C4.5276 0.196636 4.69111 0.128906 4.86161 0.128906C5.0321 0.128906 5.19562 0.196636 5.31618 0.317195C5.43674 0.437754 5.50446 0.601267 5.50446 0.771763V4.06576C5.50446 4.23626 5.43674 4.39977 5.31618 4.52033C5.19562 4.64089 5.0321 4.70862 4.86161 4.70862Z"
            fill="#535862"
          />
          <path
            d="M13.135 4.70862C13.0506 4.70869 12.967 4.69211 12.889 4.65982C12.8109 4.62754 12.74 4.58019 12.6803 4.52048C12.6206 4.46077 12.5733 4.38988 12.541 4.31185C12.5087 4.23383 12.4921 4.1502 12.4922 4.06576V0.771763C12.4922 0.601267 12.5599 0.437754 12.6805 0.317195C12.801 0.196636 12.9645 0.128906 13.135 0.128906C13.3055 0.128906 13.4691 0.196636 13.5896 0.317195C13.7102 0.437754 13.7779 0.601267 13.7779 0.771763V4.06576C13.7779 4.23626 13.7102 4.39977 13.5896 4.52033C13.4691 4.64089 13.3055 4.70862 13.135 4.70862Z"
            fill="#535862"
          />
        </g>
        <defs>
          <clipPath id="clip0_16761_9724">
            <rect width="18" height="18" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
  };

  const iconMap = {
    dashboard: CustomIcon.dashboard,
    users: CustomIcon.users,
    tests: CustomIcon.tests,
    questions: CustomIcon.questions,
    screening: CustomIcon.screening,
    applicant: CustomIcon.applicant,
    interview: CustomIcon.interview,
    archive: CustomIcon.archive,
    export: CustomIcon.export,
    assign: CustomIcon.assign,
    more: CustomIcon.more,
    eye: CustomIcon.eye,
    edit: CustomIcon.edit,
    lampGuidance: CustomIcon.lampGuidance,
    settings: CustomIcon.settings,
    infoItalic: CustomIcon.infoItalic,
    hourglass: CustomIcon.hourglass,
    customize: CustomIcon.customize,
    clock: CustomIcon.clock,
    clockTwo: CustomIcon.clockTwo,
    questionsCircle: CustomIcon.questionsCircle,
    book: CustomIcon.book,
    arrowUp: CustomIcon.arrowUp,
    arrowDown: CustomIcon.arrowDown,
    arrowRight: CustomIcon.arrowRight,
    info: CustomIcon.info,
    warning: CustomIcon.warning,
    warningLarge: CustomIcon.warningLarge,
    en: CustomIcon.en,
    discount: CustomIcon.discount,
    copy: CustomIcon.copy,
    download: CustomIcon.download,
    trash: CustomIcon.trash,
    percent: CustomIcon.percent,
    organization: CustomIcon.organization,
    cloudArrowDown: CustomIcon.cloudArrowDown,
    arrowDownRight: CustomIcon.arrowDownRight,
    arrowUpRight: CustomIcon.arrowUpRight,
    envelope: CustomIcon.envelope,
    greenRightSign: CustomIcon.greenRightSign,
    redWrongSign: CustomIcon.redWrongSign,
    imoChatInterviewAssign: CustomIcon.imoChatInterviewAssign,
    interactiveInterviewAssign: CustomIcon.interactiveInterviewAssign,
    questionMarkBorder: CustomIcon.questionMarkBorder,
    interActiveAiInterviewStars: CustomIcon.interActiveAiInterviewStars,
    manInSuit: CustomIcon.manInSuit,
    questionInBorder: CustomIcon.questionInBorder,
    clockThree: CustomIcon.clockThree,
    charts: CustomIcon.charts,
    stars: CustomIcon.stars,
    random: CustomIcon.random,
    newFolder: CustomIcon.newFolder,
    circledAdd: CustomIcon.circledAdd,
    template: CustomIcon.template,
    newUser: CustomIcon.newUser,
    nextArrow: CustomIcon.nextArrow,
    previousArrow: CustomIcon.previousArrow,
    candidateIconSvg: CustomIcon.candidateIconSvg,
    filters: CustomIcon.filters,
    userWithShield: CustomIcon.userWithShield,
    blueStars: CustomIcon.blueStars,
    average: CustomIcon.average,
    nike: CustomIcon.nike,
    userWithNike: CustomIcon.userWithNike,
    calender: CustomIcon.calender,
  };

  return <div onClick={onClick}>{iconMap[definedIcon] || null}</div>;
};
