// React
import { useContext, useState, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';

// Prop Types
import PropTypes from 'prop-types';

// Object Path
import { get } from 'object-path';

// Flowbite
import { Pagination, Spinner, ToggleSwitch, Tooltip, Dropdown, Checkbox as FlowbiteCheckbox, Radio as FlowbiteRadio } from 'flowbite-react';

// Context
import { AppContext } from '/src/components/provider';

// UI
import {
  Button,
  Icon,
  EnumText,
  useScreenSize,
  Checkbox,
  LookupText,
  CustomIcon,
  Card,
  StaticData,
  SidebarFilterPage,
  ToggleFilter,
  Jumbotron,
  NoDataFound,
  NoDataMatches,
} from '/src';

// Components
import { TablePlaceholder } from './table-placeholder';

export const Table = ({
  title,
  searchPlaceholder,
  addButtonLabel,
  addButtonPath,
  columns,
  rows,
  backupRows,
  rowKey,
  slots,
  loading,
  ready,
  count,
  search,
  pagination,
  filters,
  setFilters,
  filterFeedData,
  drawerFilter,
  actions,
  dropdown,
  breakpoint,
  onClickAdd,
  singleSelectedRow,
  multiSelectedRow,
  noDataFound,
  showMoreMap = false,
  setShowMoreMap,
  drawerClearAll,
  noDataFoundIconWidth,
  noDataFoundIconHeight,
  hideJumbotron,
  isScrollableTabsExists,
}) => {
  // Context
  const { isSidebarVisible } = useContext(AppContext);

  // State
  const screen = useScreenSize();

  // React
  const location = useLocation();

  // Single Selected Row
  const { selectedIdSingle, setSelectedIdSingle } = singleSelectedRow || {};

  // Multi Selected Row
  const { selectedIds, setSelectedIds, handleArchiveSelectedIds, handleLockSelectedIds, lockUnlockAll, handleDownloadExcelSelectedIds } =
    multiSelectedRow || {};

  // Pagination
  const { page, size } = pagination;
  const pagesCount = Math.max(Math.ceil(count / size), 1);
  const showingText = `${count ? page * size - size + 1 : count} - ${page * size > count ? count : page * size}`;
  const isPaginationActive = !!pagination.update;

  // Handle Ready
  if (!ready) {
    return <TablePlaceholder />;
  }

  const renderCell = (column, row) => {
    const value = get(row, column.key);

    if (column.buttons || column.locked || column.report) {
      let buttonsData, lockedData, reportData;
      if (column.buttons) {
        buttonsData = (
          <div className="flex gap-3">
            {column.buttons(value, row).map(({ label, color, ...button }, index) => {
              const element = () => {
                if (button.path) {
                  return (
                    <Link to={button.path}>
                      {button.icon && (
                        <Icon className={`cursor-pointer ${color} ${button.className}`} key={button.icon} width={20} icon={button.icon} {...button} />
                      )}{' '}
                      {button.customIcon && (
                        <CustomIcon
                          definedIcon={button.customIcon}
                          className={`cursor-pointer ${color} ${button.className}`}
                          width={button.iconWidth || '20'}
                          height={button.iconHeight || '20'}
                          onClick={button.onClick}
                        />
                      )}
                    </Link>
                  );
                } else if (button.dropDown) {
                  return (
                    <div className="relative" key={button.icon}>
                      <Dropdown
                        label=""
                        dismissOnClick={false}
                        className="sm:ml-11 lg:ml-5"
                        inline
                        renderTrigger={() => (
                          <span>
                            {button.icon && <Icon className={`cursor-pointer ${color}`} key={index} width={20} icon={button.icon} />}
                            {button.customIcon && (
                              <CustomIcon
                                definedIcon={button.customIcon}
                                className={`cursor-pointer ${color} ${button.className}`}
                                width={button.iconWidth || '20'}
                                height={button.iconHeight || '20'}
                              />
                            )}
                          </span>
                        )}
                      >
                        {button.dropDown.map(({ label, color, ...subButton }, index) => (
                          <div className="min-w-40" key={subButton.icon}>
                            <Dropdown.Item onClick={() => subButton.onClick()}>
                              <div className="flex gap-2 cursor-pointer w-fit">
                                {subButton.icon && <Icon className={`cursor-pointer ${color}`} key={index} width={20} icon={subButton.icon} />}
                                {subButton.customIcon && (
                                  <CustomIcon
                                    definedIcon={subButton.customIcon}
                                    className={`cursor-pointer ${color} ${subButton.className}`}
                                    width={button.iconWidth || '20'}
                                    height={button.iconHeight || '20'}
                                  />
                                )}
                                <p className="text-nowrap">{label}</p>
                                {subButton.element}
                              </div>
                            </Dropdown.Item>
                            {button.dropDown.length - 1 > index && <hr className="dark:border-gray-500" />}
                          </div>
                        ))}
                      </Dropdown>
                    </div>
                  );
                } else {
                  return (
                    <div>
                      {button.icon && <Icon className={`cursor-pointer ${color}`} key={index} width={20} {...button} />}
                      {button.customIcon && (
                        <CustomIcon
                          definedIcon={button.customIcon}
                          className={`cursor-pointer ${color} ${button.className}`}
                          width={button.iconWidth || '20'}
                          height={button.iconHeight || '20'}
                          onClick={button.onClick}
                        />
                      )}
                    </div>
                  );
                }
              };
              return !!label ? (
                <Tooltip
                  key={index}
                  content={label}
                  placement="bottom"
                  arrow={false}
                  className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                >
                  {element()}
                </Tooltip>
              ) : (
                element()
              );
            })}
          </div>
        );
      }

      if (column.locked) {
        lockedData = (
          <div>
            {column.locked(value, row).map(({ label, switchHandler }, index) => {
              const data = <ToggleSwitch key={index} checked={value} onChange={(newValue) => switchHandler(row._id, newValue)} sizing="sm" />;
              if (label) {
                return (
                  <Tooltip
                    key={index}
                    content={value ? label.unlock : label.lock}
                    placement="bottom"
                    arrow={false}
                    className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                  >
                    {data}
                  </Tooltip>
                );
              }
              return data;
            })}
          </div>
        );
      }

      if (column.report) {
        reportData = (
          <div>
            {column.report(value, row).map(({ label, color, handelReport, ...button }, index) => {
              const data = <Icon className="cursor-pointer" key={index} width={20} style={{ color: color }} {...button} />;
              if (label) {
                return (
                  <Tooltip
                    key={index}
                    content={label}
                    placement="bottom"
                    arrow={false}
                    className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                  >
                    {data}
                  </Tooltip>
                );
              }
              return data;
            })}
          </div>
        );
      }

      return (
        <div className="flex gap-3">
          {row.status === 3 && reportData}
          {buttonsData}
          {lockedData}
        </div>
      );
    }
    if (column.enum) {
      const text = <EnumText name={column.enum} value={value} />;
      return (
        <div className="w-full relative">
          <div className="lg:truncate break-all">{text}</div>
          {/* {screen.gt.lg() && (
            <Tooltip content={text} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 w-fit text-sm">
              <div className="absolute top-0 left-0 w-full h-full"></div>
            </Tooltip>
          )} */}
        </div>
      );
    }

    if (column.lookup) {
      const text = <LookupText lookup={column.lookup} id={value} />;
      return (
        <div className="w-full relative">
          <div className="lg:truncate break-all">{text}</div>
          {screen.gt.lg() && (
            <Tooltip content={text} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 w-fit text-sm">
              <div className="absolute top-0 left-0 w-full h-full"></div>
            </Tooltip>
          )}
        </div>
      );
    }

    if (slots[column.key]) {
      return slots[column.key](value, row);
    }

    if (!value) {
      return <p>—</p>;
    }

    return <p className="truncate">{value}</p>;
  };

  const toggleShowMore = (rowKey) => {
    setShowMoreMap((prevMap) => ({
      ...prevMap,
      [rowKey]: !prevMap[rowKey],
    }));
  };

  const handleBlockMobileView = (column, row, index) => (
    <div key={column.key} className="w-full border-[#EAECF0] dark:border-gray-700">
      <p
        className={`px-3 text-sm text-[#667085] font-normal dark:text-gray-400 ${
          index === 0 ? 'bg-[#F9FAFB] dark:bg-darkGrayBackground border-b py-2 rounded-t-lg' : 'pt-2.5 pb-2'
        }`}
      >
        {column.label}
      </p>
      <div
        className={`${!showMoreMap[row[rowKey]] && 'truncate'} ${
          index === 0 ? 'py-3' : 'pb-2'
        } px-3 text-sm text-gray-900 dark:text-gray-100 font-normal`}
      >
        {renderCell(column, row)}
      </div>
    </div>
  );

  // Multi-select row
  const handleCheckboxChange = (event) => {
    const { value, checked } = event.target;
    if (checked) {
      setSelectedIds((prevValues) => [...prevValues, value]);
    } else {
      setSelectedIds((prevValues) => prevValues.filter((val) => val !== value));
    }
  };
  const handleCheckboxSelectAll = (event) => {
    if (event.target.checked) {
      setSelectedIds(rows.map((row) => row._id));
    } else {
      setSelectedIds([]);
    }
  };

  /* Don't forget to enhance the same function in create template with no table */
  const handleHeight = () => {
    if (hideJumbotron) {
      if (isScrollableTabsExists) {
        return {
          table: '2xl:max-h-[calc(100vh-255px)]',
          sidebarFilter: '2xl:max-h-[calc(100vh-255px)]',
        };
      } else
        return {
          table: '',
          sidebarFilter: '',
        };
    } else {
      if (isScrollableTabsExists) {
        return {
          table: '',
          sidebarFilter: '',
        };
      } else
        return {
          table: '2xl:max-h-[calc(100vh-170px)]',
          sidebarFilter: '2xl:max-h-[calc(100vh-170px)]',
        };
    }
  };

  // Handle Render if There's no Loading
  return (
    <section className="antialiased relative space-y-2 2xl:space-y-0 overflow-hidden">
      {/* Header */}

      {(!hideJumbotron || actions.length > 0 || addButtonLabel || onClickAdd) && (
        <div className={`flex flex-wrap justify-between sm:items-center gap-4`}>
          {!hideJumbotron && <Jumbotron />}
          {/* Buttons */}
          <div className="flex gap-2 items-center flex-wrap">
            {actions.map((action) => {
              if (action?.dropdownlist) {
                return (
                  <div className="relative z-40" key={action?.label || action?.icon}>
                    <Dropdown
                      label=""
                      dismissOnClick={false}
                      renderTrigger={() => (
                        <Button
                          // Make ${screen.gt.xs() && 'min-w-[240px]'} dynamic when this button (dropdown) is used again
                          icon="line-md:link"
                          label={action.label}
                          outline
                        />
                      )}
                    >
                      {action.dropdownlist.map(({ label, color, icon, element, ...subButton }, index) => (
                        <div className="min-w-56" key={label || icon}>
                          <Dropdown.Item onClick={subButton.onClick}>
                            <div className="flex gap-2 cursor-pointer w-fit" {...subButton}>
                              {icon && <Icon className="cursor-pointer" width={20} style={{ color: color }} icon={icon} />}
                              {subButton.customIcon && (
                                <CustomIcon
                                  definedIcon={subButton.customIcon}
                                  className={`cursor-pointer ${color} ${subButton.className}`}
                                  width={subButton.iconWidth || '20'}
                                  height={subButton.iconHeight || '20'}
                                  onClick={subButton.onClick}
                                />
                              )}
                              <p className="text-nowrap text-black dark:text-white">{label}</p>
                              <p>{element}</p>
                            </div>
                          </Dropdown.Item>
                          {action.dropdownlist.length - 1 > index && <hr className="dark:border-gray-500" />}
                        </div>
                      ))}
                    </Dropdown>
                  </div>
                );
              } else {
                return (
                  <Button
                    key={action?.label || action?.icon}
                    outline
                    type="button"
                    as={action.to ? Link : undefined}
                    to={action.to}
                    onClick={action.onClick}
                  >
                    {action.icon && (
                      <div className="sm:mr-2 h-5 w-5">
                        <Icon icon={action.icon} width="22" />
                      </div>
                    )}
                    {action.label && <p className="hidden sm:block text-nowrap">{action.label}</p>}
                  </Button>
                );
              }
            })}

            {(addButtonLabel || onClickAdd) && (
              <Button type="button" to={addButtonPath} onClick={onClickAdd} className="w-fit min-w-20">
                <div className="sm:mr-2 h-5 w-5">
                  <Icon icon="mdi:add" width="22" />
                </div>
                {addButtonLabel && <p className="text-nowrap">{addButtonLabel}</p>}
              </Button>
            )}
          </div>
        </div>
      )}

      <div className="flex justify-between gap-4 pt-2">
        {/* Sidebar Filter */}
        {filterFeedData?.length > 0 && (
          <div className={`hidden 2xl:block w-full max-w-[270px] mt-3 ${handleHeight()?.sidebarFilter} `}>
            <SidebarFilterPage
              filterData={{
                filterFeedData,
                setFilters,
              }}
              searchInputField={search}
            />
          </div>
        )}

        <div className="w-full mt-3 2xl:w-fit overflow-hidden">
          {/* Content */}
          <div
            className={`bg-white border-2 border-[#F2F4F7] dark:bg-darkBackgroundCard dark:border-[#374151] rounded-lg overflow-y-auto ${
              handleHeight()?.table
            }`}
          >
            {/* Header || Search || Filter */}
            {(title || search || filters.length > 0 || drawerFilter) && (
              <div className="dark:text-white flex flex-col sm:flex-row justify-between sm:items-center gap-4 text-[13px] p-3 bg-white dark:bg-darkBackgroundCard sticky top-0 z-30">
                <div className="flex items-center gap-2">
                  <h1 className="dark:text-white flex text-lg text-[#181D27] font-semibold items-center tracking">{title}</h1>
                  {count > 0 && (
                    <div className="text-[#6941C6] border text-sm border-[#E9EAEB] dark:text-white bg-[#F9F5FF] dark:bg-[#6F3ED8] rounded-full px-3 py-1 h-fit font-medium">
                      {count}
                    </div>
                  )}
                </div>

                <div className="flex gap-x-2 flex-row justify-between items-center">
                  {/* Search bar */}
                  {search && (
                    <div
                      className={`rounded-lg flex w-full flex-row items-center space-x-3 space-y-0 justify-between shadow-sm ${
                        filterFeedData?.length > 0 && '2xl:hidden'
                      }`}
                    >
                      <div className="relative w-full">
                        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                          <Icon icon="carbon:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                        </div>
                        <input
                          type="text"
                          placeholder={searchPlaceholder}
                          className="bg-gray-white border truncate border-gray-200 text-gray-800 text-[13.5px] rounded-lg  block w-full sm:w-[270px] pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white focus:ring-0 focus:border-gray-300"
                          value={search.value}
                          onInput={(e) => search.update(e.target.value)}
                        />
                      </div>
                    </div>
                  )}

                  {/* Filters Button */}
                  {(filters.length > 0 || drawerFilter) && (
                    <div className={`${filterFeedData?.length > 0 && '2xl:hidden'}`}>
                      <ToggleFilter filters={filters} drawerFilter={drawerFilter} drawerClearAll={drawerClearAll} resultsFound={count} />
                    </div>
                  )}
                </div>

                {/* Dropdown */}
                {dropdown && (
                  <Dropdown
                    arrowIcon={false}
                    dismissOnClick={false}
                    label={
                      <>
                        <div className="sm:mr-2 h-5 w-5">
                          <Icon icon={dropdown.button.icon} width="22" />
                        </div>
                        <p className="hidden sm:block">{dropdown.button.label}</p>
                      </>
                    }
                    gradientMonochrome="purple"
                    className="rounded-lg shadow-2xl border sm:!-left-16"
                  >
                    <section className="w-[200px] py-2 space-y-2">
                      {dropdown.actions.map((action, index) => (
                        <div key={index}>
                          <Dropdown.Item onClick={action.onClick}>
                            <p>{action.label}</p>
                          </Dropdown.Item>
                          {index < dropdown.actions.length - 1 && <Dropdown.Divider />}
                        </div>
                      ))}
                    </section>
                  </Dropdown>
                )}
              </div>
            )}

            {/* Multi Select */}
            {(multiSelectedRow || handleDownloadExcelSelectedIds || handleArchiveSelectedIds || handleLockSelectedIds) && (
              <div className="flex sm:justify-start items-center space-x-2 sm:space-x-8 pt-1 sm:pt-0 dark:text-white pl-3">
                {/* {multiSelectedRow && (
                  <div className="flex border border-gray-200  dark:border-[#374151] rounded-lg px-2 py-1 ">
                    <div className="text-primaryPurple border border-gray-200 dark:border-[#374151] rounded-md min-w-6 w-fit text-center px-1">
                      {selectedIds.length}
                    </div>
                    <div className="flex">
                      <span className="px-2 hidden sm:block">Selected</span>
                      <Icon icon="uim:multiply" width={20} className="cursor-pointer" onClick={() => setSelectedIds([])} />
                    </div>
                  </div>
                )} */}
                {handleDownloadExcelSelectedIds && (
                  <div
                    className={`flex ${selectedIds.length ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'}`}
                    onClick={handleDownloadExcelSelectedIds}
                  >
                    <Icon icon="simple-icons:microsoftexcel" width={22} className="mx-2" />
                    <span className="hidden sm:block">Excel</span>
                  </div>
                )}
                {handleArchiveSelectedIds && (
                  <div
                    className={`flex ${selectedIds.length ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'}`}
                    onClick={handleArchiveSelectedIds}
                  >
                    <Icon icon="hugeicons:archive-02" width={20} className="mx-2 text-[#9061F9]" />
                    <span className="hidden sm:block">Archive</span>
                  </div>
                )}
                {handleLockSelectedIds && (
                  <div className="flex">
                    <ToggleSwitch
                      checked={lockUnlockAll}
                      onChange={(newValue) => handleLockSelectedIds(newValue)}
                      sizing="sm"
                      disabled={!selectedIds.length}
                      label={screen.gt.xs() ? 'Lock' : ''}
                    />
                  </div>
                )}
              </div>
            )}

            {/* Table (Large screens) */}
            {screen.gt.md() && (
              <table className="hidden lg:table tracking-wide w-full text-xs text-left text-gray-500 dark:text-gray-400 table-fixed">
                <thead
                  className={`text-[12px] text-[#535862] bg-[#F8FAFC] border-y dark:border-none  border-[#e7e7e7c6] dark:bg-gray-700 dark:text-gray-400 sticky ${
                    title ? (filterFeedData?.length > 0 ? 'top-[52px]' : 'top-[64px]') : 'top-0'
                  } z-20`}
                >
                  <tr>
                    {singleSelectedRow && <th className="p-3 w-4" />}
                    {multiSelectedRow && (
                      <th className="p-3 w-4">
                        <FlowbiteCheckbox
                          onChange={handleCheckboxSelectAll}
                          checked={selectedIds.length === rows.length && selectedIds.length > 0}
                          className="cursor-pointer text-purple-500"
                        />
                      </th>
                    )}
                    {columns.map((column) => (
                      <th key={column.key} className="text-start p-3 font-semibold" width={column.width}>
                        {column.label}
                      </th>
                    ))}
                  </tr>
                </thead>
                {rows.length > 0 && (
                  <tbody className="overflow-hidden">
                    {rows.map((row) => (
                      <tr key={row[rowKey]} className="font-medium border-b dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800">
                        {singleSelectedRow && (
                          <td className="p-4">
                            <FlowbiteRadio
                              value={row?._id}
                              checked={selectedIdSingle === row?._id}
                              onChange={(value) => setSelectedIdSingle(value?.target?.value)}
                              className="cursor-pointer text-purple-500"
                            />
                          </td>
                        )}
                        {multiSelectedRow && (
                          <td className="p-4">
                            <FlowbiteCheckbox
                              value={row?._id}
                              checked={selectedIds.includes(row?._id)}
                              onChange={handleCheckboxChange}
                              className="cursor-pointer text-purple-500"
                            />
                          </td>
                        )}
                        {columns.map((column) => (
                          <td key={column.key} className="text-start p-4 font-medium text-sm whitespace-nowrap dark:text-gray-400">
                            {renderCell(column, row)}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                )}
              </table>
            )}

            {/* Grid (Tablet screens) */}
            {(screen.sm() || screen.md()) && (
              <section className="hidden sm:block lg:hidden text-gray-900 dark:text-white px-5 pb-2 space-y-4 overflow-hidden">
                {rows.map((row) => (
                  <div key={row[rowKey]} className="border border-gray-200 dark:border-gray-600 p-3 rounded-lg grid grid-cols-1 gap-3">
                    {columns.map((column, index) => (
                      <div
                        key={column.key}
                        className={`p-2 w-full ${
                          index < columns.length - 1 ? 'border-b border-gray-100 dark:border-gray-700' : ''
                        } grid grid-cols-5 gap-2 p-2`}
                      >
                        {/* Label for the column */}
                        <p className="text-[13.5px] text-[#667085] font-normal dark:text-gray-400">{column.label}:</p>

                        {/* Cell content with consistent text styling */}
                        <b className="col-span-4 text-sm font-normal text-gray-900 dark:text-gray-100 break-words">{renderCell(column, row)}</b>
                      </div>
                    ))}
                  </div>
                ))}
              </section>
            )}

            {/* Truncated (Mobile screen) */}
            {screen.lt.sm() && (
              <section className="sm:hidden h-fit text-gray-900 dark:text-white px-3 py-0 space-y-4 overflow-hidden">
                {rows.map((row) => (
                  <div
                    key={row[rowKey]}
                    className={`border border-gray-200 dark:border-gray-600 rounded-lg relative divide-y divide-gray-200 dark:divide-gray-600 ${
                      !showMoreMap[row[rowKey]] && 'overflow-hidden'
                    }`}
                  >
                    {/* Show primary rows */}
                    {columns.map((column, index) => column.primary && handleBlockMobileView(column, row, index))}

                    {/* More details toggle */}
                    <div
                      onClick={() => toggleShowMore(row[rowKey])}
                      className={`flex justify-between items-center px-3 py-1 bg-[#F9FAFB] dark:bg-darkGrayBackground cursor-pointer ${
                        !showMoreMap[row[rowKey]] && 'rounded-b-lg'
                      }`}
                    >
                      <span className="text-[#98A2B3] text-sm">{showMoreMap[row[rowKey]] ? 'Less details' : 'More details'}</span>
                      <Icon
                        width={30}
                        className="text-[#98A2B3]"
                        icon={
                          showMoreMap[row[rowKey]]
                            ? 'material-symbols-light:keyboard-arrow-up-rounded'
                            : 'material-symbols-light:keyboard-arrow-down-rounded'
                        }
                      />
                    </div>

                    {/* Show the rest of rows */}
                    {columns.map((column) => !column.primary && showMoreMap[row[rowKey]] && handleBlockMobileView(column, row))}
                  </div>
                ))}
              </section>
            )}

            {/* No data created || No results found */}
            {!rows.length && (
              <div className="my-8">
                {backupRows.length > 0 ? (
                  <NoDataMatches />
                ) : (
                  <NoDataFound noDataFound={noDataFound} width={noDataFoundIconWidth} height={noDataFoundIconHeight} />
                )}
              </div>
            )}

            {/* Loading */}
            {loading && (
              <div className="absolute z-50 left-0 right-0 bottom-0 top-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80">
                <Spinner size="lg" color="purple" />
              </div>
            )}

            {/* Pagination */}
            {isPaginationActive && count > size && (
              <nav className="flex flex-row justify-center items-center px-4 pt-1 pb-2 bg-white dark:bg-darkGrayBackground sticky -bottom-0.5 z-20">
                {/* <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
                  Showing <span className="font-semibold text-gray-900 dark:text-white">{showingText}</span> of{' '}
                  <span className="font-semibold text-gray-900 dark:text-white">{count}</span>
                </span> */}
                {count > size && (
                  <Pagination
                    theme={StaticData.paginationTheme}
                    currentPage={page}
                    onPageChange={(page) => pagination.update({ page })}
                    showIcons
                    totalPages={pagesCount}
                    layout={screen.gt[breakpoint]() ? 'pagination' : 'navigation'}
                    previousLabel={<span className="hidden sm:block">Previous</span>}
                    nextLabel={<span className="hidden sm:block">Next</span>}
                  />
                )}
              </nav>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

// PropTpes
Table.propTypes = {
  rowKey: PropTypes.string.isRequired,
  title: PropTypes.string,
  searchPlaceholder: PropTypes.string,
  addButtonLabel: PropTypes.string,
  addButtonPath: PropTypes.string,
  columns: PropTypes.array.isRequired,
  rows: PropTypes.array.isRequired,
  count: PropTypes.number,
  filters: PropTypes.array,
  actions: PropTypes.array,
  slots: PropTypes.object,
  ready: PropTypes.bool,
  loading: PropTypes.bool,
  breakpoint: PropTypes.oneOf(['xxl', 'xl', 'lg', 'md', 'sm', 'xs']),
  search: PropTypes.shape({
    value: PropTypes.string,
    update: PropTypes.func,
  }),
  pagination: PropTypes.shape({
    page: PropTypes.number,
    size: PropTypes.number,
    update: PropTypes.func,
  }),
};

// Default Values
Table.defaultProps = {
  rowKey: '_id',
  count: 0,
  slots: {},
  rows: [],
  ready: true,
  loading: false,
  filters: [],
  filterFeedData: [],
  backupRows: [],
  columns: [],
  actions: [],
  pagination: {
    page: 1,
    size: 20,
  },
  breakpoint: 'md',
};
