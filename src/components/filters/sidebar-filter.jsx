// React
import { useContext, useEffect, useState } from 'react';

// Context
import { AppContext } from '/src/components/provider';

// Core
import { Icon, Form, MultiSelect, useForm, RadioGroup, Button } from '/src';

// Flowbite
import { DatePicker } from 'rsuite';

export const SidebarFilterPage = ({ filterData, searchInputField }) => {
  // Context
  // const { userData, sidebarFilter, setSidebarFilter, sidebarSearch, setSidebarSearch } = useContext(AppContext);
  // const { filterFeedData, setFilters } = sidebarFilter || {};

  // State
  const [search, setSearch] = useState(searchInputField?.value);
  const [filterCountNumber, setFilterCountNumber] = useState(0);

  // Hooks
  const { form, setFieldValue, setFormValue, resetForm } = useForm({
    // Applicant Filter
    createdAt: '',
    track: '',
    seniorityLevel: '',
    phase: '',
    warnings: '',
    recommended: null,
    // Assignment Filter
    type: '',
    category: '',
    subCategory: '',
    difficulty: '',
    grade: '',
    startDate: '',
    dueDate: '',

    /* Question */
    topic: '',
    scope: '',
  });

  // Methods drawer filter
  const HandleCollapseFilter = ({ onClick, label, actionLabel }) => (
    <div className="flex justify-between items-center p-3 bg-[#F9F8FFA3] dark:bg-darkGrayBackground border-y border-[#E8E8E8] dark:border-gray-700">
      <span className="text-[#494C54] dark:text-white text-sm font-semibold">{label}</span>
      <span className="text-[#8A43F9] text-xs font-semibold cursor-pointer" onClick={onClick}>
        {actionLabel}
      </span>
    </div>
  );

  const isAnyFilterApplied = () => {
    return (
      form.createdAt ||
      form.track.length > 0 ||
      form.seniorityLevel.length > 0 ||
      form.phase.length > 0 ||
      form.recommended ||
      form.warnings.length > 0 ||
      form.type.length > 0 ||
      form.category.length > 0 ||
      form.subCategory.length > 0 ||
      form.difficulty.length > 0 ||
      form.grade.length > 0 ||
      form.startDate ||
      form.dueDate
    );
  };

  const clearFilter = () => {
    resetForm();
    filterData?.setFilters({});
    setSearch('');
  };

  const applyFilter = () => {
    const cleanedForm = Object.fromEntries(Object.entries(form)?.filter(([_, value]) => value !== undefined && value !== null && value !== ''));
    if (!(cleanedForm?.seniorityLevel ?? [])?.includes(1)) {
      delete cleanedForm?.phase;
    }
    filterData?.setFilters(cleanedForm);

    let totalLength = 0;
    for (const key in cleanedForm) {
      if (Array.isArray(cleanedForm[key])) {
        totalLength += cleanedForm[key].length;
      } else if (typeof cleanedForm[key] === 'string' || typeof cleanedForm[key] === 'number') {
        totalLength += 1;
      } else if (cleanedForm[key] instanceof Date) {
        totalLength += 1;
      }
    }
    setFilterCountNumber(totalLength);
  };

  // 'joinDate', // Width issues
  // 'track',
  // 'seniortyLevel',
  // 'phase',
  // 'recommended', // Width issues
  // 'warnings',
  // 'type',
  // 'category',
  // 'subCategory',
  // 'difficulty',
  // 'grad',
  // 'startEndDate', // Width issues

  const filterFeedDataReference = {
    joinDate: (
      <div className="space-y-2">
        <p className="text-[13px] text-inputLabel dark:text-inputDarkLabel font-semibold">Join Date</p>
        <DatePicker
          format="dd/MM/yyyy"
          placeholder="Enter a Join Date"
          // className="w-[210px]"
          className="w-full"
          value={form?.createdAt || null}
          onChange={(value) => setFieldValue('createdAt')(value)}
          oneTap
          showMeridiem
        />
      </div>
    ),
    track: (
      <MultiSelect
        label="Interests"
        name="track"
        placeholder="Search for category"
        value={form.track}
        onChange={setFieldValue('track')}
        lookup="category"
        optionValueKey="_id"
        optionLabelKey="name"
        showSingleClear={form.track.length >= 2}
        handleSingleClear={() => setFieldValue('track')('')}
        customSize="[13px]"
        customWeight="semibold"
      />
    ),
    seniortyLevel: (
      <MultiSelect
        label="Seniority Level"
        name="seniorityLevel"
        placeholder="Search for level"
        value={form.seniorityLevel}
        onChange={setFieldValue('seniorityLevel')}
        lookup="$QuizDifficulty"
        showSingleClear={form.seniorityLevel.length >= 2}
        handleSingleClear={() => setFieldValue('seniorityLevel')('')}
        customSize="[13px]"
        customWeight="semibold"
      />
    ),
    phase: form?.seniorityLevel?.includes(1) && (
      <MultiSelect
        label="Phase"
        name="phase"
        placeholder="Search for phase"
        value={form.phase}
        onChange={setFieldValue('phase')}
        lookup="$InternPhase"
        showSingleClear={form.phase.length >= 2}
        handleSingleClear={() => setFieldValue('phase')('')}
      />
    ),
    recommended: (
      <RadioGroup
        label="Recommendation"
        name="recommended"
        value={form.recommended}
        onChange={setFieldValue('recommended', Number)}
        lookup="$Recommended"
        className="text-sm text-inputLabel dark:text-inputDarkLabel pb-1"
        showSingleClear={form.recommended}
        handleSingleClear={() => setFieldValue('recommended')('')}
        customSize="[12px]"
      />
    ),
    warnings: (
      <MultiSelect
        label="Warnings"
        name="warnings"
        placeholder="Search for warnings"
        value={form.warnings}
        onChange={setFieldValue('warnings')}
        lookup="$SubmissionWarning"
        handleSingleClear={() => setFieldValue('warnings')('')}
        showSingleClear={form.warnings.length >= 2}
        customSize="[13px]"
        customWeight="semibold"
      />
    ),
    type: (
      <MultiSelect
        label="Assessment Type"
        name="type"
        placeholder="Search for type"
        value={form.type}
        onChange={setFieldValue('type')}
        lookup="$AssignmentType"
        handleSingleClear={() => setFieldValue('type')('')}
        customSize="[13px]"
        customWeight="semibold"
        showSingleClear={form.type.length >= 2}
      />
    ),
    category: (
      <MultiSelect
        label="Category"
        name="category"
        placeholder="Search for category"
        value={form.category}
        customSize="[13px]"
        customWeight="semibold"
        onChange={(value) => {
          setFieldValue('category')(value);
          setFieldValue('subCategory')([]);
          setFieldValue('topic')([]);
        }}
        lookup="category"
        optionValueKey="_id"
        optionLabelKey="name"
        handleSingleClear={() => setFieldValue('category')('')}
        showSingleClear={form.category.length >= 2}
      />
    ),
    subCategory: (
      <MultiSelect
        label="Subcategory"
        name="subCategory"
        placeholder="Search for subcategory"
        value={form.subCategory}
        onChange={setFieldValue('subCategory')}
        lookup="subcategory"
        params={{ categoryId: form.category }}
        optionValueKey="_id"
        optionLabelKey="name"
        disabled={form.category.length <= 0}
        // disabledMessage="Please select category first"
        handleSingleClear={() => setFieldValue('subCategory')('')}
        showSingleClear={form.subCategory.length >= 2}
        customSize="[13px]"
        customWeight="semibold"
      />
    ),
    topic: (
      <MultiSelect
        label="Topic"
        name="topic"
        placeholder="Search for topic"
        value={form.topic}
        onChange={setFieldValue('topic')}
        lookup="topic"
        params={{ subcategoryId: form.subCategory }}
        optionValueKey="_id"
        optionLabelKey="name"
        disabled={form.subCategory.length <= 0}
        handleSingleClear={() => setFieldValue('subCategory')('')}
        showSingleClear={form.topic.length >= 2}
      />
    ),
    difficulty: (
      <MultiSelect
        label="Difficulty"
        name="difficulty"
        placeholder="Search for difficulty"
        value={form.difficulty}
        onChange={setFieldValue('difficulty')}
        lookup="$QuizDifficulty"
        handleSingleClear={() => setFieldValue('difficulty')('')}
        showSingleClear={form.difficulty.length >= 2}
        customSize="[13px]"
        customWeight="semibold"
      />
    ),
    grad: (
      <MultiSelect
        label="Assessment Score"
        name="grade"
        placeholder="Search for score"
        value={form.grade}
        onChange={setFieldValue('grade')}
        lookup="$grade"
        handleSingleClear={() => setFieldValue('grade')('')}
        showSingleClear={form.grade.length >= 2}
        customSize="[13px]"
        customWeight="semibold"
      />
    ),
    startEndDate: (
      <div className="space-y-2">
        <p className="text-inputLabel dark:text-inputDarkLabel text-[13px] font-semibold">Assessment Due Date</p>
        <div>
          <DatePicker
            format="dd/MM/yyyy hh:mm aa"
            placeholder="Start date"
            // className="w-[210px]"
            className="w-full"
            value={form?.startDate || null}
            onChange={setFieldValue('startDate')}
            showMeridiem
            placement="autoVerticalStart"
          />
        </div>
        <div>
          <DatePicker
            format="dd/MM/yyyy hh:mm aa"
            placeholder="End date"
            // className="w-[210px]"
            className="w-full"
            value={form?.dueDate || null}
            onChange={setFieldValue('dueDate')}
            showMeridiem
            placement="autoVerticalEnd"
          />
        </div>
      </div>
    ),
    role: (
      <MultiSelect
        label="Role"
        name="role"
        placeholder="Search for role"
        value={form.role}
        onChange={setFieldValue('role')}
        lookup="$Role"
        handleSingleClear={() => setFieldValue('difficulty')('')}
        showSingleClear={form.difficulty.length >= 2}
      />
    ),
    gender: (
      <MultiSelect
        label="Gender"
        name="gender"
        placeholder="Search for gender"
        value={form.gender}
        onChange={setFieldValue('gender')}
        lookup="$Gender"
        handleSingleClear={() => setFieldValue('difficulty')('')}
        showSingleClear={form.difficulty.length >= 2}
      />
    ),
    scope: (
      <MultiSelect
        label="Scope"
        name="scope"
        placeholder="Search for scope"
        value={form.scope}
        onChange={setFieldValue('scope')}
        lookup="$Scope"
        handleSingleClear={() => setFieldValue('difficulty')('')}
        showSingleClear={form.difficulty.length >= 2}
      />
    ),
  };

  useEffect(() => {
    searchInputField?.update(search);
  }, [search]);

  return (
    <Form className="h-full space-y-4 border dark:border-gray-700 p-2 rounded-lg overflow-y-auto">
      <div className="flex justify-between px-2 pt-3 pb-1">
        <div className="flex gap-2 items-center">
          <Icon icon="ion:filter" width="22" />
          <span className="hidden sm:block">Filters</span>
        </div>

        <p className=" h-fit pt-1 text-[13px] cursor-pointer text-[#9061F9]" onClick={clearFilter}>
          Clear All
        </p>
      </div>

      <div className="flex justify-between items-center grow space-y-0 rounded-lg relative">
        <Icon icon="carbon:search" width="20" className="size-5 text-gray-500 dark:text-gray-400 absolute left-3 pointer-events-none" />
        <input
          type="text"
          placeholder="Search..."
          className="w-full p-2 pl-10 dark:bg-gray-700 bg-gray-white text-[13.5px] text-gray-800 border border-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white truncate rounded-lg focus:ring-0 focus:border-gray-300 shadow-sm"
          value={search}
          onInput={(e) => setSearch(e.target.value)}
        />
      </div>

      {filterData?.filterFeedData?.map((filter) => filterFeedDataReference[filter])}

      <Button label="Show Result" onClick={applyFilter} size="xs" className="m-auto !mt-4" />
    </Form>
  );
};
