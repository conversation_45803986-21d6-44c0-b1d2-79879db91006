// Core
import { Drawer, SidebarFilterPage } from '/src';

export const SidebarFilterDrawer = ({ drawerFilter, filterData }) => {
  // Methods
  const onCloseDrawerFilter = () => drawerFilter.setShowDrawerFilter((prev) => !prev);

  return (
    <Drawer className="w-screen max-w-[340px]" onClose={onCloseDrawerFilter}>
      <Drawer.SingleView className="!px-0 !py-1 !space-y-0">
        <Drawer.Body className="p-2 overflow-y-auto">
          <SidebarFilterPage filterData={filterData} />
        </Drawer.Body>
      </Drawer.SingleView>
    </Drawer>
  );
};
