// UI
import { Icon } from '/src';

/*
  TODO: Delete after new styles colors
  Instructions: 6 Flags: (Be carful of ordering):

  expired ? !startedAt: Missed : startedAt: Overdue
    1- Missed: expired: true, startedAt: false
    2- Overdue: expired: true, startedAt: true (Will have the flag and score)
  locked => 3- Scheduled (locked: true)
  submittedAt => 4- Submitted (submittedAt: true) (Will have the flag and score)
  startedAt => 5- In-progress (startedAt: true)
  else => 6- Not-started
*/

export const ResultStatus = ({ test, hideScore, hideTime, hideMissed }) => {
  const publicStyles = {
    missed: 'text-statusColorMissedText bg-statusColorMissedBackground dark:text-statusDarkColorMissedText dark:bg-statusDarkColorMissedBackground',
    overdue:
      'text-statusColorOverdueText bg-statusColorOverdueBackground dark:text-statusDarkColorOverdueText dark:bg-statusDarkColorOverdueBackground',
    scheduleld:
      'text-statusColorScheduleldText bg-statusColorScheduleldBackground dark:text-statusDarkColorScheduleldText dark:bg-statusDarkColorScheduleldBackground',
    poor: 'text-statusColorPoor dark:text-statusDarkColorPoor',
    good: 'text-statusColorGood dark:text-statusDarkColorGood',
    excellent: 'text-statusColorExcellent dark:text-statusDarkColorExcellent',
    inProgress:
      'text-statusColorInProgressText dark:text-statusDarkColorInProgressText bg-statusColorInProgressBackground dark:bg-statusDarkColorInProgressBackground',
    notStarted:
      'text-statusColorNotStartedText dark:text-statusDarkColorNotStartedText bg-statusColorNotStartedBackground dark:bg-statusDarkColorNotStartedBackground',
    icon: 'material-symbols:circle',
  };

  const handleRemaningTime = () => {
    const milliseconds = new Date(test?.startDate) - new Date();
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    if (days) {
      return `${days} days left`;
    } else if (hours) {
      return `${hours} hours left`;
    } else if (minutes) {
      return `${minutes} minutes left`;
    } else {
      return `Less than a minute left`;
    }
  };

  const status = () => {
    const scoreStyle = () => {
      if (test?.score < 50) return publicStyles?.poor;
      else if (test?.score <= 80) return publicStyles?.good;
      else if (test?.score > 80) return publicStyles?.excellent;
    };

    if (test?.expired) {
      if (!test?.startedAt) {
        return {
          text: 'Missed Deadline',
          textStyles: publicStyles?.missed,
        };
      } else if (test?.startedAt) {
        return {
          text: 'Overdue',
          textStyles: publicStyles?.overdue,
          score: `${test?.score}%`,
          scoreStyles: scoreStyle(),
        };
      }
    } else if (test?.locked) {
      return {
        text: 'Scheduled',
        textStyles: publicStyles?.scheduleld,
        remaningTime: handleRemaningTime(),
      };
    } else if (test?.submittedAt) {
      return {
        score: `${test?.score}%`,
        scoreStyles: scoreStyle(),
      };
    } else if (test?.startedAt) {
      return {
        text: 'In Progress',
        textStyles: publicStyles?.inProgress,
        icon: publicStyles?.icon,
      };
    } else {
      return {
        text: 'Not Started',
        textStyles: publicStyles?.notStarted,
        icon: publicStyles?.icon,
      };
    }
  };

  return (
    <div className="flex items-center gap-1.5 leading-4">
      {status()?.score && !hideScore && <p className={`py-2 rounded-full font-medium text-sm ${status()?.scoreStyles}`}>{status()?.score}</p>}
      {status()?.text && !hideMissed && (
        <div className={`flex items-center gap-1 px-2.5 py-1 rounded-full font-normal text-xs ${status()?.textStyles} `}>
          {status()?.icon && <Icon icon={status().icon} width={8} />}
          <p className="text-nowrap">{status()?.text}</p>
        </div>
      )}
      {status()?.remaningTime && !hideTime && (
        <div className="flex gap-0.5 ml-1 w-max">
          <Icon icon="tabler:clock-hour-3" className="text-[#9CA7B6]" />
          <span className="text-[#798296]">{status()?.remaningTime}</span>
        </div>
      )}
    </div>
  );
};
