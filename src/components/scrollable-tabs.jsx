// React
import { useRef, useEffect, useState } from 'react';

// Core
import { Icon } from '/src';

export const ScrollableTabs = ({ data, selectedTab, fullWidth }) => {
  // States
  const [isAtStart, setAtStart] = useState(true);
  const [isAtEnd, setAtEnd] = useState(false);

  // Refs
  const containerRef = useRef(null);

  // Handle scroll
  const handleScroll = (direction) => {
    containerRef.current.scrollBy({ left: direction === 'left' ? -200 : 200, behavior: 'smooth' });
  };

  // Check scroll position
  const checkScroll = () => {
    if (containerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = containerRef.current;
      setAtStart(scrollLeft === 0);
      setAtEnd(Math.round(scrollLeft + clientWidth) >= scrollWidth);
    }
  };

  // Set up event listeners
  useEffect(() => {
    if (containerRef.current) {
      checkScroll();
      containerRef.current.addEventListener('scroll', checkScroll);
    }

    return () => {
      if (containerRef.current) {
        containerRef.current.removeEventListener('scroll', checkScroll);
      }
    };
  }, [isAtStart, isAtEnd]);

  // Main Tabs OR Descriptions Titles
  const handleTitleClassname = (index) => {
    if (!!selectedTab) {
      const classNameWhenSelectedTab = 'px-2 xsmd:px-6 py-1 xsmd:py-4 text-base xsmd:text-[18px] dark:text-white font-normal cursor-pointer';
      if (selectedTab?.activeTab === index) return `border-b-2 border-b-[#a169fb] ${classNameWhenSelectedTab}`;
      else return `custom-tab-border ${classNameWhenSelectedTab}`;
    } else return 'text-sm dark:bg-gray-600 dark:text-gray-300 text-gray-700 bg-white dark:bg-[#3A3A3] rounded-3xl px-3 py-1';
  };

  return (
    <div className={`relative flex items-center w-full overflow-hidden ${!!selectedTab && 'border-b dark:border-gray-700'}`}>
      {/* Left Arrow */}
      <button
        className={`px-2 py-2 rounded-full text-gray-700 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600 transition ${
          isAtStart && 'hidden'
        }`}
        onClick={() => handleScroll('left')}
        disabled={isAtStart}
      >
        <Icon icon="ep:arrow-left-bold" />
      </button>

      {/* Scrollable Container */}
      {data?.length > 0 && (
        <div
          className={`flex items-center gap-2 whitespace-nowrap overflow-x-auto scroll-smooth scrollbar-hidden ${!!selectedTab && 'justify-around'} ${
            fullWidth && 'flex-1'
          }`}
          ref={containerRef}
        >
          {data?.map((singleData, index) => (
            <div
              key={singleData?.title}
              onClick={() => !!selectedTab && selectedTab?.setActiveTab(index)}
              className={`flex flex-col  xsmd:flex-row justify-center items-center xssm:gap-4 ${handleTitleClassname(index)}`}
            >
              {singleData?.backupCount !== undefined && (
                <span className={`text-[19px] ${singleData.backupCount >= 0 && 'font-semibold'}`}>{singleData?.backupCount}</span>
              )}

              {singleData?.title && (
                <span className={`${!fullWidth && 'text-[#566577] dark:text-white'}`}>
                  <span className={`capitalize ${selectedTab?.activeTab === index && 'text-[#6C2BD9] dark:text-purple-500'}`}>
                    {singleData?.title}
                  </span>
                </span>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Right Arrow */}
      <button
        className={`px-2 py-2 rounded-full text-gray-700 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600 transition ${isAtEnd && 'hidden'}`}
        onClick={() => handleScroll('right')}
        disabled={isAtEnd}
      >
        <Icon icon="ep:arrow-right-bold" />
      </button>
    </div>
  );
};
