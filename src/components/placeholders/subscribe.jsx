// React
import { useContext } from 'react';
import { useNavigate } from 'react-router-dom';

// Context
import { AppContext } from '/src/components/provider';

// Core
import { Dialog, Icon, Button } from '/src';

export const SubscribeDialog = ({ onClose }) => {
  // Hooks
  const navigate = useNavigate();

  // User Data
  const { userData } = useContext(AppContext);

  return (
    <Dialog show popup size="md" onClose={onClose}>
      <div className="space-y-4 text-center">
        <Icon icon="material-symbols:lock-outline" width={60} className="w-fit p-4 mx-auto bg-purple-300 dark:text-white rounded-full" />

        <p className="dark:text-white text-2xl font-bold">
          {userData?.hasOwnProperty('features') ? 'You’ve Reached the Maximum Number of Uses Allowed.' : 'Subscribe to Unlock This Feature'}
        </p>
        <p className="dark:text-white">
          {userData?.hasOwnProperty('features')
            ? 'Please upgrade your plan to unlock more access.'
            : 'Complete your subscription and enjoy full access to all features and services.'}
        </p>

        <Button label="Go to Plans" onClick={() => navigate('/pricing')} className="mx-auto" />
      </div>
    </Dialog>
  );
};
