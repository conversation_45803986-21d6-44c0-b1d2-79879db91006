// Core
import { EnumText, Icon } from '/src';

// React icons
import { FaUserGraduate, FaUser, FaStar, FaMedal, FaTrophy } from 'react-icons/fa';
import { LiaCircle } from 'react-icons/lia';
import { LuDiamond } from 'react-icons/lu';
import { PiCube } from 'react-icons/pi';
import { BiPolygon, BiTrash } from 'react-icons/bi';

export const TestSeniorityLevel = ({ seniorityLevel }) => {
  const handleTestSeniorityLevel = () => {
    const iconSize = 'text-sm';

    switch (seniorityLevel) {
      case 1:
        return {
          difficultyIcon: <FaUserGraduate className={`${iconSize} text-teal-700`} />, // Intern
          difficultyColor: ' text-teal-700 ',
        };

      case 2:
        // Star Icon fresh level
        return {
          difficultyIcon: <FaUser className={`${iconSize} text-sky-800`} />, // Fresh
          difficultyColor: 'text-sky-800 ',
        };

      case 3:
        // Medal Star junior
        return {
          difficultyIcon: <FaStar className={`${iconSize} text-amber-700`} />, // Junior
          difficultyColor: ' text-amber-700 ',
        };

      case 4:
        // Medal star midlevel
        return {
          difficultyIcon: <FaMedal className={`${iconSize} text-orange-700`} />, // Mid-level
          difficultyColor: 'text-orange-700',
        };

      case 5:
        // Tropy icon for senior with star
        return {
          difficultyIcon: <Icon icon="solar:crown-star-bold" width={16} className={`text-red-800`} />, // Senior
          difficultyColor: 'text-red-800',
        };

      default:
        return { difficultyIcon: null };
    }
  };

  return (
    <span className={`inline-flex items-center py-1 text-sm font-medium rounded-full capitalize ${handleTestSeniorityLevel()?.difficultyColor}`}>
      <span className="mr-1 flex items-center justify-center">{handleTestSeniorityLevel()?.difficultyIcon}</span>
      <EnumText name={'QuizDifficulty'} value={seniorityLevel} />
    </span>
  );
};

export const TestDifficulty = ({ difficulty }) => {
  const handleTestDifficulty = () => {
    const iconSize = 'text-sm';

    switch (difficulty) {
      case 1:
        return {
          difficultyIcon: <LiaCircle className={iconSize} />,
          difficultyColor: 'border bg-[#fff] border-[#d1d5db] text-[#4BA665]',
        };
      case 2:
        return {
          difficultyIcon: <LuDiamond className={iconSize} />,
          difficultyColor: 'border bg-[#fff] border-[#d1d5db] text-[#B16A00]',
        };
      case 3:
        return {
          difficultyIcon: <BiPolygon className={iconSize} />,
          difficultyColor: 'border bg-[#fff] border-[#d1d5db] text-[#D40101]',
        };
      case 4:
        return {
          difficultyIcon: <PiCube className={iconSize} />,
          difficultyColor: 'border bg-[#fff] border-[#d1d5db] text-[#B10000]',
        };
      default:
        return { difficultyIcon: null };
    }
  };

  return (
    <span
      className={`min-w-14 flex flex-wrap justify-center items-center px-3 py-1 text-xs text-center font-medium rounded-full capitalize ${
        handleTestDifficulty()?.difficultyColor
      }`}
    >
      {/* <span className="mr-1 flex items-center justify-center">{handleTestDifficulty()?.difficultyIcon}</span> */}
      <EnumText name={'QuestionDifficulty'} value={difficulty} />
    </span>
  );
};

export const TestTypeIndicator = ({ type }) => {
  const renderStyle = () => {
    if (type === 'test') {
      return 'text-[#6234C4] bg-[#EDE9FE]';
    } else if (type === 'interview') {
      return 'text-[#2B57D6] bg-[#D8E7FE]';
    } else if (type === 'screening') {
      return 'text-[#DF7AEF] bg-[#FFECFA]';
    } else {
      return '';
    }
  };
  return <div className={`capitalize px-3 py-[1px] ${renderStyle()} text-sm rounded-full`}>{type}</div>;
};

export const AvarageScore = ({ score, label }) => {
  const handleAvarageScore = () => {
    if (score >= 90) {
      return {
        label: label || `${score}%`,
        styles: ' text-green-700 ',
      };
    } else if (score >= 50) {
      return {
        label: label || `${score}%`,
        styles: 'bg-orange-100 text-orange-700 border-orange-300',
      };
    } else if (score >= 10) {
      return {
        label: label || `${score}%`,
        styles: 'bg-red-100 text-red-700 border-red-300',
      };
    } else {
      return {
        label: label || score ? `${score}%` : '-',
        styles: 'bg-gray-100 text-gray-400 border-gray-300',
      };
    }
  };
  return (
    <div className={`${handleAvarageScore()?.styles} rounded-full px-2 py-1 border font-semibold capitalize text-[12px] truncate`}>
      {handleAvarageScore()?.label}
    </div>
  );
};
