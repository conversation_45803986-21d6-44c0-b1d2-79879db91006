import React from 'react';
import { Icon } from '/src';

export const ResultStatusSubmission = ({ statusSubmission }) => {
  const publicStyles = {
    missed: 'text-statusColorMissedText bg-statusColorMissedBackground dark:text-statusDarkColorMissedText dark:bg-statusDarkColorMissedBackground',
    overdue:
      'text-statusColorOverdueText bg-statusColorOverdueBackground dark:text-statusDarkColorOverdueText dark:bg-statusDarkColorOverdueBackground',
    scheduleld:
      'text-statusColorScheduleldText bg-statusColorScheduleldBackground dark:text-statusDarkColorScheduleldText dark:bg-statusDarkColorScheduleldBackground',
    poor: 'text-statusColorPoor dark:text-statusDarkColorPoor',
    good: 'text-statusColorGood dark:text-statusDarkColorGood  bg-statusColorInProgressBackground',
    excellent: 'text-statusColorExcellent dark:text-statusDarkColorExcellent',
    inProgress:
      'text-statusColorInProgressText dark:text-statusDarkColorInProgressText bg-statusColorInProgressBackground dark:bg-statusDarkColorInProgressBackground',
    notStarted:
      'text-statusColorNotStartedText dark:text-statusDarkColorNotStartedText bg-statusColorNotStartedBackground dark:bg-statusDarkColorNotStartedBackground',
    icon: 'material-symbols:circle',
  };
  const scoreStyle = () => {
    if (statusSubmission === 1) return publicStyles?.poor;
    else if (statusSubmission === 2) return publicStyles?.good;
    else if (statusSubmission === 3) return publicStyles?.excellent;
  };

  return (
    <div className="flex items-center gap-1.5 leading-4">
      {/* {statusSubmission()?.score && !hideScore && (
        <p className={`py-2 rounded-full font-medium text-sm ${statusSubmission()?.scoreStyles}`}>{statusSubmission()?.score}</p>
      )} */}

      <div className={`flex items-center gap-1 px-2.5 py-1 rounded-full font-normal text-xs ${scoreStyle()} `}>
        {statusSubmission === 1 && <Icon icon="material-symbols:circle" width={8} />}
        {statusSubmission === 1 ? 'Not Started' : statusSubmission === 2 ? 'In Progress' : statusSubmission === 3 ? 'Completed' : ''}
      </div>
    </div>
  );
};
