export const CompetenceScore = ({ percentage }) => {
  const handleEnglishProficiencyColors = () => {
    if (percentage < 50)
      return {
        label: 'Poor',
        color: 'text-statusColorPoor dark:text-statusDarkColorPoor',
        bg: 'bg-statusBackgroundPoor dark:bg-statusDarkBackgroundPoor',
      };
    else if (percentage <= 80)
      return {
        label: 'Good',
        color: 'text-statusColorGood dark:text-statusDarkColorGood',
        bg: 'bg-statusBackgroundGood dark:bg-statusDarkBackgroundGood',
      };
    else if (percentage > 80)
      return {
        label: 'Excellent',
        color: 'text-statusColorExcellent dark:text-statusDarkColorExcellent',
        bg: 'bg-statusBackgroundExcellent dark:bg-statusDarkBackgroundExcellent',
      };
  };

  return (
    <div className="flex items-center px-4 gap-1">
      <p
        className={`px-2.5 py-[3px] text-sm rounded-full self-center ${handleEnglishProficiencyColors()?.color} ${
          handleEnglishProficiencyColors()?.bg
        }`}
      >
        {handleEnglishProficiencyColors()?.label}
      </p>
    </div>
  );
};
