import PropTypes from "prop-types";
import { useLookups } from '/src';

export const LookupText = ({ lookup, id }) => {
  const { lookups } = useLookups(lookup);

  const selected = lookups

  if (!Array.isArray(selected)) {
    throw new Error(`lookup ${lookup} not exists.`);
  }

  return selected.find((item) => item._id === id)?.name || "—";
};

// Types
LookupText.propTypes = {
  lookup: PropTypes.string,
  id: PropTypes.string,
};
