// React
import { <PERSON> } from 'react-router-dom';

import { useEffect } from 'react';

// Flowbite
import { Button as FlowbiteButton, Tooltip } from 'flowbite-react';

// Proptypes validation
import PropTypes from 'prop-types';

// Core
import { Icon, CustomIcon } from '/src';

/*
  Refrence for Button:
    <Button
      _empty_  : Button will be primary
      outline  : add a primary colored border around the button
      tertiary : add a gray colored border around the button

      size : 
        default : will have an md sized Button
        'xs'    : But<PERSON> will have a smallest height (36px)
        'sm'    : But<PERSON> will have a smaller height (40px)
        'md'    : But<PERSON> will have a medium height (44px)
        'lg'    : But<PERSON> will have a larger height (48px)
    />
*/

export const Button = ({
  children,
  label,
  labelSize = 'text-sm',
  icon,
  iconRight,
  loading,
  to,
  danger,
  iconWidth,
  iconHeight,
  className,
  outline,
  tertiary,
  size,
  customIcon,
  tooltipPlacement,
  ...props
}) => {
  const customTheme = {
    outline: {
      off: '',
      on: 'flex w-full justify-center bg-white text-primaryPurple transition-all duration-75 ease-in group-enabled:group-hover:bg-opacity-0 group-enabled:group-hover:text-inherit dark:bg-gray-900 dark:text-primaryPurple',
    },
  };

  const handleHeightSize = () => {
    if (size === 'xs') return { width: size, height: 'h-9' }; // 36px
    else if (size === 'sm') return { width: size, height: 'h-9 sm:h-10' }; // 40px
    else if (size === 'md') return { width: size, height: 'h-9 sm:h-10 md:h-11' }; // 44px
    // else if (size === 'lg') return { width: size, height: 'h-9 sm:h-10 md:h-11 lg:h-12' }; // 48px
    else return { width: 'md', height: 'h-9 sm:h-10 md:h-11 lg:h-12' };
  };

  const element = (
    <FlowbiteButton
      theme={customTheme}
      isProcessing={loading}
      className={`${
        !!outline
          ? 'border-[2px] border-[#8D5BF8] dark:border-opacity-60 !bg-white dark:!bg-transparent text-[#7E3AF2] dark:text-white hover:!bg-[#F3EDFF] dark:hover:!bg-[#a991dc35]'
          : tertiary
          ? `border-[2px] border-[#E4E5E9] dark:border-gray-600 !bg-white dark:!bg-[#1F2837] text-[#2F2F2F] dark:text-white hover:!bg-[#F9FAFB] dark:hover:!bg-[#374151]`
          : '!bg-[#8D5BF8] dark:opacity-80 hover:!bg-[#6F3ED8]'
      } font-semibold items-center focus:ring-0 ${handleHeightSize().height} ${className}`}
      {...props}
      {...(to && { as: Link, to })}
      size={handleHeightSize().width}
    >
      {icon && (
        <div className={!!label ? 'mr-2 self-center' : ''}>
          <Icon width={iconWidth ? iconWidth : '18'} icon={icon} className="flex items-center justify-center" />
        </div>
      )}
      {customIcon && (
        <div className={!!label ? 'mr-2 self-center' : ''}>
          <CustomIcon
            definedIcon={customIcon}
            width={iconWidth ? iconWidth : '18'}
            height={iconHeight ? iconHeight : '18'}
            className="flex items-center justify-center"
          />
        </div>
      )}
      {label && labelSize && <p className={`flex self-center ${labelSize} items-center justify-center`}>{label}</p>}
      {children && children}
      {iconRight && (
        <div className={!!label ? 'ml-2' : ''}>
          <Icon width={iconWidth ? iconWidth : '22'} icon={iconRight} className="flex items-center justify-center" />
        </div>
      )}
    </FlowbiteButton>
  );
  return props.disabled && props.disabledMessage ? (
    <Tooltip placement={tooltipPlacement || 'top'} className={props?.disabledMessageClassName} content={props?.disabledMessage}>
      {element}
    </Tooltip>
  ) : (
    element
  );
};

Button.propTypes = {
  label: PropTypes.string,
  labelSize: PropTypes.string,
  icon: PropTypes.string,
  iconWidth: PropTypes.string,
  loading: PropTypes.bool,
  to: PropTypes.string,
  danger: PropTypes.bool,
  className: PropTypes.string,
  outline: PropTypes.bool,
  tertiary: PropTypes.bool,
  size: PropTypes.string,
};

Button.defaultProps = {
  label: '',
  labelSize: 'text-sm',
  icon: '',
  loading: false,
  to: '',
  danger: false,
  className: '',
  outline: false,
  tertiary: false,
};
