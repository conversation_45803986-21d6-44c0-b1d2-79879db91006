import React, { useEffect, useState } from "react";
import { TextInput, ListGroup } from "flowbite-react";

export const AutoComplete = ({
  label,
  placeholder,
  items,
  itemValueKey,
  itemLabelKey,
  excludedItems,
  onSelect,
  onSearch,
}) => {
  // State
  const [searchKeyword, setSearchKeyword] = useState("");

  // Methods
  const handleSelect = (option) => {
    const selectedItem = items.find(
      (item) => item[itemValueKey] === option[itemValueKey]
    );

    //   Emit
    if (selectedItem) {
      onSelect(selectedItem);
    }

    //   Reset
    setSearchKeyword("");
  };

  useEffect(() => {
    onSearch(searchKeyword);
  }, [searchKeyword]);

  return (
    <div className="relative">
      <TextInput
        value={searchKeyword}
        label={label}
        placeholder={placeholder}
        size="lg"
        onInput={(e) => setSearchKeyword(e.target.value)}
      />

      {items.length > 0 && (
        <div className="absolute top-[50px] left-0 right-0">
          <ListGroup className="w-full">
            {items.map((item) => (
              <ListGroup.Item
                key={item[itemValueKey]}
                disabled={excludedItems.includes(item[itemValueKey])}
                onClick={() =>
                  excludedItems.includes(item[itemValueKey])
                    ? () => {}
                    : handleSelect(item)
                }
              >
                {item[itemLabelKey]}
              </ListGroup.Item>
            ))}
          </ListGroup>
        </div>
      )}
    </div>
  );
};
