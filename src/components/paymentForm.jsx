import { useEffect } from 'react';
import { VITE_MOYASAR_PUBLISHABLE_KEY } from 'src/configs/api';

const PaymentForm = ({ amount, planId, organizationId }) => {
  const callbackUrl = `${window.location.origin}/payment-callback?planId=${planId}&organizationId=${organizationId}`;

  useEffect(() => {
    if (window.Moyasar) {
      window.Moyasar.init({
        element: '.mysr-form',
        amount: amount, // 1000 = 10 SAR
        currency: 'SAR',
        description: `Subscription for plan ${planId}`,
        publishable_api_key: VITE_MOYASAR_PUBLISHABLE_KEY,
        callback_url: callbackUrl,
        // methods: ['creditcard', 'stcpay', 'applepay'],
        methods: ['creditcard'],
        metadata: {
          planId: planId,
          organizationId: organizationId,
        },
      });
    }
  }, [amount, planId, organizationId]);

  return (
    <div style={{ padding: '2rem' }}>
      <h2>Complete your payment</h2>
      <div className="mysr-form"></div>
    </div>
  );
};

export default PaymentForm;
