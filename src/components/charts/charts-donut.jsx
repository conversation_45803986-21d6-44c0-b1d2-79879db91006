import { ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';

export const ChartsDonut = ({
  data,
  centeredTextOfChart = () => {},
  rightData = () => {},
  bottomData = () => {},
  metrics,
  innerRadius = 60,
  outerRadius = 95,
  width = '100%',
  height = 200,
  rightDataStyles,
}) => {
  return (
    <div>
      <div className="flex flex-col xslg:flex-row items-center">
        <div className="w-full xslg:w-3/5 relative">
          <ResponsiveContainer width={width} height={height}>
            <PieChart>
              <Pie data={data} cx="50%" cy="50%" innerRadius={innerRadius} outerRadius={outerRadius} fill="#8884d8" dataKey="value">
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>

          {centeredTextOfChart() && (
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">{centeredTextOfChart()}</div>
          )}
        </div>

        {rightData() && <div className={`w-fit flex flex-col justify-center space-y-2 ${rightDataStyles}`}>{rightData()}</div>}
      </div>
      {bottomData() && <div className="mt-6 pt-6 border-t">{bottomData()}</div>}
    </div>
  );
};
