// Recharts
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from 'recharts';

export const ChartsWavy = ({ data, dataKeys }) => {
  const keys = dataKeys || { a: 'A', b: 'B', c: 'C' };
  const colorConfig = {
    a: { stroke: '#3b82f6', gradient: 'colorA' },
    b: { stroke: '#10b981', gradient: 'colorB' },
    c: { stroke: '#a855f7', gradient: 'colorC' },
  };
  return (
    <ResponsiveContainer width="100%" height={300}>
      <AreaChart data={data}>
        <defs>
          <linearGradient id="colorA" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.5} />
            <stop offset="95%" stopColor="#3b82f6" stopOpacity={0} />
          </linearGradient>
          <linearGradient id="colorB" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#10b981" stopOpacity={0.5} />
            <stop offset="95%" stopColor="#10b981" stopOpacity={0} />
          </linearGradient>
          <linearGradient id="colorC" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#a855f7" stopOpacity={0.5} />
            <stop offset="95%" stopColor="#a855f7" stopOpacity={0} />
          </linearGradient>
        </defs>
        <XAxis dataKey="x" />
        <YAxis />
        <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
        <Tooltip
          formatter={(value, name) => {
            const displayName = keys[name] || name;
            return [value, displayName];
          }}
        />{' '}
        <Area type="monotone" dataKey="a" stroke="#3b82f6" fill="url(#colorA)" strokeWidth={2} name="a" />
        <Area type="monotone" dataKey="b" stroke="#10b981" fill="url(#colorB)" strokeWidth={2} name="b" />
        <Area type="monotone" dataKey="c" stroke="#a855f7" fill="url(#colorC)" strokeWidth={2} name="c" />
      </AreaChart>
    </ResponsiveContainer>
  );
};
