import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Legend, Tooltip, ResponsiveContainer } from 'recharts';

export const PieCharts = ({ data, COLORS, customLegend }) => {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <PieChart>
        <Pie data={data} cx="50%" cy="50%" outerRadius={80} dataKey="value">
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index]} />
          ))}
        </Pie>
        <Tooltip />

        {customLegend ? <Legend content={customLegend} /> : <Legend verticalAlign="bottom" align="center" layout="vertical" />}
      </PieChart>
    </ResponsiveContainer>
  );
};
