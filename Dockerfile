# Base image
FROM node:20.11.1

# Create app directory
WORKDIR /usr/src/app

# A wildcard is used to ensure both package.json AND package-lock.json are copied
COPY package*.json ./

# Install app dependencies
RUN npm install --legacy-peer-deps

# Bundle app source
COPY . .

# Build the production version of the app
RUN npm run build

# Expose port 3000
EXPOSE 3000

# Start the app using Vite preview mode
CMD ["npm", "run", "preview", "--", "--port", "3000"]
