/** @type {import('tailwindcss').Config} */
const flowbitePlugin = require('flowbite/plugin');

export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}', 'node_modules/flowbite-react/lib/esm/**/*.js'],

  theme: {
    extend: {
      // Hide scroll-x bar
      scrollbar: {
        hidden: 'scrollbar-hidden',
      },

      backgroundImage: {
        'magic-gradient': 'linear-gradient(80deg, #3b82f6, #a855f7, #ec4899)',
      },

      colors: {
        pastelPink: '#F4A8B6',
        pastelPurple: '#D3B4E0',
        pastelBlue: '#A2DFF7',
        pastelGreen: '#B6F0A1',
        pastelYellow: '#F7E0A2',
        pastelOrange: '#F9A65A',
        pastelRed: '#F9A1A1',
        lightgraybg: '#F9F9F9',
        darkGrayBackground: '#181720',
        secondaryGray: '#838383',
        grayDetail: '#6B7280',
        primaryPurple: '#7E3AF2',
        grayTextOnDarkMood: '#D6DBE3',
        darkBackgroundCard: '#22212A',
        darkBlueText: '#2F327D',
        halfGray: '#798296',
        arkHalfGray: '#d1d5db',
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
          950: '#172554',
        },
        // NB: Don't forget to change the colors in charts and PDF components
        // Poor
        chartPoorCircle: '#EF4444',
        chartPoorTextColor: '#991b1b',
        chartPoorBackground: '#FEE2E2',
        chartPoorBorder: '#FECACA',
        // Good
        chartGoodCircle: '#d97706',
        chartGoodTextColor: '#d97706',
        chartGoodBackground: '#FFFBEB',
        chartGoodBorder: '#d97706',
        // Excellent
        chartExcellentCircle: '#22C55E',
        chartExcellentTextColor: '#24C081',
        chartExcellentBackground: '#EBFFF0',
        chartExcellentBorder: '#E1FFE6',

        // *********** Dark mode Charts *************//
        // Poor
        darkChartPoorCircle: '#FCA5A5',
        darkChartPoorTextColor: '#FCA5A5',
        darkChartPoorBackground: '#4A1D1D',
        darkChartPoorBorder: '#B91C1C',

        // Good
        darkChartGoodCircle: '#fef3c7',
        darkChartGoodTextColor: '#fef3c7',
        darkChartGoodBackground: '#4E3B12',
        darkChartGoodBorder: '#fef3c7',

        // Excellet
        darkChartExcellentCircle: '86EFAC',
        darkChartExcellentTextColor: '#86EFAC',
        darkChartExcellentBackground: '#1E3A28',
        darkChartExcellentBorder: '#16A34A',

        // Status
        statusColorNotStartedText: '#667085',
        statusColorNotStartedBackground: '#F9FAFB',
        statusColorInProgressText: '#1976D2',
        statusColorInProgressBackground: '#E3F2FDC2',
        statusColorSubmittedText: '#10b981',
        statusColorSubmittedBackground: '#dcfce7',
        statusColorScheduleldText: '#6A1B9A',
        statusColorScheduleldBackground: '#F3E5F5C2',
        statusColorMissedText: '#C62828',
        statusColorMissedBackground: '#FFEBEE',
        statusColorOverdueText: '#FB8C00',
        statusColorOverdueBackground: '#FFF3E0',
        // Status dark
        statusDarkColorNotStartedText: '#F1FAFF',
        statusDarkColorNotStartedBackground: '#686774',
        statusDarkColorInProgressText: '#81D4FA',
        statusDarkColorInProgressBackground: '#2D6F8EC7',
        statusDarkColorScheduleldText: '#D1B3FF',
        statusDarkColorScheduleldBackground: '#684D7DC2',
        statusDarkColorMissedText: '#F6CFCB',
        statusDarkColorMissedBackground: '#D92D2066',
        statusDarkColorOverdueText: '#FFEBCE',
        statusDarkColorOverdueBackground: '#4A3621',
        // Status submitted
        statusColorExcellent: '#079455',
        statusBackgroundExcellent: '#ECFDF3',
        statusColorGood: '#DC6803',
        statusBackgroundGood: '#FFF6DB',
        statusColorPoor: '#D92D20',
        statusBackgroundPoor: '#FEF3F2',
        // Status dark submitted
        statusDarkColorExcellent: '#ECFDF3',
        statusDarkBackgroundExcellent: '#07945580',
        statusDarkColorGood: '#FFFAEB',
        statusDarkBackgroundGood: '#DC680380',
        statusDarkColorPoor: '#FEF3F2',
        statusDarkBackgroundPoor: '#D92D2080',
        // Inputs
        inputLabel: '#6B7280',
        inputDarkLabel: '#d1d5db',
        inputSubLabel: '#8C939F',
        // New Question Or New Answer
        newQuAnsText: '#8A43F9',
        newQuAnsDarkText: '#9ca3af', // text-gray-400
        newQuAnsBg: '#F6EAFC52',
        newQuAnsDarkBg: '#1f2937', // bg-gray-800
        // newQuAnsIcon: "", // primaryPurple
        newQuAnsHoverBg: '#f6f5ff', // bg-purple-50
      },

      screens: {
        xslg: '530px',
        xsmd: '425px',
        xssm: '375px',
      },
    },
    fontFamily: {
      body: [
        'Inter',
        'ui-sans-serif',
        'system-ui',
        '-apple-system',
        'system-ui',
        'Segoe UI',
        'Roboto',
        'Helvetica Neue',
        'Arial',
        'Noto Sans',
        'sans-serif',
        'Apple Color Emoji',
        'Segoe UI Emoji',
        'Segoe UI Symbol',
        'Noto Color Emoji',
      ],
      sans: [
        'Inter',
        'ui-sans-serif',
        'system-ui',
        '-apple-system',
        'system-ui',
        'Segoe UI',
        'Roboto',
        'Helvetica Neue',
        'Arial',
        'Noto Sans',
        'sans-serif',
        'Apple Color Emoji',
        'Segoe UI Emoji',
        'Segoe UI Symbol',
        'Noto Color Emoji',
      ],
    },
  },
  plugins: [
    flowbitePlugin,
    // Add Flowbite plugin
    // function ({ addUtilities }) {
    //   addUtilities({
    //     '.magic-gradient-border': {
    //       position: 'relative',
    //       padding: '2px',
    //       borderRadius: '4px',
    //       background: 'linear-gradient(80deg, #f472b6, #a855f7, #3b82f6)', // Gradient border
    //     },
    //     '.magic-gradient-border > *': {
    //       background: '#fff', // white background in light mode
    //       borderRadius: 'inherit',
    //     },
    //     '.dark .magic-gradient-border > *': {
    //       background: '#1f2937', // dark background in dark mode
    //       borderRadius: 'inherit',
    //     },
    //   });
    // },

    function ({ addUtilities }) {
      addUtilities(
        {
          // Hide scroll-x bar
          '.scrollbar-hidden': {
            'scrollbar-width': 'none' /* Firefox */,
            '-ms-overflow-style': 'none' /* IE and Edge */,
          },
          '.scrollbar-hidden::-webkit-scrollbar': {
            display: 'none' /* Safari and Chrome */,
          },

          /*
            Not exist in tailwind, We are safe from any overflow texting
            This will break big single word, will break large sentence
          */
          '.native-break-all-words': {
            'word-break': 'break-word',
          },
        },
        ['responsive', 'hover']
      );
    },
  ],
};
